package com.bimowu.unified.common.service;

import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;


public interface WechatService {
    /**
     * 验证微信服务器请求签名
     */
    boolean checkSignature(String signature, String timestamp, String nonce);

    /**
     * 解析微信XML消息
     */
    Map<String, String> parseXml(HttpServletRequest request) throws Exception;

    /**
     * 构建文本响应XML
     */
    String buildTextResponseXml(String toUserName, String fromUserName, String content);

    /**
     * 获取用户信息
     */
    Map<String, String> getUserInfo(String openid);
    
    /**
     * 创建临时二维码
     * @param sceneStr 场景值字符串
     * @param expireSeconds 过期时间（秒）
     * @return 包含ticket和url的Map
     */
    Map<String, String> createTempQrcode(String sceneStr, int expireSeconds);
}
