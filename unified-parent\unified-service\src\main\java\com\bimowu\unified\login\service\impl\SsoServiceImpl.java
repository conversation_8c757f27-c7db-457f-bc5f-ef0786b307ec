package com.bimowu.unified.login.service.impl;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.base.Constant;
import com.bimowu.unified.common.utils.RedisUtils;
import com.bimowu.unified.common.utils.TokenUtils;
import com.bimowu.unified.login.dao.KsUserDao;
import com.bimowu.unified.login.dao.SsoClientDao;
import com.bimowu.unified.login.model.KsUser;
import com.bimowu.unified.login.model.SsoClient;
import com.bimowu.unified.login.service.SsoService;
import com.bimowu.unified.utils.CookieUtils;
import com.bimowu.unified.utils.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * SSO单点登录服务实现类
 */
@Service
@Slf4j
public class SsoServiceImpl implements SsoService {

    @Autowired
    private KsUserDao ksUserDao;
    
    @Autowired
    private SsoClientDao ssoClientDao;
    
    @Autowired
    private RedisUtils redisUtils;
    
    @Autowired
    private HttpClientUtils httpClientUtils;
    
    // Token有效期（秒）
    private static final long TOKEN_EXPIRE = 7200; // 2小时
    
    // 刷新Token有效期（秒）
    private static final long REFRESH_TOKEN_EXPIRE = 604800; // 7天
    
    // 验证码有效期（秒）
    private static final long VERIFY_CODE_EXPIRE = 300; // 5分钟

    @Override
    public BaseResponse<Map<String, Object>> login(String username, String password, String clientId, String redirectUrl, HttpServletResponse response) {
        log.info("SSO用户登录: {}, clientId: {}", username, clientId);
        
        // 验证客户端
        SsoClient client = validateClient(clientId);
        if (client == null) {
            return BaseResponse.error(1100, "无效的客户端ID");
        }
        
        // 查询用户 - 尝试多种方式匹配用户名
        KsUser user = null;
        
        // 1. 尝试昵称匹配
        user = ksUserDao.selectByNickname(username);
        
        // 2. 尝试手机号匹配
        if (user == null) {
            user = ksUserDao.selectByPhone(username);
        }
        
        // 3. 尝试邮箱匹配
        if (user == null) {
            user = ksUserDao.selectByEmail(username);
        }
        
        if (user == null) {
            return BaseResponse.error(1001, "用户不存在");
        }
        
        // 校验密码
        String encryptPassword = DigestUtils.md5DigestAsHex(password.getBytes());
        if (!encryptPassword.equals(user.getPasswd())) {
            return BaseResponse.error(1002, "密码错误");
        }
        
        // 校验状态
        if (user.getState() != null && user.getState() != 0) {
            return BaseResponse.error(1003, "账号已禁用");
        }
        
        // 生成token和刷新token
        String token = generateToken();
        String refreshToken = generateToken();
        
        // 存储token信息到Redis
        Map<String, Object> tokenInfo = new HashMap<>();
        tokenInfo.put("userId", user.getUId());
        tokenInfo.put("clientId", clientId);
        tokenInfo.put("refreshToken", refreshToken);
        tokenInfo.put("createTime", System.currentTimeMillis());
        //存入sso cookie
        CookieUtils.setCookie(response, "/", 60 * 60 * 2, Constant.COOKIE_NAME, token);
        // 将token信息存储到Redis，设置过期时间
        redisUtils.set(String.format(Constant.TOKEN_PREFIX , token), tokenInfo, TOKEN_EXPIRE);
        
        // 更新最后登录时间
        user.setLasrLoginTime(new Date());
        ksUserDao.updateByPrimaryKeySelective(user);
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("refreshToken", refreshToken);
        result.put("expiresIn", TOKEN_EXPIRE);
        result.put("userInfo", getUserInfoMap(user));
        result.put("redirectUrl", StringUtils.isEmpty(redirectUrl)?client.getRedirectUrl():redirectUrl);
        
        return BaseResponse.ok(result);
    }

    @Override
    public BaseResponse<Map<String, Object>> mobileLogin(String mobile, String verifyCode, String clientId, String redirectUrl, HttpServletResponse response) {
        log.info("SSO手机号登录: {}, clientId: {}", mobile, clientId);
        
        // 验证客户端
        SsoClient client = validateClient(clientId);
        if (client == null) {
            return BaseResponse.error(1100, "无效的客户端ID");
        }
        
        // 校验验证码
        String cacheKey = Constant.VERIFY_CODE_PREFIX + mobile;
        Object cachedCode = redisUtils.get(cacheKey);
        if (cachedCode == null || !cachedCode.toString().equals(verifyCode)) {
            return BaseResponse.error(1004, "验证码错误或已过期");
        }
        
        // 查询用户
        KsUser user = ksUserDao.selectByPhone(mobile);
        if (user == null) {
            return BaseResponse.error(1001, "用户不存在");
        }
        
        // 校验状态
        if (user.getState() != null && user.getState() != 0) {
            return BaseResponse.error(1003, "账号已禁用");
        }
        
        // 生成token和刷新token
        String token = generateToken();
        String refreshToken = generateToken();
        
        // 存储token信息到Redis
        Map<String, Object> tokenInfo = new HashMap<>();
        tokenInfo.put("userId", user.getUId());
        tokenInfo.put("clientId", clientId);
        tokenInfo.put("refreshToken", refreshToken);
        tokenInfo.put("createTime", System.currentTimeMillis());
        //存入sso cookie
        CookieUtils.setCookie(response, "/", 60 * 60 * 2, Constant.COOKIE_NAME, token);
        // 将token信息存储到Redis，设置过期时间
        redisUtils.set(String.format(Constant.TOKEN_PREFIX , token), tokenInfo, TOKEN_EXPIRE);
        
        // 更新最后登录时间
        user.setLasrLoginTime(new Date());
        ksUserDao.updateByPrimaryKeySelective(user);
        
        // 清除验证码
        redisUtils.del(cacheKey);
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("refreshToken", refreshToken);
        result.put("expiresIn", TOKEN_EXPIRE);
        result.put("userInfo", getUserInfoMap(user));
        result.put("redirectUrl", StringUtils.isEmpty(redirectUrl)?client.getRedirectUrl():redirectUrl);
        return BaseResponse.ok(result);
    }

    @Override
    public BaseResponse<Map<String, Object>> validateToken() {
        String token = TokenUtils.getCurrentToken();
        log.info("SSO验证token: {}", token);
        
        if (StringUtils.isEmpty(token)) {
            return BaseResponse.error(1000, "token不能为空");
        }
        
        // 从Redis获取token信息
        String cacheKey = String.format(Constant.TOKEN_PREFIX ,token);
        Map<Object, Object> tokenInfo = (Map<Object, Object>)redisUtils.get(cacheKey);
        
        if (tokenInfo == null || tokenInfo.isEmpty()) {
            return BaseResponse.error(1000, "无效的token或token已过期");
        }
        
        // 获取用户ID
        Long userId = Long.parseLong(tokenInfo.get("userId").toString());
        
        // 查询用户信息
        KsUser user = ksUserDao.selectByPrimaryKey(userId);
        if (user == null) {
            return BaseResponse.error(1001, "用户不存在");
        }
        
        // 校验状态
        if (user.getState() != null && user.getState() != 0) {
            return BaseResponse.error(1003, "账号已禁用");
        }
        
        // 延长token有效期
        redisUtils.expire(cacheKey, TOKEN_EXPIRE);
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("userId", userId);
        result.put("userInfo", getUserInfoMap(user));
        result.put("clientId", tokenInfo.get("clientId"));
        
        return BaseResponse.ok(result);
    }

    @Override
    public BaseResponse<Map<String, Object>> refreshToken(String token, String clientId) {
        log.info("SSO刷新token: {}, clientId: {}", token, clientId);
        
        if (StringUtils.isEmpty(token)) {
            return BaseResponse.error(1000, "token不能为空");
        }
        
        // 验证客户端
        SsoClient client = validateClient(clientId);
        if (client == null) {
            return BaseResponse.error(1100, "无效的客户端ID");
        }
        
        // 从Redis获取token信息
        String cacheKey = Constant.TOKEN_PREFIX + token;
        Map<Object, Object> tokenInfo = redisUtils.hmget(cacheKey);
        
        if (tokenInfo == null || tokenInfo.isEmpty()) {
            return BaseResponse.error(1000, "无效的token或token已过期");
        }
        
        // 验证客户端ID是否匹配
        String storedClientId = tokenInfo.get("clientId").toString();
        if (!clientId.equals(storedClientId)) {
            return BaseResponse.error(1101, "客户端ID不匹配");
        }
        
        // 获取用户ID
        Long userId = Long.valueOf(tokenInfo.get("userId").toString());
        
        // 查询用户信息
        KsUser user = ksUserDao.selectByPrimaryKey(userId);
        if (user == null) {
            return BaseResponse.error(1001, "用户不存在");
        }
        
        // 校验状态
        if (user.getState() != null && user.getState() != 0) {
            return BaseResponse.error(1003, "账号已禁用");
        }
        
        // 生成新的token和刷新token
        String newToken = generateToken();
        String newRefreshToken = generateToken();
        
        // 存储新token信息到Redis
        Map<String, Object> newTokenInfo = new HashMap<>();
        newTokenInfo.put("userId", userId);
        newTokenInfo.put("clientId", clientId);
        newTokenInfo.put("refreshToken", newRefreshToken);
        newTokenInfo.put("createTime", System.currentTimeMillis());
        
        // 将新token信息存储到Redis，设置过期时间
        redisUtils.hmset(Constant.TOKEN_PREFIX + newToken, newTokenInfo, TOKEN_EXPIRE);
        
        // 删除旧token
        redisUtils.del(cacheKey);
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("token", newToken);
        result.put("refreshToken", newRefreshToken);
        result.put("expiresIn", TOKEN_EXPIRE);
        result.put("userInfo", getUserInfoMap(user));
        result.put("redirectUrl", client.getRedirectUrl());
        
        return BaseResponse.ok(result);
    }

    @Override
    public BaseResponse<String> logout(HttpServletResponse response, HttpServletRequest request) {
        String token = TokenUtils.getCurrentToken();
        log.info("SSO退出登录: {}", token);
        
        if (StringUtils.isEmpty(token)) {
            return BaseResponse.error(1000, "token不能为空");
        }
        
        // 从Redis获取token信息
        String cacheKey = String.format(Constant.TOKEN_PREFIX ,token);
        Map<Object, Object> tokenInfo = (Map<Object, Object>) redisUtils.get(cacheKey);
        
        // 清除cookie
        CookieUtils.clearCookie(request, response, Constant.COOKIE_NAME);
        
        // 删除当前token
        redisUtils.del(cacheKey);
        
        // 获取所有客户端，异步调用退出登录回调
        List<SsoClient> clients = ssoClientDao.selectAll();
        if (clients != null && !clients.isEmpty()) {
            log.info("开始通知所有客户端退出登录，客户端数量: {}", clients.size());
            
            // 如果tokenInfo不为空，获取用户ID和客户端ID用于通知
            String userId = null;
            String clientId = null;
            if (tokenInfo != null) {
                userId = tokenInfo.get("userId") != null ? tokenInfo.get("userId").toString() : null;
                clientId = tokenInfo.get("clientId") != null ? tokenInfo.get("clientId").toString() : null;
            }
            
            // 并行调用所有客户端的退出登录回调
            for (SsoClient client : clients) {
                if (!StringUtils.isEmpty(client.getLogoutUrl())) {
                    try {
                        // 构建请求头，将token放入请求头中
                        HttpHeaders headers = new HttpHeaders();
                        headers.set("token", token);

                        // 构建请求参数，仅包含用户ID和客户端ID
                        Map<String, Object> params = new HashMap<>();
                        if (userId != null) {
                            params.put("userId", userId);
                        }
                        if (clientId != null) {
                            params.put("clientId", clientId);
                        }
                        params.put("token", token);
                        log.info("开始调用客户端[{}]退出登录回调，参数: {}", client.getClientId(), params);
                        // 异步调用客户端退出登录回调
                        httpClientUtils.asyncRequest(client.getLogoutUrl(), HttpMethod.POST, params, headers)
                            .thenAccept(result -> log.info("客户端[{}]退出登录回调响应: {}", client.getClientId(), result))
                            .exceptionally(ex -> {
                                log.error("客户端[{}]退出登录回调异常: {}", client.getClientId(), ex.getMessage());
                                return null;
                            });
                    } catch (Exception e) {
                        log.error("调用客户端[{}]退出登录回调异常: {}", client.getClientId(), e.getMessage());
                    }
                }
            }
        }
        
        return BaseResponse.ok("退出成功");
    }

    @Override
    public BaseResponse<Map<String, String>> registerClient(String appName, String appUrl, String redirectUrl) {
        log.info("SSO注册客户端应用: {}", appName);
        
        // 检查应用名称是否已存在
        SsoClient existClient = ssoClientDao.selectByAppName(appName);
        if (existClient != null) {
            return BaseResponse.error(1102, "应用名称已存在");
        }
        
        // 生成客户端ID和密钥
        String clientId = "client_" + UUID.randomUUID().toString().replace("-", "");
        String clientSecret = UUID.randomUUID().toString().replace("-", "");
        
        // 创建客户端应用记录
        SsoClient client = new SsoClient();
        client.setClientId(clientId);
        client.setClientSecret(clientSecret);
        client.setAppName(appName);
        client.setAppUrl(appUrl);
        client.setRedirectUrl(redirectUrl);
        client.setCreateTime(new Date());
        client.setUpdateTime(new Date());
        client.setStatus(0); // 正常状态
        
        // 保存到数据库
        ssoClientDao.insert(client);
        
        // 返回结果
        Map<String, String> result = new HashMap<>();
        result.put("clientId", clientId);
        result.put("clientSecret", clientSecret);
        
        return BaseResponse.ok(result);
    }
    
    /**
     * 验证客户端是否有效
     * @param clientId 客户端ID
     * @return 客户端信息，如果无效则返回null
     */
    private SsoClient validateClient(String clientId) {
        if (StringUtils.isEmpty(clientId)) {
            return null;
        }
        
        SsoClient client = ssoClientDao.selectByPrimaryKey(clientId);
        if (client == null) {
            return null;
        }
        
        // 检查状态
        if (client.getStatus() != null && client.getStatus() != 0) {
            return null;
        }
        
        return client;
    }
    
    /**
     * 生成随机token
     * @return token字符串
     */
    private String generateToken() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 获取用户信息Map
     * @param user 用户对象
     * @return 用户信息Map
     */
    private Map<String, Object> getUserInfoMap(KsUser user) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getUId());
        userInfo.put("nickname", user.getNickname());
        userInfo.put("mobile", user.getPhone());
        userInfo.put("email", user.getEmail());
        userInfo.put("lastLoginTime", user.getLasrLoginTime());
        return userInfo;
    }
} 