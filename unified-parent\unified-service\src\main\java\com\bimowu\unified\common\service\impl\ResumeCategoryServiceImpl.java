package com.bimowu.unified.common.service.impl;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.common.dao.ResumeCategoryDao;
import com.bimowu.unified.common.model.ResumeCategory;
import com.bimowu.unified.common.service.ResumeCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 职位类别服务实现类
 */
@Service
@Slf4j
public class ResumeCategoryServiceImpl implements ResumeCategoryService {

    @Autowired
    private ResumeCategoryDao resumeCategoryDao;

    @Override
    public BaseResponse<List<ResumeCategory>> getAllEnabledCategories() {
        log.info("获取所有启用的职位类别");
        
        try {
            List<ResumeCategory> categories = resumeCategoryDao.selectEnabledCategories();
            log.info("获取到{}个职位类别", categories.size());
            return BaseResponse.ok(categories);
        } catch (Exception e) {
            log.error("获取职位类别失败", e);
            return BaseResponse.error(500, "获取职位类别失败");
        }
    }

    @Override
    public BaseResponse<List<ResumeCategory>> getCategoriesByUserId(Long userId) {
        log.info("根据用户ID获取职位类别: {}", userId);
        
        try {
            List<ResumeCategory> categories = resumeCategoryDao.selectByUserId(userId);
            log.info("用户{}有{}个职位类别", userId, categories.size());
            return BaseResponse.ok(categories);
        } catch (Exception e) {
            log.error("获取用户职位类别失败", e);
            return BaseResponse.error(500, "获取用户职位类别失败");
        }
    }
}
