<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.unified.common.dao.SysSystemDao">
    <resultMap id="BaseResultMap" type="com.bimowu.unified.common.model.SysSystem">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="description" jdbcType="VARCHAR" property="description" />
        <result column="icon" jdbcType="VARCHAR" property="icon" />
        <result column="url" jdbcType="VARCHAR" property="url" />
        <result column="bg_image" jdbcType="VARCHAR" property="bgImage" />
        <result column="sort" jdbcType="INTEGER" property="sort" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, name, description, icon, url, bg_image, sort, status, create_time, update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_system
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectAllEnabled" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_system
        where status = 1
        order by sort asc, id asc
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_system
        order by sort asc, id asc
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from sys_system
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.bimowu.unified.common.model.SysSystem">
        insert into sys_system (name, description, icon, url, bg_image, sort, status)
        values (
            #{name,jdbcType=VARCHAR},
            #{description,jdbcType=VARCHAR},
            #{icon,jdbcType=VARCHAR},
            #{url,jdbcType=VARCHAR},
            #{bgImage,jdbcType=VARCHAR},
            #{sort,jdbcType=INTEGER},
            #{status,jdbcType=INTEGER}
        )
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.bimowu.unified.common.model.SysSystem">
        update sys_system
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="icon != null">
                icon = #{icon,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="bgImage != null">
                bg_image = #{bgImage,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper> 