package com.bimowu.unified.common.dao;

import com.bimowu.unified.common.model.SysNews;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * SysNews数据库操作接口类
 */
@Repository
public interface SysNewsDao {

    /**
     * 查询所有已发布的新闻，按发布时间倒序排列
     */
    List<SysNews> selectAllPublished();

    /**
     * 查询（根据主键ID查询）
     */
    SysNews selectByPrimaryKey(@Param("id") Long id);

    /**
     * 删除（根据主键ID删除）
     */
    int deleteByPrimaryKey(@Param("id") Long id);

    /**
     * 添加
     */
    int insert(SysNews record);

    /**
     * 修改（匹配有值的字段）
     */
    int updateByPrimaryKeySelective(SysNews record);
}