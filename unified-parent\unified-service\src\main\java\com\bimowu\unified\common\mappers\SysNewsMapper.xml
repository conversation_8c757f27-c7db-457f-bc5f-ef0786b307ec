<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.unified.common.dao.SysNewsDao">
    <resultMap id="BaseResultMap" type="com.bimowu.unified.common.model.SysNews">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="title" jdbcType="VARCHAR" property="title" />
        <result column="content" jdbcType="VARCHAR" property="content" />
        <result column="author" jdbcType="VARCHAR" property="author" />
        <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, title, content, author, publish_time, status, create_time, update_time
    </sql>

    <select id="selectAllPublished" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_news
        where status = 1
        order by publish_time desc
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_news
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from sys_news
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.bimowu.unified.common.model.SysNews">
        insert into sys_news (title, content, author, publish_time, status)
        values (
            #{title,jdbcType=VARCHAR},
            #{content,jdbcType=VARCHAR},
            #{author,jdbcType=VARCHAR},
            #{publishTime,jdbcType=TIMESTAMP},
            #{status,jdbcType=INTEGER}
        )
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.bimowu.unified.common.model.SysNews">
        update sys_news
        <set>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="author != null">
                author = #{author,jdbcType=VARCHAR},
            </if>
            <if test="publishTime != null">
                publish_time = #{publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>