import request from '../utils/request'

// 账号密码登录
export function loginByPassword(data) {
  return request({
    url: '/sso/login',
    method: 'post',
    data
  })
}

// 手机号验证码登录
export function loginByMobile(data) {
  return request({
    url: '/sso/mobileLogin',
    method: 'post',
    data
  })
}

// 获取短信验证码
export function getVerifyCode(mobile) {
  return request({
    url: '/sso/verifyCode',
    method: 'get',
    params: { mobile }
  })
}

// 验证图形验证码
export function verifyCaptcha(code) {
  return request({
    url: '/sso/verifyCaptcha',
    method: 'get',
    params: { code }
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/user/info',
    method: 'get'
  })
}

// 修改密码
export function updatePassword(data) {
  return request({
    url: '/user/updatePassword',
    method: 'post',
    params: data
  })
}

// 找回密码
export function resetPassword(data) {
  return request({
    url: '/user/resetPassword',
    method: 'post',
    params: data
  })
}

// 修改用户名
export function updateUsername(data) {
  return request({
    url: '/user/updateUsername',
    method: 'post',
    params: data
  })
}

// 生成微信绑定二维码
export function generateWechatQrCode() {
  return request({
    url: '/user/generateWechatQrCode',
    method: 'get'
  })
}

// 查询微信绑定状态
export function checkWechatBindStatus() {
  return request({
    url: '/user/checkWechatBindStatus',
    method: 'get'
  })
}

// 登出
export function logout() {
  return request({
    url: '/sso/logout',
    method: 'post'
  })
} 