package com.bimowu.unified.user;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.common.model.SysUser;
import com.bimowu.unified.common.service.UserService;
import com.bimowu.unified.common.service.WechatService;
import com.bimowu.unified.common.utils.TokenUtils;
import com.bimowu.unified.login.model.KsUser;
import com.bimowu.unified.login.service.LoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Map;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/user")
@Slf4j
public class UserController {

    @Autowired
    private UserService userService;
    @Autowired
    private WechatService wechatService;
    @Autowired
    private LoginService loginService;

    /**
     * 获取用户信息
     */
    @GetMapping("/info")
    public BaseResponse<KsUser> getUserInfo(@RequestHeader Long userId) {
        log.info("获取用户信息请求");
        if (userId == null) {
            return BaseResponse.error(1000, "无效的token");
        }
        return loginService.getUserInfo(userId);
    }
    
    /**
     * 修改密码
     */
    @PostMapping("/updatePassword")
    public BaseResponse<String> updatePassword(@RequestParam String oldPassword,
                                               @RequestParam String newPassword,
                                               @RequestHeader Long userId) {
        log.info("修改密码请求");
        if (userId == null) {
            return BaseResponse.error(1000, "无效的token");
        }
        return loginService.updatePassword(userId, oldPassword, newPassword);
    }
    
    /**
     * 找回密码
     */
    @PostMapping("/resetPassword")
    public BaseResponse<String> resetPassword(@RequestParam String mobile,
                                              @RequestParam String verifyCode,
                                              @RequestParam String newPassword) {
        log.info("找回密码请求: {}", mobile);
        return loginService.resetPassword(mobile, verifyCode, newPassword);
    }
    
    /**
     * 修改用户名
     */
    @PostMapping("/updateUsername")
    public BaseResponse<String> updateUsername(@RequestParam String newUsername,@RequestHeader Long userId) {
        log.info("修改用户名请求");

        if (userId == null) {
            return BaseResponse.error(1000, "无效的token");
        }
        return loginService.updateUsername(userId, newUsername);
    }
    
    /**
     * 生成微信绑定二维码
     */
    @GetMapping("/generateWechatQrCode")
    public BaseResponse<Map<String, Object>> generateWechatQrCode(@RequestHeader Long userId) {
        log.info("生成微信绑定二维码请求");
        if (userId == null) {
            return BaseResponse.error(1000, "无效的token");
        }
        return userService.generateWechatQrCode(userId);
    }
    
    /**
     * 查询微信绑定状态
     */
    @GetMapping("/checkWechatBindStatus")
    public BaseResponse<Map<String, Object>> checkWechatBindStatus(@RequestHeader Long userId) {
        log.info("查询微信绑定状态请求");
        if (userId == null) {
            return BaseResponse.error(1000, "无效的token");
        }
        return userService.checkWechatBindStatus(userId);
    }

    /**
     * 微信公众号回调接口
     * 用于处理微信服务器发送的消息和事件通知
     */
    @RequestMapping(value = "/wechat/callback", method = {RequestMethod.GET, RequestMethod.POST})
    public void wechatCallback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("接收到微信回调请求");
        
        // 微信服务器认证请求处理
        if (request.getMethod().equals("GET")) {
            // 微信加密签名
            String signature = request.getParameter("signature");
            // 时间戳
            String timestamp = request.getParameter("timestamp");
            // 随机数
            String nonce = request.getParameter("nonce");
            // 随机字符串
            String echostr = request.getParameter("echostr");
            
            log.info("微信认证请求: signature={}, timestamp={}, nonce={}, echostr={}", 
                    signature, timestamp, nonce, echostr);
            
            // 验证请求是否来自微信服务器
            if (wechatService.checkSignature(signature, timestamp, nonce)) {
                log.info("微信认证成功");
                // 原样返回echostr参数内容
                PrintWriter out = response.getWriter();
                out.print(echostr);
                out.flush();
                out.close();
            } else {
                log.warn("微信认证失败");
            }
            return;
        }
        
        // 处理微信服务器推送的消息和事件
        if (request.getMethod().equals("POST")) {
            try {
                // 解析XML消息
                Map<String, String> requestMap = wechatService.parseXml(request);
                
                // 获取消息类型
                String msgType = requestMap.get("MsgType");
                
                // 处理事件推送
                if ("event".equals(msgType)) {
                    String event = requestMap.get("Event");
                    String openid = requestMap.get("FromUserName");
                    
                    // 处理关注事件
                    if ("subscribe".equals(event)) {
                        log.info("用户关注事件: openid={}", openid);
                        
                        // 获取场景值（如果有）
                        String eventKey = requestMap.get("EventKey");
                        if (eventKey != null && eventKey.startsWith("qrscene_")) {
                            // 提取场景值
                            String sceneStr = eventKey.substring(8); // 去掉前缀"qrscene_"
                            log.info("扫码关注，场景值: {}", sceneStr);
                            
                            // 处理用户绑定
                            String nickname = wechatService.getUserInfo(openid).get("nickname");
                            userService.handleWechatScan(sceneStr, openid, null, nickname, 1);
                        }
                        
                        // 回复欢迎消息
                        String responseXml = wechatService.buildTextResponseXml(
                                requestMap.get("FromUserName"),
                                requestMap.get("ToUserName"),
                                "嗨！墨崽终于等到你啦~\uD83C\uDF89\n" +
                                        " 这里是笔墨屋，一家专注于IT就业的互联网科技公司！"
                        );
                        response.setContentType("application/xml;charset=UTF-8");
                        PrintWriter out = response.getWriter();
                        out.print(responseXml);
                        out.flush();
                        out.close();
                        return;
                    }
                    
                    // 处理取消关注事件
                    if ("unsubscribe".equals(event)) {
                        log.info("用户取消关注: openid={}", openid);
                        // 可以在这里处理用户解绑逻辑
                    }
                    
                    // 处理已关注用户扫码事件
                    if ("SCAN".equals(event)) {
                        String sceneStr = requestMap.get("EventKey");
                        log.info("已关注用户扫码，场景值: {}", sceneStr);
                        
                        // 处理用户绑定
                        String nickname = wechatService.getUserInfo(openid).get("nickname");
                        userService.handleWechatScan(sceneStr, openid, null, nickname, 1);
                        
                        // 回复绑定成功消息
                        String responseXml = wechatService.buildTextResponseXml(
                                requestMap.get("FromUserName"),
                                requestMap.get("ToUserName"),
                                "您已成功绑定笔墨屋统一认证服务平台账号！"
                        );
                        response.setContentType("application/xml;charset=UTF-8");
                        PrintWriter out = response.getWriter();
                        out.print(responseXml);
                        out.flush();
                        out.close();
                        return;
                    }
                }
                
                // 默认回复空字符串（告知微信服务器已收到消息）
                response.getWriter().write("");
            } catch (Exception e) {
                log.error("处理微信回调异常", e);
                response.getWriter().write("");
            }
        }
    }
} 