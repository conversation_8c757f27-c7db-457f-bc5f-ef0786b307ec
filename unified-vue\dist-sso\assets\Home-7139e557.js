import{y as ae,j as H,r as y,A as le,B as re,k as l,l as c,C as m,F as s,G as t,E as g,D as e,I as r,p,K as f,J as d,L as V,M as D,N as E,O as F,P as ce,Q as ie,R as de,H as ue,S as _e,T as me,U as pe}from"./vendor-477e13dd.js";import{s as v,u as ge,b as fe}from"./index-e9d76465.js";import{P as j}from"./ProgressChart-d0e3e8df.js";import{_ as ve}from"./index-97fb9448.js";function he(){return v({url:"/todo/list",method:"get"})}function ye(L){return v({url:`/todo/complete/${L}`,method:"post"})}function ke(){return v({url:"/system/list",method:"get"})}function we(){return v({url:"/progress/user",method:"get"})}const be={class:"home-container"},Ce={class:"header-content"},Te={class:"user-info"},Ie={class:"el-dropdown-link"},Le={class:"container"},xe={class:"top-section"},Be={class:"list-container"},Se=["onClick"],ze={class:"item-info"},Pe={class:"item-date"},Me={class:"progress-container"},Ne={key:0,class:"progress-empty"},Ue={key:1,class:"progress-chart"},$e={class:"bottom-section"},He={class:"system-content",style:{background:"rgba(255,255,255,0.85)","border-radius":"8px",padding:"24px 0"}},Ve={class:"system-icon"},De={class:"system-name"},Ee={class:"system-desc"},Fe={class:"system-action"},je={components:{ProgressChart:j}},Oe=Object.assign(je,{__name:"Home",setup(L){const x=ae(),i=ge(),k=H(()=>i.userInfo),B=H(()=>i.isLoggedIn),u=y([]),S=y([]),w=y(!1),b=y(null);let h=null;const z=async()=>{if(i.isLoggedIn)try{await v({url:"/sso/validateToken",method:"get"}),console.log("Token验证成功，继续保持登录状态")}catch(o){console.error("Token验证失败:",o),o.message&&(o.message.includes("token")||o.message.includes("登录"))&&(i.logout(),g.warning("登录已过期，请重新登录"),u.value=[])}},O=o=>{const n={墨仔简历系统:"Document",墨仔面试系统:"Headset",面试喵:"Notebook",默认:"Monitor"};return n[o]||n.默认},P=async()=>{try{const o=await he();u.value=o.data||[]}catch(o){console.error("获取待办列表失败:",o),o.response&&o.response.status===401&&(u.value=[])}},R=async()=>{if(i.isLoggedIn)try{w.value=!0;const o=await we();b.value=o.data||null}catch(o){console.error("获取用户进度失败:",o),b.value=null}finally{w.value=!1}},q=async()=>{try{const o=await ke();S.value=o.data||[]}catch(o){console.error("获取系统列表失败:",o)}},A=o=>{E.confirm("您确定已完成相关知识的学习了吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then(()=>{G(o)}).catch(()=>{})},G=async o=>{try{await ye(o.id),g.success(`已完成待办事项：${o.content}`),await P()}catch(n){console.error("完成待办事项失败:",n),g.error("操作失败，请重试")}},J=o=>{window.open(o.url,"_blank")},K=o=>{o.url&&window.open(o.url,"_blank")},Q=()=>{E.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{localStorage.setItem("logoutFlag","1"),await fe(),i.logout(),g.success("已退出登录")}catch(o){console.error("退出登录失败:",o),i.logout(),g.warning("退出登录失败，但已清除本地登录状态")}}).catch(()=>{})},W=()=>{x.push({path:"/login",query:{clientId:"sso_client"}})},X=()=>{x.push("/profile")};return le(()=>{z(),h=setInterval(z,5*60*1e3),i.isLoggedIn?(P(),R()):u.value=[],q()}),re(()=>{h&&(clearInterval(h),h=null)}),(o,n)=>{const Y=l("el-avatar"),_=l("el-icon"),M=l("el-dropdown-item"),Z=l("el-dropdown-menu"),ee=l("el-dropdown"),C=l("el-button"),oe=l("el-header"),N=l("el-empty"),se=l("el-tag"),T=l("el-card"),I=l("el-col"),U=l("el-row"),te=l("el-main"),ne=l("el-footer");return c(),m("div",be,[s(oe,{class:"header"},{default:t(()=>[e("div",Ce,[n[3]||(n[3]=e("div",{class:"logo"},[e("i",{class:"el-icon-platform-eleme logo-icon"}),r(" 笔墨屋统一认证服务平台 ")],-1)),e("div",Te,[B.value?(c(),p(ee,{key:0,trigger:"click"},{dropdown:t(()=>[s(Z,null,{default:t(()=>[s(M,{onClick:X},{default:t(()=>[s(_,null,{default:t(()=>[s(f(F))]),_:1}),n[0]||(n[0]=r(" 个人中心 "))]),_:1,__:[0]}),s(M,{onClick:Q},{default:t(()=>[s(_,null,{default:t(()=>[s(f(ce))]),_:1}),n[1]||(n[1]=r(" 退出登录 "))]),_:1,__:[1]})]),_:1})]),default:t(()=>[e("span",Ie,[s(Y,{size:32,class:"user-avatar"},{default:t(()=>[r(d(k.value.nickname?k.value.nickname.substring(0,1):"U"),1)]),_:1}),r(" 欢迎，"+d(k.value.nickname)+" ",1),s(_,{class:"el-icon--right"},{default:t(()=>[s(f(ie))]),_:1})])]),_:1})):(c(),p(C,{key:1,type:"primary",onClick:W,class:"login-button"},{default:t(()=>[s(_,null,{default:t(()=>[s(f(F))]),_:1}),n[2]||(n[2]=r(" 登录 "))]),_:1,__:[2]}))])])]),_:1}),s(te,{class:"main"},{default:t(()=>[n[9]||(n[9]=e("div",{class:"banner"},[e("div",{class:"banner-content"},[e("h1",{class:"banner-title"},"欢迎使用笔墨屋统一认证服务平台"),e("p",{class:"banner-desc"},"一站式登录，畅享所有系统服务")])],-1)),e("div",Le,[e("div",xe,[s(U,{gutter:20},{default:t(()=>[s(I,{span:12},{default:t(()=>[s(T,{class:"card",shadow:"hover"},{header:t(()=>n[4]||(n[4]=[e("div",{class:"card-header"},[e("span",null,[e("i",{class:"el-icon-document-checked card-icon"}),r(" 待办事项")])],-1)])),default:t(()=>[e("div",Be,[u.value.length===0?(c(),p(N,{key:0,description:"暂无待办事项"})):(c(!0),m(V,{key:1},D(u.value,a=>(c(),m("div",{class:"list-item",key:a.id},[e("div",{class:"item-title",onClick:$=>K(a)},[s(_,null,{default:t(()=>[s(f(de))]),_:1}),r(" "+d(a.content),1)],8,Se),e("div",ze,[e("span",Pe,d(a.date||"今天"),1),s(se,{size:"small",effect:"plain",type:"info"},{default:t(()=>[r(d(a.status||"待处理"),1)]),_:2},1024),a.todoType===1?(c(),p(C,{key:0,size:"small",type:"success",onClick:$=>A(a)},{default:t(()=>n[5]||(n[5]=[r("完成")])),_:2,__:[5]},1032,["onClick"])):ue("",!0)])]))),128))])]),_:1})]),_:1}),s(I,{span:12},{default:t(()=>[s(T,{class:"card",shadow:"hover"},{header:t(()=>n[6]||(n[6]=[e("div",{class:"card-header"},[e("span",null,[e("i",{class:"el-icon-trend-charts card-icon"}),r(" 我的进度")])],-1)])),default:t(()=>[e("div",Me,[B.value?(c(),m("div",Ue,[s(j,{"user-progress":b.value,loading:w.value},null,8,["user-progress","loading"])])):(c(),m("div",Ne,[s(N,{description:"请先登录查看进度"})]))])]),_:1})]),_:1})]),_:1})]),e("div",$e,[n[8]||(n[8]=e("div",{class:"section-header"},[e("h2",{class:"section-title"},[e("i",{class:"el-icon-menu"}),r(" 系统导航")]),e("div",{class:"section-divider"})],-1)),s(U,{gutter:30},{default:t(()=>[(c(!0),m(V,null,D(S.value,a=>(c(),p(I,{xs:24,sm:12,md:8,lg:6,key:a.id},{default:t(()=>[s(T,{class:_e(["system-card",{"resume-system":a.name==="墨仔简历系统","interview-system":a.name==="墨仔面试系统"}]),shadow:"hover",onClick:$=>J(a),style:me(a.bgImage?`background-image: url(${a.bgImage}); background-size: cover; background-position: center;`:"")},{default:t(()=>[e("div",He,[e("div",Ve,[s(_,{size:32},{default:t(()=>[(c(),p(pe(O(a.name))))]),_:2},1024)]),e("div",De,d(a.name),1),e("div",Ee,d(a.description),1),e("div",Fe,[s(C,{type:"primary",size:"small",round:""},{default:t(()=>n[7]||(n[7]=[r("进入系统")])),_:1,__:[7]})])])]),_:2},1032,["onClick","style","class"])]),_:2},1024))),128))]),_:1})])])]),_:1,__:[9]}),s(ne,{class:"footer"},{default:t(()=>n[10]||(n[10]=[e("div",{class:"footer-content"},[e("div",{class:"footer-copyright"}," © 2025 笔墨屋统一认证服务平台 - 版权所有 ")],-1)])),_:1,__:[10]})])}}}),Je=ve(Oe,[["__scopeId","data-v-5e6680d1"]]);export{Je as default};
