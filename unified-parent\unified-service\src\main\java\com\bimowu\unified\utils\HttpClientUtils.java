package com.bimowu.unified.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * HTTP客户端工具类
 */
@Slf4j
@Component
public class HttpClientUtils {

    @Autowired
    private RestTemplate restTemplate;
    
    /**
     * 异步发送GET请求
     * @param url 请求地址
     * @param params 请求参数
     * @return 响应结果
     */
    public CompletableFuture<String> asyncGet(String url, Map<String, Object> params) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 构建请求URL
                StringBuilder urlBuilder = new StringBuilder(url);
                if (params != null && !params.isEmpty()) {
                    urlBuilder.append("?");
                    for (Map.Entry<String, Object> entry : params.entrySet()) {
                        urlBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                    }
                    urlBuilder.deleteCharAt(urlBuilder.length() - 1);
                }
                
                log.info("异步发送GET请求: {}", urlBuilder.toString());
                ResponseEntity<String> response = restTemplate.getForEntity(urlBuilder.toString(), String.class);
                log.info("异步GET请求响应: {}", response.getBody());
                return response.getBody();
            } catch (Exception e) {
                log.error("异步GET请求异常: {}", e.getMessage());
                return null;
            }
        });
    }
    
    /**
     * 异步发送POST请求
     * @param url 请求地址
     * @param params 请求参数
     * @return 响应结果
     */
    public CompletableFuture<String> asyncPost(String url, Map<String, Object> params) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("异步发送POST请求: {}, 参数: {}", url, params);
                ResponseEntity<String> response = restTemplate.postForEntity(url, params, String.class);
                log.info("异步POST请求响应: {}", response.getBody());
                return response.getBody();
            } catch (Exception e) {
                log.error("异步POST请求异常: {}", e.getMessage());
                return null;
            }
        });
    }
    
    /**
     * 异步发送请求
     * @param url 请求地址
     * @param method 请求方法
     * @param params 请求参数
     * @param headers 请求头
     * @return 响应结果
     */
    public CompletableFuture<String> asyncRequest(String url, HttpMethod method, Map<String, Object> params, HttpHeaders headers) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("异步发送{}请求: {}, 参数: {}", method, url, params);
                HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
                ResponseEntity<String> response = restTemplate.exchange(url, method, requestEntity, String.class);
                log.info("异步{}请求响应: {}", method, response.getBody());
                return response.getBody();
            } catch (Exception e) {
                log.error("异步{}请求异常: {}", method, e.getMessage());
                return null;
            }
        });
    }
} 