package com.bimowu.unified.controller;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.base.Constant;
import com.bimowu.unified.common.utils.RedisUtils;
import com.bimowu.unified.common.utils.TokenUtils;
import com.bimowu.unified.login.service.SsoService;
import com.bimowu.unified.login.service.VerifyCodeService;
import com.bimowu.unified.utils.CookieUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * SSO单点登录控制器
 */
@RestController
@RequestMapping("/sso")
@Slf4j
public class SsoController {

    @Autowired
    private SsoService ssoService;
    
    @Autowired
    private VerifyCodeService verifyCodeService;
    @Autowired
    private RedisUtils redisUtils;
    @Value("${login.loginUrl}")
    private String loginUrl;
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public BaseResponse<Map<String, Object>> login(@RequestBody LoginRequest request, HttpServletResponse  response) {
        log.info("SSO用户登录请求: {}, clientId: {},redirectUrl:{}", request.username, request.clientId, request.redirectUrl);
        return ssoService.login(request.username, request.password, request.clientId, request.redirectUrl,response);
    }
    
    /**
     * 手机号登录
     */
    @PostMapping("/mobileLogin")
    public BaseResponse<Map<String, Object>> mobileLogin(@RequestBody MobileLoginRequest request, HttpServletResponse  response) {
        log.info("SSO手机号登录请求: {}, clientId: {},redirectUrl:{}", request.mobile, request.clientId, request.redirectUrl);
        return ssoService.mobileLogin(request.mobile, request.verifyCode, request.clientId, request.redirectUrl,response);
    }
    
    /**
     * 获取短信验证码
     */
    @GetMapping("/verifyCode")
    public BaseResponse<String> getVerifyCode(@RequestParam String mobile) {
        log.info("SSO获取验证码请求: {}", mobile);
        return verifyCodeService.sendSmsCode(mobile);
    }
    
    /**
     * 生成图形验证码
     */
    @GetMapping("/captcha")
    public void generateCaptcha(HttpServletResponse response, HttpServletRequest request) {
        // 使用客户端IP作为会话标识，避免跨域请求时session丢失
        String clientIp = getClientIp(request);
        String sessionId = clientIp != null ? clientIp : request.getSession().getId();
        log.info("SSO生成图形验证码请求, sessionId: {}", sessionId);
        verifyCodeService.generateCaptcha(response, sessionId);
    }
    
    /**
     * 验证图形验证码
     */
    @GetMapping("/verifyCaptcha")
    public BaseResponse<Boolean> verifyCaptcha(HttpServletRequest request, @RequestParam String code) {
        // 使用客户端IP作为会话标识，避免跨域请求时session丢失
        String clientIp = getClientIp(request);
        String sessionId = clientIp != null ? clientIp : request.getSession().getId();
        log.info("SSO验证图形验证码请求, sessionId: {}, code: {}", sessionId, code);
        boolean result = verifyCodeService.verifyCaptcha(sessionId, code);
        if (result) {
            return BaseResponse.ok(true);
        } else {
            return BaseResponse.error(1202, "验证码错误");
        }
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    
    /**
     * 验证token是否有效
     */
    @GetMapping("/validateToken")
    public BaseResponse<Map<String, Object>> validateToken() {
        String token = TokenUtils.getCurrentToken();
        log.info("SSO验证token请求: {}", token);
        return ssoService.validateToken();
    }
    
    /**
     * 刷新token
     */
    @PostMapping("/refreshToken")
    public BaseResponse<Map<String, Object>> refreshToken(@RequestBody RefreshTokenRequest request) {
        log.info("SSO刷新token请求: {}", request.token);
        return ssoService.refreshToken(request.token, request.clientId);
    }
    
    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public BaseResponse<String> logout(HttpServletResponse response, HttpServletRequest request) {
        String token = TokenUtils.getCurrentToken();
        log.info("SSO退出登录请求: {}", token);
        return ssoService.logout(response, request);
    }
    
    /**
     * 注册客户端应用
     */
    @PostMapping("/registerClient")
    public BaseResponse<Map<String, String>> registerClient(@RequestBody RegisterClientRequest request) {
        log.info("SSO注册客户端应用请求: {}", request.appName);
        return ssoService.registerClient(request.appName, request.appUrl, request.redirectUrl);
    }
    @RequestMapping("/auth")
    public BaseResponse<Map<String, String>> auth(HttpServletResponse  response, HttpServletRequest  request,@RequestParam String clientId,@RequestParam String redirectUrl) throws IOException {
        String cookie = CookieUtils.getValue(request,Constant.COOKIE_NAME);
        Map<String, Object>  map = (Map<String, Object> )redisUtils.get(String.format(Constant.TOKEN_PREFIX , cookie));
        if(cookie!=null && map!=null){
            log.info("SSO登录校验:已登录");
            // 将token添加到重定向URL中，以便客户端可以获取
            String callbackUrl = redirectUrl;
            if (redirectUrl.contains("?")) {
                callbackUrl = redirectUrl + "&token=" + cookie;
            } else {
                callbackUrl = redirectUrl + "?token=" + cookie;
            }
            log.info("SSO回调URL:{}", callbackUrl);
            response.sendRedirect(callbackUrl);
        }else{
            log.info("SSO登录校验:未登录");
            //重定向到登录页面
            String resultUrl = loginUrl+"?clientId=" + clientId + "&redirectUrl=" + redirectUrl;
             log.info("SSO登录跳转:{}", resultUrl);
            response.sendRedirect(resultUrl);

        }
        return BaseResponse.OK;
    }
    
    /**
     * 登录请求
     */
    public static class LoginRequest {
        private String username;
        private String password;
        private String clientId;
        private String redirectUrl;
        
        public String getUsername() {
            return username;
        }
        
        public void setUsername(String username) {
            this.username = username;
        }
        
        public String getPassword() {
            return password;
        }
        
        public void setPassword(String password) {
            this.password = password;
        }
        
        public String getClientId() {
            return clientId;
        }
        
        public void setClientId(String clientId) {
            this.clientId = clientId;
        }

        public String getRedirectUrl() {
            return redirectUrl;
        }

        public void setRedirectUrl(String redirectUrl) {
            this.redirectUrl = redirectUrl;
        }
    }
    
    /**
     * 手机号登录请求
     */
    public static class MobileLoginRequest {
        private String mobile;
        private String verifyCode;
        private String clientId;
        private String redirectUrl;

        public String getMobile() {
            return mobile;
        }
        
        public void setMobile(String mobile) {
            this.mobile = mobile;
        }
        
        public String getVerifyCode() {
            return verifyCode;
        }
        
        public void setVerifyCode(String verifyCode) {
            this.verifyCode = verifyCode;
        }
        
        public String getClientId() {
            return clientId;
        }
        
        public void setClientId(String clientId) {
            this.clientId = clientId;
        }

        public String getRedirectUrl() {
            return redirectUrl;
        }

        public void setRedirectUrl(String redirectUrl) {
            this.redirectUrl = redirectUrl;
        }
    }
    
    /**
     * 刷新token请求
     */
    public static class RefreshTokenRequest {
        private String token;
        private String clientId;
        
        public String getToken() {
            return token;
        }
        
        public void setToken(String token) {
            this.token = token;
        }
        
        public String getClientId() {
            return clientId;
        }
        
        public void setClientId(String clientId) {
            this.clientId = clientId;
        }
    }
    
    /**
     * 注册客户端应用请求
     */
    public static class RegisterClientRequest {
        private String appName;
        private String appUrl;
        private String redirectUrl;
        
        public String getAppName() {
            return appName;
        }
        
        public void setAppName(String appName) {
            this.appName = appName;
        }
        
        public String getAppUrl() {
            return appUrl;
        }
        
        public void setAppUrl(String appUrl) {
            this.appUrl = appUrl;
        }
        
        public String getRedirectUrl() {
            return redirectUrl;
        }
        
        public void setRedirectUrl(String redirectUrl) {
            this.redirectUrl = redirectUrl;
        }
    }
} 