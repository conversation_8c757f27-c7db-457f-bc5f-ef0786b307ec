<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bimowu</groupId>
        <artifactId>unified-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <packaging>jar</packaging>
    <groupId>com.mozai</groupId>
    <artifactId>unified-web</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.bimowu</groupId>
            <artifactId>unified-service</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.6.0</version>
                <configuration>
                    <mainClass>com.bimowu.UnifiedWebApplication</mainClass>
                    <finalName>unified-web</finalName>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>