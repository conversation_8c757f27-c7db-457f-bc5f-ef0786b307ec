<template>
  <div class="profile-container">
    <div class="header">
      <div class="back" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </div>
      <h2>个人中心</h2>
    </div>

    <div class="profile-card">
      <el-tabs v-model="activeTab">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <div class="form-container">
            <el-form :model="userForm" label-width="100px">
              <el-form-item label="用户名">
                <el-input v-model="userForm.username" placeholder="请输入用户名" :disabled="!isEditingUsername" />
                <div class="form-actions">
                  <el-button v-if="!isEditingUsername" type="primary" size="small" @click="startEditUsername">修改</el-button>
                  <template v-else>
                    <el-button type="success" size="small" @click="saveUsername">保存</el-button>
                    <el-button size="small" @click="cancelEditUsername">取消</el-button>
                  </template>
                </div>
              </el-form-item>
              <el-form-item label="手机号">
                <el-input v-model="userForm.mobile" disabled />
              </el-form-item>
              <el-form-item label="邮箱">
                <el-input v-model="userForm.email" disabled />
              </el-form-item>
              <el-form-item label="真实姓名">
                <el-input v-model="userForm.realName" disabled />
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 修改密码 -->
        <el-tab-pane label="修改密码" name="password">
          <div class="form-container">
            <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="100px">
              <el-form-item label="原密码" prop="oldPassword">
                <el-input v-model="passwordForm.oldPassword" type="password" placeholder="请输入原密码" show-password />
              </el-form-item>
              <el-form-item label="新密码" prop="newPassword">
                <el-input v-model="passwordForm.newPassword" type="password" placeholder="请输入新密码" show-password />
              </el-form-item>
              <el-form-item label="确认密码" prop="confirmPassword">
                <el-input v-model="passwordForm.confirmPassword" type="password" placeholder="请再次输入新密码" show-password />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="updateUserPassword">修改密码</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 找回密码 -->
        <el-tab-pane label="找回密码" name="reset">
          <div class="form-container">
            <el-form :model="resetForm" :rules="resetRules" ref="resetFormRef" label-width="100px">
              <el-form-item label="手机号" prop="mobile">
                <el-input v-model="resetForm.mobile" placeholder="请输入手机号">
                  <template #append>
                    <el-button :disabled="isCountingDown" @click="getVerifyCodeLocal">
                      {{ isCountingDown ? `${countdown}s后重新获取` : '获取验证码' }}
                    </el-button>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item label="验证码" prop="verifyCode">
                <el-input v-model="resetForm.verifyCode" placeholder="请输入验证码" />
              </el-form-item>
              <el-form-item label="新密码" prop="newPassword">
                <el-input v-model="resetForm.newPassword" type="password" placeholder="请输入新密码" show-password />
              </el-form-item>
              <el-form-item label="确认密码" prop="confirmPassword">
                <el-input v-model="resetForm.confirmPassword" type="password" placeholder="请再次输入新密码" show-password />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="resetUserPassword">重置密码</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 微信绑定 -->
        <el-tab-pane label="微信绑定" name="wechat">
          <div class="wechat-bind-container">
            <div class="bind-status">
              <div class="status-label">绑定状态：</div>
              <div class="status-value" :class="{ 'status-bound': wechatBindStatus }">
                {{ wechatBindStatus ? '已绑定' : '未绑定' }}
              </div>
            </div>

            <div v-if="wechatBindStatus" class="bound-info">
              <div class="nickname">微信昵称：{{ wechatNickname || '未获取' }}</div>
            </div>

            <div v-else class="qrcode-container">
              <div class="qrcode-title">扫描下方二维码绑定微信</div>
              <div class="qrcode-image">
                <img v-if="wechatQrcodeUrl" :src="wechatQrcodeUrl" alt="微信公众号二维码" />
                <div v-else class="qrcode-placeholder">
                  <el-button type="primary" @click="generateQrCode">生成二维码</el-button>
                </div>
              </div>
              <div v-if="wechatQrcodeUrl" class="qrcode-tips">
                <p>1. 请使用微信扫描二维码</p>
                <p>2. 关注公众号后自动完成绑定</p>
                <p>3. 二维码有效期30分钟，过期请刷新</p>
                <p>4. 关注后点击"检查绑定状态"</p>
              </div>
              <div v-if="wechatQrcodeUrl" class="qrcode-refresh">
                <el-button @click="checkBindStatus">检查绑定状态</el-button>
                <el-button type="primary" @click="generateQrCode">刷新二维码</el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../store'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import {
  getUserInfo,
  updatePassword,
  resetPassword,
  updateUsername,
  getVerifyCode,
  checkWechatBindStatus,
  generateWechatQrCode
} from '../api/user'

const router = useRouter()
const userStore = useUserStore()

// 选项卡
const activeTab = ref('basic')

// 用户表单
const userForm = reactive({
  username: '',
  mobile: '',
  email: '',
  realName: ''
})

// 是否正在编辑用户名
const isEditingUsername = ref(false)
const originalUsername = ref('')

// 密码表单
const passwordFormRef = ref(null)
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 找回密码表单
const resetFormRef = ref(null)
const resetForm = reactive({
  mobile: '',
  verifyCode: '',
  newPassword: '',
  confirmPassword: ''
})

// 微信绑定状态
const wechatBindStatus = ref(false)
const wechatNickname = ref('')
const wechatQrcodeUrl = ref('')

// 验证码倒计时
const isCountingDown = ref(false)
const countdown = ref(60)
let timer = null

// 密码表单验证规则
const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 找回密码表单验证规则
const resetRules = {
  mobile: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  verifyCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码长度为6位', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== resetForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const res = await getUserInfo()
    const user = res.data
    userForm.username = user.nickname
    userForm.mobile = user.phone
    userForm.email = user.email
    userForm.realName = user.nickname

    // 同步更新微信绑定状态
    if (user.wechatBindStatus !== undefined) {
      wechatBindStatus.value = user.wechatBindStatus === 1 || user.wechatBindStatus === true
      wechatNickname.value = user.wechatNickname || ''

      // 更新本地存储的用户信息
      const userInfo = userStore.userInfo
      userInfo.wechatBindStatus = wechatBindStatus.value
      userInfo.wechatNickname = wechatNickname.value
      userStore.setUserInfo(userInfo)

      console.log('用户信息中的微信绑定状态:', wechatBindStatus.value)
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  }
}

// 开始编辑用户名
const startEditUsername = () => {
  originalUsername.value = userForm.username
  isEditingUsername.value = true
}

// 取消编辑用户名
const cancelEditUsername = () => {
  userForm.username = originalUsername.value
  isEditingUsername.value = false
}

// 保存用户名
const saveUsername = async () => {
  if (!userForm.username) {
    ElMessage.warning('用户名不能为空')
    return
  }

  try {
    await updateUsername({ newUsername: userForm.username })
    ElMessage.success('用户名修改成功')
    isEditingUsername.value = false

    // 更新本地存储的用户信息
    const userInfo = userStore.userInfo
    userInfo.nickname = userForm.username
    userStore.setUserInfo(userInfo)
  } catch (error) {
    console.error('修改用户名失败:', error)
    ElMessage.error(error.response?.data?.message || '修改用户名失败')
    userForm.username = originalUsername.value
  }
}

// 修改密码
const updateUserPassword = async () => {
  if (!passwordFormRef.value) return

  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await updatePassword({
          oldPassword: passwordForm.oldPassword,
          newPassword: passwordForm.newPassword
        })
        ElMessage.success('密码修改成功')

        // 清空表单
        passwordForm.oldPassword = ''
        passwordForm.newPassword = ''
        passwordForm.confirmPassword = ''
      } catch (error) {
        console.error('修改密码失败:', error)
        ElMessage.error(error.response?.data?.message || '修改密码失败')
      }
    }
  })
}

// 获取验证码
const getVerifyCodeLocal = async () => {
  if (!resetForm.mobile) {
    ElMessage.warning('请输入手机号')
    return
  }

  if (!/^1[3-9]\d{9}$/.test(resetForm.mobile)) {
    ElMessage.warning('手机号格式不正确')
    return
  }

  try {
    await getVerifyCode(resetForm.mobile)
    ElMessage.success('验证码发送成功')

    // 开始倒计时
    isCountingDown.value = true
    countdown.value = 60
    timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
        isCountingDown.value = false
      }
    }, 1000)
  } catch (error) {
    console.error('获取验证码失败:', error)
    ElMessage.error(error.response?.data?.message || '获取验证码失败')
  }
}

// 重置密码
const resetUserPassword = async () => {
  if (!resetFormRef.value) return

  await resetFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await resetPassword({
          mobile: resetForm.mobile,
          verifyCode: resetForm.verifyCode,
          newPassword: resetForm.newPassword
        })
        ElMessage.success('密码重置成功')

        // 清空表单
        resetForm.mobile = ''
        resetForm.verifyCode = ''
        resetForm.newPassword = ''
        resetForm.confirmPassword = ''

        // 清除倒计时
        if (timer) {
          clearInterval(timer)
          isCountingDown.value = false
        }
      } catch (error) {
        console.error('重置密码失败:', error)
        ElMessage.error(error.response?.data?.message || '重置密码失败')
      }
    }
  })
}

// 生成微信二维码
const generateQrCode = async () => {
  try {
    const res = await generateWechatQrCode()
    wechatQrcodeUrl.value = res.data.qrcodeUrl
    ElMessage.success('二维码生成成功，请使用微信扫码')
  } catch (error) {
    console.error('生成二维码失败:', error)
    ElMessage.error('生成二维码失败，请重试')
  }
}

// 检查绑定状态
const checkBindStatus = async () => {
  try {
    const res = await checkWechatBindStatus()
    const bindStatus = res.data.bindStatus === true || res.data.bindStatus === 1

    // 更新绑定状态
    wechatBindStatus.value = bindStatus
    wechatNickname.value = res.data.wechatNickname || ''

    // 更新本地存储的用户信息
    const userInfo = userStore.userInfo
    userInfo.wechatBindStatus = wechatBindStatus.value
    userInfo.wechatNickname = wechatNickname.value
    userStore.setUserInfo(userInfo)

    if (wechatBindStatus.value) {
      ElMessage.success('微信已成功绑定')
    } else {
      ElMessage.info('尚未绑定微信，请扫描二维码并关注公众号')

      // 如果未绑定且没有二维码，生成二维码
      if (!wechatQrcodeUrl.value) {
        generateQrCode()
      }
    }
  } catch (error) {
    console.error('检查绑定状态失败:', error)
    ElMessage.error('检查绑定状态失败')
  }
}

// 返回上一页
const goBack = () => {
  router.push('/home')
}

// 生命周期钩子
onMounted(async () => {
  // 获取用户信息
  await fetchUserInfo()

  // 检查微信绑定状态
  try {
    const res = await checkWechatBindStatus()
    const bindStatus = res.data.bindStatus === true || res.data.bindStatus === 1

    // 如果后端返回的绑定状态与前端不一致，以后端为准
    if (bindStatus !== wechatBindStatus.value) {
      console.log('绑定状态不一致，后端:', bindStatus, '前端:', wechatBindStatus.value)
      wechatBindStatus.value = bindStatus
      wechatNickname.value = res.data.wechatNickname || ''

      // 更新本地存储的用户信息
      const userInfo = userStore.userInfo
      userInfo.wechatBindStatus = wechatBindStatus.value
      userInfo.wechatNickname = wechatNickname.value
      userStore.setUserInfo(userInfo)
    }

    // 如果有二维码URL且未绑定，显示二维码
    if (res.data.qrcodeUrl && !wechatBindStatus.value) {
      wechatQrcodeUrl.value = res.data.qrcodeUrl
    }
  } catch (error) {
    console.error('检查微信绑定状态失败:', error)
  }
})
</script>

<style scoped>
.profile-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.back {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #409eff;
  margin-right: 20px;
}

.profile-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.form-container {
  max-width: 500px;
  margin: 20px auto;
}

.form-actions {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.wechat-bind-container {
  max-width: 500px;
  margin: 20px auto;
  text-align: center;
}

.bind-status {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  font-size: 18px;
}

.status-value {
  font-weight: bold;
  color: #f56c6c;
}

.status-value.status-bound {
  color: #67c23a;
}

.bound-info {
  margin-top: 20px;
  font-size: 16px;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-title {
  font-size: 16px;
  margin-bottom: 20px;
}

.qrcode-image {
  width: 200px;
  height: 200px;
  border: 1px solid #dcdfe6;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.qrcode-image img {
  max-width: 100%;
  max-height: 100%;
}

.qrcode-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.qrcode-tips {
  text-align: left;
  color: #606266;
  margin-bottom: 20px;
}

.qrcode-refresh {
  display: flex;
  gap: 10px;
  justify-content: center;
}
</style> 