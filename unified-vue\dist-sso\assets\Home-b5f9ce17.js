import{K as dm,L as rs,a as Va,f as pm,j as Be,M as gm,w as Wa,N as cd,A as vd,B as dd,O as ym,c as mm,P as zr,n as _m,y as Sm,r as na,k as kt,l as Kt,C as pr,F as j,G as nt,E as Ui,D as K,I as Qt,p as ni,J as gr,Q as Rf,R as Ef,S as kf,T as Of,U as wm,V as bm,W as xm,H as Tm,X as Cm,Y as Dm,Z as Mm}from"./vendor-a9bfabc2.js";import{s as Kn,u as Am,b as Lm}from"./index-d16c77ab.js";import{_ as Im}from"./index-35d0c8b7.js";function Pm(){return Kn({url:"/todo/list",method:"get"})}function Rm(r){return Kn({url:`/todo/complete/${r}`,method:"post"})}function Em(){return Kn({url:"/system/list",method:"get"})}function km(){return Kn({url:"/progress/user",method:"get"})}/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var _l=function(r,t){return _l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])},_l(r,t)};function O(r,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");_l(r,t);function e(){this.constructor=r}r.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}var Om=function(){function r(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return r}(),Bm=function(){function r(){this.browser=new Om,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow=typeof window<"u"}return r}(),rr=new Bm;typeof wx=="object"&&typeof wx.getSystemInfoSync=="function"?(rr.wxa=!0,rr.touchEventsSupported=!0):typeof document>"u"&&typeof self<"u"?rr.worker=!0:!rr.hasGlobalWindow||"Deno"in window?(rr.node=!0,rr.svgSupported=!0):Nm(navigator.userAgent,rr);function Nm(r,t){var e=t.browser,i=r.match(/Firefox\/([\d.]+)/),n=r.match(/MSIE\s([\d.]+)/)||r.match(/Trident\/.+?rv:(([\d.]+))/),a=r.match(/Edge?\/([\d.]+)/),o=/micromessenger/i.test(r);i&&(e.firefox=!0,e.version=i[1]),n&&(e.ie=!0,e.version=n[1]),a&&(e.edge=!0,e.version=a[1],e.newEdge=+a[1].split(".")[0]>18),o&&(e.weChat=!0),t.svgSupported=typeof SVGRect<"u",t.touchEventsSupported="ontouchstart"in window&&!e.ie&&!e.edge,t.pointerEventsSupported="onpointerdown"in window&&(e.edge||e.ie&&+e.version>=11),t.domSupported=typeof document<"u";var s=document.documentElement.style;t.transform3dSupported=(e.ie&&"transition"in s||e.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),t.transformSupported=t.transform3dSupported||e.ie&&+e.version>=9}const Y=rr;var Tu=12,Fm="sans-serif",Zr=Tu+"px "+Fm,zm=20,Hm=100,Gm="007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N";function Vm(r){var t={};if(typeof JSON>"u")return t;for(var e=0;e<r.length;e++){var i=String.fromCharCode(e+32),n=(r.charCodeAt(e)-zm)/Hm;t[i]=n}return t}var Wm=Vm(Gm),Ni={createCanvas:function(){return typeof document<"u"&&document.createElement("canvas")},measureText:function(){var r,t;return function(e,i){if(!r){var n=Ni.createCanvas();r=n&&n.getContext("2d")}if(r)return t!==i&&(t=r.font=i||Zr),r.measureText(e);e=e||"",i=i||Zr;var a=/((?:\d+)?\.?\d*)px/.exec(i),o=a&&+a[1]||Tu,s=0;if(i.indexOf("mono")>=0)s=o*e.length;else for(var l=0;l<e.length;l++){var u=Wm[e[l]];s+=u==null?o:u*o}return{width:s}}}(),loadImage:function(r,t,e){var i=new Image;return i.onload=t,i.onerror=e,i.src=r,i}},pd=Fi(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(r,t){return r["[object "+t+"]"]=!0,r},{}),gd=Fi(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(r,t){return r["[object "+t+"Array]"]=!0,r},{}),Qn=Object.prototype.toString,Lo=Array.prototype,Um=Lo.forEach,$m=Lo.filter,Cu=Lo.slice,Ym=Lo.map,Bf=(function(){}).constructor,aa=Bf?Bf.prototype:null,Du="__proto__",Xm=2311;function yd(){return Xm++}function Mu(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];typeof console<"u"&&console.error.apply(console,r)}function Q(r){if(r==null||typeof r!="object")return r;var t=r,e=Qn.call(r);if(e==="[object Array]"){if(!Sn(r)){t=[];for(var i=0,n=r.length;i<n;i++)t[i]=Q(r[i])}}else if(gd[e]){if(!Sn(r)){var a=r.constructor;if(a.from)t=a.from(r);else{t=new a(r.length);for(var i=0,n=r.length;i<n;i++)t[i]=r[i]}}}else if(!pd[e]&&!Sn(r)&&!En(r)){t={};for(var o in r)r.hasOwnProperty(o)&&o!==Du&&(t[o]=Q(r[o]))}return t}function et(r,t,e){if(!H(t)||!H(r))return e?Q(t):r;for(var i in t)if(t.hasOwnProperty(i)&&i!==Du){var n=r[i],a=t[i];H(a)&&H(n)&&!N(a)&&!N(n)&&!En(a)&&!En(n)&&!Nf(a)&&!Nf(n)&&!Sn(a)&&!Sn(n)?et(n,a,e):(e||!(i in r))&&(r[i]=Q(t[i]))}return r}function k(r,t){if(Object.assign)Object.assign(r,t);else for(var e in t)t.hasOwnProperty(e)&&e!==Du&&(r[e]=t[e]);return r}function ot(r,t,e){for(var i=gt(t),n=0,a=i.length;n<a;n++){var o=i[n];(e?t[o]!=null:r[o]==null)&&(r[o]=t[o])}return r}function lt(r,t){if(r){if(r.indexOf)return r.indexOf(t);for(var e=0,i=r.length;e<i;e++)if(r[e]===t)return e}return-1}function Zm(r,t){var e=r.prototype;function i(){}i.prototype=t.prototype,r.prototype=new i;for(var n in e)e.hasOwnProperty(n)&&(r.prototype[n]=e[n]);r.prototype.constructor=r,r.superClass=t}function Le(r,t,e){if(r="prototype"in r?r.prototype:r,t="prototype"in t?t.prototype:t,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(t),n=0;n<i.length;n++){var a=i[n];a!=="constructor"&&(e?t[a]!=null:r[a]==null)&&(r[a]=t[a])}else ot(r,t,e)}function Yt(r){return!r||typeof r=="string"?!1:typeof r.length=="number"}function M(r,t,e){if(r&&t)if(r.forEach&&r.forEach===Um)r.forEach(t,e);else if(r.length===+r.length)for(var i=0,n=r.length;i<n;i++)t.call(e,r[i],i,r);else for(var a in r)r.hasOwnProperty(a)&&t.call(e,r[a],a,r)}function V(r,t,e){if(!r)return[];if(!t)return Au(r);if(r.map&&r.map===Ym)return r.map(t,e);for(var i=[],n=0,a=r.length;n<a;n++)i.push(t.call(e,r[n],n,r));return i}function Fi(r,t,e,i){if(r&&t){for(var n=0,a=r.length;n<a;n++)e=t.call(i,e,r[n],n,r);return e}}function Tt(r,t,e){if(!r)return[];if(!t)return Au(r);if(r.filter&&r.filter===$m)return r.filter(t,e);for(var i=[],n=0,a=r.length;n<a;n++)t.call(e,r[n],n,r)&&i.push(r[n]);return i}function gt(r){if(!r)return[];if(Object.keys)return Object.keys(r);var t=[];for(var e in r)r.hasOwnProperty(e)&&t.push(e);return t}function qm(r,t){for(var e=[],i=2;i<arguments.length;i++)e[i-2]=arguments[i];return function(){return r.apply(t,e.concat(Cu.call(arguments)))}}var pt=aa&&U(aa.bind)?aa.call.bind(aa.bind):qm;function bt(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return function(){return r.apply(this,t.concat(Cu.call(arguments)))}}function N(r){return Array.isArray?Array.isArray(r):Qn.call(r)==="[object Array]"}function U(r){return typeof r=="function"}function z(r){return typeof r=="string"}function Sl(r){return Qn.call(r)==="[object String]"}function yt(r){return typeof r=="number"}function H(r){var t=typeof r;return t==="function"||!!r&&t==="object"}function Nf(r){return!!pd[Qn.call(r)]}function Xt(r){return!!gd[Qn.call(r)]}function En(r){return typeof r=="object"&&typeof r.nodeType=="number"&&typeof r.ownerDocument=="object"}function Io(r){return r.colorStops!=null}function Km(r){return r.image!=null}function oo(r){return r!==r}function kn(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];for(var e=0,i=r.length;e<i;e++)if(r[e]!=null)return r[e]}function Z(r,t){return r??t}function Ua(r,t,e){return r??t??e}function Au(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return Cu.apply(r,t)}function md(r){if(typeof r=="number")return[r,r,r,r];var t=r.length;return t===2?[r[0],r[1],r[0],r[1]]:t===3?[r[0],r[1],r[2],r[1]]:r}function Ge(r,t){if(!r)throw new Error(t)}function Ce(r){return r==null?null:typeof r.trim=="function"?r.trim():r.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var _d="__ec_primitive__";function wl(r){r[_d]=!0}function Sn(r){return r[_d]}var Qm=function(){function r(){this.data={}}return r.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},r.prototype.has=function(t){return this.data.hasOwnProperty(t)},r.prototype.get=function(t){return this.data[t]},r.prototype.set=function(t,e){return this.data[t]=e,this},r.prototype.keys=function(){return gt(this.data)},r.prototype.forEach=function(t){var e=this.data;for(var i in e)e.hasOwnProperty(i)&&t(e[i],i)},r}(),Sd=typeof Map=="function";function Jm(){return Sd?new Map:new Qm}var jm=function(){function r(t){var e=N(t);this.data=Jm();var i=this;t instanceof r?t.each(n):t&&M(t,n);function n(a,o){e?i.set(a,o):i.set(o,a)}}return r.prototype.hasKey=function(t){return this.data.has(t)},r.prototype.get=function(t){return this.data.get(t)},r.prototype.set=function(t,e){return this.data.set(t,e),e},r.prototype.each=function(t,e){this.data.forEach(function(i,n){t.call(e,i,n)})},r.prototype.keys=function(){var t=this.data.keys();return Sd?Array.from(t):t},r.prototype.removeKey=function(t){this.data.delete(t)},r}();function X(r){return new jm(r)}function t0(r,t){for(var e=new r.constructor(r.length+t.length),i=0;i<r.length;i++)e[i]=r[i];for(var n=r.length,i=0;i<t.length;i++)e[i+n]=t[i];return e}function Po(r,t){var e;if(Object.create)e=Object.create(r);else{var i=function(){};i.prototype=r,e=new i}return t&&k(e,t),e}function wd(r){var t=r.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function qr(r,t){return r.hasOwnProperty(t)}function $t(){}var e0=180/Math.PI;function zi(r,t){return r==null&&(r=0),t==null&&(t=0),[r,t]}function r0(r){return[r[0],r[1]]}function Ff(r,t,e){return r[0]=t[0]+e[0],r[1]=t[1]+e[1],r}function i0(r,t,e){return r[0]=t[0]-e[0],r[1]=t[1]-e[1],r}function n0(r){return Math.sqrt(a0(r))}function a0(r){return r[0]*r[0]+r[1]*r[1]}function is(r,t,e){return r[0]=t[0]*e,r[1]=t[1]*e,r}function o0(r,t){var e=n0(t);return e===0?(r[0]=0,r[1]=0):(r[0]=t[0]/e,r[1]=t[1]/e),r}function bl(r,t){return Math.sqrt((r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1]))}var s0=bl;function l0(r,t){return(r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1])}var Mi=l0;function he(r,t,e){var i=t[0],n=t[1];return r[0]=e[0]*i+e[2]*n+e[4],r[1]=e[1]*i+e[3]*n+e[5],r}function bi(r,t,e){return r[0]=Math.min(t[0],e[0]),r[1]=Math.min(t[1],e[1]),r}function xi(r,t,e){return r[0]=Math.max(t[0],e[0]),r[1]=Math.max(t[1],e[1]),r}var ai=function(){function r(t,e){this.target=t,this.topTarget=e&&e.topTarget}return r}(),u0=function(){function r(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return r.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new ai(e,t),"dragstart",t.event))},r.prototype._drag=function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,a=i-this._x,o=n-this._y;this._x=i,this._y=n,e.drift(a,o,t),this.handler.dispatchToElement(new ai(e,t),"drag",t.event);var s=this.handler.findHover(i,n,e).target,l=this._dropTarget;this._dropTarget=s,e!==s&&(l&&s!==l&&this.handler.dispatchToElement(new ai(l,t),"dragleave",t.event),s&&s!==l&&this.handler.dispatchToElement(new ai(s,t),"dragenter",t.event))}},r.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new ai(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new ai(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},r}();const f0=u0;var h0=function(){function r(t){t&&(this._$eventProcessor=t)}return r.prototype.on=function(t,e,i,n){this._$handlers||(this._$handlers={});var a=this._$handlers;if(typeof e=="function"&&(n=i,i=e,e=null),!i||!t)return this;var o=this._$eventProcessor;e!=null&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),a[t]||(a[t]=[]);for(var s=0;s<a[t].length;s++)if(a[t][s].h===i)return this;var l={h:i,query:e,ctx:n||this,callAtLast:i.zrEventfulCallAtLast},u=a[t].length-1,f=a[t][u];return f&&f.callAtLast?a[t].splice(u,0,l):a[t].push(l),this},r.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},r.prototype.off=function(t,e){var i=this._$handlers;if(!i)return this;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],a=0,o=i[t].length;a<o;a++)i[t][a].h!==e&&n.push(i[t][a]);i[t]=n}i[t]&&i[t].length===0&&delete i[t]}else delete i[t];return this},r.prototype.trigger=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var o=e.length,s=n.length,l=0;l<s;l++){var u=n[l];if(!(a&&a.filter&&u.query!=null&&!a.filter(t,u.query)))switch(o){case 0:u.h.call(u.ctx);break;case 1:u.h.call(u.ctx,e[0]);break;case 2:u.h.call(u.ctx,e[0],e[1]);break;default:u.h.apply(u.ctx,e);break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r.prototype.triggerWithContext=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var o=e.length,s=e[o-1],l=n.length,u=0;u<l;u++){var f=n[u];if(!(a&&a.filter&&f.query!=null&&!a.filter(t,f.query)))switch(o){case 0:f.h.call(s);break;case 1:f.h.call(s,e[0]);break;case 2:f.h.call(s,e[0],e[1]);break;default:f.h.apply(s,e.slice(1,o-1));break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r}();const Ie=h0;var c0=Math.log(2);function xl(r,t,e,i,n,a){var o=i+"-"+n,s=r.length;if(a.hasOwnProperty(o))return a[o];if(t===1){var l=Math.round(Math.log((1<<s)-1&~n)/c0);return r[e][l]}for(var u=i|1<<e,f=e+1;i&1<<f;)f++;for(var h=0,v=0,c=0;v<s;v++){var d=1<<v;d&n||(h+=(c%2?-1:1)*r[e][v]*xl(r,t-1,f,u,n|d,a),c++)}return a[o]=h,h}function zf(r,t){var e=[[r[0],r[1],1,0,0,0,-t[0]*r[0],-t[0]*r[1]],[0,0,0,r[0],r[1],1,-t[1]*r[0],-t[1]*r[1]],[r[2],r[3],1,0,0,0,-t[2]*r[2],-t[2]*r[3]],[0,0,0,r[2],r[3],1,-t[3]*r[2],-t[3]*r[3]],[r[4],r[5],1,0,0,0,-t[4]*r[4],-t[4]*r[5]],[0,0,0,r[4],r[5],1,-t[5]*r[4],-t[5]*r[5]],[r[6],r[7],1,0,0,0,-t[6]*r[6],-t[6]*r[7]],[0,0,0,r[6],r[7],1,-t[7]*r[6],-t[7]*r[7]]],i={},n=xl(e,8,0,0,0,i);if(n!==0){for(var a=[],o=0;o<8;o++)for(var s=0;s<8;s++)a[s]==null&&(a[s]=0),a[s]+=((o+s)%2?-1:1)*xl(e,7,o===0?1:0,1<<o,1<<s,i)/n*t[o];return function(l,u,f){var h=u*a[6]+f*a[7]+1;l[0]=(u*a[0]+f*a[1]+a[2])/h,l[1]=(u*a[3]+f*a[4]+a[5])/h}}}var Hf="___zrEVENTSAVED",ns=[];function v0(r,t,e,i,n){return Tl(ns,t,i,n,!0)&&Tl(r,e,ns[0],ns[1])}function Tl(r,t,e,i,n){if(t.getBoundingClientRect&&Y.domSupported&&!bd(t)){var a=t[Hf]||(t[Hf]={}),o=d0(t,a),s=p0(o,a,n);if(s)return s(r,e,i),!0}return!1}function d0(r,t){var e=t.markers;if(e)return e;e=t.markers=[];for(var i=["left","right"],n=["top","bottom"],a=0;a<4;a++){var o=document.createElement("div"),s=o.style,l=a%2,u=(a>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[l]+":0",n[u]+":0",i[1-l]+":auto",n[1-u]+":auto",""].join("!important;"),r.appendChild(o),e.push(o)}return e}function p0(r,t,e){for(var i=e?"invTrans":"trans",n=t[i],a=t.srcCoords,o=[],s=[],l=!0,u=0;u<4;u++){var f=r[u].getBoundingClientRect(),h=2*u,v=f.left,c=f.top;o.push(v,c),l=l&&a&&v===a[h]&&c===a[h+1],s.push(r[u].offsetLeft,r[u].offsetTop)}return l&&n?n:(t.srcCoords=o,t[i]=e?zf(s,o):zf(o,s))}function bd(r){return r.nodeName.toUpperCase()==="CANVAS"}var g0=/([&<>"'])/g,y0={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Vt(r){return r==null?"":(r+"").replace(g0,function(t,e){return y0[e]})}var m0=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,as=[],_0=Y.browser.firefox&&+Y.browser.version.split(".")[0]<39;function Cl(r,t,e,i){return e=e||{},i?Gf(r,t,e):_0&&t.layerX!=null&&t.layerX!==t.offsetX?(e.zrX=t.layerX,e.zrY=t.layerY):t.offsetX!=null?(e.zrX=t.offsetX,e.zrY=t.offsetY):Gf(r,t,e),e}function Gf(r,t,e){if(Y.domSupported&&r.getBoundingClientRect){var i=t.clientX,n=t.clientY;if(bd(r)){var a=r.getBoundingClientRect();e.zrX=i-a.left,e.zrY=n-a.top;return}else if(Tl(as,r,i,n)){e.zrX=as[0],e.zrY=as[1];return}}e.zrX=e.zrY=0}function Lu(r){return r||window.event}function ne(r,t,e){if(t=Lu(t),t.zrX!=null)return t;var i=t.type,n=i&&i.indexOf("touch")>=0;if(n){var o=i!=="touchend"?t.targetTouches[0]:t.changedTouches[0];o&&Cl(r,o,t,e)}else{Cl(r,t,t,e);var a=S0(t);t.zrDelta=a?a/120:-(t.detail||0)/3}var s=t.button;return t.which==null&&s!==void 0&&m0.test(t.type)&&(t.which=s&1?1:s&2?3:s&4?2:0),t}function S0(r){var t=r.wheelDelta;if(t)return t;var e=r.deltaX,i=r.deltaY;if(e==null||i==null)return t;var n=Math.abs(i!==0?i:e),a=i>0?-1:i<0?1:e>0?-1:1;return 3*n*a}function w0(r,t,e,i){r.addEventListener(t,e,i)}function b0(r,t,e,i){r.removeEventListener(t,e,i)}var xd=function(r){r.preventDefault(),r.stopPropagation(),r.cancelBubble=!0},x0=function(){function r(){this._track=[]}return r.prototype.recognize=function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},r.prototype.clear=function(){return this._track.length=0,this},r.prototype._doTrack=function(t,e,i){var n=t.touches;if(n){for(var a={points:[],touches:[],target:e,event:t},o=0,s=n.length;o<s;o++){var l=n[o],u=Cl(i,l,{});a.points.push([u.zrX,u.zrY]),a.touches.push(l)}this._track.push(a)}},r.prototype._recognize=function(t){for(var e in os)if(os.hasOwnProperty(e)){var i=os[e](this._track,t);if(i)return i}},r}();function Vf(r){var t=r[1][0]-r[0][0],e=r[1][1]-r[0][1];return Math.sqrt(t*t+e*e)}function T0(r){return[(r[0][0]+r[1][0])/2,(r[0][1]+r[1][1])/2]}var os={pinch:function(r,t){var e=r.length;if(e){var i=(r[e-1]||{}).points,n=(r[e-2]||{}).points||i;if(n&&n.length>1&&i&&i.length>1){var a=Vf(i)/Vf(n);!isFinite(a)&&(a=1),t.pinchScale=a;var o=T0(i);return t.pinchX=o[0],t.pinchY=o[1],{type:"pinch",target:r[0].target,event:t}}}}};function Ai(){return[1,0,0,1,0,0]}function Iu(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=1,r[4]=0,r[5]=0,r}function C0(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4],r[5]=t[5],r}function Li(r,t,e){var i=t[0]*e[0]+t[2]*e[1],n=t[1]*e[0]+t[3]*e[1],a=t[0]*e[2]+t[2]*e[3],o=t[1]*e[2]+t[3]*e[3],s=t[0]*e[4]+t[2]*e[5]+t[4],l=t[1]*e[4]+t[3]*e[5]+t[5];return r[0]=i,r[1]=n,r[2]=a,r[3]=o,r[4]=s,r[5]=l,r}function Dl(r,t,e){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4]+e[0],r[5]=t[5]+e[1],r}function Pu(r,t,e,i){i===void 0&&(i=[0,0]);var n=t[0],a=t[2],o=t[4],s=t[1],l=t[3],u=t[5],f=Math.sin(e),h=Math.cos(e);return r[0]=n*h+s*f,r[1]=-n*f+s*h,r[2]=a*h+l*f,r[3]=-a*f+h*l,r[4]=h*(o-i[0])+f*(u-i[1])+i[0],r[5]=h*(u-i[1])-f*(o-i[0])+i[1],r}function D0(r,t,e){var i=e[0],n=e[1];return r[0]=t[0]*i,r[1]=t[1]*n,r[2]=t[2]*i,r[3]=t[3]*n,r[4]=t[4]*i,r[5]=t[5]*n,r}function Ru(r,t){var e=t[0],i=t[2],n=t[4],a=t[1],o=t[3],s=t[5],l=e*o-a*i;return l?(l=1/l,r[0]=o*l,r[1]=-a*l,r[2]=-i*l,r[3]=e*l,r[4]=(i*s-o*n)*l,r[5]=(a*n-e*s)*l,r):null}var M0=function(){function r(t,e){this.x=t||0,this.y=e||0}return r.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},r.prototype.clone=function(){return new r(this.x,this.y)},r.prototype.set=function(t,e){return this.x=t,this.y=e,this},r.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},r.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},r.prototype.scale=function(t){this.x*=t,this.y*=t},r.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},r.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},r.prototype.dot=function(t){return this.x*t.x+this.y*t.y},r.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},r.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},r.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},r.prototype.distance=function(t){var e=this.x-t.x,i=this.y-t.y;return Math.sqrt(e*e+i*i)},r.prototype.distanceSquare=function(t){var e=this.x-t.x,i=this.y-t.y;return e*e+i*i},r.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},r.prototype.transform=function(t){if(t){var e=this.x,i=this.y;return this.x=t[0]*e+t[2]*i+t[4],this.y=t[1]*e+t[3]*i+t[5],this}},r.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},r.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},r.set=function(t,e,i){t.x=e,t.y=i},r.copy=function(t,e){t.x=e.x,t.y=e.y},r.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},r.lenSquare=function(t){return t.x*t.x+t.y*t.y},r.dot=function(t,e){return t.x*e.x+t.y*e.y},r.add=function(t,e,i){t.x=e.x+i.x,t.y=e.y+i.y},r.sub=function(t,e,i){t.x=e.x-i.x,t.y=e.y-i.y},r.scale=function(t,e,i){t.x=e.x*i,t.y=e.y*i},r.scaleAndAdd=function(t,e,i,n){t.x=e.x+i.x*n,t.y=e.y+i.y*n},r.lerp=function(t,e,i,n){var a=1-n;t.x=a*e.x+n*i.x,t.y=a*e.y+n*i.y},r}();const ut=M0;var oa=Math.min,sa=Math.max,yr=new ut,mr=new ut,_r=new ut,Sr=new ut,$i=new ut,Yi=new ut,A0=function(){function r(t,e,i,n){i<0&&(t=t+i,i=-i),n<0&&(e=e+n,n=-n),this.x=t,this.y=e,this.width=i,this.height=n}return r.prototype.union=function(t){var e=oa(t.x,this.x),i=oa(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=sa(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=sa(t.y+t.height,this.y+this.height)-i:this.height=t.height,this.x=e,this.y=i},r.prototype.applyTransform=function(t){r.applyTransform(this,this,t)},r.prototype.calculateTransform=function(t){var e=this,i=t.width/e.width,n=t.height/e.height,a=Ai();return Dl(a,a,[-e.x,-e.y]),D0(a,a,[i,n]),Dl(a,a,[t.x,t.y]),a},r.prototype.intersect=function(t,e){if(!t)return!1;t instanceof r||(t=r.create(t));var i=this,n=i.x,a=i.x+i.width,o=i.y,s=i.y+i.height,l=t.x,u=t.x+t.width,f=t.y,h=t.y+t.height,v=!(a<l||u<n||s<f||h<o);if(e){var c=1/0,d=0,y=Math.abs(a-l),p=Math.abs(u-n),g=Math.abs(s-f),m=Math.abs(h-o),_=Math.min(y,p),S=Math.min(g,m);a<l||u<n?_>d&&(d=_,y<p?ut.set(Yi,-y,0):ut.set(Yi,p,0)):_<c&&(c=_,y<p?ut.set($i,y,0):ut.set($i,-p,0)),s<f||h<o?S>d&&(d=S,g<m?ut.set(Yi,0,-g):ut.set(Yi,0,m)):_<c&&(c=_,g<m?ut.set($i,0,g):ut.set($i,0,-m))}return e&&ut.copy(e,v?$i:Yi),v},r.prototype.contain=function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i.height},r.prototype.clone=function(){return new r(this.x,this.y,this.width,this.height)},r.prototype.copy=function(t){r.copy(this,t)},r.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},r.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},r.prototype.isZero=function(){return this.width===0||this.height===0},r.create=function(t){return new r(t.x,t.y,t.width,t.height)},r.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},r.applyTransform=function(t,e,i){if(!i){t!==e&&r.copy(t,e);return}if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var n=i[0],a=i[3],o=i[4],s=i[5];t.x=e.x*n+o,t.y=e.y*a+s,t.width=e.width*n,t.height=e.height*a,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height);return}yr.x=_r.x=e.x,yr.y=Sr.y=e.y,mr.x=Sr.x=e.x+e.width,mr.y=_r.y=e.y+e.height,yr.transform(i),Sr.transform(i),mr.transform(i),_r.transform(i),t.x=oa(yr.x,mr.x,_r.x,Sr.x),t.y=oa(yr.y,mr.y,_r.y,Sr.y);var l=sa(yr.x,mr.x,_r.x,Sr.x),u=sa(yr.y,mr.y,_r.y,Sr.y);t.width=l-t.x,t.height=u-t.y},r}();const rt=A0;var Td="silent";function L0(r,t,e){return{type:r,event:e,target:t.target,topTarget:t.topTarget,cancelBubble:!1,offsetX:e.zrX,offsetY:e.zrY,gestureEvent:e.gestureEvent,pinchX:e.pinchX,pinchY:e.pinchY,pinchScale:e.pinchScale,wheelDelta:e.zrDelta,zrByTouch:e.zrByTouch,which:e.which,stop:I0}}function I0(){xd(this.event)}var P0=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.handler=null,e}return t.prototype.dispose=function(){},t.prototype.setCursor=function(){},t}(Ie),Xi=function(){function r(t,e){this.x=t,this.y=e}return r}(),R0=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],ss=new rt(0,0,0,0),Cd=function(r){O(t,r);function t(e,i,n,a,o){var s=r.call(this)||this;return s._hovered=new Xi(0,0),s.storage=e,s.painter=i,s.painterRoot=a,s._pointerSize=o,n=n||new P0,s.proxy=null,s.setHandlerProxy(n),s._draggingMgr=new f0(s),s}return t.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(M(R0,function(i){e.on&&e.on(i,this[i],this)},this),e.handler=this),this.proxy=e},t.prototype.mousemove=function(e){var i=e.zrX,n=e.zrY,a=Dd(this,i,n),o=this._hovered,s=o.target;s&&!s.__zr&&(o=this.findHover(o.x,o.y),s=o.target);var l=this._hovered=a?new Xi(i,n):this.findHover(i,n),u=l.target,f=this.proxy;f.setCursor&&f.setCursor(u?u.cursor:"default"),s&&u!==s&&this.dispatchToElement(o,"mouseout",e),this.dispatchToElement(l,"mousemove",e),u&&u!==s&&this.dispatchToElement(l,"mouseover",e)},t.prototype.mouseout=function(e){var i=e.zrEventControl;i!=="only_globalout"&&this.dispatchToElement(this._hovered,"mouseout",e),i!=="no_globalout"&&this.trigger("globalout",{type:"globalout",event:e})},t.prototype.resize=function(){this._hovered=new Xi(0,0)},t.prototype.dispatch=function(e,i){var n=this[e];n&&n.call(this,i)},t.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},t.prototype.setCursorStyle=function(e){var i=this.proxy;i.setCursor&&i.setCursor(e)},t.prototype.dispatchToElement=function(e,i,n){e=e||{};var a=e.target;if(!(a&&a.silent)){for(var o="on"+i,s=L0(i,e,n);a&&(a[o]&&(s.cancelBubble=!!a[o].call(a,s)),a.trigger(i,s),a=a.__hostTarget?a.__hostTarget:a.parent,!s.cancelBubble););s.cancelBubble||(this.trigger(i,s),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(l){typeof l[o]=="function"&&l[o].call(l,s),l.trigger&&l.trigger(i,s)}))}},t.prototype.findHover=function(e,i,n){var a=this.storage.getDisplayList(),o=new Xi(e,i);if(Wf(a,o,e,i,n),this._pointerSize&&!o.target){for(var s=[],l=this._pointerSize,u=l/2,f=new rt(e-u,i-u,l,l),h=a.length-1;h>=0;h--){var v=a[h];v!==n&&!v.ignore&&!v.ignoreCoarsePointer&&(!v.parent||!v.parent.ignoreCoarsePointer)&&(ss.copy(v.getBoundingRect()),v.transform&&ss.applyTransform(v.transform),ss.intersect(f)&&s.push(v))}if(s.length)for(var c=4,d=Math.PI/12,y=Math.PI*2,p=0;p<u;p+=c)for(var g=0;g<y;g+=d){var m=e+p*Math.cos(g),_=i+p*Math.sin(g);if(Wf(s,o,m,_,n),o.target)return o}}return o},t.prototype.processGesture=function(e,i){this._gestureMgr||(this._gestureMgr=new x0);var n=this._gestureMgr;i==="start"&&n.clear();var a=n.recognize(e,this.findHover(e.zrX,e.zrY,null).target,this.proxy.dom);if(i==="end"&&n.clear(),a){var o=a.type;e.gestureEvent=o;var s=new Xi;s.target=a.target,this.dispatchToElement(s,o,a.event)}},t}(Ie);M(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(r){Cd.prototype[r]=function(t){var e=t.zrX,i=t.zrY,n=Dd(this,e,i),a,o;if((r!=="mouseup"||!n)&&(a=this.findHover(e,i),o=a.target),r==="mousedown")this._downEl=o,this._downPoint=[t.zrX,t.zrY],this._upEl=o;else if(r==="mouseup")this._upEl=o;else if(r==="click"){if(this._downEl!==this._upEl||!this._downPoint||s0(this._downPoint,[t.zrX,t.zrY])>4)return;this._downPoint=null}this.dispatchToElement(a,r,t)}});function E0(r,t,e){if(r[r.rectHover?"rectContain":"contain"](t,e)){for(var i=r,n=void 0,a=!1;i;){if(i.ignoreClip&&(a=!0),!a){var o=i.getClipPath();if(o&&!o.contain(t,e))return!1}i.silent&&(n=!0);var s=i.__hostTarget;i=s||i.parent}return n?Td:!0}return!1}function Wf(r,t,e,i,n){for(var a=r.length-1;a>=0;a--){var o=r[a],s=void 0;if(o!==n&&!o.ignore&&(s=E0(o,e,i))&&(!t.topTarget&&(t.topTarget=o),s!==Td)){t.target=o;break}}}function Dd(r,t,e){var i=r.painter;return t<0||t>i.getWidth()||e<0||e>i.getHeight()}const k0=Cd;var Md=32,Zi=7;function O0(r){for(var t=0;r>=Md;)t|=r&1,r>>=1;return r+t}function Uf(r,t,e,i){var n=t+1;if(n===e)return 1;if(i(r[n++],r[t])<0){for(;n<e&&i(r[n],r[n-1])<0;)n++;B0(r,t,n)}else for(;n<e&&i(r[n],r[n-1])>=0;)n++;return n-t}function B0(r,t,e){for(e--;t<e;){var i=r[t];r[t++]=r[e],r[e--]=i}}function $f(r,t,e,i,n){for(i===t&&i++;i<e;i++){for(var a=r[i],o=t,s=i,l;o<s;)l=o+s>>>1,n(a,r[l])<0?s=l:o=l+1;var u=i-o;switch(u){case 3:r[o+3]=r[o+2];case 2:r[o+2]=r[o+1];case 1:r[o+1]=r[o];break;default:for(;u>0;)r[o+u]=r[o+u-1],u--}r[o]=a}}function ls(r,t,e,i,n,a){var o=0,s=0,l=1;if(a(r,t[e+n])>0){for(s=i-n;l<s&&a(r,t[e+n+l])>0;)o=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s),o+=n,l+=n}else{for(s=n+1;l<s&&a(r,t[e+n-l])<=0;)o=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s);var u=o;o=n-l,l=n-u}for(o++;o<l;){var f=o+(l-o>>>1);a(r,t[e+f])>0?o=f+1:l=f}return l}function us(r,t,e,i,n,a){var o=0,s=0,l=1;if(a(r,t[e+n])<0){for(s=n+1;l<s&&a(r,t[e+n-l])<0;)o=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s);var u=o;o=n-l,l=n-u}else{for(s=i-n;l<s&&a(r,t[e+n+l])>=0;)o=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s),o+=n,l+=n}for(o++;o<l;){var f=o+(l-o>>>1);a(r,t[e+f])<0?l=f:o=f+1}return l}function N0(r,t){var e=Zi,i,n,a=0,o=[];i=[],n=[];function s(c,d){i[a]=c,n[a]=d,a+=1}function l(){for(;a>1;){var c=a-2;if(c>=1&&n[c-1]<=n[c]+n[c+1]||c>=2&&n[c-2]<=n[c]+n[c-1])n[c-1]<n[c+1]&&c--;else if(n[c]>n[c+1])break;f(c)}}function u(){for(;a>1;){var c=a-2;c>0&&n[c-1]<n[c+1]&&c--,f(c)}}function f(c){var d=i[c],y=n[c],p=i[c+1],g=n[c+1];n[c]=y+g,c===a-3&&(i[c+1]=i[c+2],n[c+1]=n[c+2]),a--;var m=us(r[p],r,d,y,0,t);d+=m,y-=m,y!==0&&(g=ls(r[d+y-1],r,p,g,g-1,t),g!==0&&(y<=g?h(d,y,p,g):v(d,y,p,g)))}function h(c,d,y,p){var g=0;for(g=0;g<d;g++)o[g]=r[c+g];var m=0,_=y,S=c;if(r[S++]=r[_++],--p===0){for(g=0;g<d;g++)r[S+g]=o[m+g];return}if(d===1){for(g=0;g<p;g++)r[S+g]=r[_+g];r[S+p]=o[m];return}for(var b=e,w,x,A;;){w=0,x=0,A=!1;do if(t(r[_],o[m])<0){if(r[S++]=r[_++],x++,w=0,--p===0){A=!0;break}}else if(r[S++]=o[m++],w++,x=0,--d===1){A=!0;break}while((w|x)<b);if(A)break;do{if(w=us(r[_],o,m,d,0,t),w!==0){for(g=0;g<w;g++)r[S+g]=o[m+g];if(S+=w,m+=w,d-=w,d<=1){A=!0;break}}if(r[S++]=r[_++],--p===0){A=!0;break}if(x=ls(o[m],r,_,p,0,t),x!==0){for(g=0;g<x;g++)r[S+g]=r[_+g];if(S+=x,_+=x,p-=x,p===0){A=!0;break}}if(r[S++]=o[m++],--d===1){A=!0;break}b--}while(w>=Zi||x>=Zi);if(A)break;b<0&&(b=0),b+=2}if(e=b,e<1&&(e=1),d===1){for(g=0;g<p;g++)r[S+g]=r[_+g];r[S+p]=o[m]}else{if(d===0)throw new Error;for(g=0;g<d;g++)r[S+g]=o[m+g]}}function v(c,d,y,p){var g=0;for(g=0;g<p;g++)o[g]=r[y+g];var m=c+d-1,_=p-1,S=y+p-1,b=0,w=0;if(r[S--]=r[m--],--d===0){for(b=S-(p-1),g=0;g<p;g++)r[b+g]=o[g];return}if(p===1){for(S-=d,m-=d,w=S+1,b=m+1,g=d-1;g>=0;g--)r[w+g]=r[b+g];r[S]=o[_];return}for(var x=e;;){var A=0,C=0,D=!1;do if(t(o[_],r[m])<0){if(r[S--]=r[m--],A++,C=0,--d===0){D=!0;break}}else if(r[S--]=o[_--],C++,A=0,--p===1){D=!0;break}while((A|C)<x);if(D)break;do{if(A=d-us(o[_],r,c,d,d-1,t),A!==0){for(S-=A,m-=A,d-=A,w=S+1,b=m+1,g=A-1;g>=0;g--)r[w+g]=r[b+g];if(d===0){D=!0;break}}if(r[S--]=o[_--],--p===1){D=!0;break}if(C=p-ls(r[m],o,0,p,p-1,t),C!==0){for(S-=C,_-=C,p-=C,w=S+1,b=_+1,g=0;g<C;g++)r[w+g]=o[b+g];if(p<=1){D=!0;break}}if(r[S--]=r[m--],--d===0){D=!0;break}x--}while(A>=Zi||C>=Zi);if(D)break;x<0&&(x=0),x+=2}if(e=x,e<1&&(e=1),p===1){for(S-=d,m-=d,w=S+1,b=m+1,g=d-1;g>=0;g--)r[w+g]=r[b+g];r[S]=o[_]}else{if(p===0)throw new Error;for(b=S-(p-1),g=0;g<p;g++)r[b+g]=o[g]}}return{mergeRuns:l,forceMergeRuns:u,pushRun:s}}function $a(r,t,e,i){e||(e=0),i||(i=r.length);var n=i-e;if(!(n<2)){var a=0;if(n<Md){a=Uf(r,e,i,t),$f(r,e,i,e+a,t);return}var o=N0(r,t),s=O0(n);do{if(a=Uf(r,e,i,t),a<s){var l=n;l>s&&(l=s),$f(r,e,e+l,e+a,t),a=l}o.pushRun(e,a),o.mergeRuns(),n-=a,e+=a}while(n!==0);o.forceMergeRuns()}}var jt=1,vn=2,Si=4,Yf=!1;function fs(){Yf||(Yf=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function Xf(r,t){return r.zlevel===t.zlevel?r.z===t.z?r.z2-t.z2:r.z-t.z:r.zlevel-t.zlevel}var F0=function(){function r(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=Xf}return r.prototype.traverse=function(t,e){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,e)},r.prototype.getDisplayList=function(t,e){e=e||!1;var i=this._displayList;return(t||!i.length)&&this.updateDisplayList(e),i},r.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,i=this._displayList,n=0,a=e.length;n<a;n++)this._updateAndAddDisplayable(e[n],null,t);i.length=this._displayListLen,$a(i,Xf)},r.prototype._updateAndAddDisplayable=function(t,e,i){if(!(t.ignore&&!i)){t.beforeUpdate(),t.update(),t.afterUpdate();var n=t.getClipPath();if(t.ignoreClip)e=null;else if(n){e?e=e.slice():e=[];for(var a=n,o=t;a;)a.parent=o,a.updateTransform(),e.push(a),o=a,a=a.getClipPath()}if(t.childrenRef){for(var s=t.childrenRef(),l=0;l<s.length;l++){var u=s[l];t.__dirty&&(u.__dirty|=jt),this._updateAndAddDisplayable(u,e,i)}t.__dirty=0}else{var f=t;e&&e.length?f.__clipPaths=e:f.__clipPaths&&f.__clipPaths.length>0&&(f.__clipPaths=[]),isNaN(f.z)&&(fs(),f.z=0),isNaN(f.z2)&&(fs(),f.z2=0),isNaN(f.zlevel)&&(fs(),f.zlevel=0),this._displayList[this._displayListLen++]=f}var h=t.getDecalElement&&t.getDecalElement();h&&this._updateAndAddDisplayable(h,e,i);var v=t.getTextGuideLine();v&&this._updateAndAddDisplayable(v,e,i);var c=t.getTextContent();c&&this._updateAndAddDisplayable(c,e,i)}},r.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},r.prototype.delRoot=function(t){if(t instanceof Array){for(var e=0,i=t.length;e<i;e++)this.delRoot(t[e]);return}var n=lt(this._roots,t);n>=0&&this._roots.splice(n,1)},r.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},r.prototype.getRoots=function(){return this._roots},r.prototype.dispose=function(){this._displayList=null,this._roots=null},r}();const z0=F0;var Ad;Ad=Y.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(r){return setTimeout(r,16)};const Ml=Ad;var Ya={linear:function(r){return r},quadraticIn:function(r){return r*r},quadraticOut:function(r){return r*(2-r)},quadraticInOut:function(r){return(r*=2)<1?.5*r*r:-.5*(--r*(r-2)-1)},cubicIn:function(r){return r*r*r},cubicOut:function(r){return--r*r*r+1},cubicInOut:function(r){return(r*=2)<1?.5*r*r*r:.5*((r-=2)*r*r+2)},quarticIn:function(r){return r*r*r*r},quarticOut:function(r){return 1- --r*r*r*r},quarticInOut:function(r){return(r*=2)<1?.5*r*r*r*r:-.5*((r-=2)*r*r*r-2)},quinticIn:function(r){return r*r*r*r*r},quinticOut:function(r){return--r*r*r*r*r+1},quinticInOut:function(r){return(r*=2)<1?.5*r*r*r*r*r:.5*((r-=2)*r*r*r*r+2)},sinusoidalIn:function(r){return 1-Math.cos(r*Math.PI/2)},sinusoidalOut:function(r){return Math.sin(r*Math.PI/2)},sinusoidalInOut:function(r){return .5*(1-Math.cos(Math.PI*r))},exponentialIn:function(r){return r===0?0:Math.pow(1024,r-1)},exponentialOut:function(r){return r===1?1:1-Math.pow(2,-10*r)},exponentialInOut:function(r){return r===0?0:r===1?1:(r*=2)<1?.5*Math.pow(1024,r-1):.5*(-Math.pow(2,-10*(r-1))+2)},circularIn:function(r){return 1-Math.sqrt(1-r*r)},circularOut:function(r){return Math.sqrt(1- --r*r)},circularInOut:function(r){return(r*=2)<1?-.5*(Math.sqrt(1-r*r)-1):.5*(Math.sqrt(1-(r-=2)*r)+1)},elasticIn:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),-(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)))},elasticOut:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),e*Math.pow(2,-10*r)*Math.sin((r-t)*(2*Math.PI)/i)+1)},elasticInOut:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),(r*=2)<1?-.5*(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)):e*Math.pow(2,-10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)*.5+1)},backIn:function(r){var t=1.70158;return r*r*((t+1)*r-t)},backOut:function(r){var t=1.70158;return--r*r*((t+1)*r+t)+1},backInOut:function(r){var t=2.5949095;return(r*=2)<1?.5*(r*r*((t+1)*r-t)):.5*((r-=2)*r*((t+1)*r+t)+2)},bounceIn:function(r){return 1-Ya.bounceOut(1-r)},bounceOut:function(r){return r<1/2.75?7.5625*r*r:r<2/2.75?7.5625*(r-=1.5/2.75)*r+.75:r<2.5/2.75?7.5625*(r-=2.25/2.75)*r+.9375:7.5625*(r-=2.625/2.75)*r+.984375},bounceInOut:function(r){return r<.5?Ya.bounceIn(r*2)*.5:Ya.bounceOut(r*2-1)*.5+.5}};const Ld=Ya;var la=Math.pow,sr=Math.sqrt,so=1e-8,Id=1e-4,Zf=sr(3),ua=1/3,Te=zi(),se=zi(),Ii=zi();function ar(r){return r>-so&&r<so}function Pd(r){return r>so||r<-so}function St(r,t,e,i,n){var a=1-n;return a*a*(a*r+3*n*t)+n*n*(n*i+3*a*e)}function qf(r,t,e,i,n){var a=1-n;return 3*(((t-r)*a+2*(e-t)*n)*a+(i-e)*n*n)}function lo(r,t,e,i,n,a){var o=i+3*(t-e)-r,s=3*(e-t*2+r),l=3*(t-r),u=r-n,f=s*s-3*o*l,h=s*l-9*o*u,v=l*l-3*s*u,c=0;if(ar(f)&&ar(h))if(ar(s))a[0]=0;else{var d=-l/s;d>=0&&d<=1&&(a[c++]=d)}else{var y=h*h-4*f*v;if(ar(y)){var p=h/f,d=-s/o+p,g=-p/2;d>=0&&d<=1&&(a[c++]=d),g>=0&&g<=1&&(a[c++]=g)}else if(y>0){var m=sr(y),_=f*s+1.5*o*(-h+m),S=f*s+1.5*o*(-h-m);_<0?_=-la(-_,ua):_=la(_,ua),S<0?S=-la(-S,ua):S=la(S,ua);var d=(-s-(_+S))/(3*o);d>=0&&d<=1&&(a[c++]=d)}else{var b=(2*f*s-3*o*h)/(2*sr(f*f*f)),w=Math.acos(b)/3,x=sr(f),A=Math.cos(w),d=(-s-2*x*A)/(3*o),g=(-s+x*(A+Zf*Math.sin(w)))/(3*o),C=(-s+x*(A-Zf*Math.sin(w)))/(3*o);d>=0&&d<=1&&(a[c++]=d),g>=0&&g<=1&&(a[c++]=g),C>=0&&C<=1&&(a[c++]=C)}}return c}function Rd(r,t,e,i,n){var a=6*e-12*t+6*r,o=9*t+3*i-3*r-9*e,s=3*t-3*r,l=0;if(ar(o)){if(Pd(a)){var u=-s/a;u>=0&&u<=1&&(n[l++]=u)}}else{var f=a*a-4*o*s;if(ar(f))n[0]=-a/(2*o);else if(f>0){var h=sr(f),u=(-a+h)/(2*o),v=(-a-h)/(2*o);u>=0&&u<=1&&(n[l++]=u),v>=0&&v<=1&&(n[l++]=v)}}return l}function uo(r,t,e,i,n,a){var o=(t-r)*n+r,s=(e-t)*n+t,l=(i-e)*n+e,u=(s-o)*n+o,f=(l-s)*n+s,h=(f-u)*n+u;a[0]=r,a[1]=o,a[2]=u,a[3]=h,a[4]=h,a[5]=f,a[6]=l,a[7]=i}function H0(r,t,e,i,n,a,o,s,l,u,f){var h,v=.005,c=1/0,d,y,p,g;Te[0]=l,Te[1]=u;for(var m=0;m<1;m+=.05)se[0]=St(r,e,n,o,m),se[1]=St(t,i,a,s,m),p=Mi(Te,se),p<c&&(h=m,c=p);c=1/0;for(var _=0;_<32&&!(v<Id);_++)d=h-v,y=h+v,se[0]=St(r,e,n,o,d),se[1]=St(t,i,a,s,d),p=Mi(se,Te),d>=0&&p<c?(h=d,c=p):(Ii[0]=St(r,e,n,o,y),Ii[1]=St(t,i,a,s,y),g=Mi(Ii,Te),y<=1&&g<c?(h=y,c=g):v*=.5);return f&&(f[0]=St(r,e,n,o,h),f[1]=St(t,i,a,s,h)),sr(c)}function G0(r,t,e,i,n,a,o,s,l){for(var u=r,f=t,h=0,v=1/l,c=1;c<=l;c++){var d=c*v,y=St(r,e,n,o,d),p=St(t,i,a,s,d),g=y-u,m=p-f;h+=Math.sqrt(g*g+m*m),u=y,f=p}return h}function It(r,t,e,i){var n=1-i;return n*(n*r+2*i*t)+i*i*e}function Kf(r,t,e,i){return 2*((1-i)*(t-r)+i*(e-t))}function V0(r,t,e,i,n){var a=r-2*t+e,o=2*(t-r),s=r-i,l=0;if(ar(a)){if(Pd(o)){var u=-s/o;u>=0&&u<=1&&(n[l++]=u)}}else{var f=o*o-4*a*s;if(ar(f)){var u=-o/(2*a);u>=0&&u<=1&&(n[l++]=u)}else if(f>0){var h=sr(f),u=(-o+h)/(2*a),v=(-o-h)/(2*a);u>=0&&u<=1&&(n[l++]=u),v>=0&&v<=1&&(n[l++]=v)}}return l}function Ed(r,t,e){var i=r+e-2*t;return i===0?.5:(r-t)/i}function fo(r,t,e,i,n){var a=(t-r)*i+r,o=(e-t)*i+t,s=(o-a)*i+a;n[0]=r,n[1]=a,n[2]=s,n[3]=s,n[4]=o,n[5]=e}function W0(r,t,e,i,n,a,o,s,l){var u,f=.005,h=1/0;Te[0]=o,Te[1]=s;for(var v=0;v<1;v+=.05){se[0]=It(r,e,n,v),se[1]=It(t,i,a,v);var c=Mi(Te,se);c<h&&(u=v,h=c)}h=1/0;for(var d=0;d<32&&!(f<Id);d++){var y=u-f,p=u+f;se[0]=It(r,e,n,y),se[1]=It(t,i,a,y);var c=Mi(se,Te);if(y>=0&&c<h)u=y,h=c;else{Ii[0]=It(r,e,n,p),Ii[1]=It(t,i,a,p);var g=Mi(Ii,Te);p<=1&&g<h?(u=p,h=g):f*=.5}}return l&&(l[0]=It(r,e,n,u),l[1]=It(t,i,a,u)),sr(h)}function U0(r,t,e,i,n,a,o){for(var s=r,l=t,u=0,f=1/o,h=1;h<=o;h++){var v=h*f,c=It(r,e,n,v),d=It(t,i,a,v),y=c-s,p=d-l;u+=Math.sqrt(y*y+p*p),s=c,l=d}return u}var $0=/cubic-bezier\(([0-9,\.e ]+)\)/;function kd(r){var t=r&&$0.exec(r);if(t){var e=t[1].split(","),i=+Ce(e[0]),n=+Ce(e[1]),a=+Ce(e[2]),o=+Ce(e[3]);if(isNaN(i+n+a+o))return;var s=[];return function(l){return l<=0?0:l>=1?1:lo(0,i,a,1,l,s)&&St(0,n,o,1,s[0])}}}var Y0=function(){function r(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||$t,this.ondestroy=t.ondestroy||$t,this.onrestart=t.onrestart||$t,t.easing&&this.setEasing(t.easing)}return r.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),this._paused){this._pausedTime+=e;return}var i=this._life,n=t-this._startTime-this._pausedTime,a=n/i;a<0&&(a=0),a=Math.min(a,1);var o=this.easingFunc,s=o?o(a):a;if(this.onframe(s),a===1)if(this.loop){var l=n%i;this._startTime=t-l,this._pausedTime=0,this.onrestart()}else return!0;return!1},r.prototype.pause=function(){this._paused=!0},r.prototype.resume=function(){this._paused=!1},r.prototype.setEasing=function(t){this.easing=t,this.easingFunc=U(t)?t:Ld[t]||kd(t)},r}();const X0=Y0;var Od=function(){function r(t){this.value=t}return r}(),Z0=function(){function r(){this._len=0}return r.prototype.insert=function(t){var e=new Od(t);return this.insertEntry(e),e},r.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},r.prototype.remove=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},r.prototype.len=function(){return this._len},r.prototype.clear=function(){this.head=this.tail=null,this._len=0},r}(),q0=function(){function r(t){this._list=new Z0,this._maxSize=10,this._map={},this._maxSize=t}return r.prototype.put=function(t,e){var i=this._list,n=this._map,a=null;if(n[t]==null){var o=i.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var l=i.head;i.remove(l),delete n[l.key],a=l.value,this._lastRemovedEntry=l}s?s.value=e:s=new Od(e),s.key=t,i.insertEntry(s),n[t]=s}return a},r.prototype.get=function(t){var e=this._map[t],i=this._list;if(e!=null)return e!==i.tail&&(i.remove(e),i.insertEntry(e)),e.value},r.prototype.clear=function(){this._list.clear(),this._map={}},r.prototype.len=function(){return this._list.len()},r}();const Jn=q0;var Qf={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function lr(r){return r=Math.round(r),r<0?0:r>255?255:r}function Al(r){return r<0?0:r>1?1:r}function hs(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?lr(parseFloat(t)/100*255):lr(parseInt(t,10))}function wn(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?Al(parseFloat(t)/100):Al(parseFloat(t))}function cs(r,t,e){return e<0?e+=1:e>1&&(e-=1),e*6<1?r+(t-r)*e*6:e*2<1?t:e*3<2?r+(t-r)*(2/3-e)*6:r}function fa(r,t,e){return r+(t-r)*e}function ie(r,t,e,i,n){return r[0]=t,r[1]=e,r[2]=i,r[3]=n,r}function Ll(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r}var Bd=new Jn(20),ha=null;function oi(r,t){ha&&Ll(ha,t),ha=Bd.put(r,ha||t.slice())}function ze(r,t){if(r){t=t||[];var e=Bd.get(r);if(e)return Ll(t,e);r=r+"";var i=r.replace(/ /g,"").toLowerCase();if(i in Qf)return Ll(t,Qf[i]),oi(r,t),t;var n=i.length;if(i.charAt(0)==="#"){if(n===4||n===5){var a=parseInt(i.slice(1,4),16);if(!(a>=0&&a<=4095)){ie(t,0,0,0,1);return}return ie(t,(a&3840)>>4|(a&3840)>>8,a&240|(a&240)>>4,a&15|(a&15)<<4,n===5?parseInt(i.slice(4),16)/15:1),oi(r,t),t}else if(n===7||n===9){var a=parseInt(i.slice(1,7),16);if(!(a>=0&&a<=16777215)){ie(t,0,0,0,1);return}return ie(t,(a&16711680)>>16,(a&65280)>>8,a&255,n===9?parseInt(i.slice(7),16)/255:1),oi(r,t),t}return}var o=i.indexOf("("),s=i.indexOf(")");if(o!==-1&&s+1===n){var l=i.substr(0,o),u=i.substr(o+1,s-(o+1)).split(","),f=1;switch(l){case"rgba":if(u.length!==4)return u.length===3?ie(t,+u[0],+u[1],+u[2],1):ie(t,0,0,0,1);f=wn(u.pop());case"rgb":if(u.length>=3)return ie(t,hs(u[0]),hs(u[1]),hs(u[2]),u.length===3?f:wn(u[3])),oi(r,t),t;ie(t,0,0,0,1);return;case"hsla":if(u.length!==4){ie(t,0,0,0,1);return}return u[3]=wn(u[3]),Jf(u,t),oi(r,t),t;case"hsl":if(u.length!==3){ie(t,0,0,0,1);return}return Jf(u,t),oi(r,t),t;default:return}}ie(t,0,0,0,1)}}function Jf(r,t){var e=(parseFloat(r[0])%360+360)%360/360,i=wn(r[1]),n=wn(r[2]),a=n<=.5?n*(i+1):n+i-n*i,o=n*2-a;return t=t||[],ie(t,lr(cs(o,a,e+1/3)*255),lr(cs(o,a,e)*255),lr(cs(o,a,e-1/3)*255),1),r.length===4&&(t[3]=r[3]),t}function jf(r,t){var e=ze(r);if(e){for(var i=0;i<3;i++)t<0?e[i]=e[i]*(1-t)|0:e[i]=(255-e[i])*t+e[i]|0,e[i]>255?e[i]=255:e[i]<0&&(e[i]=0);return Ro(e,e.length===4?"rgba":"rgb")}}function K0(r,t,e){if(!(!(t&&t.length)||!(r>=0&&r<=1))){var i=r*(t.length-1),n=Math.floor(i),a=Math.ceil(i),o=ze(t[n]),s=ze(t[a]),l=i-n,u=Ro([lr(fa(o[0],s[0],l)),lr(fa(o[1],s[1],l)),lr(fa(o[2],s[2],l)),Al(fa(o[3],s[3],l))],"rgba");return e?{color:u,leftIndex:n,rightIndex:a,value:i}:u}}function Ro(r,t){if(!(!r||!r.length)){var e=r[0]+","+r[1]+","+r[2];return(t==="rgba"||t==="hsva"||t==="hsla")&&(e+=","+r[3]),t+"("+e+")"}}function ho(r,t){var e=ze(r);return e?(.299*e[0]+.587*e[1]+.114*e[2])*e[3]/255+(1-e[3])*t:0}var th=new Jn(100);function eh(r){if(z(r)){var t=th.get(r);return t||(t=jf(r,-.1),th.put(r,t)),t}else if(Io(r)){var e=k({},r);return e.colorStops=V(r.colorStops,function(i){return{offset:i.offset,color:jf(i.color,-.1)}}),e}return r}function Q0(r){return r.type==="linear"}function J0(r){return r.type==="radial"}(function(){return Y.hasGlobalWindow&&U(window.btoa)?function(r){return window.btoa(unescape(encodeURIComponent(r)))}:typeof Buffer<"u"?function(r){return Buffer.from(r).toString("base64")}:function(r){return null}})();var Il=Array.prototype.slice;function Oe(r,t,e){return(t-r)*e+r}function vs(r,t,e,i){for(var n=t.length,a=0;a<n;a++)r[a]=Oe(t[a],e[a],i);return r}function j0(r,t,e,i){for(var n=t.length,a=n&&t[0].length,o=0;o<n;o++){r[o]||(r[o]=[]);for(var s=0;s<a;s++)r[o][s]=Oe(t[o][s],e[o][s],i)}return r}function ca(r,t,e,i){for(var n=t.length,a=0;a<n;a++)r[a]=t[a]+e[a]*i;return r}function rh(r,t,e,i){for(var n=t.length,a=n&&t[0].length,o=0;o<n;o++){r[o]||(r[o]=[]);for(var s=0;s<a;s++)r[o][s]=t[o][s]+e[o][s]*i}return r}function t_(r,t){for(var e=r.length,i=t.length,n=e>i?t:r,a=Math.min(e,i),o=n[a-1]||{color:[0,0,0,0],offset:0},s=a;s<Math.max(e,i);s++)n.push({offset:o.offset,color:o.color.slice()})}function e_(r,t,e){var i=r,n=t;if(!(!i.push||!n.push)){var a=i.length,o=n.length;if(a!==o){var s=a>o;if(s)i.length=o;else for(var l=a;l<o;l++)i.push(e===1?n[l]:Il.call(n[l]))}for(var u=i[0]&&i[0].length,l=0;l<i.length;l++)if(e===1)isNaN(i[l])&&(i[l]=n[l]);else for(var f=0;f<u;f++)isNaN(i[l][f])&&(i[l][f]=n[l][f])}}function Xa(r){if(Yt(r)){var t=r.length;if(Yt(r[0])){for(var e=[],i=0;i<t;i++)e.push(Il.call(r[i]));return e}return Il.call(r)}return r}function Za(r){return r[0]=Math.floor(r[0])||0,r[1]=Math.floor(r[1])||0,r[2]=Math.floor(r[2])||0,r[3]=r[3]==null?1:r[3],"rgba("+r.join(",")+")"}function r_(r){return Yt(r&&r[0])?2:1}var va=0,qa=1,Nd=2,dn=3,Pl=4,Rl=5,ih=6;function nh(r){return r===Pl||r===Rl}function da(r){return r===qa||r===Nd}var qi=[0,0,0,0],i_=function(){function r(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return r.prototype.isFinished=function(){return this._finished},r.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},r.prototype.needsAnimate=function(){return this.keyframes.length>=1},r.prototype.getAdditiveTrack=function(){return this._additiveTrack},r.prototype.addKeyframe=function(t,e,i){this._needsSort=!0;var n=this.keyframes,a=n.length,o=!1,s=ih,l=e;if(Yt(e)){var u=r_(e);s=u,(u===1&&!yt(e[0])||u===2&&!yt(e[0][0]))&&(o=!0)}else if(yt(e)&&!oo(e))s=va;else if(z(e))if(!isNaN(+e))s=va;else{var f=ze(e);f&&(l=f,s=dn)}else if(Io(e)){var h=k({},l);h.colorStops=V(e.colorStops,function(c){return{offset:c.offset,color:ze(c.color)}}),Q0(e)?s=Pl:J0(e)&&(s=Rl),l=h}a===0?this.valType=s:(s!==this.valType||s===ih)&&(o=!0),this.discrete=this.discrete||o;var v={time:t,value:l,rawValue:e,percent:0};return i&&(v.easing=i,v.easingFunc=U(i)?i:Ld[i]||kd(i)),n.push(v),v},r.prototype.prepare=function(t,e){var i=this.keyframes;this._needsSort&&i.sort(function(y,p){return y.time-p.time});for(var n=this.valType,a=i.length,o=i[a-1],s=this.discrete,l=da(n),u=nh(n),f=0;f<a;f++){var h=i[f],v=h.value,c=o.value;h.percent=h.time/t,s||(l&&f!==a-1?e_(v,c,n):u&&t_(v.colorStops,c.colorStops))}if(!s&&n!==Rl&&e&&this.needsAnimate()&&e.needsAnimate()&&n===e.valType&&!e._finished){this._additiveTrack=e;for(var d=i[0].value,f=0;f<a;f++)n===va?i[f].additiveValue=i[f].value-d:n===dn?i[f].additiveValue=ca([],i[f].value,d,-1):da(n)&&(i[f].additiveValue=n===qa?ca([],i[f].value,d,-1):rh([],i[f].value,d,-1))}},r.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var i=this._additiveTrack!=null,n=i?"additiveValue":"value",a=this.valType,o=this.keyframes,s=o.length,l=this.propName,u=a===dn,f,h=this._lastFr,v=Math.min,c,d;if(s===1)c=d=o[0];else{if(e<0)f=0;else if(e<this._lastFrP){var y=v(h+1,s-1);for(f=y;f>=0&&!(o[f].percent<=e);f--);f=v(f,s-2)}else{for(f=h;f<s&&!(o[f].percent>e);f++);f=v(f-1,s-2)}d=o[f+1],c=o[f]}if(c&&d){this._lastFr=f,this._lastFrP=e;var p=d.percent-c.percent,g=p===0?1:v((e-c.percent)/p,1);d.easingFunc&&(g=d.easingFunc(g));var m=i?this._additiveValue:u?qi:t[l];if((da(a)||u)&&!m&&(m=this._additiveValue=[]),this.discrete)t[l]=g<1?c.rawValue:d.rawValue;else if(da(a))a===qa?vs(m,c[n],d[n],g):j0(m,c[n],d[n],g);else if(nh(a)){var _=c[n],S=d[n],b=a===Pl;t[l]={type:b?"linear":"radial",x:Oe(_.x,S.x,g),y:Oe(_.y,S.y,g),colorStops:V(_.colorStops,function(x,A){var C=S.colorStops[A];return{offset:Oe(x.offset,C.offset,g),color:Za(vs([],x.color,C.color,g))}}),global:S.global},b?(t[l].x2=Oe(_.x2,S.x2,g),t[l].y2=Oe(_.y2,S.y2,g)):t[l].r=Oe(_.r,S.r,g)}else if(u)vs(m,c[n],d[n],g),i||(t[l]=Za(m));else{var w=Oe(c[n],d[n],g);i?this._additiveValue=w:t[l]=w}i&&this._addToTarget(t)}}},r.prototype._addToTarget=function(t){var e=this.valType,i=this.propName,n=this._additiveValue;e===va?t[i]=t[i]+n:e===dn?(ze(t[i],qi),ca(qi,qi,n,1),t[i]=Za(qi)):e===qa?ca(t[i],t[i],n,1):e===Nd&&rh(t[i],t[i],n,1)},r}(),n_=function(){function r(t,e,i,n){if(this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&n){Mu("Can' use additive animation on looped animation.");return}this._additiveAnimators=n,this._allowDiscrete=i}return r.prototype.getMaxTime=function(){return this._maxTime},r.prototype.getDelay=function(){return this._delay},r.prototype.getLoop=function(){return this._loop},r.prototype.getTarget=function(){return this._target},r.prototype.changeTarget=function(t){this._target=t},r.prototype.when=function(t,e,i){return this.whenWithKeys(t,e,gt(e),i)},r.prototype.whenWithKeys=function(t,e,i,n){for(var a=this._tracks,o=0;o<i.length;o++){var s=i[o],l=a[s];if(!l){l=a[s]=new i_(s);var u=void 0,f=this._getAdditiveTrack(s);if(f){var h=f.keyframes,v=h[h.length-1];u=v&&v.value,f.valType===dn&&u&&(u=Za(u))}else u=this._target[s];if(u==null)continue;t>0&&l.addKeyframe(0,Xa(u),n),this._trackKeys.push(s)}l.addKeyframe(t,Xa(e[s]),n)}return this._maxTime=Math.max(this._maxTime,t),this},r.prototype.pause=function(){this._clip.pause(),this._paused=!0},r.prototype.resume=function(){this._clip.resume(),this._paused=!1},r.prototype.isPaused=function(){return!!this._paused},r.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},r.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,i=0;i<e;i++)t[i].call(this)},r.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var i=0;i<e.length;i++)e[i].call(this)},r.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,i=0;i<e.length;i++)t[e[i]].setFinished()},r.prototype._getAdditiveTrack=function(t){var e,i=this._additiveAnimators;if(i)for(var n=0;n<i.length;n++){var a=i[n].getTrack(t);a&&(e=a)}return e},r.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,i=[],n=this._maxTime||0,a=0;a<this._trackKeys.length;a++){var o=this._trackKeys[a],s=this._tracks[o],l=this._getAdditiveTrack(o),u=s.keyframes,f=u.length;if(s.prepare(n,l),s.needsAnimate())if(!this._allowDiscrete&&s.discrete){var h=u[f-1];h&&(e._target[s.propName]=h.rawValue),s.setFinished()}else i.push(s)}if(i.length||this._force){var v=new X0({life:n,loop:this._loop,delay:this._delay||0,onframe:function(c){e._started=2;var d=e._additiveAnimators;if(d){for(var y=!1,p=0;p<d.length;p++)if(d[p]._clip){y=!0;break}y||(e._additiveAnimators=null)}for(var p=0;p<i.length;p++)i[p].step(e._target,c);var g=e._onframeCbs;if(g)for(var p=0;p<g.length;p++)g[p](e._target,c)},ondestroy:function(){e._doneCallback()}});this._clip=v,this.animation&&this.animation.addClip(v),t&&v.setEasing(t)}else this._doneCallback();return this}},r.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},r.prototype.delay=function(t){return this._delay=t,this},r.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},r.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},r.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},r.prototype.getClip=function(){return this._clip},r.prototype.getTrack=function(t){return this._tracks[t]},r.prototype.getTracks=function(){var t=this;return V(this._trackKeys,function(e){return t._tracks[e]})},r.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var i=this._tracks,n=this._trackKeys,a=0;a<t.length;a++){var o=i[t[a]];o&&!o.isFinished()&&(e?o.step(this._target,1):this._started===1&&o.step(this._target,0),o.setFinished())}for(var s=!0,a=0;a<n.length;a++)if(!i[n[a]].isFinished()){s=!1;break}return s&&this._abortedCallback(),s},r.prototype.saveTo=function(t,e,i){if(t){e=e||this._trackKeys;for(var n=0;n<e.length;n++){var a=e[n],o=this._tracks[a];if(!(!o||o.isFinished())){var s=o.keyframes,l=s[i?0:s.length-1];l&&(t[a]=Xa(l.rawValue))}}}},r.prototype.__changeFinalValue=function(t,e){e=e||gt(t);for(var i=0;i<e.length;i++){var n=e[i],a=this._tracks[n];if(a){var o=a.keyframes;if(o.length>1){var s=o.pop();a.addKeyframe(s.time,t[n]),a.prepare(this._maxTime,a.getAdditiveTrack())}}}},r}();const Eu=n_;function Ti(){return new Date().getTime()}var a_=function(r){O(t,r);function t(e){var i=r.call(this)||this;return i._running=!1,i._time=0,i._pausedTime=0,i._pauseStart=0,i._paused=!1,e=e||{},i.stage=e.stage||{},i}return t.prototype.addClip=function(e){e.animation&&this.removeClip(e),this._head?(this._tail.next=e,e.prev=this._tail,e.next=null,this._tail=e):this._head=this._tail=e,e.animation=this},t.prototype.addAnimator=function(e){e.animation=this;var i=e.getClip();i&&this.addClip(i)},t.prototype.removeClip=function(e){if(e.animation){var i=e.prev,n=e.next;i?i.next=n:this._head=n,n?n.prev=i:this._tail=i,e.next=e.prev=e.animation=null}},t.prototype.removeAnimator=function(e){var i=e.getClip();i&&this.removeClip(i),e.animation=null},t.prototype.update=function(e){for(var i=Ti()-this._pausedTime,n=i-this._time,a=this._head;a;){var o=a.next,s=a.step(i,n);s&&(a.ondestroy(),this.removeClip(a)),a=o}this._time=i,e||(this.trigger("frame",n),this.stage.update&&this.stage.update())},t.prototype._startLoop=function(){var e=this;this._running=!0;function i(){e._running&&(Ml(i),!e._paused&&e.update())}Ml(i)},t.prototype.start=function(){this._running||(this._time=Ti(),this._pausedTime=0,this._startLoop())},t.prototype.stop=function(){this._running=!1},t.prototype.pause=function(){this._paused||(this._pauseStart=Ti(),this._paused=!0)},t.prototype.resume=function(){this._paused&&(this._pausedTime+=Ti()-this._pauseStart,this._paused=!1)},t.prototype.clear=function(){for(var e=this._head;e;){var i=e.next;e.prev=e.next=e.animation=null,e=i}this._head=this._tail=null},t.prototype.isFinished=function(){return this._head==null},t.prototype.animate=function(e,i){i=i||{},this.start();var n=new Eu(e,i.loop);return this.addAnimator(n),n},t}(Ie);const o_=a_;var s_=300,ds=Y.domSupported,ps=function(){var r=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],t=["touchstart","touchend","touchmove"],e={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=V(r,function(n){var a=n.replace("mouse","pointer");return e.hasOwnProperty(a)?a:n});return{mouse:r,touch:t,pointer:i}}(),ah={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},oh=!1;function El(r){var t=r.pointerType;return t==="pen"||t==="touch"}function l_(r){r.touching=!0,r.touchTimer!=null&&(clearTimeout(r.touchTimer),r.touchTimer=null),r.touchTimer=setTimeout(function(){r.touching=!1,r.touchTimer=null},700)}function gs(r){r&&(r.zrByTouch=!0)}function u_(r,t){return ne(r.dom,new f_(r,t),!0)}function Fd(r,t){for(var e=t,i=!1;e&&e.nodeType!==9&&!(i=e.domBelongToZr||e!==t&&e===r.painterRoot);)e=e.parentNode;return i}var f_=function(){function r(t,e){this.stopPropagation=$t,this.stopImmediatePropagation=$t,this.preventDefault=$t,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}return r}(),ge={mousedown:function(r){r=ne(this.dom,r),this.__mayPointerCapture=[r.zrX,r.zrY],this.trigger("mousedown",r)},mousemove:function(r){r=ne(this.dom,r);var t=this.__mayPointerCapture;t&&(r.zrX!==t[0]||r.zrY!==t[1])&&this.__togglePointerCapture(!0),this.trigger("mousemove",r)},mouseup:function(r){r=ne(this.dom,r),this.__togglePointerCapture(!1),this.trigger("mouseup",r)},mouseout:function(r){r=ne(this.dom,r);var t=r.toElement||r.relatedTarget;Fd(this,t)||(this.__pointerCapturing&&(r.zrEventControl="no_globalout"),this.trigger("mouseout",r))},wheel:function(r){oh=!0,r=ne(this.dom,r),this.trigger("mousewheel",r)},mousewheel:function(r){oh||(r=ne(this.dom,r),this.trigger("mousewheel",r))},touchstart:function(r){r=ne(this.dom,r),gs(r),this.__lastTouchMoment=new Date,this.handler.processGesture(r,"start"),ge.mousemove.call(this,r),ge.mousedown.call(this,r)},touchmove:function(r){r=ne(this.dom,r),gs(r),this.handler.processGesture(r,"change"),ge.mousemove.call(this,r)},touchend:function(r){r=ne(this.dom,r),gs(r),this.handler.processGesture(r,"end"),ge.mouseup.call(this,r),+new Date-+this.__lastTouchMoment<s_&&ge.click.call(this,r)},pointerdown:function(r){ge.mousedown.call(this,r)},pointermove:function(r){El(r)||ge.mousemove.call(this,r)},pointerup:function(r){ge.mouseup.call(this,r)},pointerout:function(r){El(r)||ge.mouseout.call(this,r)}};M(["click","dblclick","contextmenu"],function(r){ge[r]=function(t){t=ne(this.dom,t),this.trigger(r,t)}});var kl={pointermove:function(r){El(r)||kl.mousemove.call(this,r)},pointerup:function(r){kl.mouseup.call(this,r)},mousemove:function(r){this.trigger("mousemove",r)},mouseup:function(r){var t=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",r),t&&(r.zrEventControl="only_globalout",this.trigger("mouseout",r))}};function h_(r,t){var e=t.domHandlers;Y.pointerEventsSupported?M(ps.pointer,function(i){Ka(t,i,function(n){e[i].call(r,n)})}):(Y.touchEventsSupported&&M(ps.touch,function(i){Ka(t,i,function(n){e[i].call(r,n),l_(t)})}),M(ps.mouse,function(i){Ka(t,i,function(n){n=Lu(n),t.touching||e[i].call(r,n)})}))}function c_(r,t){Y.pointerEventsSupported?M(ah.pointer,e):Y.touchEventsSupported||M(ah.mouse,e);function e(i){function n(a){a=Lu(a),Fd(r,a.target)||(a=u_(r,a),t.domHandlers[i].call(r,a))}Ka(t,i,n,{capture:!0})}}function Ka(r,t,e,i){r.mounted[t]=e,r.listenerOpts[t]=i,w0(r.domTarget,t,e,i)}function ys(r){var t=r.mounted;for(var e in t)t.hasOwnProperty(e)&&b0(r.domTarget,e,t[e],r.listenerOpts[e]);r.mounted={}}var sh=function(){function r(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e}return r}(),v_=function(r){O(t,r);function t(e,i){var n=r.call(this)||this;return n.__pointerCapturing=!1,n.dom=e,n.painterRoot=i,n._localHandlerScope=new sh(e,ge),ds&&(n._globalHandlerScope=new sh(document,kl)),h_(n,n._localHandlerScope),n}return t.prototype.dispose=function(){ys(this._localHandlerScope),ds&&ys(this._globalHandlerScope)},t.prototype.setCursor=function(e){this.dom.style&&(this.dom.style.cursor=e||"default")},t.prototype.__togglePointerCapture=function(e){if(this.__mayPointerCapture=null,ds&&+this.__pointerCapturing^+e){this.__pointerCapturing=e;var i=this._globalHandlerScope;e?c_(this,i):ys(i)}},t}(Ie);const d_=v_;var zd=1;Y.hasGlobalWindow&&(zd=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var co=zd,Ol=.4,Bl="#333",Nl="#ccc",p_="#eee",lh=Iu,uh=5e-5;function wr(r){return r>uh||r<-uh}var br=[],si=[],ms=Ai(),_s=Math.abs,g_=function(){function r(){}return r.prototype.getLocalTransform=function(t){return r.getLocalTransform(this,t)},r.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},r.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},r.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},r.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},r.prototype.needLocalTransform=function(){return wr(this.rotation)||wr(this.x)||wr(this.y)||wr(this.scaleX-1)||wr(this.scaleY-1)||wr(this.skewX)||wr(this.skewY)},r.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),i=this.transform;if(!(e||t)){i&&(lh(i),this.invTransform=null);return}i=i||Ai(),e?this.getLocalTransform(i):lh(i),t&&(e?Li(i,t,i):C0(i,t)),this.transform=i,this._resolveGlobalScaleRatio(i)},r.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(e!=null&&e!==1){this.getGlobalScale(br);var i=br[0]<0?-1:1,n=br[1]<0?-1:1,a=((br[0]-i)*e+i)/br[0]||0,o=((br[1]-n)*e+n)/br[1]||0;t[0]*=a,t[1]*=a,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||Ai(),Ru(this.invTransform,t)},r.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},r.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],n=Math.atan2(t[1],t[0]),a=Math.PI/2+n-Math.atan2(t[3],t[2]);i=Math.sqrt(i)*Math.cos(a),e=Math.sqrt(e),this.skewX=a,this.skewY=0,this.rotation=-n,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=i,this.originX=0,this.originY=0}},r.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||Ai(),Li(si,t.invTransform,e),e=si);var i=this.originX,n=this.originY;(i||n)&&(ms[4]=i,ms[5]=n,Li(si,e,ms),si[4]-=i,si[5]-=n,e=si),this.setLocalTransform(e)}},r.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},r.prototype.transformCoordToLocal=function(t,e){var i=[t,e],n=this.invTransform;return n&&he(i,i,n),i},r.prototype.transformCoordToGlobal=function(t,e){var i=[t,e],n=this.transform;return n&&he(i,i,n),i},r.prototype.getLineScale=function(){var t=this.transform;return t&&_s(t[0]-1)>1e-10&&_s(t[3]-1)>1e-10?Math.sqrt(_s(t[0]*t[3]-t[2]*t[1])):1},r.prototype.copyTransform=function(t){y_(this,t)},r.getLocalTransform=function(t,e){e=e||[];var i=t.originX||0,n=t.originY||0,a=t.scaleX,o=t.scaleY,s=t.anchorX,l=t.anchorY,u=t.rotation||0,f=t.x,h=t.y,v=t.skewX?Math.tan(t.skewX):0,c=t.skewY?Math.tan(-t.skewY):0;if(i||n||s||l){var d=i+s,y=n+l;e[4]=-d*a-v*y*o,e[5]=-y*o-c*d*a}else e[4]=e[5]=0;return e[0]=a,e[3]=o,e[1]=c*a,e[2]=v*o,u&&Pu(e,e,u),e[4]+=i+f,e[5]+=n+h,e},r.initDefaultProps=function(){var t=r.prototype;t.scaleX=t.scaleY=t.globalScaleRatio=1,t.x=t.y=t.originX=t.originY=t.skewX=t.skewY=t.rotation=t.anchorX=t.anchorY=0}(),r}(),On=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function y_(r,t){for(var e=0;e<On.length;e++){var i=On[e];r[i]=t[i]}}const ku=g_;var fh={};function te(r,t){t=t||Zr;var e=fh[t];e||(e=fh[t]=new Jn(500));var i=e.get(r);return i==null&&(i=Ni.measureText(r,t).width,e.put(r,i)),i}function hh(r,t,e,i){var n=te(r,t),a=Bu(t),o=pn(0,n,e),s=wi(0,a,i),l=new rt(o,s,n,a);return l}function Ou(r,t,e,i){var n=((r||"")+"").split(`
`),a=n.length;if(a===1)return hh(n[0],t,e,i);for(var o=new rt(0,0,0,0),s=0;s<n.length;s++){var l=hh(n[s],t,e,i);s===0?o.copy(l):o.union(l)}return o}function pn(r,t,e){return e==="right"?r-=t:e==="center"&&(r-=t/2),r}function wi(r,t,e){return e==="middle"?r-=t/2:e==="bottom"&&(r-=t),r}function Bu(r){return te("国",r)}function Kr(r,t){return typeof r=="string"?r.lastIndexOf("%")>=0?parseFloat(r)/100*t:parseFloat(r):r}function Hd(r,t,e){var i=t.position||"inside",n=t.distance!=null?t.distance:5,a=e.height,o=e.width,s=a/2,l=e.x,u=e.y,f="left",h="top";if(i instanceof Array)l+=Kr(i[0],e.width),u+=Kr(i[1],e.height),f=null,h=null;else switch(i){case"left":l-=n,u+=s,f="right",h="middle";break;case"right":l+=n+o,u+=s,h="middle";break;case"top":l+=o/2,u-=n,f="center",h="bottom";break;case"bottom":l+=o/2,u+=a+n,f="center";break;case"inside":l+=o/2,u+=s,f="center",h="middle";break;case"insideLeft":l+=n,u+=s,h="middle";break;case"insideRight":l+=o-n,u+=s,f="right",h="middle";break;case"insideTop":l+=o/2,u+=n,f="center";break;case"insideBottom":l+=o/2,u+=a-n,f="center",h="bottom";break;case"insideTopLeft":l+=n,u+=n;break;case"insideTopRight":l+=o-n,u+=n,f="right";break;case"insideBottomLeft":l+=n,u+=a-n,h="bottom";break;case"insideBottomRight":l+=o-n,u+=a-n,f="right",h="bottom";break}return r=r||{},r.x=l,r.y=u,r.align=f,r.verticalAlign=h,r}var Ss="__zr_normal__",ws=On.concat(["ignore"]),m_=Fi(On,function(r,t){return r[t]=!0,r},{ignore:!1}),li={},__=new rt(0,0,0,0),Nu=function(){function r(t){this.id=yd(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return r.prototype._init=function(t){this.attr(t)},r.prototype.drift=function(t,e,i){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0;break}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=e,this.decomposeTransform(),this.markRedraw()},r.prototype.beforeUpdate=function(){},r.prototype.afterUpdate=function(){},r.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},r.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var i=this.textConfig,n=i.local,a=e.innerTransformable,o=void 0,s=void 0,l=!1;a.parent=n?this:null;var u=!1;if(a.copyTransform(e),i.position!=null){var f=__;i.layoutRect?f.copy(i.layoutRect):f.copy(this.getBoundingRect()),n||f.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(li,i,f):Hd(li,i,f),a.x=li.x,a.y=li.y,o=li.align,s=li.verticalAlign;var h=i.origin;if(h&&i.rotation!=null){var v=void 0,c=void 0;h==="center"?(v=f.width*.5,c=f.height*.5):(v=Kr(h[0],f.width),c=Kr(h[1],f.height)),u=!0,a.originX=-a.x+v+(n?0:f.x),a.originY=-a.y+c+(n?0:f.y)}}i.rotation!=null&&(a.rotation=i.rotation);var d=i.offset;d&&(a.x+=d[0],a.y+=d[1],u||(a.originX=-d[0],a.originY=-d[1]));var y=i.inside==null?typeof i.position=="string"&&i.position.indexOf("inside")>=0:i.inside,p=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),g=void 0,m=void 0,_=void 0;y&&this.canBeInsideText()?(g=i.insideFill,m=i.insideStroke,(g==null||g==="auto")&&(g=this.getInsideTextFill()),(m==null||m==="auto")&&(m=this.getInsideTextStroke(g),_=!0)):(g=i.outsideFill,m=i.outsideStroke,(g==null||g==="auto")&&(g=this.getOutsideFill()),(m==null||m==="auto")&&(m=this.getOutsideStroke(g),_=!0)),g=g||"#000",(g!==p.fill||m!==p.stroke||_!==p.autoStroke||o!==p.align||s!==p.verticalAlign)&&(l=!0,p.fill=g,p.stroke=m,p.autoStroke=_,p.align=o,p.verticalAlign=s,e.setDefaultTextStyle(p)),e.__dirty|=jt,l&&e.dirtyStyle(!0)}},r.prototype.canBeInsideText=function(){return!0},r.prototype.getInsideTextFill=function(){return"#fff"},r.prototype.getInsideTextStroke=function(t){return"#000"},r.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?Nl:Bl},r.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),i=typeof e=="string"&&ze(e);i||(i=[255,255,255,1]);for(var n=i[3],a=this.__zr.isDarkMode(),o=0;o<3;o++)i[o]=i[o]*n+(a?0:255)*(1-n);return i[3]=1,Ro(i,"rgba")},r.prototype.traverse=function(t,e){},r.prototype.attrKV=function(t,e){t==="textConfig"?this.setTextConfig(e):t==="textContent"?this.setTextContent(e):t==="clipPath"?this.setClipPath(e):t==="extra"?(this.extra=this.extra||{},k(this.extra,e)):this[t]=e},r.prototype.hide=function(){this.ignore=!0,this.markRedraw()},r.prototype.show=function(){this.ignore=!1,this.markRedraw()},r.prototype.attr=function(t,e){if(typeof t=="string")this.attrKV(t,e);else if(H(t))for(var i=t,n=gt(i),a=0;a<n.length;a++){var o=n[a];this.attrKV(o,t[o])}return this.markRedraw(),this},r.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,i=0;i<this.animators.length;i++){var n=this.animators[i],a=n.__fromStateTransition;if(!(n.getLoop()||a&&a!==Ss)){var o=n.targetName,s=o?e[o]:e;n.saveTo(s)}}},r.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,ws)},r.prototype._savePrimaryToNormal=function(t,e,i){for(var n=0;n<i.length;n++){var a=i[n];t[a]!=null&&!(a in e)&&(e[a]=this[a])}},r.prototype.hasState=function(){return this.currentStates.length>0},r.prototype.getState=function(t){return this.states[t]},r.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},r.prototype.clearStates=function(t){this.useState(Ss,!1,t)},r.prototype.useState=function(t,e,i,n){var a=t===Ss,o=this.hasState();if(!(!o&&a)){var s=this.currentStates,l=this.stateTransition;if(!(lt(s,t)>=0&&(e||s.length===1))){var u;if(this.stateProxy&&!a&&(u=this.stateProxy(t)),u||(u=this.states&&this.states[t]),!u&&!a){Mu("State "+t+" not exists.");return}a||this.saveCurrentToNormalState(u);var f=!!(u&&u.hoverLayer||n);f&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,u,this._normalState,e,!i&&!this.__inHover&&l&&l.duration>0,l);var h=this._textContent,v=this._textGuide;return h&&h.useState(t,e,i,f),v&&v.useState(t,e,i,f),a?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!f&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~jt),u}}},r.prototype.useStates=function(t,e,i){if(!t.length)this.clearStates();else{var n=[],a=this.currentStates,o=t.length,s=o===a.length;if(s){for(var l=0;l<o;l++)if(t[l]!==a[l]){s=!1;break}}if(s)return;for(var l=0;l<o;l++){var u=t[l],f=void 0;this.stateProxy&&(f=this.stateProxy(u,t)),f||(f=this.states[u]),f&&n.push(f)}var h=n[o-1],v=!!(h&&h.hoverLayer||i);v&&this._toggleHoverLayerFlag(!0);var c=this._mergeStates(n),d=this.stateTransition;this.saveCurrentToNormalState(c),this._applyStateObj(t.join(","),c,this._normalState,!1,!e&&!this.__inHover&&d&&d.duration>0,d);var y=this._textContent,p=this._textGuide;y&&y.useStates(t,e,v),p&&p.useStates(t,e,v),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!v&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~jt)}},r.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},r.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},r.prototype.removeState=function(t){var e=lt(this.currentStates,t);if(e>=0){var i=this.currentStates.slice();i.splice(e,1),this.useStates(i)}},r.prototype.replaceState=function(t,e,i){var n=this.currentStates.slice(),a=lt(n,t),o=lt(n,e)>=0;a>=0?o?n.splice(a,1):n[a]=e:i&&!o&&n.push(e),this.useStates(n)},r.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},r.prototype._mergeStates=function(t){for(var e={},i,n=0;n<t.length;n++){var a=t[n];k(e,a),a.textConfig&&(i=i||{},k(i,a.textConfig))}return i&&(e.textConfig=i),e},r.prototype._applyStateObj=function(t,e,i,n,a,o){var s=!(e&&n);e&&e.textConfig?(this.textConfig=k({},n?this.textConfig:i.textConfig),k(this.textConfig,e.textConfig)):s&&i.textConfig&&(this.textConfig=i.textConfig);for(var l={},u=!1,f=0;f<ws.length;f++){var h=ws[f],v=a&&m_[h];e&&e[h]!=null?v?(u=!0,l[h]=e[h]):this[h]=e[h]:s&&i[h]!=null&&(v?(u=!0,l[h]=i[h]):this[h]=i[h])}if(!a)for(var f=0;f<this.animators.length;f++){var c=this.animators[f],d=c.targetName;c.getLoop()||c.__changeFinalValue(d?(e||i)[d]:e||i)}u&&this._transitionState(t,l,o)},r.prototype._attachComponent=function(t){if(!(t.__zr&&!t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},r.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},r.prototype.getClipPath=function(){return this._clipPath},r.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},r.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},r.prototype.getTextContent=function(){return this._textContent},r.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new ku,this._attachComponent(t),this._textContent=t,this.markRedraw())},r.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),k(this.textConfig,t),this.markRedraw()},r.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},r.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},r.prototype.getTextGuideLine=function(){return this._textGuide},r.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},r.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},r.prototype.markRedraw=function(){this.__dirty|=jt;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},r.prototype.dirty=function(){this.markRedraw()},r.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,i=this._textGuide;e&&(e.__inHover=t),i&&(i.__inHover=t)},r.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.addAnimator(e[i]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.removeAnimator(e[i]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},r.prototype.animate=function(t,e,i){var n=t?this[t]:this,a=new Eu(n,e,i);return t&&(a.targetName=t),this.addAnimator(a,t),a},r.prototype.addAnimator=function(t,e){var i=this.__zr,n=this;t.during(function(){n.updateDuringAnimation(e)}).done(function(){var a=n.animators,o=lt(a,t);o>=0&&a.splice(o,1)}),this.animators.push(t),i&&i.animation.addAnimator(t),i&&i.wakeUp()},r.prototype.updateDuringAnimation=function(t){this.markRedraw()},r.prototype.stopAnimation=function(t,e){for(var i=this.animators,n=i.length,a=[],o=0;o<n;o++){var s=i[o];!t||t===s.scope?s.stop(e):a.push(s)}return this.animators=a,this},r.prototype.animateTo=function(t,e,i){bs(this,t,e,i)},r.prototype.animateFrom=function(t,e,i){bs(this,t,e,i,!0)},r.prototype._transitionState=function(t,e,i,n){for(var a=bs(this,e,i,n),o=0;o<a.length;o++)a[o].__fromStateTransition=t},r.prototype.getBoundingRect=function(){return null},r.prototype.getPaintRect=function(){return null},r.initDefaultProps=function(){var t=r.prototype;t.type="element",t.name="",t.ignore=t.silent=t.isGroup=t.draggable=t.dragging=t.ignoreClip=t.__inHover=!1,t.__dirty=jt;function e(i,n,a,o){Object.defineProperty(t,i,{get:function(){if(!this[n]){var l=this[n]=[];s(this,l)}return this[n]},set:function(l){this[a]=l[0],this[o]=l[1],this[n]=l,s(this,l)}});function s(l,u){Object.defineProperty(u,0,{get:function(){return l[a]},set:function(f){l[a]=f}}),Object.defineProperty(u,1,{get:function(){return l[o]},set:function(f){l[o]=f}})}}Object.defineProperty&&(e("position","_legacyPos","x","y"),e("scale","_legacyScale","scaleX","scaleY"),e("origin","_legacyOrigin","originX","originY"))}(),r}();Le(Nu,Ie);Le(Nu,ku);function bs(r,t,e,i,n){e=e||{};var a=[];Gd(r,"",r,t,e,i,a,n);var o=a.length,s=!1,l=e.done,u=e.aborted,f=function(){s=!0,o--,o<=0&&(s?l&&l():u&&u())},h=function(){o--,o<=0&&(s?l&&l():u&&u())};o||l&&l(),a.length>0&&e.during&&a[0].during(function(d,y){e.during(y)});for(var v=0;v<a.length;v++){var c=a[v];f&&c.done(f),h&&c.aborted(h),e.force&&c.duration(e.duration),c.start(e.easing)}return a}function xs(r,t,e){for(var i=0;i<e;i++)r[i]=t[i]}function S_(r){return Yt(r[0])}function w_(r,t,e){if(Yt(t[e]))if(Yt(r[e])||(r[e]=[]),Xt(t[e])){var i=t[e].length;r[e].length!==i&&(r[e]=new t[e].constructor(i),xs(r[e],t[e],i))}else{var n=t[e],a=r[e],o=n.length;if(S_(n))for(var s=n[0].length,l=0;l<o;l++)a[l]?xs(a[l],n[l],s):a[l]=Array.prototype.slice.call(n[l]);else xs(a,n,o);a.length=n.length}else r[e]=t[e]}function b_(r,t){return r===t||Yt(r)&&Yt(t)&&x_(r,t)}function x_(r,t){var e=r.length;if(e!==t.length)return!1;for(var i=0;i<e;i++)if(r[i]!==t[i])return!1;return!0}function Gd(r,t,e,i,n,a,o,s){for(var l=gt(i),u=n.duration,f=n.delay,h=n.additive,v=n.setToFinal,c=!H(a),d=r.animators,y=[],p=0;p<l.length;p++){var g=l[p],m=i[g];if(m!=null&&e[g]!=null&&(c||a[g]))if(H(m)&&!Yt(m)&&!Io(m)){if(t){s||(e[g]=m,r.updateDuringAnimation(t));continue}Gd(r,g,e[g],m,n,a&&a[g],o,s)}else y.push(g);else s||(e[g]=m,r.updateDuringAnimation(t),y.push(g))}var _=y.length;if(!h&&_)for(var S=0;S<d.length;S++){var b=d[S];if(b.targetName===t){var w=b.stopTracks(y);if(w){var x=lt(d,b);d.splice(x,1)}}}if(n.force||(y=Tt(y,function(T){return!b_(i[T],e[T])}),_=y.length),_>0||n.force&&!o.length){var A=void 0,C=void 0,D=void 0;if(s){C={},v&&(A={});for(var S=0;S<_;S++){var g=y[S];C[g]=e[g],v?A[g]=i[g]:e[g]=i[g]}}else if(v){D={};for(var S=0;S<_;S++){var g=y[S];D[g]=Xa(e[g]),w_(e,i,g)}}var b=new Eu(e,!1,!1,h?Tt(d,function(L){return L.targetName===t}):null);b.targetName=t,n.scope&&(b.scope=n.scope),v&&A&&b.whenWithKeys(0,A,y),D&&b.whenWithKeys(0,D,y),b.whenWithKeys(u??500,s?C:i,y).delay(f||0),r.addAnimator(b,t),o.push(b)}}const Vd=Nu;var Wd=function(r){O(t,r);function t(e){var i=r.call(this)||this;return i.isGroup=!0,i._children=[],i.attr(e),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.children=function(){return this._children.slice()},t.prototype.childAt=function(e){return this._children[e]},t.prototype.childOfName=function(e){for(var i=this._children,n=0;n<i.length;n++)if(i[n].name===e)return i[n]},t.prototype.childCount=function(){return this._children.length},t.prototype.add=function(e){return e&&e!==this&&e.parent!==this&&(this._children.push(e),this._doAdd(e)),this},t.prototype.addBefore=function(e,i){if(e&&e!==this&&e.parent!==this&&i&&i.parent===this){var n=this._children,a=n.indexOf(i);a>=0&&(n.splice(a,0,e),this._doAdd(e))}return this},t.prototype.replace=function(e,i){var n=lt(this._children,e);return n>=0&&this.replaceAt(i,n),this},t.prototype.replaceAt=function(e,i){var n=this._children,a=n[i];if(e&&e!==this&&e.parent!==this&&e!==a){n[i]=e,a.parent=null;var o=this.__zr;o&&a.removeSelfFromZr(o),this._doAdd(e)}return this},t.prototype._doAdd=function(e){e.parent&&e.parent.remove(e),e.parent=this;var i=this.__zr;i&&i!==e.__zr&&e.addSelfToZr(i),i&&i.refresh()},t.prototype.remove=function(e){var i=this.__zr,n=this._children,a=lt(n,e);return a<0?this:(n.splice(a,1),e.parent=null,i&&e.removeSelfFromZr(i),i&&i.refresh(),this)},t.prototype.removeAll=function(){for(var e=this._children,i=this.__zr,n=0;n<e.length;n++){var a=e[n];i&&a.removeSelfFromZr(i),a.parent=null}return e.length=0,this},t.prototype.eachChild=function(e,i){for(var n=this._children,a=0;a<n.length;a++){var o=n[a];e.call(i,o,a)}return this},t.prototype.traverse=function(e,i){for(var n=0;n<this._children.length;n++){var a=this._children[n],o=e.call(i,a);a.isGroup&&!o&&a.traverse(e,i)}return this},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.addSelfToZr(e)}},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.removeSelfFromZr(e)}},t.prototype.getBoundingRect=function(e){for(var i=new rt(0,0,0,0),n=e||this._children,a=[],o=null,s=0;s<n.length;s++){var l=n[s];if(!(l.ignore||l.invisible)){var u=l.getBoundingRect(),f=l.getLocalTransform(a);f?(rt.applyTransform(i,u,f),o=o||i.clone(),o.union(i)):(o=o||u.clone(),o.union(u))}}return o||i},t}(Vd);Wd.prototype.type="group";const Ft=Wd;/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var Qa={},Ud={};function T_(r){delete Ud[r]}function C_(r){if(!r)return!1;if(typeof r=="string")return ho(r,1)<Ol;if(r.colorStops){for(var t=r.colorStops,e=0,i=t.length,n=0;n<i;n++)e+=ho(t[n].color,1);return e/=i,e<Ol}return!1}var D_=function(){function r(t,e,i){var n=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,i=i||{},this.dom=e,this.id=t;var a=new z0,o=i.renderer||"canvas";Qa[o]||(o=gt(Qa)[0]),i.useDirtyRect=i.useDirtyRect==null?!1:i.useDirtyRect;var s=new Qa[o](e,a,i,t),l=i.ssr||s.ssrOnly;this.storage=a,this.painter=s;var u=!Y.node&&!Y.worker&&!l?new d_(s.getViewportRoot(),s.root):null,f=i.useCoarsePointer,h=f==null||f==="auto"?Y.touchEventsSupported:!!f,v=44,c;h&&(c=Z(i.pointerSize,v)),this.handler=new k0(a,s,u,s.root,c),this.animation=new o_({stage:{update:l?null:function(){return n._flush(!0)}}}),l||this.animation.start()}return r.prototype.add=function(t){this._disposed||!t||(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},r.prototype.remove=function(t){this._disposed||!t||(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},r.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},r.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=C_(t))},r.prototype.getBackgroundColor=function(){return this._backgroundColor},r.prototype.setDarkMode=function(t){this._darkMode=t},r.prototype.isDarkMode=function(){return this._darkMode},r.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},r.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},r.prototype.flush=function(){this._disposed||this._flush(!1)},r.prototype._flush=function(t){var e,i=Ti();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var n=Ti();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:n-i})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},r.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},r.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},r.prototype.refreshHover=function(){this._needsRefreshHover=!0},r.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.getType()==="canvas"&&this.painter.refreshHover())},r.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},r.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},r.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},r.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},r.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},r.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},r.prototype.on=function(t,e,i){return this._disposed||this.handler.on(t,e,i),this},r.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},r.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},r.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Ft&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},r.prototype.dispose=function(){this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,T_(this.id))},r}();function ch(r,t){var e=new D_(yd(),r,t);return Ud[e.id]=e,e}function M_(r,t){Qa[r]=t}var vh=1e-4,$d=20;function A_(r){return r.replace(/^\s+|\s+$/g,"")}function dh(r,t,e,i){var n=t[0],a=t[1],o=e[0],s=e[1],l=a-n,u=s-o;if(l===0)return u===0?o:(o+s)/2;if(i)if(l>0){if(r<=n)return o;if(r>=a)return s}else{if(r>=n)return o;if(r<=a)return s}else{if(r===n)return o;if(r===a)return s}return(r-n)/l*u+o}function Bt(r,t){switch(r){case"center":case"middle":r="50%";break;case"left":case"top":r="0%";break;case"right":case"bottom":r="100%";break}return z(r)?A_(r).match(/%$/)?parseFloat(r)/100*t:parseFloat(r):r==null?NaN:+r}function _t(r,t,e){return t==null&&(t=10),t=Math.min(Math.max(0,t),$d),r=(+r).toFixed(t),e?r:+r}function Ne(r){if(r=+r,isNaN(r))return 0;if(r>1e-14){for(var t=1,e=0;e<15;e++,t*=10)if(Math.round(r*t)/t===r)return e}return L_(r)}function L_(r){var t=r.toString().toLowerCase(),e=t.indexOf("e"),i=e>0?+t.slice(e+1):0,n=e>0?e:t.length,a=t.indexOf("."),o=a<0?0:n-1-a;return Math.max(0,o-i)}function I_(r,t){var e=Math.log,i=Math.LN10,n=Math.floor(e(r[1]-r[0])/i),a=Math.round(e(Math.abs(t[1]-t[0]))/i),o=Math.min(Math.max(-n+a,0),20);return isFinite(o)?o:20}function P_(r,t){var e=Math.max(Ne(r),Ne(t)),i=r+t;return e>$d?i:_t(i,e)}function Yd(r){var t=Math.PI*2;return(r%t+t)%t}function vo(r){return r>-vh&&r<vh}var R_=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Ve(r){if(r instanceof Date)return r;if(z(r)){var t=R_.exec(r);if(!t)return new Date(NaN);if(t[8]){var e=+t[4]||0;return t[8].toUpperCase()!=="Z"&&(e-=+t[8].slice(0,3)),new Date(Date.UTC(+t[1],+(t[2]||1)-1,+t[3]||1,e,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0))}else return new Date(+t[1],+(t[2]||1)-1,+t[3]||1,+t[4]||0,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0)}else if(r==null)return new Date(NaN);return new Date(Math.round(r))}function E_(r){return Math.pow(10,Fu(r))}function Fu(r){if(r===0)return 0;var t=Math.floor(Math.log(r)/Math.LN10);return r/Math.pow(10,t)>=10&&t++,t}function Xd(r,t){var e=Fu(r),i=Math.pow(10,e),n=r/i,a;return t?n<1.5?a=1:n<2.5?a=2:n<4?a=3:n<7?a=5:a=10:n<1?a=1:n<2?a=2:n<3?a=3:n<5?a=5:a=10,r=a*i,e>=-20?+r.toFixed(e<0?-e:0):r}function po(r){var t=parseFloat(r);return t==r&&(t!==0||!z(r)||r.indexOf("x")<=0)?t:NaN}function k_(r){return!isNaN(po(r))}function Zd(){return Math.round(Math.random()*9)}function qd(r,t){return t===0?r:qd(t,r%t)}function ph(r,t){return r==null?t:t==null?r:r*t/qd(r,t)}function Wt(r){throw new Error(r)}function gh(r,t,e){return(t-r)*e+r}var Kd="series\0",O_="\0_ec_\0";function Nt(r){return r instanceof Array?r:r==null?[]:[r]}function yh(r,t,e){if(r){r[t]=r[t]||{},r.emphasis=r.emphasis||{},r.emphasis[t]=r.emphasis[t]||{};for(var i=0,n=e.length;i<n;i++){var a=e[i];!r.emphasis[t].hasOwnProperty(a)&&r[t].hasOwnProperty(a)&&(r.emphasis[t][a]=r[t][a])}}}var mh=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function jn(r){return H(r)&&!N(r)&&!(r instanceof Date)?r.value:r}function B_(r){return H(r)&&!(r instanceof Array)}function N_(r,t,e){var i=e==="normalMerge",n=e==="replaceMerge",a=e==="replaceAll";r=r||[],t=(t||[]).slice();var o=X();M(t,function(l,u){if(!H(l)){t[u]=null;return}});var s=F_(r,o,e);return(i||n)&&z_(s,r,o,t),i&&H_(s,t),i||n?G_(s,t,n):a&&V_(s,t),W_(s),s}function F_(r,t,e){var i=[];if(e==="replaceAll")return i;for(var n=0;n<r.length;n++){var a=r[n];a&&a.id!=null&&t.set(a.id,n),i.push({existing:e==="replaceMerge"||Bn(a)?null:a,newOption:null,keyInfo:null,brandNew:null})}return i}function z_(r,t,e,i){M(i,function(n,a){if(!(!n||n.id==null)){var o=bn(n.id),s=e.get(o);if(s!=null){var l=r[s];Ge(!l.newOption,'Duplicated option on id "'+o+'".'),l.newOption=n,l.existing=t[s],i[a]=null}}})}function H_(r,t){M(t,function(e,i){if(!(!e||e.name==null))for(var n=0;n<r.length;n++){var a=r[n].existing;if(!r[n].newOption&&a&&(a.id==null||e.id==null)&&!Bn(e)&&!Bn(a)&&Qd("name",a,e)){r[n].newOption=e,t[i]=null;return}}})}function G_(r,t,e){M(t,function(i){if(i){for(var n,a=0;(n=r[a])&&(n.newOption||Bn(n.existing)||n.existing&&i.id!=null&&!Qd("id",i,n.existing));)a++;n?(n.newOption=i,n.brandNew=e):r.push({newOption:i,brandNew:e,existing:null,keyInfo:null}),a++}})}function V_(r,t){M(t,function(e){r.push({newOption:e,brandNew:!0,existing:null,keyInfo:null})})}function W_(r){var t=X();M(r,function(e){var i=e.existing;i&&t.set(i.id,e)}),M(r,function(e){var i=e.newOption;Ge(!i||i.id==null||!t.get(i.id)||t.get(i.id)===e,"id duplicates: "+(i&&i.id)),i&&i.id!=null&&t.set(i.id,e),!e.keyInfo&&(e.keyInfo={})}),M(r,function(e,i){var n=e.existing,a=e.newOption,o=e.keyInfo;if(H(a)){if(o.name=a.name!=null?bn(a.name):n?n.name:Kd+i,n)o.id=bn(n.id);else if(a.id!=null)o.id=bn(a.id);else{var s=0;do o.id="\0"+o.name+"\0"+s++;while(t.get(o.id))}t.set(o.id,e)}})}function Qd(r,t,e){var i=Me(t[r],null),n=Me(e[r],null);return i!=null&&n!=null&&i===n}function bn(r){return Me(r,"")}function Me(r,t){return r==null?t:z(r)?r:yt(r)||Sl(r)?r+"":t}function zu(r){var t=r.name;return!!(t&&t.indexOf(Kd))}function Bn(r){return r&&r.id!=null&&bn(r.id).indexOf(O_)===0}function U_(r,t,e){M(r,function(i){var n=i.newOption;H(n)&&(i.keyInfo.mainType=t,i.keyInfo.subType=$_(t,n,i.existing,e))})}function $_(r,t,e,i){var n=t.type?t.type:e?e.subType:i.determineSubType(r,t);return n}function Qr(r,t){if(t.dataIndexInside!=null)return t.dataIndexInside;if(t.dataIndex!=null)return N(t.dataIndex)?V(t.dataIndex,function(e){return r.indexOfRawIndex(e)}):r.indexOfRawIndex(t.dataIndex);if(t.name!=null)return N(t.name)?V(t.name,function(e){return r.indexOfName(e)}):r.indexOfName(t.name)}function wt(){var r="__ec_inner_"+Y_++;return function(t){return t[r]||(t[r]={})}}var Y_=Zd();function Ts(r,t,e){var i=Hu(t,e),n=i.mainTypeSpecified,a=i.queryOptionMap,o=i.others,s=o,l=e?e.defaultMainType:null;return!n&&l&&a.set(l,{}),a.each(function(u,f){var h=ta(r,f,u,{useDefault:l===f,enableAll:e&&e.enableAll!=null?e.enableAll:!0,enableNone:e&&e.enableNone!=null?e.enableNone:!0});s[f+"Models"]=h.models,s[f+"Model"]=h.models[0]}),s}function Hu(r,t){var e;if(z(r)){var i={};i[r+"Index"]=0,e=i}else e=r;var n=X(),a={},o=!1;return M(e,function(s,l){if(l==="dataIndex"||l==="dataIndexInside"){a[l]=s;return}var u=l.match(/^(\w+)(Index|Id|Name)$/)||[],f=u[1],h=(u[2]||"").toLowerCase();if(!(!f||!h||t&&t.includeMainTypes&&lt(t.includeMainTypes,f)<0)){o=o||!!f;var v=n.get(f)||n.set(f,{});v[h]=s}}),{mainTypeSpecified:o,queryOptionMap:n,others:a}}var me={useDefault:!0,enableAll:!1,enableNone:!1};function ta(r,t,e,i){i=i||me;var n=e.index,a=e.id,o=e.name,s={models:null,specified:n!=null||a!=null||o!=null};if(!s.specified){var l=void 0;return s.models=i.useDefault&&(l=r.getComponent(t))?[l]:[],s}return n==="none"||n===!1?(Ge(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),s.models=[],s):(n==="all"&&(Ge(i.enableAll,'`"all"` is not a valid value on index option.'),n=a=o=null),s.models=r.queryComponents({mainType:t,index:n,id:a,name:o}),s)}function Jd(r,t,e){r.setAttribute?r.setAttribute(t,e):r[t]=e}function X_(r,t){return r.getAttribute?r.getAttribute(t):r[t]}function Z_(r){return r==="auto"?Y.domSupported?"html":"richText":r||"html"}function q_(r,t,e,i,n){var a=t==null||t==="auto";if(i==null)return i;if(yt(i)){var o=gh(e||0,i,n);return _t(o,a?Math.max(Ne(e||0),Ne(i)):t)}else{if(z(i))return n<1?e:i;for(var s=[],l=e,u=i,f=Math.max(l?l.length:0,u.length),h=0;h<f;++h){var v=r.getDimensionInfo(h);if(v&&v.type==="ordinal")s[h]=(n<1&&l?l:u)[h];else{var c=l&&l[h]?l[h]:0,d=u[h],o=gh(c,d,n);s[h]=_t(o,a?Math.max(Ne(c),Ne(d)):t)}}return s}}var K_=".",xr="___EC__COMPONENT__CONTAINER___",jd="___EC__EXTENDED_CLASS___";function De(r){var t={main:"",sub:""};if(r){var e=r.split(K_);t.main=e[0]||"",t.sub=e[1]||""}return t}function Q_(r){Ge(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(r),'componentType "'+r+'" illegal')}function J_(r){return!!(r&&r[jd])}function Gu(r,t){r.$constructor=r,r.extend=function(e){var i=this,n;return j_(i)?n=function(a){O(o,a);function o(){return a.apply(this,arguments)||this}return o}(i):(n=function(){(e.$constructor||i).apply(this,arguments)},Zm(n,this)),k(n.prototype,e),n[jd]=!0,n.extend=this.extend,n.superCall=r1,n.superApply=i1,n.superClass=i,n}}function j_(r){return U(r)&&/^class\s/.test(Function.prototype.toString.call(r))}function tp(r,t){r.extend=t.extend}var t1=Math.round(Math.random()*10);function e1(r){var t=["__\0is_clz",t1++].join("_");r.prototype[t]=!0,r.isInstance=function(e){return!!(e&&e[t])}}function r1(r,t){for(var e=[],i=2;i<arguments.length;i++)e[i-2]=arguments[i];return this.superClass.prototype[t].apply(r,e)}function i1(r,t,e){return this.superClass.prototype[t].apply(r,e)}function Eo(r){var t={};r.registerClass=function(i){var n=i.type||i.prototype.type;if(n){Q_(n),i.prototype.type=n;var a=De(n);if(!a.sub)t[a.main]=i;else if(a.sub!==xr){var o=e(a);o[a.sub]=i}}return i},r.getClass=function(i,n,a){var o=t[i];if(o&&o[xr]&&(o=n?o[n]:null),a&&!o)throw new Error(n?"Component "+i+"."+(n||"")+" is used but not imported.":i+".type should be specified.");return o},r.getClassesByMainType=function(i){var n=De(i),a=[],o=t[n.main];return o&&o[xr]?M(o,function(s,l){l!==xr&&a.push(s)}):a.push(o),a},r.hasClass=function(i){var n=De(i);return!!t[n.main]},r.getAllClassMainTypes=function(){var i=[];return M(t,function(n,a){i.push(a)}),i},r.hasSubTypes=function(i){var n=De(i),a=t[n.main];return a&&a[xr]};function e(i){var n=t[i.main];return(!n||!n[xr])&&(n=t[i.main]={},n[xr]=!0),n}}function Nn(r,t){for(var e=0;e<r.length;e++)r[e][1]||(r[e][1]=r[e][0]);return t=t||!1,function(i,n,a){for(var o={},s=0;s<r.length;s++){var l=r[s][1];if(!(n&&lt(n,l)>=0||a&&lt(a,l)<0)){var u=i.getShallow(l,t);u!=null&&(o[r[s][0]]=u)}}return o}}var n1=[["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]],a1=Nn(n1),o1=function(){function r(){}return r.prototype.getAreaStyle=function(t,e){return a1(this,t,e)},r}(),Fl=new Jn(50);function s1(r){if(typeof r=="string"){var t=Fl.get(r);return t&&t.image}else return r}function ep(r,t,e,i,n){if(r)if(typeof r=="string"){if(t&&t.__zrImageSrc===r||!e)return t;var a=Fl.get(r),o={hostEl:e,cb:i,cbPayload:n};return a?(t=a.image,!ko(t)&&a.pending.push(o)):(t=Ni.loadImage(r,_h,_h),t.__zrImageSrc=r,Fl.put(r,t.__cachedImgObj={image:t,pending:[o]})),t}else return r;else return t}function _h(){var r=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var t=0;t<r.pending.length;t++){var e=r.pending[t],i=e.cb;i&&i(this,e.cbPayload),e.hostEl.dirty()}r.pending.length=0}function ko(r){return r&&r.width&&r.height}var Cs=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function l1(r,t,e,i,n,a){if(!e){r.text="",r.isTruncated=!1;return}var o=(t+"").split(`
`);a=rp(e,i,n,a);for(var s=!1,l={},u=0,f=o.length;u<f;u++)ip(l,o[u],a),o[u]=l.textLine,s=s||l.isTruncated;r.text=o.join(`
`),r.isTruncated=s}function rp(r,t,e,i){i=i||{};var n=k({},i);n.font=t,e=Z(e,"..."),n.maxIterations=Z(i.maxIterations,2);var a=n.minChar=Z(i.minChar,0);n.cnCharWidth=te("国",t);var o=n.ascCharWidth=te("a",t);n.placeholder=Z(i.placeholder,"");for(var s=r=Math.max(0,r-1),l=0;l<a&&s>=o;l++)s-=o;var u=te(e,t);return u>s&&(e="",u=0),s=r-u,n.ellipsis=e,n.ellipsisWidth=u,n.contentWidth=s,n.containerWidth=r,n}function ip(r,t,e){var i=e.containerWidth,n=e.font,a=e.contentWidth;if(!i){r.textLine="",r.isTruncated=!1;return}var o=te(t,n);if(o<=i){r.textLine=t,r.isTruncated=!1;return}for(var s=0;;s++){if(o<=a||s>=e.maxIterations){t+=e.ellipsis;break}var l=s===0?u1(t,a,e.ascCharWidth,e.cnCharWidth):o>0?Math.floor(t.length*a/o):0;t=t.substr(0,l),o=te(t,n)}t===""&&(t=e.placeholder),r.textLine=t,r.isTruncated=!0}function u1(r,t,e,i){for(var n=0,a=0,o=r.length;a<o&&n<t;a++){var s=r.charCodeAt(a);n+=0<=s&&s<=127?e:i}return a}function f1(r,t){r!=null&&(r+="");var e=t.overflow,i=t.padding,n=t.font,a=e==="truncate",o=Bu(n),s=Z(t.lineHeight,o),l=!!t.backgroundColor,u=t.lineOverflow==="truncate",f=!1,h=t.width,v;h!=null&&(e==="break"||e==="breakAll")?v=r?np(r,t.font,h,e==="breakAll",0).lines:[]:v=r?r.split(`
`):[];var c=v.length*s,d=Z(t.height,c);if(c>d&&u){var y=Math.floor(d/s);f=f||v.length>y,v=v.slice(0,y)}if(r&&a&&h!=null)for(var p=rp(h,n,t.ellipsis,{minChar:t.truncateMinChar,placeholder:t.placeholder}),g={},m=0;m<v.length;m++)ip(g,v[m],p),v[m]=g.textLine,f=f||g.isTruncated;for(var _=d,S=0,m=0;m<v.length;m++)S=Math.max(te(v[m],n),S);h==null&&(h=S);var b=S;return i&&(_+=i[0]+i[2],b+=i[1]+i[3],h+=i[1]+i[3]),l&&(b=h),{lines:v,height:d,outerWidth:b,outerHeight:_,lineHeight:s,calculatedLineHeight:o,contentWidth:S,contentHeight:c,width:h,isTruncated:f}}var h1=function(){function r(){}return r}(),Sh=function(){function r(t){this.tokens=[],t&&(this.tokens=t)}return r}(),c1=function(){function r(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1}return r}();function v1(r,t){var e=new c1;if(r!=null&&(r+=""),!r)return e;for(var i=t.width,n=t.height,a=t.overflow,o=(a==="break"||a==="breakAll")&&i!=null?{width:i,accumWidth:0,breakAll:a==="breakAll"}:null,s=Cs.lastIndex=0,l;(l=Cs.exec(r))!=null;){var u=l.index;u>s&&Ds(e,r.substring(s,u),t,o),Ds(e,l[2],t,o,l[1]),s=Cs.lastIndex}s<r.length&&Ds(e,r.substring(s,r.length),t,o);var f=[],h=0,v=0,c=t.padding,d=a==="truncate",y=t.lineOverflow==="truncate",p={};function g($,it,J){$.width=it,$.lineHeight=J,h+=J,v=Math.max(v,it)}t:for(var m=0;m<e.lines.length;m++){for(var _=e.lines[m],S=0,b=0,w=0;w<_.tokens.length;w++){var x=_.tokens[w],A=x.styleName&&t.rich[x.styleName]||{},C=x.textPadding=A.padding,D=C?C[1]+C[3]:0,T=x.font=A.font||t.font;x.contentHeight=Bu(T);var L=Z(A.height,x.contentHeight);if(x.innerHeight=L,C&&(L+=C[0]+C[2]),x.height=L,x.lineHeight=Ua(A.lineHeight,t.lineHeight,L),x.align=A&&A.align||t.align,x.verticalAlign=A&&A.verticalAlign||"middle",y&&n!=null&&h+x.lineHeight>n){var P=e.lines.length;w>0?(_.tokens=_.tokens.slice(0,w),g(_,b,S),e.lines=e.lines.slice(0,m+1)):e.lines=e.lines.slice(0,m),e.isTruncated=e.isTruncated||e.lines.length<P;break t}var I=A.width,R=I==null||I==="auto";if(typeof I=="string"&&I.charAt(I.length-1)==="%")x.percentWidth=I,f.push(x),x.contentWidth=te(x.text,T);else{if(R){var E=A.backgroundColor,G=E&&E.image;G&&(G=s1(G),ko(G)&&(x.width=Math.max(x.width,G.width*L/G.height)))}var B=d&&i!=null?i-b:null;B!=null&&B<x.width?!R||B<D?(x.text="",x.width=x.contentWidth=0):(l1(p,x.text,B-D,T,t.ellipsis,{minChar:t.truncateMinChar}),x.text=p.text,e.isTruncated=e.isTruncated||p.isTruncated,x.width=x.contentWidth=te(x.text,T)):x.contentWidth=te(x.text,T)}x.width+=D,b+=x.width,A&&(S=Math.max(S,x.lineHeight))}g(_,b,S)}e.outerWidth=e.width=Z(i,v),e.outerHeight=e.height=Z(n,h),e.contentHeight=h,e.contentWidth=v,c&&(e.outerWidth+=c[1]+c[3],e.outerHeight+=c[0]+c[2]);for(var m=0;m<f.length;m++){var x=f[m],F=x.percentWidth;x.width=parseInt(F,10)/100*e.width}return e}function Ds(r,t,e,i,n){var a=t==="",o=n&&e.rich[n]||{},s=r.lines,l=o.font||e.font,u=!1,f,h;if(i){var v=o.padding,c=v?v[1]+v[3]:0;if(o.width!=null&&o.width!=="auto"){var d=Kr(o.width,i.width)+c;s.length>0&&d+i.accumWidth>i.width&&(f=t.split(`
`),u=!0),i.accumWidth=d}else{var y=np(t,l,i.width,i.breakAll,i.accumWidth);i.accumWidth=y.accumWidth+c,h=y.linesWidths,f=y.lines}}else f=t.split(`
`);for(var p=0;p<f.length;p++){var g=f[p],m=new h1;if(m.styleName=n,m.text=g,m.isLineHolder=!g&&!a,typeof o.width=="number"?m.width=o.width:m.width=h?h[p]:te(g,l),!p&&!u){var _=(s[s.length-1]||(s[0]=new Sh)).tokens,S=_.length;S===1&&_[0].isLineHolder?_[0]=m:(g||!S||a)&&_.push(m)}else s.push(new Sh([m]))}}function d1(r){var t=r.charCodeAt(0);return t>=32&&t<=591||t>=880&&t<=4351||t>=4608&&t<=5119||t>=7680&&t<=8303}var p1=Fi(",&?/;] ".split(""),function(r,t){return r[t]=!0,r},{});function g1(r){return d1(r)?!!p1[r]:!0}function np(r,t,e,i,n){for(var a=[],o=[],s="",l="",u=0,f=0,h=0;h<r.length;h++){var v=r.charAt(h);if(v===`
`){l&&(s+=l,f+=u),a.push(s),o.push(f),s="",l="",u=0,f=0;continue}var c=te(v,t),d=i?!1:!g1(v);if(a.length?f+c>e:n+f+c>e){f?(s||l)&&(d?(s||(s=l,l="",u=0,f=u),a.push(s),o.push(f-u),l+=v,u+=c,s="",f=u):(l&&(s+=l,l="",u=0),a.push(s),o.push(f),s=v,f=c)):d?(a.push(l),o.push(u),l=v,u=c):(a.push(v),o.push(c));continue}f+=c,d?(l+=v,u+=c):(l&&(s+=l,l="",u=0),s+=v)}return!a.length&&!s&&(s=r,l="",u=0),l&&(s+=l),s&&(a.push(s),o.push(f)),a.length===1&&(f+=n),{accumWidth:f,lines:a,linesWidths:o}}var zl="__zr_style_"+Math.round(Math.random()*10),Ur={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},Oo={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};Ur[zl]=!0;var wh=["z","z2","invisible"],y1=["invisible"],m1=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype._init=function(e){for(var i=gt(e),n=0;n<i.length;n++){var a=i[n];a==="style"?this.useStyle(e[a]):r.prototype.attrKV.call(this,a,e[a])}this.style||this.useStyle({})},t.prototype.beforeBrush=function(){},t.prototype.afterBrush=function(){},t.prototype.innerBeforeBrush=function(){},t.prototype.innerAfterBrush=function(){},t.prototype.shouldBePainted=function(e,i,n,a){var o=this.transform;if(this.ignore||this.invisible||this.style.opacity===0||this.culling&&_1(this,e,i)||o&&!o[0]&&!o[3])return!1;if(n&&this.__clipPaths){for(var s=0;s<this.__clipPaths.length;++s)if(this.__clipPaths[s].isZeroArea())return!1}if(a&&this.parent)for(var l=this.parent;l;){if(l.ignore)return!1;l=l.parent}return!0},t.prototype.contain=function(e,i){return this.rectContain(e,i)},t.prototype.traverse=function(e,i){e.call(i,this)},t.prototype.rectContain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect();return a.contain(n[0],n[1])},t.prototype.getPaintRect=function(){var e=this._paintRect;if(!this._paintRect||this.__dirty){var i=this.transform,n=this.getBoundingRect(),a=this.style,o=a.shadowBlur||0,s=a.shadowOffsetX||0,l=a.shadowOffsetY||0;e=this._paintRect||(this._paintRect=new rt(0,0,0,0)),i?rt.applyTransform(e,n,i):e.copy(n),(o||s||l)&&(e.width+=o*2+Math.abs(s),e.height+=o*2+Math.abs(l),e.x=Math.min(e.x,e.x+s-o),e.y=Math.min(e.y,e.y+l-o));var u=this.dirtyRectTolerance;e.isZero()||(e.x=Math.floor(e.x-u),e.y=Math.floor(e.y-u),e.width=Math.ceil(e.width+1+u*2),e.height=Math.ceil(e.height+1+u*2))}return e},t.prototype.setPrevPaintRect=function(e){e?(this._prevPaintRect=this._prevPaintRect||new rt(0,0,0,0),this._prevPaintRect.copy(e)):this._prevPaintRect=null},t.prototype.getPrevPaintRect=function(){return this._prevPaintRect},t.prototype.animateStyle=function(e){return this.animate("style",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():this.markRedraw()},t.prototype.attrKV=function(e,i){e!=="style"?r.prototype.attrKV.call(this,e,i):this.style?this.setStyle(i):this.useStyle(i)},t.prototype.setStyle=function(e,i){return typeof e=="string"?this.style[e]=i:k(this.style,e),this.dirtyStyle(),this},t.prototype.dirtyStyle=function(e){e||this.markRedraw(),this.__dirty|=vn,this._rect&&(this._rect=null)},t.prototype.dirty=function(){this.dirtyStyle()},t.prototype.styleChanged=function(){return!!(this.__dirty&vn)},t.prototype.styleUpdated=function(){this.__dirty&=~vn},t.prototype.createStyle=function(e){return Po(Ur,e)},t.prototype.useStyle=function(e){e[zl]||(e=this.createStyle(e)),this.__inHover?this.__hoverStyle=e:this.style=e,this.dirtyStyle()},t.prototype.isStyleObject=function(e){return e[zl]},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var i=this._normalState;e.style&&!i.style&&(i.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,i,wh)},t.prototype._applyStateObj=function(e,i,n,a,o,s){r.prototype._applyStateObj.call(this,e,i,n,a,o,s);var l=!(i&&a),u;if(i&&i.style?o?a?u=i.style:(u=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(u,i.style)):(u=this._mergeStyle(this.createStyle(),a?this.style:n.style),this._mergeStyle(u,i.style)):l&&(u=n.style),u)if(o){var f=this.style;if(this.style=this.createStyle(l?{}:f),l)for(var h=gt(f),v=0;v<h.length;v++){var c=h[v];c in u&&(u[c]=u[c],this.style[c]=f[c])}for(var d=gt(u),v=0;v<d.length;v++){var c=d[v];this.style[c]=this.style[c]}this._transitionState(e,{style:u},s,this.getAnimationStyleProps())}else this.useStyle(u);for(var y=this.__inHover?y1:wh,v=0;v<y.length;v++){var c=y[v];i&&i[c]!=null?this[c]=i[c]:l&&n[c]!=null&&(this[c]=n[c])}},t.prototype._mergeStates=function(e){for(var i=r.prototype._mergeStates.call(this,e),n,a=0;a<e.length;a++){var o=e[a];o.style&&(n=n||{},this._mergeStyle(n,o.style))}return n&&(i.style=n),i},t.prototype._mergeStyle=function(e,i){return k(e,i),e},t.prototype.getAnimationStyleProps=function(){return Oo},t.initDefaultProps=function(){var e=t.prototype;e.type="displayable",e.invisible=!1,e.z=0,e.z2=0,e.zlevel=0,e.culling=!1,e.cursor="pointer",e.rectHover=!1,e.incremental=!1,e._rect=null,e.dirtyRectTolerance=0,e.__dirty=jt|vn}(),t}(Vd),Ms=new rt(0,0,0,0),As=new rt(0,0,0,0);function _1(r,t,e){return Ms.copy(r.getBoundingRect()),r.transform&&Ms.applyTransform(r.transform),As.width=t,As.height=e,!Ms.intersect(As)}const ea=m1;var le=Math.min,ue=Math.max,Ls=Math.sin,Is=Math.cos,Tr=Math.PI*2,pa=zi(),ga=zi(),ya=zi();function bh(r,t,e,i,n,a){n[0]=le(r,e),n[1]=le(t,i),a[0]=ue(r,e),a[1]=ue(t,i)}var xh=[],Th=[];function S1(r,t,e,i,n,a,o,s,l,u){var f=Rd,h=St,v=f(r,e,n,o,xh);l[0]=1/0,l[1]=1/0,u[0]=-1/0,u[1]=-1/0;for(var c=0;c<v;c++){var d=h(r,e,n,o,xh[c]);l[0]=le(d,l[0]),u[0]=ue(d,u[0])}v=f(t,i,a,s,Th);for(var c=0;c<v;c++){var y=h(t,i,a,s,Th[c]);l[1]=le(y,l[1]),u[1]=ue(y,u[1])}l[0]=le(r,l[0]),u[0]=ue(r,u[0]),l[0]=le(o,l[0]),u[0]=ue(o,u[0]),l[1]=le(t,l[1]),u[1]=ue(t,u[1]),l[1]=le(s,l[1]),u[1]=ue(s,u[1])}function w1(r,t,e,i,n,a,o,s){var l=Ed,u=It,f=ue(le(l(r,e,n),1),0),h=ue(le(l(t,i,a),1),0),v=u(r,e,n,f),c=u(t,i,a,h);o[0]=le(r,n,v),o[1]=le(t,a,c),s[0]=ue(r,n,v),s[1]=ue(t,a,c)}function b1(r,t,e,i,n,a,o,s,l){var u=bi,f=xi,h=Math.abs(n-a);if(h%Tr<1e-4&&h>1e-4){s[0]=r-e,s[1]=t-i,l[0]=r+e,l[1]=t+i;return}if(pa[0]=Is(n)*e+r,pa[1]=Ls(n)*i+t,ga[0]=Is(a)*e+r,ga[1]=Ls(a)*i+t,u(s,pa,ga),f(l,pa,ga),n=n%Tr,n<0&&(n=n+Tr),a=a%Tr,a<0&&(a=a+Tr),n>a&&!o?a+=Tr:n<a&&o&&(n+=Tr),o){var v=a;a=n,n=v}for(var c=0;c<a;c+=Math.PI/2)c>n&&(ya[0]=Is(c)*e+r,ya[1]=Ls(c)*i+t,u(s,ya,s),f(l,ya,l))}var tt={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Cr=[],Dr=[],Se=[],Xe=[],we=[],be=[],Ps=Math.min,Rs=Math.max,Mr=Math.cos,Ar=Math.sin,Ee=Math.abs,Hl=Math.PI,ir=Hl*2,Es=typeof Float32Array<"u",Ki=[];function ks(r){var t=Math.round(r/Hl*1e8)/1e8;return t%2*Hl}function x1(r,t){var e=ks(r[0]);e<0&&(e+=ir);var i=e-r[0],n=r[1];n+=i,!t&&n-e>=ir?n=e+ir:t&&e-n>=ir?n=e-ir:!t&&e>n?n=e+(ir-ks(e-n)):t&&e<n&&(n=e-(ir-ks(n-e))),r[0]=e,r[1]=n}var T1=function(){function r(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return r.prototype.increaseVersion=function(){this._version++},r.prototype.getVersion=function(){return this._version},r.prototype.setScale=function(t,e,i){i=i||0,i>0&&(this._ux=Ee(i/co/t)||0,this._uy=Ee(i/co/e)||0)},r.prototype.setDPR=function(t){this.dpr=t},r.prototype.setContext=function(t){this._ctx=t},r.prototype.getContext=function(){return this._ctx},r.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},r.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},r.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(tt.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},r.prototype.lineTo=function(t,e){var i=Ee(t-this._xi),n=Ee(e-this._yi),a=i>this._ux||n>this._uy;if(this.addData(tt.L,t,e),this._ctx&&a&&this._ctx.lineTo(t,e),a)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=i*i+n*n;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},r.prototype.bezierCurveTo=function(t,e,i,n,a,o){return this._drawPendingPt(),this.addData(tt.C,t,e,i,n,a,o),this._ctx&&this._ctx.bezierCurveTo(t,e,i,n,a,o),this._xi=a,this._yi=o,this},r.prototype.quadraticCurveTo=function(t,e,i,n){return this._drawPendingPt(),this.addData(tt.Q,t,e,i,n),this._ctx&&this._ctx.quadraticCurveTo(t,e,i,n),this._xi=i,this._yi=n,this},r.prototype.arc=function(t,e,i,n,a,o){this._drawPendingPt(),Ki[0]=n,Ki[1]=a,x1(Ki,o),n=Ki[0],a=Ki[1];var s=a-n;return this.addData(tt.A,t,e,i,i,n,s,0,o?0:1),this._ctx&&this._ctx.arc(t,e,i,n,a,o),this._xi=Mr(a)*i+t,this._yi=Ar(a)*i+e,this},r.prototype.arcTo=function(t,e,i,n,a){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,i,n,a),this},r.prototype.rect=function(t,e,i,n){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,i,n),this.addData(tt.R,t,e,i,n),this},r.prototype.closePath=function(){this._drawPendingPt(),this.addData(tt.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&t.closePath(),this._xi=e,this._yi=i,this},r.prototype.fill=function(t){t&&t.fill(),this.toStatic()},r.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},r.prototype.len=function(){return this._len},r.prototype.setData=function(t){var e=t.length;!(this.data&&this.data.length===e)&&Es&&(this.data=new Float32Array(e));for(var i=0;i<e;i++)this.data[i]=t[i];this._len=e},r.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,i=0,n=this._len,a=0;a<e;a++)i+=t[a].len();Es&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var a=0;a<e;a++)for(var o=t[a].data,s=0;s<o.length;s++)this.data[n++]=o[s];this._len=n},r.prototype.addData=function(t,e,i,n,a,o,s,l,u){if(this._saveData){var f=this.data;this._len+arguments.length>f.length&&(this._expandData(),f=this.data);for(var h=0;h<arguments.length;h++)f[this._len++]=arguments[h]}},r.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},r.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},r.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,Es&&this._len>11&&(this.data=new Float32Array(t)))}},r.prototype.getBoundingRect=function(){Se[0]=Se[1]=we[0]=we[1]=Number.MAX_VALUE,Xe[0]=Xe[1]=be[0]=be[1]=-Number.MAX_VALUE;var t=this.data,e=0,i=0,n=0,a=0,o;for(o=0;o<this._len;){var s=t[o++],l=o===1;switch(l&&(e=t[o],i=t[o+1],n=e,a=i),s){case tt.M:e=n=t[o++],i=a=t[o++],we[0]=n,we[1]=a,be[0]=n,be[1]=a;break;case tt.L:bh(e,i,t[o],t[o+1],we,be),e=t[o++],i=t[o++];break;case tt.C:S1(e,i,t[o++],t[o++],t[o++],t[o++],t[o],t[o+1],we,be),e=t[o++],i=t[o++];break;case tt.Q:w1(e,i,t[o++],t[o++],t[o],t[o+1],we,be),e=t[o++],i=t[o++];break;case tt.A:var u=t[o++],f=t[o++],h=t[o++],v=t[o++],c=t[o++],d=t[o++]+c;o+=1;var y=!t[o++];l&&(n=Mr(c)*h+u,a=Ar(c)*v+f),b1(u,f,h,v,c,d,y,we,be),e=Mr(d)*h+u,i=Ar(d)*v+f;break;case tt.R:n=e=t[o++],a=i=t[o++];var p=t[o++],g=t[o++];bh(n,a,n+p,a+g,we,be);break;case tt.Z:e=n,i=a;break}bi(Se,Se,we),xi(Xe,Xe,be)}return o===0&&(Se[0]=Se[1]=Xe[0]=Xe[1]=0),new rt(Se[0],Se[1],Xe[0]-Se[0],Xe[1]-Se[1])},r.prototype._calculateLength=function(){var t=this.data,e=this._len,i=this._ux,n=this._uy,a=0,o=0,s=0,l=0;this._pathSegLen||(this._pathSegLen=[]);for(var u=this._pathSegLen,f=0,h=0,v=0;v<e;){var c=t[v++],d=v===1;d&&(a=t[v],o=t[v+1],s=a,l=o);var y=-1;switch(c){case tt.M:a=s=t[v++],o=l=t[v++];break;case tt.L:{var p=t[v++],g=t[v++],m=p-a,_=g-o;(Ee(m)>i||Ee(_)>n||v===e-1)&&(y=Math.sqrt(m*m+_*_),a=p,o=g);break}case tt.C:{var S=t[v++],b=t[v++],p=t[v++],g=t[v++],w=t[v++],x=t[v++];y=G0(a,o,S,b,p,g,w,x,10),a=w,o=x;break}case tt.Q:{var S=t[v++],b=t[v++],p=t[v++],g=t[v++];y=U0(a,o,S,b,p,g,10),a=p,o=g;break}case tt.A:var A=t[v++],C=t[v++],D=t[v++],T=t[v++],L=t[v++],P=t[v++],I=P+L;v+=1,d&&(s=Mr(L)*D+A,l=Ar(L)*T+C),y=Rs(D,T)*Ps(ir,Math.abs(P)),a=Mr(I)*D+A,o=Ar(I)*T+C;break;case tt.R:{s=a=t[v++],l=o=t[v++];var R=t[v++],E=t[v++];y=R*2+E*2;break}case tt.Z:{var m=s-a,_=l-o;y=Math.sqrt(m*m+_*_),a=s,o=l;break}}y>=0&&(u[h++]=y,f+=y)}return this._pathLen=f,f},r.prototype.rebuildPath=function(t,e){var i=this.data,n=this._ux,a=this._uy,o=this._len,s,l,u,f,h,v,c=e<1,d,y,p=0,g=0,m,_=0,S,b;if(!(c&&(this._pathSegLen||this._calculateLength(),d=this._pathSegLen,y=this._pathLen,m=e*y,!m)))t:for(var w=0;w<o;){var x=i[w++],A=w===1;switch(A&&(u=i[w],f=i[w+1],s=u,l=f),x!==tt.L&&_>0&&(t.lineTo(S,b),_=0),x){case tt.M:s=u=i[w++],l=f=i[w++],t.moveTo(u,f);break;case tt.L:{h=i[w++],v=i[w++];var C=Ee(h-u),D=Ee(v-f);if(C>n||D>a){if(c){var T=d[g++];if(p+T>m){var L=(m-p)/T;t.lineTo(u*(1-L)+h*L,f*(1-L)+v*L);break t}p+=T}t.lineTo(h,v),u=h,f=v,_=0}else{var P=C*C+D*D;P>_&&(S=h,b=v,_=P)}break}case tt.C:{var I=i[w++],R=i[w++],E=i[w++],G=i[w++],B=i[w++],F=i[w++];if(c){var T=d[g++];if(p+T>m){var L=(m-p)/T;uo(u,I,E,B,L,Cr),uo(f,R,G,F,L,Dr),t.bezierCurveTo(Cr[1],Dr[1],Cr[2],Dr[2],Cr[3],Dr[3]);break t}p+=T}t.bezierCurveTo(I,R,E,G,B,F),u=B,f=F;break}case tt.Q:{var I=i[w++],R=i[w++],E=i[w++],G=i[w++];if(c){var T=d[g++];if(p+T>m){var L=(m-p)/T;fo(u,I,E,L,Cr),fo(f,R,G,L,Dr),t.quadraticCurveTo(Cr[1],Dr[1],Cr[2],Dr[2]);break t}p+=T}t.quadraticCurveTo(I,R,E,G),u=E,f=G;break}case tt.A:var $=i[w++],it=i[w++],J=i[w++],st=i[w++],ht=i[w++],ct=i[w++],qt=i[w++],Re=!i[w++],vt=J>st?J:st,Dt=Ee(J-st)>.001,xt=ht+ct,W=!1;if(c){var T=d[g++];p+T>m&&(xt=ht+ct*(m-p)/T,W=!0),p+=T}if(Dt&&t.ellipse?t.ellipse($,it,J,st,qt,ht,xt,Re):t.arc($,it,vt,ht,xt,Re),W)break t;A&&(s=Mr(ht)*J+$,l=Ar(ht)*st+it),u=Mr(xt)*J+$,f=Ar(xt)*st+it;break;case tt.R:s=u=i[w],l=f=i[w+1],h=i[w++],v=i[w++];var q=i[w++],dr=i[w++];if(c){var T=d[g++];if(p+T>m){var Et=m-p;t.moveTo(h,v),t.lineTo(h+Ps(Et,q),v),Et-=q,Et>0&&t.lineTo(h+q,v+Ps(Et,dr)),Et-=dr,Et>0&&t.lineTo(h+Rs(q-Et,0),v+dr),Et-=q,Et>0&&t.lineTo(h,v+Rs(dr-Et,0));break t}p+=T}t.rect(h,v,q,dr);break;case tt.Z:if(c){var T=d[g++];if(p+T>m){var L=(m-p)/T;t.lineTo(u*(1-L)+s*L,f*(1-L)+l*L);break t}p+=T}t.closePath(),u=s,f=l}}},r.prototype.clone=function(){var t=new r,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},r.CMD=tt,r.initDefaultProps=function(){var t=r.prototype;t._saveData=!0,t._ux=0,t._uy=0,t._pendingPtDist=0,t._version=0}(),r}();const Jr=T1;function ui(r,t,e,i,n,a,o){if(n===0)return!1;var s=n,l=0,u=r;if(o>t+s&&o>i+s||o<t-s&&o<i-s||a>r+s&&a>e+s||a<r-s&&a<e-s)return!1;if(r!==e)l=(t-i)/(r-e),u=(r*i-e*t)/(r-e);else return Math.abs(a-r)<=s/2;var f=l*a-o+u,h=f*f/(l*l+1);return h<=s/2*s/2}function C1(r,t,e,i,n,a,o,s,l,u,f){if(l===0)return!1;var h=l;if(f>t+h&&f>i+h&&f>a+h&&f>s+h||f<t-h&&f<i-h&&f<a-h&&f<s-h||u>r+h&&u>e+h&&u>n+h&&u>o+h||u<r-h&&u<e-h&&u<n-h&&u<o-h)return!1;var v=H0(r,t,e,i,n,a,o,s,u,f,null);return v<=h/2}function D1(r,t,e,i,n,a,o,s,l){if(o===0)return!1;var u=o;if(l>t+u&&l>i+u&&l>a+u||l<t-u&&l<i-u&&l<a-u||s>r+u&&s>e+u&&s>n+u||s<r-u&&s<e-u&&s<n-u)return!1;var f=W0(r,t,e,i,n,a,s,l,null);return f<=u/2}var Ch=Math.PI*2;function ma(r){return r%=Ch,r<0&&(r+=Ch),r}var Qi=Math.PI*2;function M1(r,t,e,i,n,a,o,s,l){if(o===0)return!1;var u=o;s-=r,l-=t;var f=Math.sqrt(s*s+l*l);if(f-u>e||f+u<e)return!1;if(Math.abs(i-n)%Qi<1e-4)return!0;if(a){var h=i;i=ma(n),n=ma(h)}else i=ma(i),n=ma(n);i>n&&(n+=Qi);var v=Math.atan2(l,s);return v<0&&(v+=Qi),v>=i&&v<=n||v+Qi>=i&&v+Qi<=n}function Lr(r,t,e,i,n,a){if(a>t&&a>i||a<t&&a<i||i===t)return 0;var o=(a-t)/(i-t),s=i<t?1:-1;(o===1||o===0)&&(s=i<t?.5:-.5);var l=o*(e-r)+r;return l===n?1/0:l>n?s:0}var Ze=Jr.CMD,Ir=Math.PI*2,A1=1e-4;function L1(r,t){return Math.abs(r-t)<A1}var Ot=[-1,-1,-1],oe=[-1,-1];function I1(){var r=oe[0];oe[0]=oe[1],oe[1]=r}function P1(r,t,e,i,n,a,o,s,l,u){if(u>t&&u>i&&u>a&&u>s||u<t&&u<i&&u<a&&u<s)return 0;var f=lo(t,i,a,s,u,Ot);if(f===0)return 0;for(var h=0,v=-1,c=void 0,d=void 0,y=0;y<f;y++){var p=Ot[y],g=p===0||p===1?.5:1,m=St(r,e,n,o,p);m<l||(v<0&&(v=Rd(t,i,a,s,oe),oe[1]<oe[0]&&v>1&&I1(),c=St(t,i,a,s,oe[0]),v>1&&(d=St(t,i,a,s,oe[1]))),v===2?p<oe[0]?h+=c<t?g:-g:p<oe[1]?h+=d<c?g:-g:h+=s<d?g:-g:p<oe[0]?h+=c<t?g:-g:h+=s<c?g:-g)}return h}function R1(r,t,e,i,n,a,o,s){if(s>t&&s>i&&s>a||s<t&&s<i&&s<a)return 0;var l=V0(t,i,a,s,Ot);if(l===0)return 0;var u=Ed(t,i,a);if(u>=0&&u<=1){for(var f=0,h=It(t,i,a,u),v=0;v<l;v++){var c=Ot[v]===0||Ot[v]===1?.5:1,d=It(r,e,n,Ot[v]);d<o||(Ot[v]<u?f+=h<t?c:-c:f+=a<h?c:-c)}return f}else{var c=Ot[0]===0||Ot[0]===1?.5:1,d=It(r,e,n,Ot[0]);return d<o?0:a<t?c:-c}}function E1(r,t,e,i,n,a,o,s){if(s-=t,s>e||s<-e)return 0;var l=Math.sqrt(e*e-s*s);Ot[0]=-l,Ot[1]=l;var u=Math.abs(i-n);if(u<1e-4)return 0;if(u>=Ir-1e-4){i=0,n=Ir;var f=a?1:-1;return o>=Ot[0]+r&&o<=Ot[1]+r?f:0}if(i>n){var h=i;i=n,n=h}i<0&&(i+=Ir,n+=Ir);for(var v=0,c=0;c<2;c++){var d=Ot[c];if(d+r>o){var y=Math.atan2(s,d),f=a?1:-1;y<0&&(y=Ir+y),(y>=i&&y<=n||y+Ir>=i&&y+Ir<=n)&&(y>Math.PI/2&&y<Math.PI*1.5&&(f=-f),v+=f)}}return v}function ap(r,t,e,i,n){for(var a=r.data,o=r.len(),s=0,l=0,u=0,f=0,h=0,v,c,d=0;d<o;){var y=a[d++],p=d===1;switch(y===Ze.M&&d>1&&(e||(s+=Lr(l,u,f,h,i,n))),p&&(l=a[d],u=a[d+1],f=l,h=u),y){case Ze.M:f=a[d++],h=a[d++],l=f,u=h;break;case Ze.L:if(e){if(ui(l,u,a[d],a[d+1],t,i,n))return!0}else s+=Lr(l,u,a[d],a[d+1],i,n)||0;l=a[d++],u=a[d++];break;case Ze.C:if(e){if(C1(l,u,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],t,i,n))return!0}else s+=P1(l,u,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],i,n)||0;l=a[d++],u=a[d++];break;case Ze.Q:if(e){if(D1(l,u,a[d++],a[d++],a[d],a[d+1],t,i,n))return!0}else s+=R1(l,u,a[d++],a[d++],a[d],a[d+1],i,n)||0;l=a[d++],u=a[d++];break;case Ze.A:var g=a[d++],m=a[d++],_=a[d++],S=a[d++],b=a[d++],w=a[d++];d+=1;var x=!!(1-a[d++]);v=Math.cos(b)*_+g,c=Math.sin(b)*S+m,p?(f=v,h=c):s+=Lr(l,u,v,c,i,n);var A=(i-g)*S/_+g;if(e){if(M1(g,m,S,b,b+w,x,t,A,n))return!0}else s+=E1(g,m,S,b,b+w,x,A,n);l=Math.cos(b+w)*_+g,u=Math.sin(b+w)*S+m;break;case Ze.R:f=l=a[d++],h=u=a[d++];var C=a[d++],D=a[d++];if(v=f+C,c=h+D,e){if(ui(f,h,v,h,t,i,n)||ui(v,h,v,c,t,i,n)||ui(v,c,f,c,t,i,n)||ui(f,c,f,h,t,i,n))return!0}else s+=Lr(v,h,v,c,i,n),s+=Lr(f,c,f,h,i,n);break;case Ze.Z:if(e){if(ui(l,u,f,h,t,i,n))return!0}else s+=Lr(l,u,f,h,i,n);l=f,u=h;break}}return!e&&!L1(u,h)&&(s+=Lr(l,u,f,h,i,n)||0),s!==0}function k1(r,t,e){return ap(r,0,!1,t,e)}function O1(r,t,e,i){return ap(r,t,!0,e,i)}var op=ot({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},Ur),B1={style:ot({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},Oo.style)},Os=On.concat(["invisible","culling","z","z2","zlevel","parent"]),N1=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.update=function(){var e=this;r.prototype.update.call(this);var i=this.style;if(i.decal){var n=this._decalEl=this._decalEl||new t;n.buildPath===t.prototype.buildPath&&(n.buildPath=function(l){e.buildPath(l,e.shape)}),n.silent=!0;var a=n.style;for(var o in i)a[o]!==i[o]&&(a[o]=i[o]);a.fill=i.fill?i.decal:null,a.decal=null,a.shadowColor=null,i.strokeFirst&&(a.stroke=null);for(var s=0;s<Os.length;++s)n[Os[s]]=this[Os[s]];n.__dirty|=jt}else this._decalEl&&(this._decalEl=null)},t.prototype.getDecalElement=function(){return this._decalEl},t.prototype._init=function(e){var i=gt(e);this.shape=this.getDefaultShape();var n=this.getDefaultStyle();n&&this.useStyle(n);for(var a=0;a<i.length;a++){var o=i[a],s=e[o];o==="style"?this.style?k(this.style,s):this.useStyle(s):o==="shape"?k(this.shape,s):r.prototype.attrKV.call(this,o,s)}this.style||this.useStyle({})},t.prototype.getDefaultStyle=function(){return null},t.prototype.getDefaultShape=function(){return{}},t.prototype.canBeInsideText=function(){return this.hasFill()},t.prototype.getInsideTextFill=function(){var e=this.style.fill;if(e!=="none"){if(z(e)){var i=ho(e,0);return i>.5?Bl:i>.2?p_:Nl}else if(e)return Nl}return Bl},t.prototype.getInsideTextStroke=function(e){var i=this.style.fill;if(z(i)){var n=this.__zr,a=!!(n&&n.isDarkMode()),o=ho(e,0)<Ol;if(a===o)return i}},t.prototype.buildPath=function(e,i,n){},t.prototype.pathUpdated=function(){this.__dirty&=~Si},t.prototype.getUpdatedPathProxy=function(e){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,e),this.path},t.prototype.createPathProxy=function(){this.path=new Jr(!1)},t.prototype.hasStroke=function(){var e=this.style,i=e.stroke;return!(i==null||i==="none"||!(e.lineWidth>0))},t.prototype.hasFill=function(){var e=this.style,i=e.fill;return i!=null&&i!=="none"},t.prototype.getBoundingRect=function(){var e=this._rect,i=this.style,n=!e;if(n){var a=!1;this.path||(a=!0,this.createPathProxy());var o=this.path;(a||this.__dirty&Si)&&(o.beginPath(),this.buildPath(o,this.shape,!1),this.pathUpdated()),e=o.getBoundingRect()}if(this._rect=e,this.hasStroke()&&this.path&&this.path.len()>0){var s=this._rectStroke||(this._rectStroke=e.clone());if(this.__dirty||n){s.copy(e);var l=i.strokeNoScale?this.getLineScale():1,u=i.lineWidth;if(!this.hasFill()){var f=this.strokeContainThreshold;u=Math.max(u,f??4)}l>1e-10&&(s.width+=u/l,s.height+=u/l,s.x-=u/l/2,s.y-=u/l/2)}return s}return e},t.prototype.contain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect(),o=this.style;if(e=n[0],i=n[1],a.contain(e,i)){var s=this.path;if(this.hasStroke()){var l=o.lineWidth,u=o.strokeNoScale?this.getLineScale():1;if(u>1e-10&&(this.hasFill()||(l=Math.max(l,this.strokeContainThreshold)),O1(s,l/u,e,i)))return!0}if(this.hasFill())return k1(s,e,i)}return!1},t.prototype.dirtyShape=function(){this.__dirty|=Si,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},t.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},t.prototype.animateShape=function(e){return this.animate("shape",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():e==="shape"?this.dirtyShape():this.markRedraw()},t.prototype.attrKV=function(e,i){e==="shape"?this.setShape(i):r.prototype.attrKV.call(this,e,i)},t.prototype.setShape=function(e,i){var n=this.shape;return n||(n=this.shape={}),typeof e=="string"?n[e]=i:k(n,e),this.dirtyShape(),this},t.prototype.shapeChanged=function(){return!!(this.__dirty&Si)},t.prototype.createStyle=function(e){return Po(op,e)},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var i=this._normalState;e.shape&&!i.shape&&(i.shape=k({},this.shape))},t.prototype._applyStateObj=function(e,i,n,a,o,s){r.prototype._applyStateObj.call(this,e,i,n,a,o,s);var l=!(i&&a),u;if(i&&i.shape?o?a?u=i.shape:(u=k({},n.shape),k(u,i.shape)):(u=k({},a?this.shape:n.shape),k(u,i.shape)):l&&(u=n.shape),u)if(o){this.shape=k({},this.shape);for(var f={},h=gt(u),v=0;v<h.length;v++){var c=h[v];typeof u[c]=="object"?this.shape[c]=u[c]:f[c]=u[c]}this._transitionState(e,{shape:f},s)}else this.shape=u,this.dirtyShape()},t.prototype._mergeStates=function(e){for(var i=r.prototype._mergeStates.call(this,e),n,a=0;a<e.length;a++){var o=e[a];o.shape&&(n=n||{},this._mergeStyle(n,o.shape))}return n&&(i.shape=n),i},t.prototype.getAnimationStyleProps=function(){return B1},t.prototype.isZeroArea=function(){return!1},t.extend=function(e){var i=function(a){O(o,a);function o(s){var l=a.call(this,s)||this;return e.init&&e.init.call(l,s),l}return o.prototype.getDefaultStyle=function(){return Q(e.style)},o.prototype.getDefaultShape=function(){return Q(e.shape)},o}(t);for(var n in e)typeof e[n]=="function"&&(i.prototype[n]=e[n]);return i},t.initDefaultProps=function(){var e=t.prototype;e.type="path",e.strokeContainThreshold=5,e.segmentIgnoreThreshold=0,e.subPixelOptimize=!1,e.autoBatch=!1,e.__dirty=jt|vn|Si}(),t}(ea);const ft=N1;var F1=ot({strokeFirst:!0,font:Zr,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},op),sp=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.hasStroke=function(){var e=this.style,i=e.stroke;return i!=null&&i!=="none"&&e.lineWidth>0},t.prototype.hasFill=function(){var e=this.style,i=e.fill;return i!=null&&i!=="none"},t.prototype.createStyle=function(e){return Po(F1,e)},t.prototype.setBoundingRect=function(e){this._rect=e},t.prototype.getBoundingRect=function(){var e=this.style;if(!this._rect){var i=e.text;i!=null?i+="":i="";var n=Ou(i,e.font,e.textAlign,e.textBaseline);if(n.x+=e.x||0,n.y+=e.y||0,this.hasStroke()){var a=e.lineWidth;n.x-=a/2,n.y-=a/2,n.width+=a,n.height+=a}this._rect=n}return this._rect},t.initDefaultProps=function(){var e=t.prototype;e.dirtyRectTolerance=10}(),t}(ea);sp.prototype.type="tspan";const Gl=sp;var z1=ot({x:0,y:0},Ur),H1={style:ot({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},Oo.style)};function G1(r){return!!(r&&typeof r!="string"&&r.width&&r.height)}var lp=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.createStyle=function(e){return Po(z1,e)},t.prototype._getSize=function(e){var i=this.style,n=i[e];if(n!=null)return n;var a=G1(i.image)?i.image:this.__image;if(!a)return 0;var o=e==="width"?"height":"width",s=i[o];return s==null?a[e]:a[e]/a[o]*s},t.prototype.getWidth=function(){return this._getSize("width")},t.prototype.getHeight=function(){return this._getSize("height")},t.prototype.getAnimationStyleProps=function(){return H1},t.prototype.getBoundingRect=function(){var e=this.style;return this._rect||(this._rect=new rt(e.x||0,e.y||0,this.getWidth(),this.getHeight())),this._rect},t}(ea);lp.prototype.type="image";const ei=lp;function V1(r,t){var e=t.x,i=t.y,n=t.width,a=t.height,o=t.r,s,l,u,f;n<0&&(e=e+n,n=-n),a<0&&(i=i+a,a=-a),typeof o=="number"?s=l=u=f=o:o instanceof Array?o.length===1?s=l=u=f=o[0]:o.length===2?(s=u=o[0],l=f=o[1]):o.length===3?(s=o[0],l=f=o[1],u=o[2]):(s=o[0],l=o[1],u=o[2],f=o[3]):s=l=u=f=0;var h;s+l>n&&(h=s+l,s*=n/h,l*=n/h),u+f>n&&(h=u+f,u*=n/h,f*=n/h),l+u>a&&(h=l+u,l*=a/h,u*=a/h),s+f>a&&(h=s+f,s*=a/h,f*=a/h),r.moveTo(e+s,i),r.lineTo(e+n-l,i),l!==0&&r.arc(e+n-l,i+l,l,-Math.PI/2,0),r.lineTo(e+n,i+a-u),u!==0&&r.arc(e+n-u,i+a-u,u,0,Math.PI/2),r.lineTo(e+f,i+a),f!==0&&r.arc(e+f,i+a-f,f,Math.PI/2,Math.PI),r.lineTo(e,i+s),s!==0&&r.arc(e+s,i+s,s,Math.PI,Math.PI*1.5)}var Ci=Math.round;function up(r,t,e){if(t){var i=t.x1,n=t.x2,a=t.y1,o=t.y2;r.x1=i,r.x2=n,r.y1=a,r.y2=o;var s=e&&e.lineWidth;return s&&(Ci(i*2)===Ci(n*2)&&(r.x1=r.x2=Gr(i,s,!0)),Ci(a*2)===Ci(o*2)&&(r.y1=r.y2=Gr(a,s,!0))),r}}function fp(r,t,e){if(t){var i=t.x,n=t.y,a=t.width,o=t.height;r.x=i,r.y=n,r.width=a,r.height=o;var s=e&&e.lineWidth;return s&&(r.x=Gr(i,s,!0),r.y=Gr(n,s,!0),r.width=Math.max(Gr(i+a,s,!1)-r.x,a===0?0:1),r.height=Math.max(Gr(n+o,s,!1)-r.y,o===0?0:1)),r}}function Gr(r,t,e){if(!t)return r;var i=Ci(r*2);return(i+Ci(t))%2===0?i/2:(i+(e?1:-1))/2}var W1=function(){function r(){this.x=0,this.y=0,this.width=0,this.height=0}return r}(),U1={},hp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new W1},t.prototype.buildPath=function(e,i){var n,a,o,s;if(this.subPixelOptimize){var l=fp(U1,i,this.style);n=l.x,a=l.y,o=l.width,s=l.height,l.r=i.r,i=l}else n=i.x,a=i.y,o=i.width,s=i.height;i.r?V1(e,i):e.rect(n,a,o,s)},t.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},t}(ft);hp.prototype.type="rect";const Ct=hp;var Dh={fill:"#000"},Mh=2,$1={style:ot({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},Oo.style)},cp=function(r){O(t,r);function t(e){var i=r.call(this)||this;return i.type="text",i._children=[],i._defaultStyle=Dh,i.attr(e),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.update=function(){r.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var i=this._children[e];i.zlevel=this.zlevel,i.z=this.z,i.z2=this.z2,i.culling=this.culling,i.cursor=this.cursor,i.invisible=this.invisible}},t.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):r.prototype.updateTransform.call(this)},t.prototype.getLocalTransform=function(e){var i=this.innerTransformable;return i?i.getLocalTransform(e):r.prototype.getLocalTransform.call(this,e)},t.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),r.prototype.getComputedTransform.call(this)},t.prototype._updateSubTexts=function(){this._childCursor=0,K1(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var i=0;i<this._children.length;i++)this._children[i].__zr=e},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var i=0;i<this._children.length;i++)this._children[i].__zr=null},t.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var e=new rt(0,0,0,0),i=this._children,n=[],a=null,o=0;o<i.length;o++){var s=i[o],l=s.getBoundingRect(),u=s.getLocalTransform(n);u?(e.copy(l),e.applyTransform(u),a=a||e.clone(),a.union(e)):(a=a||l.clone(),a.union(l))}this._rect=a||e}return this._rect},t.prototype.setDefaultTextStyle=function(e){this._defaultStyle=e||Dh},t.prototype.setTextContent=function(e){},t.prototype._mergeStyle=function(e,i){if(!i)return e;var n=i.rich,a=e.rich||n&&{};return k(e,i),n&&a?(this._mergeRich(a,n),e.rich=a):a&&(e.rich=a),e},t.prototype._mergeRich=function(e,i){for(var n=gt(i),a=0;a<n.length;a++){var o=n[a];e[o]=e[o]||{},k(e[o],i[o])}},t.prototype.getAnimationStyleProps=function(){return $1},t.prototype._getOrCreateChild=function(e){var i=this._children[this._childCursor];return(!i||!(i instanceof e))&&(i=new e),this._children[this._childCursor++]=i,i.__zr=this.__zr,i.parent=this,i},t.prototype._updatePlainTexts=function(){var e=this.style,i=e.font||Zr,n=e.padding,a=kh(e),o=f1(a,e),s=Bs(e),l=!!e.backgroundColor,u=o.outerHeight,f=o.outerWidth,h=o.contentWidth,v=o.lines,c=o.lineHeight,d=this._defaultStyle;this.isTruncated=!!o.isTruncated;var y=e.x||0,p=e.y||0,g=e.align||d.align||"left",m=e.verticalAlign||d.verticalAlign||"top",_=y,S=wi(p,o.contentHeight,m);if(s||n){var b=pn(y,f,g),w=wi(p,u,m);s&&this._renderBackground(e,e,b,w,f,u)}S+=c/2,n&&(_=Eh(y,g,n),m==="top"?S+=n[0]:m==="bottom"&&(S-=n[2]));for(var x=0,A=!1,C=Rh("fill"in e?e.fill:(A=!0,d.fill)),D=Ph("stroke"in e?e.stroke:!l&&(!d.autoStroke||A)?(x=Mh,d.stroke):null),T=e.textShadowBlur>0,L=e.width!=null&&(e.overflow==="truncate"||e.overflow==="break"||e.overflow==="breakAll"),P=o.calculatedLineHeight,I=0;I<v.length;I++){var R=this._getOrCreateChild(Gl),E=R.createStyle();R.useStyle(E),E.text=v[I],E.x=_,E.y=S,g&&(E.textAlign=g),E.textBaseline="middle",E.opacity=e.opacity,E.strokeFirst=!0,T&&(E.shadowBlur=e.textShadowBlur||0,E.shadowColor=e.textShadowColor||"transparent",E.shadowOffsetX=e.textShadowOffsetX||0,E.shadowOffsetY=e.textShadowOffsetY||0),E.stroke=D,E.fill=C,D&&(E.lineWidth=e.lineWidth||x,E.lineDash=e.lineDash,E.lineDashOffset=e.lineDashOffset||0),E.font=i,Lh(E,e),S+=c,L&&R.setBoundingRect(new rt(pn(E.x,h,E.textAlign),wi(E.y,P,E.textBaseline),h,P))}},t.prototype._updateRichTexts=function(){var e=this.style,i=kh(e),n=v1(i,e),a=n.width,o=n.outerWidth,s=n.outerHeight,l=e.padding,u=e.x||0,f=e.y||0,h=this._defaultStyle,v=e.align||h.align,c=e.verticalAlign||h.verticalAlign;this.isTruncated=!!n.isTruncated;var d=pn(u,o,v),y=wi(f,s,c),p=d,g=y;l&&(p+=l[3],g+=l[0]);var m=p+a;Bs(e)&&this._renderBackground(e,e,d,y,o,s);for(var _=!!e.backgroundColor,S=0;S<n.lines.length;S++){for(var b=n.lines[S],w=b.tokens,x=w.length,A=b.lineHeight,C=b.width,D=0,T=p,L=m,P=x-1,I=void 0;D<x&&(I=w[D],!I.align||I.align==="left");)this._placeToken(I,e,A,g,T,"left",_),C-=I.width,T+=I.width,D++;for(;P>=0&&(I=w[P],I.align==="right");)this._placeToken(I,e,A,g,L,"right",_),C-=I.width,L-=I.width,P--;for(T+=(a-(T-p)-(m-L)-C)/2;D<=P;)I=w[D],this._placeToken(I,e,A,g,T+I.width/2,"center",_),T+=I.width,D++;g+=A}},t.prototype._placeToken=function(e,i,n,a,o,s,l){var u=i.rich[e.styleName]||{};u.text=e.text;var f=e.verticalAlign,h=a+n/2;f==="top"?h=a+e.height/2:f==="bottom"&&(h=a+n-e.height/2);var v=!e.isLineHolder&&Bs(u);v&&this._renderBackground(u,i,s==="right"?o-e.width:s==="center"?o-e.width/2:o,h-e.height/2,e.width,e.height);var c=!!u.backgroundColor,d=e.textPadding;d&&(o=Eh(o,s,d),h-=e.height/2-d[0]-e.innerHeight/2);var y=this._getOrCreateChild(Gl),p=y.createStyle();y.useStyle(p);var g=this._defaultStyle,m=!1,_=0,S=Rh("fill"in u?u.fill:"fill"in i?i.fill:(m=!0,g.fill)),b=Ph("stroke"in u?u.stroke:"stroke"in i?i.stroke:!c&&!l&&(!g.autoStroke||m)?(_=Mh,g.stroke):null),w=u.textShadowBlur>0||i.textShadowBlur>0;p.text=e.text,p.x=o,p.y=h,w&&(p.shadowBlur=u.textShadowBlur||i.textShadowBlur||0,p.shadowColor=u.textShadowColor||i.textShadowColor||"transparent",p.shadowOffsetX=u.textShadowOffsetX||i.textShadowOffsetX||0,p.shadowOffsetY=u.textShadowOffsetY||i.textShadowOffsetY||0),p.textAlign=s,p.textBaseline="middle",p.font=e.font||Zr,p.opacity=Ua(u.opacity,i.opacity,1),Lh(p,u),b&&(p.lineWidth=Ua(u.lineWidth,i.lineWidth,_),p.lineDash=Z(u.lineDash,i.lineDash),p.lineDashOffset=i.lineDashOffset||0,p.stroke=b),S&&(p.fill=S);var x=e.contentWidth,A=e.contentHeight;y.setBoundingRect(new rt(pn(p.x,x,p.textAlign),wi(p.y,A,p.textBaseline),x,A))},t.prototype._renderBackground=function(e,i,n,a,o,s){var l=e.backgroundColor,u=e.borderWidth,f=e.borderColor,h=l&&l.image,v=l&&!h,c=e.borderRadius,d=this,y,p;if(v||e.lineHeight||u&&f){y=this._getOrCreateChild(Ct),y.useStyle(y.createStyle()),y.style.fill=null;var g=y.shape;g.x=n,g.y=a,g.width=o,g.height=s,g.r=c,y.dirtyShape()}if(v){var m=y.style;m.fill=l||null,m.fillOpacity=Z(e.fillOpacity,1)}else if(h){p=this._getOrCreateChild(ei),p.onload=function(){d.dirtyStyle()};var _=p.style;_.image=l.image,_.x=n,_.y=a,_.width=o,_.height=s}if(u&&f){var m=y.style;m.lineWidth=u,m.stroke=f,m.strokeOpacity=Z(e.strokeOpacity,1),m.lineDash=e.borderDash,m.lineDashOffset=e.borderDashOffset||0,y.strokeContainThreshold=0,y.hasFill()&&y.hasStroke()&&(m.strokeFirst=!0,m.lineWidth*=2)}var S=(y||p).style;S.shadowBlur=e.shadowBlur||0,S.shadowColor=e.shadowColor||"transparent",S.shadowOffsetX=e.shadowOffsetX||0,S.shadowOffsetY=e.shadowOffsetY||0,S.opacity=Ua(e.opacity,i.opacity,1)},t.makeFont=function(e){var i="";return q1(e)&&(i=[e.fontStyle,e.fontWeight,Z1(e.fontSize),e.fontFamily||"sans-serif"].join(" ")),i&&Ce(i)||e.textFont||e.font},t}(ea),Y1={left:!0,right:1,center:1},X1={top:1,bottom:1,middle:1},Ah=["fontStyle","fontWeight","fontSize","fontFamily"];function Z1(r){return typeof r=="string"&&(r.indexOf("px")!==-1||r.indexOf("rem")!==-1||r.indexOf("em")!==-1)?r:isNaN(+r)?Tu+"px":r+"px"}function Lh(r,t){for(var e=0;e<Ah.length;e++){var i=Ah[e],n=t[i];n!=null&&(r[i]=n)}}function q1(r){return r.fontSize!=null||r.fontFamily||r.fontWeight}function K1(r){return Ih(r),M(r.rich,Ih),r}function Ih(r){if(r){r.font=cp.makeFont(r);var t=r.align;t==="middle"&&(t="center"),r.align=t==null||Y1[t]?t:"left";var e=r.verticalAlign;e==="center"&&(e="middle"),r.verticalAlign=e==null||X1[e]?e:"top";var i=r.padding;i&&(r.padding=md(r.padding))}}function Ph(r,t){return r==null||t<=0||r==="transparent"||r==="none"?null:r.image||r.colorStops?"#000":r}function Rh(r){return r==null||r==="none"?null:r.image||r.colorStops?"#000":r}function Eh(r,t,e){return t==="right"?r-e[1]:t==="center"?r+e[3]/2-e[1]/2:r+e[3]}function kh(r){var t=r.text;return t!=null&&(t+=""),t}function Bs(r){return!!(r.backgroundColor||r.lineHeight||r.borderWidth&&r.borderColor)}const zt=cp;var at=wt(),Q1=function(r,t,e,i){if(i){var n=at(i);n.dataIndex=e,n.dataType=t,n.seriesIndex=r,n.ssrType="chart",i.type==="group"&&i.traverse(function(a){var o=at(a);o.seriesIndex=r,o.dataIndex=e,o.dataType=t,o.ssrType="chart"})}},Oh=1,Bh={},vp=wt(),Vu=wt(),Wu=0,Bo=1,No=2,Ae=["emphasis","blur","select"],Nh=["normal","emphasis","blur","select"],J1=10,j1=9,$r="highlight",Ja="downplay",xn="select",ja="unselect",Tn="toggleSelect";function fi(r){return r!=null&&r!=="none"}function Fo(r,t,e){r.onHoverStateChange&&(r.hoverState||0)!==e&&r.onHoverStateChange(t),r.hoverState=e}function dp(r){Fo(r,"emphasis",No)}function pp(r){r.hoverState===No&&Fo(r,"normal",Wu)}function Uu(r){Fo(r,"blur",Bo)}function gp(r){r.hoverState===Bo&&Fo(r,"normal",Wu)}function tS(r){r.selected=!0}function eS(r){r.selected=!1}function Fh(r,t,e){t(r,e)}function Ue(r,t,e){Fh(r,t,e),r.isGroup&&r.traverse(function(i){Fh(i,t,e)})}function zh(r,t){switch(t){case"emphasis":r.hoverState=No;break;case"normal":r.hoverState=Wu;break;case"blur":r.hoverState=Bo;break;case"select":r.selected=!0}}function rS(r,t,e,i){for(var n=r.style,a={},o=0;o<t.length;o++){var s=t[o],l=n[s];a[s]=l??(i&&i[s])}for(var o=0;o<r.animators.length;o++){var u=r.animators[o];u.__fromStateTransition&&u.__fromStateTransition.indexOf(e)<0&&u.targetName==="style"&&u.saveTo(a,t)}return a}function iS(r,t,e,i){var n=e&&lt(e,"select")>=0,a=!1;if(r instanceof ft){var o=vp(r),s=n&&o.selectFill||o.normalFill,l=n&&o.selectStroke||o.normalStroke;if(fi(s)||fi(l)){i=i||{};var u=i.style||{};u.fill==="inherit"?(a=!0,i=k({},i),u=k({},u),u.fill=s):!fi(u.fill)&&fi(s)?(a=!0,i=k({},i),u=k({},u),u.fill=eh(s)):!fi(u.stroke)&&fi(l)&&(a||(i=k({},i),u=k({},u)),u.stroke=eh(l)),i.style=u}}if(i&&i.z2==null){a||(i=k({},i));var f=r.z2EmphasisLift;i.z2=r.z2+(f??J1)}return i}function nS(r,t,e){if(e&&e.z2==null){e=k({},e);var i=r.z2SelectLift;e.z2=r.z2+(i??j1)}return e}function aS(r,t,e){var i=lt(r.currentStates,t)>=0,n=r.style.opacity,a=i?null:rS(r,["opacity"],t,{opacity:1});e=e||{};var o=e.style||{};return o.opacity==null&&(e=k({},e),o=k({opacity:i?n:a.opacity*.1},o),e.style=o),e}function Ns(r,t){var e=this.states[r];if(this.style){if(r==="emphasis")return iS(this,r,t,e);if(r==="blur")return aS(this,r,e);if(r==="select")return nS(this,r,e)}return e}function oS(r){r.stateProxy=Ns;var t=r.getTextContent(),e=r.getTextGuideLine();t&&(t.stateProxy=Ns),e&&(e.stateProxy=Ns)}function Hh(r,t){!Sp(r,t)&&!r.__highByOuter&&Ue(r,dp)}function Gh(r,t){!Sp(r,t)&&!r.__highByOuter&&Ue(r,pp)}function go(r,t){r.__highByOuter|=1<<(t||0),Ue(r,dp)}function yo(r,t){!(r.__highByOuter&=~(1<<(t||0)))&&Ue(r,pp)}function sS(r){Ue(r,Uu)}function yp(r){Ue(r,gp)}function mp(r){Ue(r,tS)}function _p(r){Ue(r,eS)}function Sp(r,t){return r.__highDownSilentOnTouch&&t.zrByTouch}function wp(r){var t=r.getModel(),e=[],i=[];t.eachComponent(function(n,a){var o=Vu(a),s=n==="series",l=s?r.getViewOfSeriesModel(a):r.getViewOfComponentModel(a);!s&&i.push(l),o.isBlured&&(l.group.traverse(function(u){gp(u)}),s&&e.push(a)),o.isBlured=!1}),M(i,function(n){n&&n.toggleBlurSeries&&n.toggleBlurSeries(e,!1,t)})}function Vl(r,t,e,i){var n=i.getModel();e=e||"coordinateSystem";function a(u,f){for(var h=0;h<f.length;h++){var v=u.getItemGraphicEl(f[h]);v&&yp(v)}}if(r!=null&&!(!t||t==="none")){var o=n.getSeriesByIndex(r),s=o.coordinateSystem;s&&s.master&&(s=s.master);var l=[];n.eachSeries(function(u){var f=o===u,h=u.coordinateSystem;h&&h.master&&(h=h.master);var v=h&&s?h===s:f;if(!(e==="series"&&!f||e==="coordinateSystem"&&!v||t==="series"&&f)){var c=i.getViewOfSeriesModel(u);if(c.group.traverse(function(p){p.__highByOuter&&f&&t==="self"||Uu(p)}),Yt(t))a(u.getData(),t);else if(H(t))for(var d=gt(t),y=0;y<d.length;y++)a(u.getData(d[y]),t[d[y]]);l.push(u),Vu(u).isBlured=!0}}),n.eachComponent(function(u,f){if(u!=="series"){var h=i.getViewOfComponentModel(f);h&&h.toggleBlurSeries&&h.toggleBlurSeries(l,!0,n)}})}}function Wl(r,t,e){if(!(r==null||t==null)){var i=e.getModel().getComponent(r,t);if(i){Vu(i).isBlured=!0;var n=e.getViewOfComponentModel(i);!n||!n.focusBlurEnabled||n.group.traverse(function(a){Uu(a)})}}}function lS(r,t,e){var i=r.seriesIndex,n=r.getData(t.dataType);if(n){var a=Qr(n,t);a=(N(a)?a[0]:a)||0;var o=n.getItemGraphicEl(a);if(!o)for(var s=n.count(),l=0;!o&&l<s;)o=n.getItemGraphicEl(l++);if(o){var u=at(o);Vl(i,u.focus,u.blurScope,e)}else{var f=r.get(["emphasis","focus"]),h=r.get(["emphasis","blurScope"]);f!=null&&Vl(i,f,h,e)}}}function $u(r,t,e,i){var n={focusSelf:!1,dispatchers:null};if(r==null||r==="series"||t==null||e==null)return n;var a=i.getModel().getComponent(r,t);if(!a)return n;var o=i.getViewOfComponentModel(a);if(!o||!o.findHighDownDispatchers)return n;for(var s=o.findHighDownDispatchers(e),l,u=0;u<s.length;u++)if(at(s[u]).focus==="self"){l=!0;break}return{focusSelf:l,dispatchers:s}}function uS(r,t,e){var i=at(r),n=$u(i.componentMainType,i.componentIndex,i.componentHighDownName,e),a=n.dispatchers,o=n.focusSelf;a?(o&&Wl(i.componentMainType,i.componentIndex,e),M(a,function(s){return Hh(s,t)})):(Vl(i.seriesIndex,i.focus,i.blurScope,e),i.focus==="self"&&Wl(i.componentMainType,i.componentIndex,e),Hh(r,t))}function fS(r,t,e){wp(e);var i=at(r),n=$u(i.componentMainType,i.componentIndex,i.componentHighDownName,e).dispatchers;n?M(n,function(a){return Gh(a,t)}):Gh(r,t)}function hS(r,t,e){if(Xl(t)){var i=t.dataType,n=r.getData(i),a=Qr(n,t);N(a)||(a=[a]),r[t.type===Tn?"toggleSelect":t.type===xn?"select":"unselect"](a,i)}}function Vh(r){var t=r.getAllData();M(t,function(e){var i=e.data,n=e.type;i.eachItemGraphicEl(function(a,o){r.isSelected(o,n)?mp(a):_p(a)})})}function cS(r){var t=[];return r.eachSeries(function(e){var i=e.getAllData();M(i,function(n){n.data;var a=n.type,o=e.getSelectedDataIndices();if(o.length>0){var s={dataIndex:o,seriesIndex:e.seriesIndex};a!=null&&(s.dataType=a),t.push(s)}})}),t}function Ul(r,t,e){bp(r,!0),Ue(r,oS),dS(r,t,e)}function vS(r){bp(r,!1)}function $l(r,t,e,i){i?vS(r):Ul(r,t,e)}function dS(r,t,e){var i=at(r);t!=null?(i.focus=t,i.blurScope=e):i.focus&&(i.focus=null)}var Wh=["emphasis","blur","select"],pS={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function Uh(r,t,e,i){e=e||"itemStyle";for(var n=0;n<Wh.length;n++){var a=Wh[n],o=t.getModel([a,e]),s=r.ensureState(a);s.style=i?i(o):o[pS[e]]()}}function bp(r,t){var e=t===!1,i=r;r.highDownSilentOnTouch&&(i.__highDownSilentOnTouch=r.highDownSilentOnTouch),(!e||i.__highDownDispatcher)&&(i.__highByOuter=i.__highByOuter||0,i.__highDownDispatcher=!e)}function Yl(r){return!!(r&&r.__highDownDispatcher)}function gS(r){var t=Bh[r];return t==null&&Oh<=32&&(t=Bh[r]=Oh++),t}function Xl(r){var t=r.type;return t===xn||t===ja||t===Tn}function $h(r){var t=r.type;return t===$r||t===Ja}function yS(r){var t=vp(r);t.normalFill=r.style.fill,t.normalStroke=r.style.stroke;var e=r.states.select||{};t.selectFill=e.style&&e.style.fill||null,t.selectStroke=e.style&&e.style.stroke||null}var hi=Jr.CMD,mS=[[],[],[]],Yh=Math.sqrt,_S=Math.atan2;function SS(r,t){if(t){var e=r.data,i=r.len(),n,a,o,s,l,u,f=hi.M,h=hi.C,v=hi.L,c=hi.R,d=hi.A,y=hi.Q;for(o=0,s=0;o<i;){switch(n=e[o++],s=o,a=0,n){case f:a=1;break;case v:a=1;break;case h:a=3;break;case y:a=2;break;case d:var p=t[4],g=t[5],m=Yh(t[0]*t[0]+t[1]*t[1]),_=Yh(t[2]*t[2]+t[3]*t[3]),S=_S(-t[1]/_,t[0]/m);e[o]*=m,e[o++]+=p,e[o]*=_,e[o++]+=g,e[o++]*=m,e[o++]*=_,e[o++]+=S,e[o++]+=S,o+=2,s=o;break;case c:u[0]=e[o++],u[1]=e[o++],he(u,u,t),e[s++]=u[0],e[s++]=u[1],u[0]+=e[o++],u[1]+=e[o++],he(u,u,t),e[s++]=u[0],e[s++]=u[1]}for(l=0;l<a;l++){var b=mS[l];b[0]=e[o++],b[1]=e[o++],he(b,b,t),e[s++]=b[0],e[s++]=b[1]}}r.increaseVersion()}}var Fs=Math.sqrt,_a=Math.sin,Sa=Math.cos,Ji=Math.PI;function Xh(r){return Math.sqrt(r[0]*r[0]+r[1]*r[1])}function Zl(r,t){return(r[0]*t[0]+r[1]*t[1])/(Xh(r)*Xh(t))}function Zh(r,t){return(r[0]*t[1]<r[1]*t[0]?-1:1)*Math.acos(Zl(r,t))}function qh(r,t,e,i,n,a,o,s,l,u,f){var h=l*(Ji/180),v=Sa(h)*(r-e)/2+_a(h)*(t-i)/2,c=-1*_a(h)*(r-e)/2+Sa(h)*(t-i)/2,d=v*v/(o*o)+c*c/(s*s);d>1&&(o*=Fs(d),s*=Fs(d));var y=(n===a?-1:1)*Fs((o*o*(s*s)-o*o*(c*c)-s*s*(v*v))/(o*o*(c*c)+s*s*(v*v)))||0,p=y*o*c/s,g=y*-s*v/o,m=(r+e)/2+Sa(h)*p-_a(h)*g,_=(t+i)/2+_a(h)*p+Sa(h)*g,S=Zh([1,0],[(v-p)/o,(c-g)/s]),b=[(v-p)/o,(c-g)/s],w=[(-1*v-p)/o,(-1*c-g)/s],x=Zh(b,w);if(Zl(b,w)<=-1&&(x=Ji),Zl(b,w)>=1&&(x=0),x<0){var A=Math.round(x/Ji*1e6)/1e6;x=Ji*2+A%2*Ji}f.addData(u,m,_,o,s,S,x,h,a)}var wS=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig,bS=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function xS(r){var t=new Jr;if(!r)return t;var e=0,i=0,n=e,a=i,o,s=Jr.CMD,l=r.match(wS);if(!l)return t;for(var u=0;u<l.length;u++){for(var f=l[u],h=f.charAt(0),v=void 0,c=f.match(bS)||[],d=c.length,y=0;y<d;y++)c[y]=parseFloat(c[y]);for(var p=0;p<d;){var g=void 0,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0,x=void 0,A=e,C=i,D=void 0,T=void 0;switch(h){case"l":e+=c[p++],i+=c[p++],v=s.L,t.addData(v,e,i);break;case"L":e=c[p++],i=c[p++],v=s.L,t.addData(v,e,i);break;case"m":e+=c[p++],i+=c[p++],v=s.M,t.addData(v,e,i),n=e,a=i,h="l";break;case"M":e=c[p++],i=c[p++],v=s.M,t.addData(v,e,i),n=e,a=i,h="L";break;case"h":e+=c[p++],v=s.L,t.addData(v,e,i);break;case"H":e=c[p++],v=s.L,t.addData(v,e,i);break;case"v":i+=c[p++],v=s.L,t.addData(v,e,i);break;case"V":i=c[p++],v=s.L,t.addData(v,e,i);break;case"C":v=s.C,t.addData(v,c[p++],c[p++],c[p++],c[p++],c[p++],c[p++]),e=c[p-2],i=c[p-1];break;case"c":v=s.C,t.addData(v,c[p++]+e,c[p++]+i,c[p++]+e,c[p++]+i,c[p++]+e,c[p++]+i),e+=c[p-2],i+=c[p-1];break;case"S":g=e,m=i,D=t.len(),T=t.data,o===s.C&&(g+=e-T[D-4],m+=i-T[D-3]),v=s.C,A=c[p++],C=c[p++],e=c[p++],i=c[p++],t.addData(v,g,m,A,C,e,i);break;case"s":g=e,m=i,D=t.len(),T=t.data,o===s.C&&(g+=e-T[D-4],m+=i-T[D-3]),v=s.C,A=e+c[p++],C=i+c[p++],e+=c[p++],i+=c[p++],t.addData(v,g,m,A,C,e,i);break;case"Q":A=c[p++],C=c[p++],e=c[p++],i=c[p++],v=s.Q,t.addData(v,A,C,e,i);break;case"q":A=c[p++]+e,C=c[p++]+i,e+=c[p++],i+=c[p++],v=s.Q,t.addData(v,A,C,e,i);break;case"T":g=e,m=i,D=t.len(),T=t.data,o===s.Q&&(g+=e-T[D-4],m+=i-T[D-3]),e=c[p++],i=c[p++],v=s.Q,t.addData(v,g,m,e,i);break;case"t":g=e,m=i,D=t.len(),T=t.data,o===s.Q&&(g+=e-T[D-4],m+=i-T[D-3]),e+=c[p++],i+=c[p++],v=s.Q,t.addData(v,g,m,e,i);break;case"A":_=c[p++],S=c[p++],b=c[p++],w=c[p++],x=c[p++],A=e,C=i,e=c[p++],i=c[p++],v=s.A,qh(A,C,e,i,w,x,_,S,b,v,t);break;case"a":_=c[p++],S=c[p++],b=c[p++],w=c[p++],x=c[p++],A=e,C=i,e+=c[p++],i+=c[p++],v=s.A,qh(A,C,e,i,w,x,_,S,b,v,t);break}}(h==="z"||h==="Z")&&(v=s.Z,t.addData(v),e=n,i=a),o=v}return t.toStatic(),t}var xp=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.applyTransform=function(e){},t}(ft);function Tp(r){return r.setData!=null}function Cp(r,t){var e=xS(r),i=k({},t);return i.buildPath=function(n){if(Tp(n)){n.setData(e.data);var a=n.getContext();a&&n.rebuildPath(a,1)}else{var a=n;e.rebuildPath(a,1)}},i.applyTransform=function(n){SS(e,n),this.dirtyShape()},i}function TS(r,t){return new xp(Cp(r,t))}function CS(r,t){var e=Cp(r,t),i=function(n){O(a,n);function a(o){var s=n.call(this,o)||this;return s.applyTransform=e.applyTransform,s.buildPath=e.buildPath,s}return a}(xp);return i}function DS(r,t){for(var e=[],i=r.length,n=0;n<i;n++){var a=r[n];e.push(a.getUpdatedPathProxy(!0))}var o=new ft(t);return o.createPathProxy(),o.buildPath=function(s){if(Tp(s)){s.appendPath(e);var l=s.getContext();l&&s.rebuildPath(l,1)}},o}var MS=function(){function r(){this.cx=0,this.cy=0,this.r=0}return r}(),Dp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new MS},t.prototype.buildPath=function(e,i){e.moveTo(i.cx+i.r,i.cy),e.arc(i.cx,i.cy,i.r,0,Math.PI*2)},t}(ft);Dp.prototype.type="circle";const Yu=Dp;var AS=function(){function r(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return r}(),Mp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new AS},t.prototype.buildPath=function(e,i){var n=.5522848,a=i.cx,o=i.cy,s=i.rx,l=i.ry,u=s*n,f=l*n;e.moveTo(a-s,o),e.bezierCurveTo(a-s,o-f,a-u,o-l,a,o-l),e.bezierCurveTo(a+u,o-l,a+s,o-f,a+s,o),e.bezierCurveTo(a+s,o+f,a+u,o+l,a,o+l),e.bezierCurveTo(a-u,o+l,a-s,o+f,a-s,o),e.closePath()},t}(ft);Mp.prototype.type="ellipse";const Ap=Mp;var Lp=Math.PI,zs=Lp*2,Pr=Math.sin,ci=Math.cos,LS=Math.acos,Mt=Math.atan2,Kh=Math.abs,Cn=Math.sqrt,gn=Math.max,xe=Math.min,pe=1e-4;function IS(r,t,e,i,n,a,o,s){var l=e-r,u=i-t,f=o-n,h=s-a,v=h*l-f*u;if(!(v*v<pe))return v=(f*(t-a)-h*(r-n))/v,[r+v*l,t+v*u]}function wa(r,t,e,i,n,a,o){var s=r-e,l=t-i,u=(o?a:-a)/Cn(s*s+l*l),f=u*l,h=-u*s,v=r+f,c=t+h,d=e+f,y=i+h,p=(v+d)/2,g=(c+y)/2,m=d-v,_=y-c,S=m*m+_*_,b=n-a,w=v*y-d*c,x=(_<0?-1:1)*Cn(gn(0,b*b*S-w*w)),A=(w*_-m*x)/S,C=(-w*m-_*x)/S,D=(w*_+m*x)/S,T=(-w*m+_*x)/S,L=A-p,P=C-g,I=D-p,R=T-g;return L*L+P*P>I*I+R*R&&(A=D,C=T),{cx:A,cy:C,x0:-f,y0:-h,x1:A*(n/b-1),y1:C*(n/b-1)}}function PS(r){var t;if(N(r)){var e=r.length;if(!e)return r;e===1?t=[r[0],r[0],0,0]:e===2?t=[r[0],r[0],r[1],r[1]]:e===3?t=r.concat(r[2]):t=r}else t=[r,r,r,r];return t}function RS(r,t){var e,i=gn(t.r,0),n=gn(t.r0||0,0),a=i>0,o=n>0;if(!(!a&&!o)){if(a||(i=n,n=0),n>i){var s=i;i=n,n=s}var l=t.startAngle,u=t.endAngle;if(!(isNaN(l)||isNaN(u))){var f=t.cx,h=t.cy,v=!!t.clockwise,c=Kh(u-l),d=c>zs&&c%zs;if(d>pe&&(c=d),!(i>pe))r.moveTo(f,h);else if(c>zs-pe)r.moveTo(f+i*ci(l),h+i*Pr(l)),r.arc(f,h,i,l,u,!v),n>pe&&(r.moveTo(f+n*ci(u),h+n*Pr(u)),r.arc(f,h,n,u,l,v));else{var y=void 0,p=void 0,g=void 0,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0,x=void 0,A=void 0,C=void 0,D=void 0,T=void 0,L=void 0,P=void 0,I=void 0,R=i*ci(l),E=i*Pr(l),G=n*ci(u),B=n*Pr(u),F=c>pe;if(F){var $=t.cornerRadius;$&&(e=PS($),y=e[0],p=e[1],g=e[2],m=e[3]);var it=Kh(i-n)/2;if(_=xe(it,g),S=xe(it,m),b=xe(it,y),w=xe(it,p),C=x=gn(_,S),D=A=gn(b,w),(x>pe||A>pe)&&(T=i*ci(u),L=i*Pr(u),P=n*ci(l),I=n*Pr(l),c<Lp)){var J=IS(R,E,P,I,T,L,G,B);if(J){var st=R-J[0],ht=E-J[1],ct=T-J[0],qt=L-J[1],Re=1/Pr(LS((st*ct+ht*qt)/(Cn(st*st+ht*ht)*Cn(ct*ct+qt*qt)))/2),vt=Cn(J[0]*J[0]+J[1]*J[1]);C=xe(x,(i-vt)/(Re+1)),D=xe(A,(n-vt)/(Re-1))}}}if(!F)r.moveTo(f+R,h+E);else if(C>pe){var Dt=xe(g,C),xt=xe(m,C),W=wa(P,I,R,E,i,Dt,v),q=wa(T,L,G,B,i,xt,v);r.moveTo(f+W.cx+W.x0,h+W.cy+W.y0),C<x&&Dt===xt?r.arc(f+W.cx,h+W.cy,C,Mt(W.y0,W.x0),Mt(q.y0,q.x0),!v):(Dt>0&&r.arc(f+W.cx,h+W.cy,Dt,Mt(W.y0,W.x0),Mt(W.y1,W.x1),!v),r.arc(f,h,i,Mt(W.cy+W.y1,W.cx+W.x1),Mt(q.cy+q.y1,q.cx+q.x1),!v),xt>0&&r.arc(f+q.cx,h+q.cy,xt,Mt(q.y1,q.x1),Mt(q.y0,q.x0),!v))}else r.moveTo(f+R,h+E),r.arc(f,h,i,l,u,!v);if(!(n>pe)||!F)r.lineTo(f+G,h+B);else if(D>pe){var Dt=xe(y,D),xt=xe(p,D),W=wa(G,B,T,L,n,-xt,v),q=wa(R,E,P,I,n,-Dt,v);r.lineTo(f+W.cx+W.x0,h+W.cy+W.y0),D<A&&Dt===xt?r.arc(f+W.cx,h+W.cy,D,Mt(W.y0,W.x0),Mt(q.y0,q.x0),!v):(xt>0&&r.arc(f+W.cx,h+W.cy,xt,Mt(W.y0,W.x0),Mt(W.y1,W.x1),!v),r.arc(f,h,n,Mt(W.cy+W.y1,W.cx+W.x1),Mt(q.cy+q.y1,q.cx+q.x1),v),Dt>0&&r.arc(f+q.cx,h+q.cy,Dt,Mt(q.y1,q.x1),Mt(q.y0,q.x0),!v))}else r.lineTo(f+G,h+B),r.arc(f,h,n,u,l,v)}r.closePath()}}}var ES=function(){function r(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0,this.cornerRadius=0}return r}(),Ip=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new ES},t.prototype.buildPath=function(e,i){RS(e,i)},t.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},t}(ft);Ip.prototype.type="sector";const Xu=Ip;var kS=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return r}(),Pp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new kS},t.prototype.buildPath=function(e,i){var n=i.cx,a=i.cy,o=Math.PI*2;e.moveTo(n+i.r,a),e.arc(n,a,i.r,0,o,!1),e.moveTo(n+i.r0,a),e.arc(n,a,i.r0,0,o,!0)},t}(ft);Pp.prototype.type="ring";const Rp=Pp;function OS(r,t,e,i){var n=[],a=[],o=[],s=[],l,u,f,h;if(i){f=[1/0,1/0],h=[-1/0,-1/0];for(var v=0,c=r.length;v<c;v++)bi(f,f,r[v]),xi(h,h,r[v]);bi(f,f,i[0]),xi(h,h,i[1])}for(var v=0,c=r.length;v<c;v++){var d=r[v];if(e)l=r[v?v-1:c-1],u=r[(v+1)%c];else if(v===0||v===c-1){n.push(r0(r[v]));continue}else l=r[v-1],u=r[v+1];i0(a,u,l),is(a,a,t);var y=bl(d,l),p=bl(d,u),g=y+p;g!==0&&(y/=g,p/=g),is(o,a,-y),is(s,a,p);var m=Ff([],d,o),_=Ff([],d,s);i&&(xi(m,m,f),bi(m,m,h),xi(_,_,f),bi(_,_,h)),n.push(m),n.push(_)}return e&&n.push(n.shift()),n}function Ep(r,t,e){var i=t.smooth,n=t.points;if(n&&n.length>=2){if(i){var a=OS(n,i,e,t.smoothConstraint);r.moveTo(n[0][0],n[0][1]);for(var o=n.length,s=0;s<(e?o:o-1);s++){var l=a[s*2],u=a[s*2+1],f=n[(s+1)%o];r.bezierCurveTo(l[0],l[1],u[0],u[1],f[0],f[1])}}else{r.moveTo(n[0][0],n[0][1]);for(var s=1,h=n.length;s<h;s++)r.lineTo(n[s][0],n[s][1])}e&&r.closePath()}}var BS=function(){function r(){this.points=null,this.smooth=0,this.smoothConstraint=null}return r}(),kp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new BS},t.prototype.buildPath=function(e,i){Ep(e,i,!0)},t}(ft);kp.prototype.type="polygon";const Op=kp;var NS=function(){function r(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return r}(),Bp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new NS},t.prototype.buildPath=function(e,i){Ep(e,i,!1)},t}(ft);Bp.prototype.type="polyline";const Np=Bp;var FS={},zS=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return r}(),Fp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new zS},t.prototype.buildPath=function(e,i){var n,a,o,s;if(this.subPixelOptimize){var l=up(FS,i,this.style);n=l.x1,a=l.y1,o=l.x2,s=l.y2}else n=i.x1,a=i.y1,o=i.x2,s=i.y2;var u=i.percent;u!==0&&(e.moveTo(n,a),u<1&&(o=n*(1-u)+o*u,s=a*(1-u)+s*u),e.lineTo(o,s))},t.prototype.pointAt=function(e){var i=this.shape;return[i.x1*(1-e)+i.x2*e,i.y1*(1-e)+i.y2*e]},t}(ft);Fp.prototype.type="line";const jr=Fp;var Ht=[],HS=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return r}();function Qh(r,t,e){var i=r.cpx2,n=r.cpy2;return i!=null||n!=null?[(e?qf:St)(r.x1,r.cpx1,r.cpx2,r.x2,t),(e?qf:St)(r.y1,r.cpy1,r.cpy2,r.y2,t)]:[(e?Kf:It)(r.x1,r.cpx1,r.x2,t),(e?Kf:It)(r.y1,r.cpy1,r.y2,t)]}var zp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new HS},t.prototype.buildPath=function(e,i){var n=i.x1,a=i.y1,o=i.x2,s=i.y2,l=i.cpx1,u=i.cpy1,f=i.cpx2,h=i.cpy2,v=i.percent;v!==0&&(e.moveTo(n,a),f==null||h==null?(v<1&&(fo(n,l,o,v,Ht),l=Ht[1],o=Ht[2],fo(a,u,s,v,Ht),u=Ht[1],s=Ht[2]),e.quadraticCurveTo(l,u,o,s)):(v<1&&(uo(n,l,f,o,v,Ht),l=Ht[1],f=Ht[2],o=Ht[3],uo(a,u,h,s,v,Ht),u=Ht[1],h=Ht[2],s=Ht[3]),e.bezierCurveTo(l,u,f,h,o,s)))},t.prototype.pointAt=function(e){return Qh(this.shape,e,!1)},t.prototype.tangentAt=function(e){var i=Qh(this.shape,e,!0);return o0(i,i)},t}(ft);zp.prototype.type="bezier-curve";const Hp=zp;var GS=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return r}(),Gp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new GS},t.prototype.buildPath=function(e,i){var n=i.cx,a=i.cy,o=Math.max(i.r,0),s=i.startAngle,l=i.endAngle,u=i.clockwise,f=Math.cos(s),h=Math.sin(s);e.moveTo(f*o+n,h*o+a),e.arc(n,a,o,s,l,!u)},t}(ft);Gp.prototype.type="arc";const Zu=Gp;var VS=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="compound",e}return t.prototype._updatePathDirty=function(){for(var e=this.shape.paths,i=this.shapeChanged(),n=0;n<e.length;n++)i=i||e[n].shapeChanged();i&&this.dirtyShape()},t.prototype.beforeBrush=function(){this._updatePathDirty();for(var e=this.shape.paths||[],i=this.getGlobalScale(),n=0;n<e.length;n++)e[n].path||e[n].createPathProxy(),e[n].path.setScale(i[0],i[1],e[n].segmentIgnoreThreshold)},t.prototype.buildPath=function(e,i){for(var n=i.paths||[],a=0;a<n.length;a++)n[a].buildPath(e,n[a].shape,!0)},t.prototype.afterBrush=function(){for(var e=this.shape.paths||[],i=0;i<e.length;i++)e[i].pathUpdated()},t.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),ft.prototype.getBoundingRect.call(this)},t}(ft);const WS=VS;var US=function(){function r(t){this.colorStops=t||[]}return r.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},r}();const Vp=US;var $S=function(r){O(t,r);function t(e,i,n,a,o,s){var l=r.call(this,o)||this;return l.x=e??0,l.y=i??0,l.x2=n??1,l.y2=a??0,l.type="linear",l.global=s||!1,l}return t}(Vp);const Wp=$S;var YS=function(r){O(t,r);function t(e,i,n,a,o){var s=r.call(this,a)||this;return s.x=e??.5,s.y=i??.5,s.r=n??.5,s.type="radial",s.global=o||!1,s}return t}(Vp);const XS=YS;var Rr=[0,0],Er=[0,0],ba=new ut,xa=new ut,ZS=function(){function r(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var i=0;i<4;i++)this._corners[i]=new ut;for(var i=0;i<2;i++)this._axes[i]=new ut;t&&this.fromBoundingRect(t,e)}return r.prototype.fromBoundingRect=function(t,e){var i=this._corners,n=this._axes,a=t.x,o=t.y,s=a+t.width,l=o+t.height;if(i[0].set(a,o),i[1].set(s,o),i[2].set(s,l),i[3].set(a,l),e)for(var u=0;u<4;u++)i[u].transform(e);ut.sub(n[0],i[1],i[0]),ut.sub(n[1],i[3],i[0]),n[0].normalize(),n[1].normalize();for(var u=0;u<2;u++)this._origin[u]=n[u].dot(i[0])},r.prototype.intersect=function(t,e){var i=!0,n=!e;return ba.set(1/0,1/0),xa.set(0,0),!this._intersectCheckOneSide(this,t,ba,xa,n,1)&&(i=!1,n)||!this._intersectCheckOneSide(t,this,ba,xa,n,-1)&&(i=!1,n)||n||ut.copy(e,i?ba:xa),i},r.prototype._intersectCheckOneSide=function(t,e,i,n,a,o){for(var s=!0,l=0;l<2;l++){var u=this._axes[l];if(this._getProjMinMaxOnAxis(l,t._corners,Rr),this._getProjMinMaxOnAxis(l,e._corners,Er),Rr[1]<Er[0]||Rr[0]>Er[1]){if(s=!1,a)return s;var f=Math.abs(Er[0]-Rr[1]),h=Math.abs(Rr[0]-Er[1]);Math.min(f,h)>n.len()&&(f<h?ut.scale(n,u,-f*o):ut.scale(n,u,h*o))}else if(i){var f=Math.abs(Er[0]-Rr[1]),h=Math.abs(Rr[0]-Er[1]);Math.min(f,h)<i.len()&&(f<h?ut.scale(i,u,f*o):ut.scale(i,u,-h*o))}}return s},r.prototype._getProjMinMaxOnAxis=function(t,e,i){for(var n=this._axes[t],a=this._origin,o=e[0].dot(n)+a[t],s=o,l=o,u=1;u<e.length;u++){var f=e[u].dot(n)+a[t];s=Math.min(f,s),l=Math.max(f,l)}i[0]=s,i[1]=l},r}();const mo=ZS;var qS=[],KS=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return t.prototype.traverse=function(e,i){e.call(i,this)},t.prototype.useStyle=function(){this.style={}},t.prototype.getCursor=function(){return this._cursor},t.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},t.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},t.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},t.prototype.addDisplayable=function(e,i){i?this._temporaryDisplayables.push(e):this._displayables.push(e),this.markRedraw()},t.prototype.addDisplayables=function(e,i){i=i||!1;for(var n=0;n<e.length;n++)this.addDisplayable(e[n],i)},t.prototype.getDisplayables=function(){return this._displayables},t.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},t.prototype.eachPendingDisplayable=function(e){for(var i=this._cursor;i<this._displayables.length;i++)e&&e(this._displayables[i]);for(var i=0;i<this._temporaryDisplayables.length;i++)e&&e(this._temporaryDisplayables[i])},t.prototype.update=function(){this.updateTransform();for(var e=this._cursor;e<this._displayables.length;e++){var i=this._displayables[e];i.parent=this,i.update(),i.parent=null}for(var e=0;e<this._temporaryDisplayables.length;e++){var i=this._temporaryDisplayables[e];i.parent=this,i.update(),i.parent=null}},t.prototype.getBoundingRect=function(){if(!this._rect){for(var e=new rt(1/0,1/0,-1/0,-1/0),i=0;i<this._displayables.length;i++){var n=this._displayables[i],a=n.getBoundingRect().clone();n.needLocalTransform()&&a.applyTransform(n.getLocalTransform(qS)),e.union(a)}this._rect=e}return this._rect},t.prototype.contain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect();if(a.contain(n[0],n[1]))for(var o=0;o<this._displayables.length;o++){var s=this._displayables[o];if(s.contain(e,i))return!0}return!1},t}(ea);const QS=KS;var JS=wt();function jS(r,t,e,i,n){var a;if(t&&t.ecModel){var o=t.ecModel.getUpdatePayload();a=o&&o.animation}var s=t&&t.isAnimationEnabled(),l=r==="update";if(s){var u=void 0,f=void 0,h=void 0;i?(u=Z(i.duration,200),f=Z(i.easing,"cubicOut"),h=0):(u=t.getShallow(l?"animationDurationUpdate":"animationDuration"),f=t.getShallow(l?"animationEasingUpdate":"animationEasing"),h=t.getShallow(l?"animationDelayUpdate":"animationDelay")),a&&(a.duration!=null&&(u=a.duration),a.easing!=null&&(f=a.easing),a.delay!=null&&(h=a.delay)),U(h)&&(h=h(e,n)),U(u)&&(u=u(e));var v={duration:u||0,delay:h,easing:f};return v}else return null}function qu(r,t,e,i,n,a,o){var s=!1,l;U(n)?(o=a,a=n,n=null):H(n)&&(a=n.cb,o=n.during,s=n.isFrom,l=n.removeOpt,n=n.dataIndex);var u=r==="leave";u||t.stopAnimation("leave");var f=jS(r,i,n,u?l||{}:null,i&&i.getAnimationDelayParams?i.getAnimationDelayParams(t,n):null);if(f&&f.duration>0){var h=f.duration,v=f.delay,c=f.easing,d={duration:h,delay:v||0,easing:c,done:a,force:!!a||!!o,setToFinal:!u,scope:r,during:o};s?t.animateFrom(e,d):t.animateTo(e,d)}else t.stopAnimation(),!s&&t.attr(e),o&&o(1),a&&a()}function hr(r,t,e,i,n,a){qu("update",r,t,e,i,n,a)}function ra(r,t,e,i,n,a){qu("enter",r,t,e,i,n,a)}function Dn(r){if(!r.__zr)return!0;for(var t=0;t<r.animators.length;t++){var e=r.animators[t];if(e.scope==="leave")return!0}return!1}function _o(r,t,e,i,n,a){Dn(r)||qu("leave",r,t,e,i,n,a)}function Jh(r,t,e,i){r.removeTextContent(),r.removeTextGuideLine(),_o(r,{style:{opacity:0}},t,e,i)}function tw(r,t,e){function i(){r.parent&&r.parent.remove(r)}r.isGroup?r.traverse(function(n){n.isGroup||Jh(n,t,e,i)}):Jh(r,t,e,i)}function ew(r){JS(r).oldStyle=r.style}var So=Math.max,wo=Math.min,ql={};function rw(r){return ft.extend(r)}var iw=CS;function nw(r,t){return iw(r,t)}function _e(r,t){ql[r]=t}function aw(r){if(ql.hasOwnProperty(r))return ql[r]}function Ku(r,t,e,i){var n=TS(r,t);return e&&(i==="center"&&(e=$p(e,n.getBoundingRect())),Yp(n,e)),n}function Up(r,t,e){var i=new ei({style:{image:r,x:t.x,y:t.y,width:t.width,height:t.height},onload:function(n){if(e==="center"){var a={width:n.width,height:n.height};i.setStyle($p(t,a))}}});return i}function $p(r,t){var e=t.width/t.height,i=r.height*e,n;i<=r.width?n=r.height:(i=r.width,n=i/e);var a=r.x+r.width/2,o=r.y+r.height/2;return{x:a-i/2,y:o-n/2,width:i,height:n}}var ow=DS;function Yp(r,t){if(r.applyTransform){var e=r.getBoundingRect(),i=e.calculateTransform(t);r.applyTransform(i)}}function Fn(r,t){return up(r,r,{lineWidth:t}),r}function sw(r){return fp(r.shape,r.shape,r.style),r}var lw=Gr;function uw(r,t){for(var e=Iu([]);r&&r!==t;)Li(e,r.getLocalTransform(),e),r=r.parent;return e}function Qu(r,t,e){return t&&!Yt(t)&&(t=ku.getLocalTransform(t)),e&&(t=Ru([],t)),he([],r,t)}function fw(r,t,e){var i=t[4]===0||t[5]===0||t[0]===0?1:Math.abs(2*t[4]/t[0]),n=t[4]===0||t[5]===0||t[2]===0?1:Math.abs(2*t[4]/t[2]),a=[r==="left"?-i:r==="right"?i:0,r==="top"?-n:r==="bottom"?n:0];return a=Qu(a,t,e),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"}function jh(r){return!r.isGroup}function hw(r){return r.shape!=null}function Xp(r,t,e){if(!r||!t)return;function i(o){var s={};return o.traverse(function(l){jh(l)&&l.anid&&(s[l.anid]=l)}),s}function n(o){var s={x:o.x,y:o.y,rotation:o.rotation};return hw(o)&&(s.shape=k({},o.shape)),s}var a=i(r);t.traverse(function(o){if(jh(o)&&o.anid){var s=a[o.anid];if(s){var l=n(o);o.attr(n(s)),hr(o,l,e,at(o).dataIndex)}}})}function cw(r,t){return V(r,function(e){var i=e[0];i=So(i,t.x),i=wo(i,t.x+t.width);var n=e[1];return n=So(n,t.y),n=wo(n,t.y+t.height),[i,n]})}function vw(r,t){var e=So(r.x,t.x),i=wo(r.x+r.width,t.x+t.width),n=So(r.y,t.y),a=wo(r.y+r.height,t.y+t.height);if(i>=e&&a>=n)return{x:e,y:n,width:i-e,height:a-n}}function Ju(r,t,e){var i=k({rectHover:!0},t),n=i.style={strokeNoScale:!0};if(e=e||{x:-1,y:-1,width:2,height:2},r)return r.indexOf("image://")===0?(n.image=r.slice(8),ot(n,e),new ei(i)):Ku(r.replace("path://",""),i,e,"center")}function dw(r,t,e,i,n){for(var a=0,o=n[n.length-1];a<n.length;a++){var s=n[a];if(Zp(r,t,e,i,s[0],s[1],o[0],o[1]))return!0;o=s}}function Zp(r,t,e,i,n,a,o,s){var l=e-r,u=i-t,f=o-n,h=s-a,v=Hs(f,h,l,u);if(pw(v))return!1;var c=r-n,d=t-a,y=Hs(c,d,l,u)/v;if(y<0||y>1)return!1;var p=Hs(c,d,f,h)/v;return!(p<0||p>1)}function Hs(r,t,e,i){return r*i-e*t}function pw(r){return r<=1e-6&&r>=-1e-6}function zo(r){var t=r.itemTooltipOption,e=r.componentModel,i=r.itemName,n=z(t)?{formatter:t}:t,a=e.mainType,o=e.componentIndex,s={componentType:a,name:i,$vars:["name"]};s[a+"Index"]=o;var l=r.formatterParamsExtra;l&&M(gt(l),function(f){qr(s,f)||(s[f]=l[f],s.$vars.push(f))});var u=at(r.el);u.componentMainType=a,u.componentIndex=o,u.tooltipConfig={name:i,option:ot({content:i,encodeHTMLContent:!0,formatterParams:s},n)}}function tc(r,t){var e;r.isGroup&&(e=t(r)),e||r.traverse(t)}function ju(r,t){if(r)if(N(r))for(var e=0;e<r.length;e++)tc(r[e],t);else tc(r,t)}_e("circle",Yu);_e("ellipse",Ap);_e("sector",Xu);_e("ring",Rp);_e("polygon",Op);_e("polyline",Np);_e("rect",Ct);_e("line",jr);_e("bezierCurve",Hp);_e("arc",Zu);const gw=Object.freeze(Object.defineProperty({__proto__:null,Arc:Zu,BezierCurve:Hp,BoundingRect:rt,Circle:Yu,CompoundPath:WS,Ellipse:Ap,Group:Ft,Image:ei,IncrementalDisplayable:QS,Line:jr,LinearGradient:Wp,OrientedBoundingRect:mo,Path:ft,Point:ut,Polygon:Op,Polyline:Np,RadialGradient:XS,Rect:Ct,Ring:Rp,Sector:Xu,Text:zt,applyTransform:Qu,clipPointsByRect:cw,clipRectByRect:vw,createIcon:Ju,extendPath:nw,extendShape:rw,getShapeClass:aw,getTransform:uw,groupTransition:Xp,initProps:ra,isElementRemoved:Dn,lineLineIntersect:Zp,linePolygonIntersect:dw,makeImage:Up,makePath:Ku,mergePath:ow,registerShape:_e,removeElement:_o,removeElementWithFadeOut:tw,resizePath:Yp,setTooltipConfig:zo,subPixelOptimize:lw,subPixelOptimizeLine:Fn,subPixelOptimizeRect:sw,transformDirection:fw,traverseElements:ju,updateProps:hr},Symbol.toStringTag,{value:"Module"}));var Ho={};function yw(r,t){for(var e=0;e<Ae.length;e++){var i=Ae[e],n=t[i],a=r.ensureState(i);a.style=a.style||{},a.style.text=n}var o=r.currentStates.slice();r.clearStates(!0),r.setStyle({text:t.normal}),r.useStates(o,!0)}function ec(r,t,e){var i=r.labelFetcher,n=r.labelDataIndex,a=r.labelDimIndex,o=t.normal,s;i&&(s=i.getFormattedLabel(n,"normal",null,a,o&&o.get("formatter"),e!=null?{interpolatedValue:e}:null)),s==null&&(s=U(r.defaultText)?r.defaultText(n,r,e):r.defaultText);for(var l={normal:s},u=0;u<Ae.length;u++){var f=Ae[u],h=t[f];l[f]=Z(i?i.getFormattedLabel(n,f,null,a,h&&h.get("formatter")):null,s)}return l}function tf(r,t,e,i){e=e||Ho;for(var n=r instanceof zt,a=!1,o=0;o<Nh.length;o++){var s=t[Nh[o]];if(s&&s.getShallow("show")){a=!0;break}}var l=n?r:r.getTextContent();if(a){n||(l||(l=new zt,r.setTextContent(l)),r.stateProxy&&(l.stateProxy=r.stateProxy));var u=ec(e,t),f=t.normal,h=!!f.getShallow("show"),v=cr(f,i&&i.normal,e,!1,!n);v.text=u.normal,n||r.setTextConfig(rc(f,e,!1));for(var o=0;o<Ae.length;o++){var c=Ae[o],s=t[c];if(s){var d=l.ensureState(c),y=!!Z(s.getShallow("show"),h);if(y!==h&&(d.ignore=!y),d.style=cr(s,i&&i[c],e,!0,!n),d.style.text=u[c],!n){var p=r.ensureState(c);p.textConfig=rc(s,e,!0)}}}l.silent=!!f.getShallow("silent"),l.style.x!=null&&(v.x=l.style.x),l.style.y!=null&&(v.y=l.style.y),l.ignore=!h,l.useStyle(v),l.dirty(),e.enableTextSetter&&(qp(l).setLabelText=function(g){var m=ec(e,t,g);yw(l,m)})}else l&&(l.ignore=!0);r.dirty()}function ef(r,t){t=t||"label";for(var e={normal:r.getModel(t)},i=0;i<Ae.length;i++){var n=Ae[i];e[n]=r.getModel([n,t])}return e}function cr(r,t,e,i,n){var a={};return mw(a,r,e,i,n),t&&k(a,t),a}function rc(r,t,e){t=t||{};var i={},n,a=r.getShallow("rotate"),o=Z(r.getShallow("distance"),e?null:5),s=r.getShallow("offset");return n=r.getShallow("position")||(e?null:"inside"),n==="outside"&&(n=t.defaultOutsidePosition||"top"),n!=null&&(i.position=n),s!=null&&(i.offset=s),a!=null&&(a*=Math.PI/180,i.rotation=a),o!=null&&(i.distance=o),i.outsideFill=r.get("color")==="inherit"?t.inheritColor||null:"auto",i}function mw(r,t,e,i,n){e=e||Ho;var a=t.ecModel,o=a&&a.option.textStyle,s=_w(t),l;if(s){l={};for(var u in s)if(s.hasOwnProperty(u)){var f=t.getModel(["rich",u]);oc(l[u]={},f,o,e,i,n,!1,!0)}}l&&(r.rich=l);var h=t.get("overflow");h&&(r.overflow=h);var v=t.get("minMargin");v!=null&&(r.margin=v),oc(r,t,o,e,i,n,!0,!1)}function _w(r){for(var t;r&&r!==r.ecModel;){var e=(r.option||Ho).rich;if(e){t=t||{};for(var i=gt(e),n=0;n<i.length;n++){var a=i[n];t[a]=1}}r=r.parentModel}return t}var ic=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],nc=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],ac=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function oc(r,t,e,i,n,a,o,s){e=!n&&e||Ho;var l=i&&i.inheritColor,u=t.getShallow("color"),f=t.getShallow("textBorderColor"),h=Z(t.getShallow("opacity"),e.opacity);(u==="inherit"||u==="auto")&&(l?u=l:u=null),(f==="inherit"||f==="auto")&&(l?f=l:f=null),a||(u=u||e.color,f=f||e.textBorderColor),u!=null&&(r.fill=u),f!=null&&(r.stroke=f);var v=Z(t.getShallow("textBorderWidth"),e.textBorderWidth);v!=null&&(r.lineWidth=v);var c=Z(t.getShallow("textBorderType"),e.textBorderType);c!=null&&(r.lineDash=c);var d=Z(t.getShallow("textBorderDashOffset"),e.textBorderDashOffset);d!=null&&(r.lineDashOffset=d),!n&&h==null&&!s&&(h=i&&i.defaultOpacity),h!=null&&(r.opacity=h),!n&&!a&&r.fill==null&&i.inheritColor&&(r.fill=i.inheritColor);for(var y=0;y<ic.length;y++){var p=ic[y],g=Z(t.getShallow(p),e[p]);g!=null&&(r[p]=g)}for(var y=0;y<nc.length;y++){var p=nc[y],g=t.getShallow(p);g!=null&&(r[p]=g)}if(r.verticalAlign==null){var m=t.getShallow("baseline");m!=null&&(r.verticalAlign=m)}if(!o||!i.disableBox){for(var y=0;y<ac.length;y++){var p=ac[y],g=t.getShallow(p);g!=null&&(r[p]=g)}var _=t.getShallow("borderType");_!=null&&(r.borderDash=_),(r.backgroundColor==="auto"||r.backgroundColor==="inherit")&&l&&(r.backgroundColor=l),(r.borderColor==="auto"||r.borderColor==="inherit")&&l&&(r.borderColor=l)}}function Sw(r,t){var e=t&&t.getModel("textStyle");return Ce([r.fontStyle||e&&e.getShallow("fontStyle")||"",r.fontWeight||e&&e.getShallow("fontWeight")||"",(r.fontSize||e&&e.getShallow("fontSize")||12)+"px",r.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "))}var qp=wt(),ww=["textStyle","color"],Gs=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],Vs=new zt,bw=function(){function r(){}return r.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(ww):null)},r.prototype.getFont=function(){return Sw({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},r.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},i=0;i<Gs.length;i++)e[Gs[i]]=this.getShallow(Gs[i]);return Vs.useStyle(e),Vs.update(),Vs.getBoundingRect()},r}();const xw=bw;var Kp=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],Tw=Nn(Kp),Cw=function(){function r(){}return r.prototype.getLineStyle=function(t){return Tw(this,t)},r}(),Qp=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],Dw=Nn(Qp),Mw=function(){function r(){}return r.prototype.getItemStyle=function(t,e){return Dw(this,t,e)},r}(),ri=function(){function r(t,e,i){this.parentModel=e,this.ecModel=i,this.option=t}return r.prototype.init=function(t,e,i){},r.prototype.mergeOption=function(t,e){et(this.option,t,!0)},r.prototype.get=function(t,e){return t==null?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},r.prototype.getShallow=function(t,e){var i=this.option,n=i==null?i:i[t];if(n==null&&!e){var a=this.parentModel;a&&(n=a.getShallow(t))}return n},r.prototype.getModel=function(t,e){var i=t!=null,n=i?this.parsePath(t):null,a=i?this._doGet(n):this.option;return e=e||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(n)),new r(a,e,this.ecModel)},r.prototype.isEmpty=function(){return this.option==null},r.prototype.restoreData=function(){},r.prototype.clone=function(){var t=this.constructor;return new t(Q(this.option))},r.prototype.parsePath=function(t){return typeof t=="string"?t.split("."):t},r.prototype.resolveParentPath=function(t){return t},r.prototype.isAnimationEnabled=function(){if(!Y.node&&this.option){if(this.option.animation!=null)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},r.prototype._doGet=function(t,e){var i=this.option;if(!t)return i;for(var n=0;n<t.length&&!(t[n]&&(i=i&&typeof i=="object"?i[t[n]]:null,i==null));n++);return i==null&&e&&(i=e._doGet(this.resolveParentPath(t),e.parentModel)),i},r}();Gu(ri);e1(ri);Le(ri,Cw);Le(ri,Mw);Le(ri,o1);Le(ri,xw);const Rt=ri;var Aw=Math.round(Math.random()*10);function Go(r){return[r||"",Aw++].join("_")}function Lw(r){var t={};r.registerSubTypeDefaulter=function(e,i){var n=De(e);t[n.main]=i},r.determineSubType=function(e,i){var n=i.type;if(!n){var a=De(e).main;r.hasSubTypes(e)&&t[a]&&(n=t[a](i))}return n}}function Iw(r,t){r.topologicalTravel=function(a,o,s,l){if(!a.length)return;var u=e(o),f=u.graph,h=u.noEntryList,v={};for(M(a,function(m){v[m]=!0});h.length;){var c=h.pop(),d=f[c],y=!!v[c];y&&(s.call(l,c,d.originalDeps.slice()),delete v[c]),M(d.successor,y?g:p)}M(v,function(){var m="";throw new Error(m)});function p(m){f[m].entryCount--,f[m].entryCount===0&&h.push(m)}function g(m){v[m]=!0,p(m)}};function e(a){var o={},s=[];return M(a,function(l){var u=i(o,l),f=u.originalDeps=t(l),h=n(f,a);u.entryCount=h.length,u.entryCount===0&&s.push(l),M(h,function(v){lt(u.predecessor,v)<0&&u.predecessor.push(v);var c=i(o,v);lt(c.successor,v)<0&&c.successor.push(l)})}),{graph:o,noEntryList:s}}function i(a,o){return a[o]||(a[o]={predecessor:[],successor:[]}),a[o]}function n(a,o){var s=[];return M(a,function(l){lt(o,l)>=0&&s.push(l)}),s}}function Pw(r,t){return et(et({},r,!0),t,!0)}const Rw={time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}},Ew={time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}};var bo="ZH",rf="EN",Pi=rf,to={},nf={},Jp=Y.domSupported?function(){var r=(document.documentElement.lang||navigator.language||navigator.browserLanguage||Pi).toUpperCase();return r.indexOf(bo)>-1?bo:Pi}():Pi;function jp(r,t){r=r.toUpperCase(),nf[r]=new Rt(t),to[r]=t}function kw(r){if(z(r)){var t=to[r.toUpperCase()]||{};return r===bo||r===rf?Q(t):et(Q(t),Q(to[Pi]),!1)}else return et(Q(r),Q(to[Pi]),!1)}function Ow(r){return nf[r]}function Bw(){return nf[Pi]}jp(rf,Rw);jp(bo,Ew);var af=1e3,of=af*60,Mn=of*60,fe=Mn*24,sc=fe*365,yn={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},Ta="{yyyy}-{MM}-{dd}",lc={year:"{yyyy}",month:"{yyyy}-{MM}",day:Ta,hour:Ta+" "+yn.hour,minute:Ta+" "+yn.minute,second:Ta+" "+yn.second,millisecond:yn.none},Ws=["year","month","day","hour","minute","second","millisecond"],tg=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function qe(r,t){return r+="","0000".substr(0,t-r.length)+r}function Ri(r){switch(r){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return r}}function Nw(r){return r===Ri(r)}function Fw(r){switch(r){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}function Vo(r,t,e,i){var n=Ve(r),a=n[sf(e)](),o=n[Ei(e)]()+1,s=Math.floor((o-1)/3)+1,l=n[Wo(e)](),u=n["get"+(e?"UTC":"")+"Day"](),f=n[zn(e)](),h=(f-1)%12+1,v=n[Uo(e)](),c=n[$o(e)](),d=n[Yo(e)](),y=f>=12?"pm":"am",p=y.toUpperCase(),g=i instanceof Rt?i:Ow(i||Jp)||Bw(),m=g.getModel("time"),_=m.get("month"),S=m.get("monthAbbr"),b=m.get("dayOfWeek"),w=m.get("dayOfWeekAbbr");return(t||"").replace(/{a}/g,y+"").replace(/{A}/g,p+"").replace(/{yyyy}/g,a+"").replace(/{yy}/g,qe(a%100+"",2)).replace(/{Q}/g,s+"").replace(/{MMMM}/g,_[o-1]).replace(/{MMM}/g,S[o-1]).replace(/{MM}/g,qe(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,qe(l,2)).replace(/{d}/g,l+"").replace(/{eeee}/g,b[u]).replace(/{ee}/g,w[u]).replace(/{e}/g,u+"").replace(/{HH}/g,qe(f,2)).replace(/{H}/g,f+"").replace(/{hh}/g,qe(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,qe(v,2)).replace(/{m}/g,v+"").replace(/{ss}/g,qe(c,2)).replace(/{s}/g,c+"").replace(/{SSS}/g,qe(d,3)).replace(/{S}/g,d+"")}function zw(r,t,e,i,n){var a=null;if(z(e))a=e;else if(U(e))a=e(r.value,t,{level:r.level});else{var o=k({},yn);if(r.level>0)for(var s=0;s<Ws.length;++s)o[Ws[s]]="{primary|"+o[Ws[s]]+"}";var l=e?e.inherit===!1?e:ot(e,o):o,u=eg(r.value,n);if(l[u])a=l[u];else if(l.inherit){for(var f=tg.indexOf(u),s=f-1;s>=0;--s)if(l[u]){a=l[u];break}a=a||o.none}if(N(a)){var h=r.level==null?0:r.level>=0?r.level:a.length+r.level;h=Math.min(h,a.length-1),a=a[h]}}return Vo(new Date(r.value),a,n,i)}function eg(r,t){var e=Ve(r),i=e[Ei(t)]()+1,n=e[Wo(t)](),a=e[zn(t)](),o=e[Uo(t)](),s=e[$o(t)](),l=e[Yo(t)](),u=l===0,f=u&&s===0,h=f&&o===0,v=h&&a===0,c=v&&n===1,d=c&&i===1;return d?"year":c?"month":v?"day":h?"hour":f?"minute":u?"second":"millisecond"}function uc(r,t,e){var i=yt(r)?Ve(r):r;switch(t=t||eg(r,e),t){case"year":return i[sf(e)]();case"half-year":return i[Ei(e)]()>=6?1:0;case"quarter":return Math.floor((i[Ei(e)]()+1)/4);case"month":return i[Ei(e)]();case"day":return i[Wo(e)]();case"half-day":return i[zn(e)]()/24;case"hour":return i[zn(e)]();case"minute":return i[Uo(e)]();case"second":return i[$o(e)]();case"millisecond":return i[Yo(e)]()}}function sf(r){return r?"getUTCFullYear":"getFullYear"}function Ei(r){return r?"getUTCMonth":"getMonth"}function Wo(r){return r?"getUTCDate":"getDate"}function zn(r){return r?"getUTCHours":"getHours"}function Uo(r){return r?"getUTCMinutes":"getMinutes"}function $o(r){return r?"getUTCSeconds":"getSeconds"}function Yo(r){return r?"getUTCMilliseconds":"getMilliseconds"}function Hw(r){return r?"setUTCFullYear":"setFullYear"}function rg(r){return r?"setUTCMonth":"setMonth"}function ig(r){return r?"setUTCDate":"setDate"}function ng(r){return r?"setUTCHours":"setHours"}function ag(r){return r?"setUTCMinutes":"setMinutes"}function og(r){return r?"setUTCSeconds":"setSeconds"}function sg(r){return r?"setUTCMilliseconds":"setMilliseconds"}function lg(r){if(!k_(r))return z(r)?r:"-";var t=(r+"").split(".");return t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:"")}function ug(r,t){return r=(r||"").toLowerCase().replace(/-(.)/g,function(e,i){return i.toUpperCase()}),t&&r&&(r=r.charAt(0).toUpperCase()+r.slice(1)),r}var Xo=md;function Kl(r,t,e){var i="{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}";function n(f){return f&&Ce(f)?f:"-"}function a(f){return!!(f!=null&&!isNaN(f)&&isFinite(f))}var o=t==="time",s=r instanceof Date;if(o||s){var l=o?Ve(r):r;if(isNaN(+l)){if(s)return"-"}else return Vo(l,i,e)}if(t==="ordinal")return Sl(r)?n(r):yt(r)&&a(r)?r+"":"-";var u=po(r);return a(u)?lg(u):Sl(r)?n(r):typeof r=="boolean"?r+"":"-"}var fc=["a","b","c","d","e","f","g"],Us=function(r,t){return"{"+r+(t??"")+"}"};function fg(r,t,e){N(t)||(t=[t]);var i=t.length;if(!i)return"";for(var n=t[0].$vars||[],a=0;a<n.length;a++){var o=fc[a];r=r.replace(Us(o),Us(o,0))}for(var s=0;s<i;s++)for(var l=0;l<n.length;l++){var u=t[s][n[l]];r=r.replace(Us(fc[l],s),e?Vt(u):u)}return r}function Gw(r,t){var e=z(r)?{color:r,extraCssText:t}:r||{},i=e.color,n=e.type;t=e.extraCssText;var a=e.renderMode||"html";if(!i)return"";if(a==="html")return n==="subItem"?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Vt(i)+";"+(t||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+Vt(i)+";"+(t||"")+'"></span>';var o=e.markerId||"markerX";return{renderMode:a,content:"{"+o+"|}  ",style:n==="subItem"?{width:4,height:4,borderRadius:2,backgroundColor:i}:{width:10,height:10,borderRadius:5,backgroundColor:i}}}function ti(r,t){return t=t||"transparent",z(r)?r:H(r)&&r.colorStops&&(r.colorStops[0]||{}).color||t}function hc(r,t){if(t==="_blank"||t==="blank"){var e=window.open();e.opener=null,e.location.href=r}else window.open(r,t)}var eo=M,Vw=["left","right","top","bottom","width","height"],Ca=[["width","left","right"],["height","top","bottom"]];function lf(r,t,e,i,n){var a=0,o=0;i==null&&(i=1/0),n==null&&(n=1/0);var s=0;t.eachChild(function(l,u){var f=l.getBoundingRect(),h=t.childAt(u+1),v=h&&h.getBoundingRect(),c,d;if(r==="horizontal"){var y=f.width+(v?-v.x+f.x:0);c=a+y,c>i||l.newline?(a=0,c=y,o+=s+e,s=f.height):s=Math.max(s,f.height)}else{var p=f.height+(v?-v.y+f.y:0);d=o+p,d>n||l.newline?(a+=s+e,o=0,d=p,s=f.width):s=Math.max(s,f.width)}l.newline||(l.x=a,l.y=o,l.markRedraw(),r==="horizontal"?a=c+e:o=d+e)})}var An=lf;bt(lf,"vertical");bt(lf,"horizontal");function Hn(r,t,e){e=Xo(e||0);var i=t.width,n=t.height,a=Bt(r.left,i),o=Bt(r.top,n),s=Bt(r.right,i),l=Bt(r.bottom,n),u=Bt(r.width,i),f=Bt(r.height,n),h=e[2]+e[0],v=e[1]+e[3],c=r.aspect;switch(isNaN(u)&&(u=i-s-v-a),isNaN(f)&&(f=n-l-h-o),c!=null&&(isNaN(u)&&isNaN(f)&&(c>i/n?u=i*.8:f=n*.8),isNaN(u)&&(u=c*f),isNaN(f)&&(f=u/c)),isNaN(a)&&(a=i-s-u-v),isNaN(o)&&(o=n-l-f-h),r.left||r.right){case"center":a=i/2-u/2-e[3];break;case"right":a=i-u-v;break}switch(r.top||r.bottom){case"middle":case"center":o=n/2-f/2-e[0];break;case"bottom":o=n-f-h;break}a=a||0,o=o||0,isNaN(u)&&(u=i-v-a-(s||0)),isNaN(f)&&(f=n-h-o-(l||0));var d=new rt(a+e[3],o+e[0],u,f);return d.margin=e,d}function Gn(r){var t=r.layoutMode||r.constructor.layoutMode;return H(t)?t:t?{type:t}:null}function ki(r,t,e){var i=e&&e.ignoreSize;!N(i)&&(i=[i,i]);var n=o(Ca[0],0),a=o(Ca[1],1);u(Ca[0],r,n),u(Ca[1],r,a);function o(f,h){var v={},c=0,d={},y=0,p=2;if(eo(f,function(_){d[_]=r[_]}),eo(f,function(_){s(t,_)&&(v[_]=d[_]=t[_]),l(v,_)&&c++,l(d,_)&&y++}),i[h])return l(t,f[1])?d[f[2]]=null:l(t,f[2])&&(d[f[1]]=null),d;if(y===p||!c)return d;if(c>=p)return v;for(var g=0;g<f.length;g++){var m=f[g];if(!s(v,m)&&s(r,m)){v[m]=r[m];break}}return v}function s(f,h){return f.hasOwnProperty(h)}function l(f,h){return f[h]!=null&&f[h]!=="auto"}function u(f,h,v){eo(f,function(c){h[c]=v[c]})}}function Zo(r){return Ww({},r)}function Ww(r,t){return t&&r&&eo(Vw,function(e){t.hasOwnProperty(e)&&(r[e]=t[e])}),r}var Uw=wt(),Hi=function(r){O(t,r);function t(e,i,n){var a=r.call(this,e,i,n)||this;return a.uid=Go("ec_cpt_model"),a}return t.prototype.init=function(e,i,n){this.mergeDefaultAndTheme(e,n)},t.prototype.mergeDefaultAndTheme=function(e,i){var n=Gn(this),a=n?Zo(e):{},o=i.getTheme();et(e,o.get(this.mainType)),et(e,this.getDefaultOption()),n&&ki(e,a,n)},t.prototype.mergeOption=function(e,i){et(this.option,e,!0);var n=Gn(this);n&&ki(this.option,e,n)},t.prototype.optionUpdated=function(e,i){},t.prototype.getDefaultOption=function(){var e=this.constructor;if(!J_(e))return e.defaultOption;var i=Uw(this);if(!i.defaultOption){for(var n=[],a=e;a;){var o=a.prototype.defaultOption;o&&n.push(o),a=a.superClass}for(var s={},l=n.length-1;l>=0;l--)s=et(s,n[l],!0);i.defaultOption=s}return i.defaultOption},t.prototype.getReferringComponents=function(e,i){var n=e+"Index",a=e+"Id";return ta(this.ecModel,e,{index:this.get(n,!0),id:this.get(a,!0)},i)},t.prototype.getBoxLayoutParams=function(){var e=this;return{left:e.get("left"),top:e.get("top"),right:e.get("right"),bottom:e.get("bottom"),width:e.get("width"),height:e.get("height")}},t.prototype.getZLevelKey=function(){return""},t.prototype.setZLevel=function(e){this.option.zlevel=e},t.protoInitialize=function(){var e=t.prototype;e.type="component",e.id="",e.name="",e.mainType="",e.subType="",e.componentIndex=0}(),t}(Rt);tp(Hi,Rt);Eo(Hi);Lw(Hi);Iw(Hi,$w);function $w(r){var t=[];return M(Hi.getClassesByMainType(r),function(e){t=t.concat(e.dependencies||e.prototype.dependencies||[])}),t=V(t,function(e){return De(e).main}),r!=="dataset"&&lt(t,"dataset")<=0&&t.unshift("dataset"),t}const dt=Hi;var hg="";typeof navigator<"u"&&(hg=navigator.platform||"");var vi="rgba(0, 0, 0, 0.2)";const Yw={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:vi,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:vi,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:vi,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:vi,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:vi,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:vi,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:hg.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1};var cg=X(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),ce="original",Zt="arrayRows",Pe="objectRows",$e="keyedColumns",ur="typedArray",vg="unknown",He="column",Gi="row",Jt={Must:1,Might:2,Not:3},dg=wt();function Xw(r){dg(r).datasetMap=X()}function Zw(r,t,e){var i={},n=pg(t);if(!n||!r)return i;var a=[],o=[],s=t.ecModel,l=dg(s).datasetMap,u=n.uid+"_"+e.seriesLayoutBy,f,h;r=r.slice(),M(r,function(y,p){var g=H(y)?y:r[p]={name:y};g.type==="ordinal"&&f==null&&(f=p,h=d(g)),i[g.name]=[]});var v=l.get(u)||l.set(u,{categoryWayDim:h,valueWayDim:0});M(r,function(y,p){var g=y.name,m=d(y);if(f==null){var _=v.valueWayDim;c(i[g],_,m),c(o,_,m),v.valueWayDim+=m}else if(f===p)c(i[g],0,m),c(a,0,m);else{var _=v.categoryWayDim;c(i[g],_,m),c(o,_,m),v.categoryWayDim+=m}});function c(y,p,g){for(var m=0;m<g;m++)y.push(p+m)}function d(y){var p=y.dimsDef;return p?p.length:1}return a.length&&(i.itemName=a),o.length&&(i.seriesName=o),i}function pg(r){var t=r.get("data",!0);if(!t)return ta(r.ecModel,"dataset",{index:r.get("datasetIndex",!0),id:r.get("datasetId",!0)},me).models[0]}function qw(r){return!r.get("transform",!0)&&!r.get("fromTransformResult",!0)?[]:ta(r.ecModel,"dataset",{index:r.get("fromDatasetIndex",!0),id:r.get("fromDatasetId",!0)},me).models}function gg(r,t){return Kw(r.data,r.sourceFormat,r.seriesLayoutBy,r.dimensionsDefine,r.startIndex,t)}function Kw(r,t,e,i,n,a){var o,s=5;if(Xt(r))return Jt.Not;var l,u;if(i){var f=i[a];H(f)?(l=f.name,u=f.type):z(f)&&(l=f)}if(u!=null)return u==="ordinal"?Jt.Must:Jt.Not;if(t===Zt){var h=r;if(e===Gi){for(var v=h[a],c=0;c<(v||[]).length&&c<s;c++)if((o=S(v[n+c]))!=null)return o}else for(var c=0;c<h.length&&c<s;c++){var d=h[n+c];if(d&&(o=S(d[a]))!=null)return o}}else if(t===Pe){var y=r;if(!l)return Jt.Not;for(var c=0;c<y.length&&c<s;c++){var p=y[c];if(p&&(o=S(p[l]))!=null)return o}}else if(t===$e){var g=r;if(!l)return Jt.Not;var v=g[l];if(!v||Xt(v))return Jt.Not;for(var c=0;c<v.length&&c<s;c++)if((o=S(v[c]))!=null)return o}else if(t===ce)for(var m=r,c=0;c<m.length&&c<s;c++){var p=m[c],_=jn(p);if(!N(_))return Jt.Not;if((o=S(_[a]))!=null)return o}function S(b){var w=z(b);if(b!=null&&Number.isFinite(Number(b))&&b!=="")return w?Jt.Might:Jt.Not;if(w&&b!=="-")return Jt.Must}return Jt.Not}var Qw=X();function Jw(r,t,e){var i=Qw.get(t);if(!i)return e;var n=i(r);return n?e.concat(n):e}var cc=wt();wt();var uf=function(){function r(){}return r.prototype.getColorFromPalette=function(t,e,i){var n=Nt(this.get("color",!0)),a=this.get("colorLayer",!0);return tb(this,cc,n,a,t,e,i)},r.prototype.clearColorPalette=function(){eb(this,cc)},r}();function jw(r,t){for(var e=r.length,i=0;i<e;i++)if(r[i].length>t)return r[i];return r[e-1]}function tb(r,t,e,i,n,a,o){a=a||r;var s=t(a),l=s.paletteIdx||0,u=s.paletteNameMap=s.paletteNameMap||{};if(u.hasOwnProperty(n))return u[n];var f=o==null||!i?e:jw(i,o);if(f=f||e,!(!f||!f.length)){var h=f[l];return n&&(u[n]=h),s.paletteIdx=(l+1)%f.length,h}}function eb(r,t){t(r).paletteIdx=0,t(r).paletteNameMap={}}var Da,ji,vc,dc="\0_ec_inner",rb=1,yg=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.init=function(e,i,n,a,o,s){a=a||{},this.option=null,this._theme=new Rt(a),this._locale=new Rt(o),this._optionManager=s},t.prototype.setOption=function(e,i,n){var a=yc(i);this._optionManager.setOption(e,n,a),this._resetOption(null,a)},t.prototype.resetOption=function(e,i){return this._resetOption(e,yc(i))},t.prototype._resetOption=function(e,i){var n=!1,a=this._optionManager;if(!e||e==="recreate"){var o=a.mountOption(e==="recreate");!this.option||e==="recreate"?vc(this,o):(this.restoreData(),this._mergeOption(o,i)),n=!0}if((e==="timeline"||e==="media")&&this.restoreData(),!e||e==="recreate"||e==="timeline"){var s=a.getTimelineOption(this);s&&(n=!0,this._mergeOption(s,i))}if(!e||e==="recreate"||e==="media"){var l=a.getMediaOption(this);l.length&&M(l,function(u){n=!0,this._mergeOption(u,i)},this)}return n},t.prototype.mergeOption=function(e){this._mergeOption(e,null)},t.prototype._mergeOption=function(e,i){var n=this.option,a=this._componentsMap,o=this._componentsCount,s=[],l=X(),u=i&&i.replaceMergeMainTypeMap;Xw(this),M(e,function(h,v){h!=null&&(dt.hasClass(v)?v&&(s.push(v),l.set(v,!0)):n[v]=n[v]==null?Q(h):et(n[v],h,!0))}),u&&u.each(function(h,v){dt.hasClass(v)&&!l.get(v)&&(s.push(v),l.set(v,!0))}),dt.topologicalTravel(s,dt.getAllClassMainTypes(),f,this);function f(h){var v=Jw(this,h,Nt(e[h])),c=a.get(h),d=c?u&&u.get(h)?"replaceMerge":"normalMerge":"replaceAll",y=N_(c,v,d);U_(y,h,dt),n[h]=null,a.set(h,null),o.set(h,0);var p=[],g=[],m=0,_;M(y,function(S,b){var w=S.existing,x=S.newOption;if(!x)w&&(w.mergeOption({},this),w.optionUpdated({},!1));else{var A=h==="series",C=dt.getClass(h,S.keyInfo.subType,!A);if(!C)return;if(h==="tooltip"){if(_)return;_=!0}if(w&&w.constructor===C)w.name=S.keyInfo.name,w.mergeOption(x,this),w.optionUpdated(x,!1);else{var D=k({componentIndex:b},S.keyInfo);w=new C(x,this,this,D),k(w,D),S.brandNew&&(w.__requireNewView=!0),w.init(x,this,this),w.optionUpdated(null,!0)}}w?(p.push(w.option),g.push(w),m++):(p.push(void 0),g.push(void 0))},this),n[h]=p,a.set(h,g),o.set(h,m),h==="series"&&Da(this)}this._seriesIndices||Da(this)},t.prototype.getOption=function(){var e=Q(this.option);return M(e,function(i,n){if(dt.hasClass(n)){for(var a=Nt(i),o=a.length,s=!1,l=o-1;l>=0;l--)a[l]&&!Bn(a[l])?s=!0:(a[l]=null,!s&&o--);a.length=o,e[n]=a}}),delete e[dc],e},t.prototype.getTheme=function(){return this._theme},t.prototype.getLocaleModel=function(){return this._locale},t.prototype.setUpdatePayload=function(e){this._payload=e},t.prototype.getUpdatePayload=function(){return this._payload},t.prototype.getComponent=function(e,i){var n=this._componentsMap.get(e);if(n){var a=n[i||0];if(a)return a;if(i==null){for(var o=0;o<n.length;o++)if(n[o])return n[o]}}},t.prototype.queryComponents=function(e){var i=e.mainType;if(!i)return[];var n=e.index,a=e.id,o=e.name,s=this._componentsMap.get(i);if(!s||!s.length)return[];var l;return n!=null?(l=[],M(Nt(n),function(u){s[u]&&l.push(s[u])})):a!=null?l=pc("id",a,s):o!=null?l=pc("name",o,s):l=Tt(s,function(u){return!!u}),gc(l,e)},t.prototype.findComponents=function(e){var i=e.query,n=e.mainType,a=s(i),o=a?this.queryComponents(a):Tt(this._componentsMap.get(n),function(u){return!!u});return l(gc(o,e));function s(u){var f=n+"Index",h=n+"Id",v=n+"Name";return u&&(u[f]!=null||u[h]!=null||u[v]!=null)?{mainType:n,index:u[f],id:u[h],name:u[v]}:null}function l(u){return e.filter?Tt(u,e.filter):u}},t.prototype.eachComponent=function(e,i,n){var a=this._componentsMap;if(U(e)){var o=i,s=e;a.each(function(h,v){for(var c=0;h&&c<h.length;c++){var d=h[c];d&&s.call(o,v,d,d.componentIndex)}})}else for(var l=z(e)?a.get(e):H(e)?this.findComponents(e):null,u=0;l&&u<l.length;u++){var f=l[u];f&&i.call(n,f,f.componentIndex)}},t.prototype.getSeriesByName=function(e){var i=Me(e,null);return Tt(this._componentsMap.get("series"),function(n){return!!n&&i!=null&&n.name===i})},t.prototype.getSeriesByIndex=function(e){return this._componentsMap.get("series")[e]},t.prototype.getSeriesByType=function(e){return Tt(this._componentsMap.get("series"),function(i){return!!i&&i.subType===e})},t.prototype.getSeries=function(){return Tt(this._componentsMap.get("series"),function(e){return!!e})},t.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},t.prototype.eachSeries=function(e,i){ji(this),M(this._seriesIndices,function(n){var a=this._componentsMap.get("series")[n];e.call(i,a,n)},this)},t.prototype.eachRawSeries=function(e,i){M(this._componentsMap.get("series"),function(n){n&&e.call(i,n,n.componentIndex)})},t.prototype.eachSeriesByType=function(e,i,n){ji(this),M(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];o.subType===e&&i.call(n,o,a)},this)},t.prototype.eachRawSeriesByType=function(e,i,n){return M(this.getSeriesByType(e),i,n)},t.prototype.isSeriesFiltered=function(e){return ji(this),this._seriesIndicesMap.get(e.componentIndex)==null},t.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},t.prototype.filterSeries=function(e,i){ji(this);var n=[];M(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];e.call(i,o,a)&&n.push(a)},this),this._seriesIndices=n,this._seriesIndicesMap=X(n)},t.prototype.restoreData=function(e){Da(this);var i=this._componentsMap,n=[];i.each(function(a,o){dt.hasClass(o)&&n.push(o)}),dt.topologicalTravel(n,dt.getAllClassMainTypes(),function(a){M(i.get(a),function(o){o&&(a!=="series"||!ib(o,e))&&o.restoreData()})})},t.internalField=function(){Da=function(e){var i=e._seriesIndices=[];M(e._componentsMap.get("series"),function(n){n&&i.push(n.componentIndex)}),e._seriesIndicesMap=X(i)},ji=function(e){},vc=function(e,i){e.option={},e.option[dc]=rb,e._componentsMap=X({series:[]}),e._componentsCount=X();var n=i.aria;H(n)&&n.enabled==null&&(n.enabled=!0),nb(i,e._theme.option),et(i,Yw,!1),e._mergeOption(i,null)}}(),t}(Rt);function ib(r,t){if(t){var e=t.seriesIndex,i=t.seriesId,n=t.seriesName;return e!=null&&r.componentIndex!==e||i!=null&&r.id!==i||n!=null&&r.name!==n}}function nb(r,t){var e=r.color&&!r.colorLayer;M(t,function(i,n){n==="colorLayer"&&e||dt.hasClass(n)||(typeof i=="object"?r[n]=r[n]?et(r[n],i,!1):Q(i):r[n]==null&&(r[n]=i))})}function pc(r,t,e){if(N(t)){var i=X();return M(t,function(a){if(a!=null){var o=Me(a,null);o!=null&&i.set(a,!0)}}),Tt(e,function(a){return a&&i.get(a[r])})}else{var n=Me(t,null);return Tt(e,function(a){return a&&n!=null&&a[r]===n})}}function gc(r,t){return t.hasOwnProperty("subType")?Tt(r,function(e){return e&&e.subType===t.subType}):r}function yc(r){var t=X();return r&&M(Nt(r.replaceMerge),function(e){t.set(e,!0)}),{replaceMergeMainTypeMap:t}}Le(yg,uf);const mg=yg;var ab=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],ob=function(){function r(t){M(ab,function(e){this[e]=pt(t[e],t)},this)}return r}();const _g=ob;var $s={},sb=function(){function r(){this._coordinateSystems=[]}return r.prototype.create=function(t,e){var i=[];M($s,function(n,a){var o=n.create(t,e);i=i.concat(o||[])}),this._coordinateSystems=i},r.prototype.update=function(t,e){M(this._coordinateSystems,function(i){i.update&&i.update(t,e)})},r.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},r.register=function(t,e){$s[t]=e},r.get=function(t){return $s[t]},r}();const ff=sb;var lb=/^(min|max)?(.+)$/,ub=function(){function r(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return r.prototype.setOption=function(t,e,i){t&&(M(Nt(t.series),function(o){o&&o.data&&Xt(o.data)&&wl(o.data)}),M(Nt(t.dataset),function(o){o&&o.source&&Xt(o.source)&&wl(o.source)})),t=Q(t);var n=this._optionBackup,a=fb(t,e,!n);this._newBaseOption=a.baseOption,n?(a.timelineOptions.length&&(n.timelineOptions=a.timelineOptions),a.mediaList.length&&(n.mediaList=a.mediaList),a.mediaDefault&&(n.mediaDefault=a.mediaDefault)):this._optionBackup=a},r.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],Q(t?e.baseOption:this._newBaseOption)},r.prototype.getTimelineOption=function(t){var e,i=this._timelineOptions;if(i.length){var n=t.getComponent("timeline");n&&(e=Q(i[n.getCurrentIndex()]))}return e},r.prototype.getMediaOption=function(t){var e=this._api.getWidth(),i=this._api.getHeight(),n=this._mediaList,a=this._mediaDefault,o=[],s=[];if(!n.length&&!a)return s;for(var l=0,u=n.length;l<u;l++)hb(n[l].query,e,i)&&o.push(l);return!o.length&&a&&(o=[-1]),o.length&&!vb(o,this._currentMediaIndices)&&(s=V(o,function(f){return Q(f===-1?a.option:n[f].option)})),this._currentMediaIndices=o,s},r}();function fb(r,t,e){var i=[],n,a,o=r.baseOption,s=r.timeline,l=r.options,u=r.media,f=!!r.media,h=!!(l||s||o&&o.timeline);o?(a=o,a.timeline||(a.timeline=s)):((h||f)&&(r.options=r.media=null),a=r),f&&N(u)&&M(u,function(c){c&&c.option&&(c.query?i.push(c):n||(n=c))}),v(a),M(l,function(c){return v(c)}),M(i,function(c){return v(c.option)});function v(c){M(t,function(d){d(c,e)})}return{baseOption:a,timelineOptions:l||[],mediaDefault:n,mediaList:i}}function hb(r,t,e){var i={width:t,height:e,aspectratio:t/e},n=!0;return M(r,function(a,o){var s=o.match(lb);if(!(!s||!s[1]||!s[2])){var l=s[1],u=s[2].toLowerCase();cb(i[u],a,l)||(n=!1)}}),n}function cb(r,t,e){return e==="min"?r>=t:e==="max"?r<=t:r===t}function vb(r,t){return r.join(",")===t.join(",")}const db=ub;var ve=M,Vn=H,mc=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function Ys(r){var t=r&&r.itemStyle;if(t)for(var e=0,i=mc.length;e<i;e++){var n=mc[e],a=t.normal,o=t.emphasis;a&&a[n]&&(r[n]=r[n]||{},r[n].normal?et(r[n].normal,a[n]):r[n].normal=a[n],a[n]=null),o&&o[n]&&(r[n]=r[n]||{},r[n].emphasis?et(r[n].emphasis,o[n]):r[n].emphasis=o[n],o[n]=null)}}function Pt(r,t,e){if(r&&r[t]&&(r[t].normal||r[t].emphasis)){var i=r[t].normal,n=r[t].emphasis;i&&(e?(r[t].normal=r[t].emphasis=null,ot(r[t],i)):r[t]=i),n&&(r.emphasis=r.emphasis||{},r.emphasis[t]=n,n.focus&&(r.emphasis.focus=n.focus),n.blurScope&&(r.emphasis.blurScope=n.blurScope))}}function mn(r){Pt(r,"itemStyle"),Pt(r,"lineStyle"),Pt(r,"areaStyle"),Pt(r,"label"),Pt(r,"labelLine"),Pt(r,"upperLabel"),Pt(r,"edgeLabel")}function mt(r,t){var e=Vn(r)&&r[t],i=Vn(e)&&e.textStyle;if(i)for(var n=0,a=mh.length;n<a;n++){var o=mh[n];i.hasOwnProperty(o)&&(e[o]=i[o])}}function ae(r){r&&(mn(r),mt(r,"label"),r.emphasis&&mt(r.emphasis,"label"))}function pb(r){if(Vn(r)){Ys(r),mn(r),mt(r,"label"),mt(r,"upperLabel"),mt(r,"edgeLabel"),r.emphasis&&(mt(r.emphasis,"label"),mt(r.emphasis,"upperLabel"),mt(r.emphasis,"edgeLabel"));var t=r.markPoint;t&&(Ys(t),ae(t));var e=r.markLine;e&&(Ys(e),ae(e));var i=r.markArea;i&&ae(i);var n=r.data;if(r.type==="graph"){n=n||r.nodes;var a=r.links||r.edges;if(a&&!Xt(a))for(var o=0;o<a.length;o++)ae(a[o]);M(r.categories,function(u){mn(u)})}if(n&&!Xt(n))for(var o=0;o<n.length;o++)ae(n[o]);if(t=r.markPoint,t&&t.data)for(var s=t.data,o=0;o<s.length;o++)ae(s[o]);if(e=r.markLine,e&&e.data)for(var l=e.data,o=0;o<l.length;o++)N(l[o])?(ae(l[o][0]),ae(l[o][1])):ae(l[o]);r.type==="gauge"?(mt(r,"axisLabel"),mt(r,"title"),mt(r,"detail")):r.type==="treemap"?(Pt(r.breadcrumb,"itemStyle"),M(r.levels,function(u){mn(u)})):r.type==="tree"&&mn(r.leaves)}}function ke(r){return N(r)?r:r?[r]:[]}function _c(r){return(N(r)?r[0]:r)||{}}function gb(r,t){ve(ke(r.series),function(i){Vn(i)&&pb(i)});var e=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&e.push("valueAxis","categoryAxis","logAxis","timeAxis"),ve(e,function(i){ve(ke(r[i]),function(n){n&&(mt(n,"axisLabel"),mt(n.axisPointer,"label"))})}),ve(ke(r.parallel),function(i){var n=i&&i.parallelAxisDefault;mt(n,"axisLabel"),mt(n&&n.axisPointer,"label")}),ve(ke(r.calendar),function(i){Pt(i,"itemStyle"),mt(i,"dayLabel"),mt(i,"monthLabel"),mt(i,"yearLabel")}),ve(ke(r.radar),function(i){mt(i,"name"),i.name&&i.axisName==null&&(i.axisName=i.name,delete i.name),i.nameGap!=null&&i.axisNameGap==null&&(i.axisNameGap=i.nameGap,delete i.nameGap)}),ve(ke(r.geo),function(i){Vn(i)&&(ae(i),ve(ke(i.regions),function(n){ae(n)}))}),ve(ke(r.timeline),function(i){ae(i),Pt(i,"label"),Pt(i,"itemStyle"),Pt(i,"controlStyle",!0);var n=i.data;N(n)&&M(n,function(a){H(a)&&(Pt(a,"label"),Pt(a,"itemStyle"))})}),ve(ke(r.toolbox),function(i){Pt(i,"iconStyle"),ve(i.feature,function(n){Pt(n,"iconStyle")})}),mt(_c(r.axisPointer),"label"),mt(_c(r.tooltip).axisPointer,"label")}function yb(r,t){for(var e=t.split(","),i=r,n=0;n<e.length&&(i=i&&i[e[n]],i!=null);n++);return i}function mb(r,t,e,i){for(var n=t.split(","),a=r,o,s=0;s<n.length-1;s++)o=n[s],a[o]==null&&(a[o]={}),a=a[o];(i||a[n[s]]==null)&&(a[n[s]]=e)}function Sc(r){r&&M(_b,function(t){t[0]in r&&!(t[1]in r)&&(r[t[1]]=r[t[0]])})}var _b=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],Sb=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Xs=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function tn(r){var t=r&&r.itemStyle;if(t)for(var e=0;e<Xs.length;e++){var i=Xs[e][1],n=Xs[e][0];t[i]!=null&&(t[n]=t[i])}}function wc(r){r&&r.alignTo==="edge"&&r.margin!=null&&r.edgeDistance==null&&(r.edgeDistance=r.margin)}function bc(r){r&&r.downplay&&!r.blur&&(r.blur=r.downplay)}function wb(r){r&&r.focusNodeAdjacency!=null&&(r.emphasis=r.emphasis||{},r.emphasis.focus==null&&(r.emphasis.focus="adjacency"))}function Sg(r,t){if(r)for(var e=0;e<r.length;e++)t(r[e]),r[e]&&Sg(r[e].children,t)}function wg(r,t){gb(r,t),r.series=Nt(r.series),M(r.series,function(e){if(H(e)){var i=e.type;if(i==="line")e.clipOverflow!=null&&(e.clip=e.clipOverflow);else if(i==="pie"||i==="gauge"){e.clockWise!=null&&(e.clockwise=e.clockWise),wc(e.label);var n=e.data;if(n&&!Xt(n))for(var a=0;a<n.length;a++)wc(n[a]);e.hoverOffset!=null&&(e.emphasis=e.emphasis||{},(e.emphasis.scaleSize=null)&&(e.emphasis.scaleSize=e.hoverOffset))}else if(i==="gauge"){var o=yb(e,"pointer.color");o!=null&&mb(e,"itemStyle.color",o)}else if(i==="bar"){tn(e),tn(e.backgroundStyle),tn(e.emphasis);var n=e.data;if(n&&!Xt(n))for(var a=0;a<n.length;a++)typeof n[a]=="object"&&(tn(n[a]),tn(n[a]&&n[a].emphasis))}else if(i==="sunburst"){var s=e.highlightPolicy;s&&(e.emphasis=e.emphasis||{},e.emphasis.focus||(e.emphasis.focus=s)),bc(e),Sg(e.data,bc)}else i==="graph"||i==="sankey"?wb(e):i==="map"&&(e.mapType&&!e.map&&(e.map=e.mapType),e.mapLocation&&ot(e,e.mapLocation));e.hoverAnimation!=null&&(e.emphasis=e.emphasis||{},e.emphasis&&e.emphasis.scale==null&&(e.emphasis.scale=e.hoverAnimation)),Sc(e)}}),r.dataRange&&(r.visualMap=r.dataRange),M(Sb,function(e){var i=r[e];i&&(N(i)||(i=[i]),M(i,function(n){Sc(n)}))})}function bb(r){var t=X();r.eachSeries(function(e){var i=e.get("stack");if(i){var n=t.get(i)||t.set(i,[]),a=e.getData(),o={stackResultDimension:a.getCalculationInfo("stackResultDimension"),stackedOverDimension:a.getCalculationInfo("stackedOverDimension"),stackedDimension:a.getCalculationInfo("stackedDimension"),stackedByDimension:a.getCalculationInfo("stackedByDimension"),isStackedByIndex:a.getCalculationInfo("isStackedByIndex"),data:a,seriesModel:e};if(!o.stackedDimension||!(o.isStackedByIndex||o.stackedByDimension))return;n.length&&a.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(o)}}),t.each(xb)}function xb(r){M(r,function(t,e){var i=[],n=[NaN,NaN],a=[t.stackResultDimension,t.stackedOverDimension],o=t.data,s=t.isStackedByIndex,l=t.seriesModel.get("stackStrategy")||"samesign";o.modify(a,function(u,f,h){var v=o.get(t.stackedDimension,h);if(isNaN(v))return n;var c,d;s?d=o.getRawIndex(h):c=o.get(t.stackedByDimension,h);for(var y=NaN,p=e-1;p>=0;p--){var g=r[p];if(s||(d=g.data.rawIndexOf(g.stackedByDimension,c)),d>=0){var m=g.data.getByRawIndex(g.stackResultDimension,d);if(l==="all"||l==="positive"&&m>0||l==="negative"&&m<0||l==="samesign"&&v>=0&&m>0||l==="samesign"&&v<=0&&m<0){v=P_(v,m),y=m;break}}}return i[0]=v,i[1]=y,i})})}var qo=function(){function r(t){this.data=t.data||(t.sourceFormat===$e?{}:[]),this.sourceFormat=t.sourceFormat||vg,this.seriesLayoutBy=t.seriesLayoutBy||He,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var i=0;i<e.length;i++){var n=e[i];n.type==null&&gg(this,i)===Jt.Must&&(n.type="ordinal")}}return r}();function hf(r){return r instanceof qo}function Ql(r,t,e){e=e||bg(r);var i=t.seriesLayoutBy,n=Cb(r,e,i,t.sourceHeader,t.dimensions),a=new qo({data:r,sourceFormat:e,seriesLayoutBy:i,dimensionsDefine:n.dimensionsDefine,startIndex:n.startIndex,dimensionsDetectedCount:n.dimensionsDetectedCount,metaRawOption:Q(t)});return a}function cf(r){return new qo({data:r,sourceFormat:Xt(r)?ur:ce})}function Tb(r){return new qo({data:r.data,sourceFormat:r.sourceFormat,seriesLayoutBy:r.seriesLayoutBy,dimensionsDefine:Q(r.dimensionsDefine),startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount})}function bg(r){var t=vg;if(Xt(r))t=ur;else if(N(r)){r.length===0&&(t=Zt);for(var e=0,i=r.length;e<i;e++){var n=r[e];if(n!=null){if(N(n)||Xt(n)){t=Zt;break}else if(H(n)){t=Pe;break}}}}else if(H(r)){for(var a in r)if(qr(r,a)&&Yt(r[a])){t=$e;break}}return t}function Cb(r,t,e,i,n){var a,o;if(!r)return{dimensionsDefine:xc(n),startIndex:o,dimensionsDetectedCount:a};if(t===Zt){var s=r;i==="auto"||i==null?Tc(function(u){u!=null&&u!=="-"&&(z(u)?o==null&&(o=1):o=0)},e,s,10):o=yt(i)?i:i?1:0,!n&&o===1&&(n=[],Tc(function(u,f){n[f]=u!=null?u+"":""},e,s,1/0)),a=n?n.length:e===Gi?s.length:s[0]?s[0].length:null}else if(t===Pe)n||(n=Db(r));else if(t===$e)n||(n=[],M(r,function(u,f){n.push(f)}));else if(t===ce){var l=jn(r[0]);a=N(l)&&l.length||1}return{startIndex:o,dimensionsDefine:xc(n),dimensionsDetectedCount:a}}function Db(r){for(var t=0,e;t<r.length&&!(e=r[t++]););if(e)return gt(e)}function xc(r){if(r){var t=X();return V(r,function(e,i){e=H(e)?e:{name:e};var n={name:e.name,displayName:e.displayName,type:e.type};if(n.name==null)return n;n.name+="",n.displayName==null&&(n.displayName=n.name);var a=t.get(n.name);return a?n.name+="-"+a.count++:t.set(n.name,{count:1}),n})}}function Tc(r,t,e,i){if(t===Gi)for(var n=0;n<e.length&&n<i;n++)r(e[n]?e[n][0]:null,n);else for(var a=e[0]||[],n=0;n<a.length&&n<i;n++)r(a[n],n)}function xg(r){var t=r.sourceFormat;return t===Pe||t===$e}var kr,Or,Br,Cc,Dc,Tg=function(){function r(t,e){var i=hf(t)?t:cf(t);this._source=i;var n=this._data=i.data;i.sourceFormat===ur&&(this._offset=0,this._dimSize=e,this._data=n),Dc(this,n,i)}return r.prototype.getSource=function(){return this._source},r.prototype.count=function(){return 0},r.prototype.getItem=function(t,e){},r.prototype.appendData=function(t){},r.prototype.clean=function(){},r.protoInitialize=function(){var t=r.prototype;t.pure=!1,t.persistent=!0}(),r.internalField=function(){var t;Dc=function(o,s,l){var u=l.sourceFormat,f=l.seriesLayoutBy,h=l.startIndex,v=l.dimensionsDefine,c=Cc[vf(u,f)];if(k(o,c),u===ur)o.getItem=e,o.count=n,o.fillStorage=i;else{var d=Cg(u,f);o.getItem=pt(d,null,s,h,v);var y=Dg(u,f);o.count=pt(y,null,s,h,v)}};var e=function(o,s){o=o-this._offset,s=s||[];for(var l=this._data,u=this._dimSize,f=u*o,h=0;h<u;h++)s[h]=l[f+h];return s},i=function(o,s,l,u){for(var f=this._data,h=this._dimSize,v=0;v<h;v++){for(var c=u[v],d=c[0]==null?1/0:c[0],y=c[1]==null?-1/0:c[1],p=s-o,g=l[v],m=0;m<p;m++){var _=f[m*h+v];g[o+m]=_,_<d&&(d=_),_>y&&(y=_)}c[0]=d,c[1]=y}},n=function(){return this._data?this._data.length/this._dimSize:0};Cc=(t={},t[Zt+"_"+He]={pure:!0,appendData:a},t[Zt+"_"+Gi]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[Pe]={pure:!0,appendData:a},t[$e]={pure:!0,appendData:function(o){var s=this._data;M(o,function(l,u){for(var f=s[u]||(s[u]=[]),h=0;h<(l||[]).length;h++)f.push(l[h])})}},t[ce]={appendData:a},t[ur]={persistent:!1,pure:!0,appendData:function(o){this._data=o},clean:function(){this._offset+=this.count(),this._data=null}},t);function a(o){for(var s=0;s<o.length;s++)this._data.push(o[s])}}(),r}(),Mc=function(r,t,e,i){return r[i]},Mb=(kr={},kr[Zt+"_"+He]=function(r,t,e,i){return r[i+t]},kr[Zt+"_"+Gi]=function(r,t,e,i,n){i+=t;for(var a=n||[],o=r,s=0;s<o.length;s++){var l=o[s];a[s]=l?l[i]:null}return a},kr[Pe]=Mc,kr[$e]=function(r,t,e,i,n){for(var a=n||[],o=0;o<e.length;o++){var s=e[o].name,l=r[s];a[o]=l?l[i]:null}return a},kr[ce]=Mc,kr);function Cg(r,t){var e=Mb[vf(r,t)];return e}var Ac=function(r,t,e){return r.length},Ab=(Or={},Or[Zt+"_"+He]=function(r,t,e){return Math.max(0,r.length-t)},Or[Zt+"_"+Gi]=function(r,t,e){var i=r[0];return i?Math.max(0,i.length-t):0},Or[Pe]=Ac,Or[$e]=function(r,t,e){var i=e[0].name,n=r[i];return n?n.length:0},Or[ce]=Ac,Or);function Dg(r,t){var e=Ab[vf(r,t)];return e}var Zs=function(r,t,e){return r[t]},Lb=(Br={},Br[Zt]=Zs,Br[Pe]=function(r,t,e){return r[e]},Br[$e]=Zs,Br[ce]=function(r,t,e){var i=jn(r);return i instanceof Array?i[t]:i},Br[ur]=Zs,Br);function Mg(r){var t=Lb[r];return t}function vf(r,t){return r===Zt?r+"_"+t:r}function Oi(r,t,e){if(r){var i=r.getRawDataItem(t);if(i!=null){var n=r.getStore(),a=n.getSource().sourceFormat;if(e!=null){var o=r.getDimensionIndex(e),s=n.getDimensionProperty(o);return Mg(a)(i,o,s)}else{var l=i;return a===ce&&(l=jn(i)),l}}}}var Ib=/\{@(.+?)\}/g,Pb=function(){function r(){}return r.prototype.getDataParams=function(t,e){var i=this.getData(e),n=this.getRawValue(t,e),a=i.getRawIndex(t),o=i.getName(t),s=i.getRawDataItem(t),l=i.getItemVisual(t,"style"),u=l&&l[i.getItemVisual(t,"drawType")||"fill"],f=l&&l.stroke,h=this.mainType,v=h==="series",c=i.userOutput&&i.userOutput.get();return{componentType:h,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:v?this.subType:null,seriesIndex:this.seriesIndex,seriesId:v?this.id:null,seriesName:v?this.name:null,name:o,dataIndex:a,data:s,dataType:e,value:n,color:u,borderColor:f,dimensionNames:c?c.fullDimensions:null,encode:c?c.encode:null,$vars:["seriesName","name","value"]}},r.prototype.getFormattedLabel=function(t,e,i,n,a,o){e=e||"normal";var s=this.getData(i),l=this.getDataParams(t,i);if(o&&(l.value=o.interpolatedValue),n!=null&&N(l.value)&&(l.value=l.value[n]),!a){var u=s.getItemModel(t);a=u.get(e==="normal"?["label","formatter"]:[e,"label","formatter"])}if(U(a))return l.status=e,l.dimensionIndex=n,a(l);if(z(a)){var f=fg(a,l);return f.replace(Ib,function(h,v){var c=v.length,d=v;d.charAt(0)==="["&&d.charAt(c-1)==="]"&&(d=+d.slice(1,c-1));var y=Oi(s,t,d);if(o&&N(o.interpolatedValue)){var p=s.getDimensionIndex(d);p>=0&&(y=o.interpolatedValue[p])}return y!=null?y+"":""})}},r.prototype.getRawValue=function(t,e){return Oi(this.getData(e),t)},r.prototype.formatTooltip=function(t,e,i){},r}();function Lc(r){var t,e;return H(r)?r.type&&(e=r):t=r,{text:t,frag:e}}function Ln(r){return new Rb(r)}var Rb=function(){function r(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return r.prototype.perform=function(t){var e=this._upstream,i=t&&t.skip;if(this._dirty&&e){var n=this.context;n.data=n.outputData=e.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var a;this._plan&&!i&&(a=this._plan(this.context));var o=f(this._modBy),s=this._modDataCount||0,l=f(t&&t.modBy),u=t&&t.modDataCount||0;(o!==l||s!==u)&&(a="reset");function f(m){return!(m>=1)&&(m=1),m}var h;(this._dirty||a==="reset")&&(this._dirty=!1,h=this._doReset(i)),this._modBy=l,this._modDataCount=u;var v=t&&t.step;if(e?this._dueEnd=e._outputDueEnd:this._dueEnd=this._count?this._count(this.context):1/0,this._progress){var c=this._dueIndex,d=Math.min(v!=null?this._dueIndex+v:1/0,this._dueEnd);if(!i&&(h||c<d)){var y=this._progress;if(N(y))for(var p=0;p<y.length;p++)this._doProgress(y[p],c,d,l,u);else this._doProgress(y,c,d,l,u)}this._dueIndex=d;var g=this._settedOutputEnd!=null?this._settedOutputEnd:d;this._outputDueEnd=g}else this._dueIndex=this._outputDueEnd=this._settedOutputEnd!=null?this._settedOutputEnd:this._dueEnd;return this.unfinished()},r.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},r.prototype._doProgress=function(t,e,i,n,a){Ic.reset(e,i,n,a),this._callingProgress=t,this._callingProgress({start:e,end:i,count:i-e,next:Ic.next},this.context)},r.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null;var e,i;!t&&this._reset&&(e=this._reset(this.context),e&&e.progress&&(i=e.forceFirstProgress,e=e.progress),N(e)&&!e.length&&(e=null)),this._progress=e,this._modBy=this._modDataCount=null;var n=this._downstream;return n&&n.dirty(),i},r.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},r.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},r.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},r.prototype.getUpstream=function(){return this._upstream},r.prototype.getDownstream=function(){return this._downstream},r.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},r}(),Ic=function(){var r,t,e,i,n,a={reset:function(l,u,f,h){t=l,r=u,e=f,i=h,n=Math.ceil(i/e),a.next=e>1&&i>0?s:o}};return a;function o(){return t<r?t++:null}function s(){var l=t%n*e+Math.ceil(t/n),u=t>=r?null:l<i?l:t;return t++,u}}();function ro(r,t){var e=t&&t.type;return e==="ordinal"?r:(e==="time"&&!yt(r)&&r!=null&&r!=="-"&&(r=+Ve(r)),r==null||r===""?NaN:Number(r))}X({number:function(r){return parseFloat(r)},time:function(r){return+Ve(r)},trim:function(r){return z(r)?Ce(r):r}});var Eb=function(){function r(t,e){var i=t==="desc";this._resultLT=i?1:-1,e==null&&(e=i?"min":"max"),this._incomparable=e==="min"?-1/0:1/0}return r.prototype.evaluate=function(t,e){var i=yt(t)?t:po(t),n=yt(e)?e:po(e),a=isNaN(i),o=isNaN(n);if(a&&(i=this._incomparable),o&&(n=this._incomparable),a&&o){var s=z(t),l=z(e);s&&(i=l?t:0),l&&(n=s?e:0)}return i<n?this._resultLT:i>n?-this._resultLT:0},r}(),kb=function(){function r(){}return r.prototype.getRawData=function(){throw new Error("not supported")},r.prototype.getRawDataItem=function(t){throw new Error("not supported")},r.prototype.cloneRawData=function(){},r.prototype.getDimensionInfo=function(t){},r.prototype.cloneAllDimensionInfo=function(){},r.prototype.count=function(){},r.prototype.retrieveValue=function(t,e){},r.prototype.retrieveValueFromItem=function(t,e){},r.prototype.convertValue=function(t,e){return ro(t,e)},r}();function Ob(r,t){var e=new kb,i=r.data,n=e.sourceFormat=r.sourceFormat,a=r.startIndex,o="";r.seriesLayoutBy!==He&&Wt(o);var s=[],l={},u=r.dimensionsDefine;if(u)M(u,function(y,p){var g=y.name,m={index:p,name:g,displayName:y.displayName};if(s.push(m),g!=null){var _="";qr(l,g)&&Wt(_),l[g]=m}});else for(var f=0;f<r.dimensionsDetectedCount;f++)s.push({index:f});var h=Cg(n,He);t.__isBuiltIn&&(e.getRawDataItem=function(y){return h(i,a,s,y)},e.getRawData=pt(Bb,null,r)),e.cloneRawData=pt(Nb,null,r);var v=Dg(n,He);e.count=pt(v,null,i,a,s);var c=Mg(n);e.retrieveValue=function(y,p){var g=h(i,a,s,y);return d(g,p)};var d=e.retrieveValueFromItem=function(y,p){if(y!=null){var g=s[p];if(g)return c(y,p,g.name)}};return e.getDimensionInfo=pt(Fb,null,s,l),e.cloneAllDimensionInfo=pt(zb,null,s),e}function Bb(r){var t=r.sourceFormat;if(!df(t)){var e="";Wt(e)}return r.data}function Nb(r){var t=r.sourceFormat,e=r.data;if(!df(t)){var i="";Wt(i)}if(t===Zt){for(var n=[],a=0,o=e.length;a<o;a++)n.push(e[a].slice());return n}else if(t===Pe){for(var n=[],a=0,o=e.length;a<o;a++)n.push(k({},e[a]));return n}}function Fb(r,t,e){if(e!=null){if(yt(e)||!isNaN(e)&&!qr(t,e))return r[e];if(qr(t,e))return t[e]}}function zb(r){return Q(r)}var Ag=X();function Hb(r){r=Q(r);var t=r.type,e="";t||Wt(e);var i=t.split(":");i.length!==2&&Wt(e);var n=!1;i[0]==="echarts"&&(t=i[1],n=!0),r.__isBuiltIn=n,Ag.set(t,r)}function Gb(r,t,e){var i=Nt(r),n=i.length,a="";n||Wt(a);for(var o=0,s=n;o<s;o++){var l=i[o];t=Vb(l,t),o!==s-1&&(t.length=Math.max(t.length,1))}return t}function Vb(r,t,e,i){var n="";t.length||Wt(n),H(r)||Wt(n);var a=r.type,o=Ag.get(a);o||Wt(n);var s=V(t,function(u){return Ob(u,o)}),l=Nt(o.transform({upstream:s[0],upstreamList:s,config:Q(r.config)}));return V(l,function(u,f){var h="";H(u)||Wt(h),u.data||Wt(h);var v=bg(u.data);df(v)||Wt(h);var c,d=t[0];if(d&&f===0&&!u.dimensions){var y=d.startIndex;y&&(u.data=d.data.slice(0,y).concat(u.data)),c={seriesLayoutBy:He,sourceHeader:y,dimensions:d.metaRawOption.dimensions}}else c={seriesLayoutBy:He,sourceHeader:0,dimensions:u.dimensions};return Ql(u.data,c,null)})}function df(r){return r===Zt||r===Pe}var Ko="undefined",Wb=typeof Uint32Array===Ko?Array:Uint32Array,Ub=typeof Uint16Array===Ko?Array:Uint16Array,Lg=typeof Int32Array===Ko?Array:Int32Array,Pc=typeof Float64Array===Ko?Array:Float64Array,Ig={float:Pc,int:Lg,ordinal:Array,number:Array,time:Pc},qs;function di(r){return r>65535?Wb:Ub}function pi(){return[1/0,-1/0]}function $b(r){var t=r.constructor;return t===Array?r.slice():new t(r)}function Rc(r,t,e,i,n){var a=Ig[e||"float"];if(n){var o=r[t],s=o&&o.length;if(s!==i){for(var l=new a(i),u=0;u<s;u++)l[u]=o[u];r[t]=l}}else r[t]=new a(i)}var Jl=function(){function r(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=X()}return r.prototype.initData=function(t,e,i){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var n=t.getSource(),a=this.defaultDimValueGetter=qs[n.sourceFormat];this._dimValueGetter=i||a,this._rawExtent=[],xg(n),this._dimensions=V(e,function(o){return{type:o.type,property:o.property}}),this._initDataFromProvider(0,t.count())},r.prototype.getProvider=function(){return this._provider},r.prototype.getSource=function(){return this._provider.getSource()},r.prototype.ensureCalculationDimension=function(t,e){var i=this._calcDimNameToIdx,n=this._dimensions,a=i.get(t);if(a!=null){if(n[a].type===e)return a}else a=n.length;return n[a]={type:e},i.set(t,a),this._chunks[a]=new Ig[e||"float"](this._rawCount),this._rawExtent[a]=pi(),a},r.prototype.collectOrdinalMeta=function(t,e){var i=this._chunks[t],n=this._dimensions[t],a=this._rawExtent,o=n.ordinalOffset||0,s=i.length;o===0&&(a[t]=pi());for(var l=a[t],u=o;u<s;u++){var f=i[u]=e.parseAndCollect(i[u]);isNaN(f)||(l[0]=Math.min(f,l[0]),l[1]=Math.max(f,l[1]))}n.ordinalMeta=e,n.ordinalOffset=s,n.type="ordinal"},r.prototype.getOrdinalMeta=function(t){var e=this._dimensions[t],i=e.ordinalMeta;return i},r.prototype.getDimensionProperty=function(t){var e=this._dimensions[t];return e&&e.property},r.prototype.appendData=function(t){var e=this._provider,i=this.count();e.appendData(t);var n=e.count();return e.persistent||(n+=i),i<n&&this._initDataFromProvider(i,n,!0),[i,n]},r.prototype.appendValues=function(t,e){for(var i=this._chunks,n=this._dimensions,a=n.length,o=this._rawExtent,s=this.count(),l=s+Math.max(t.length,e||0),u=0;u<a;u++){var f=n[u];Rc(i,u,f.type,l,!0)}for(var h=[],v=s;v<l;v++)for(var c=v-s,d=0;d<a;d++){var f=n[d],y=qs.arrayRows.call(this,t[c]||h,f.property,c,d);i[d][v]=y;var p=o[d];y<p[0]&&(p[0]=y),y>p[1]&&(p[1]=y)}return this._rawCount=this._count=l,{start:s,end:l}},r.prototype._initDataFromProvider=function(t,e,i){for(var n=this._provider,a=this._chunks,o=this._dimensions,s=o.length,l=this._rawExtent,u=V(o,function(m){return m.property}),f=0;f<s;f++){var h=o[f];l[f]||(l[f]=pi()),Rc(a,f,h.type,e,i)}if(n.fillStorage)n.fillStorage(t,e,a,l);else for(var v=[],c=t;c<e;c++){v=n.getItem(c,v);for(var d=0;d<s;d++){var y=a[d],p=this._dimValueGetter(v,u[d],c,d);y[c]=p;var g=l[d];p<g[0]&&(g[0]=p),p>g[1]&&(g[1]=p)}}!n.persistent&&n.clean&&n.clean(),this._rawCount=this._count=e,this._extent=[]},r.prototype.count=function(){return this._count},r.prototype.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var i=this._chunks[t];return i?i[this.getRawIndex(e)]:NaN},r.prototype.getValues=function(t,e){var i=[],n=[];if(e==null){e=t,t=[];for(var a=0;a<this._dimensions.length;a++)n.push(a)}else n=t;for(var a=0,o=n.length;a<o;a++)i.push(this.get(n[a],e));return i},r.prototype.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var i=this._chunks[t];return i?i[e]:NaN},r.prototype.getSum=function(t){var e=this._chunks[t],i=0;if(e)for(var n=0,a=this.count();n<a;n++){var o=this.get(t,n);isNaN(o)||(i+=o)}return i},r.prototype.getMedian=function(t){var e=[];this.each([t],function(a){isNaN(a)||e.push(a)});var i=e.sort(function(a,o){return a-o}),n=this.count();return n===0?0:n%2===1?i[(n-1)/2]:(i[n/2]+i[n/2-1])/2},r.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,i=e[t];if(i!=null&&i<this._count&&i===t)return t;for(var n=0,a=this._count-1;n<=a;){var o=(n+a)/2|0;if(e[o]<t)n=o+1;else if(e[o]>t)a=o-1;else return o}return-1},r.prototype.indicesOfNearest=function(t,e,i){var n=this._chunks,a=n[t],o=[];if(!a)return o;i==null&&(i=1/0);for(var s=1/0,l=-1,u=0,f=0,h=this.count();f<h;f++){var v=this.getRawIndex(f),c=e-a[v],d=Math.abs(c);d<=i&&((d<s||d===s&&c>=0&&l<0)&&(s=d,l=c,u=0),c===l&&(o[u++]=f))}return o.length=u,o},r.prototype.getIndices=function(){var t,e=this._indices;if(e){var i=e.constructor,n=this._count;if(i===Array){t=new i(n);for(var a=0;a<n;a++)t[a]=e[a]}else t=new i(e.buffer,0,n)}else{var i=di(this._rawCount);t=new i(this.count());for(var a=0;a<t.length;a++)t[a]=a}return t},r.prototype.filter=function(t,e){if(!this._count)return this;for(var i=this.clone(),n=i.count(),a=di(i._rawCount),o=new a(n),s=[],l=t.length,u=0,f=t[0],h=i._chunks,v=0;v<n;v++){var c=void 0,d=i.getRawIndex(v);if(l===0)c=e(v);else if(l===1){var y=h[f][d];c=e(y,v)}else{for(var p=0;p<l;p++)s[p]=h[t[p]][d];s[p]=v,c=e.apply(null,s)}c&&(o[u++]=d)}return u<n&&(i._indices=o),i._count=u,i._extent=[],i._updateGetRawIdx(),i},r.prototype.selectRange=function(t){var e=this.clone(),i=e._count;if(!i)return this;var n=gt(t),a=n.length;if(!a)return this;var o=e.count(),s=di(e._rawCount),l=new s(o),u=0,f=n[0],h=t[f][0],v=t[f][1],c=e._chunks,d=!1;if(!e._indices){var y=0;if(a===1){for(var p=c[n[0]],g=0;g<i;g++){var m=p[g];(m>=h&&m<=v||isNaN(m))&&(l[u++]=y),y++}d=!0}else if(a===2){for(var p=c[n[0]],_=c[n[1]],S=t[n[1]][0],b=t[n[1]][1],g=0;g<i;g++){var m=p[g],w=_[g];(m>=h&&m<=v||isNaN(m))&&(w>=S&&w<=b||isNaN(w))&&(l[u++]=y),y++}d=!0}}if(!d)if(a===1)for(var g=0;g<o;g++){var x=e.getRawIndex(g),m=c[n[0]][x];(m>=h&&m<=v||isNaN(m))&&(l[u++]=x)}else for(var g=0;g<o;g++){for(var A=!0,x=e.getRawIndex(g),C=0;C<a;C++){var D=n[C],m=c[D][x];(m<t[D][0]||m>t[D][1])&&(A=!1)}A&&(l[u++]=e.getRawIndex(g))}return u<o&&(e._indices=l),e._count=u,e._extent=[],e._updateGetRawIdx(),e},r.prototype.map=function(t,e){var i=this.clone(t);return this._updateDims(i,t,e),i},r.prototype.modify=function(t,e){this._updateDims(this,t,e)},r.prototype._updateDims=function(t,e,i){for(var n=t._chunks,a=[],o=e.length,s=t.count(),l=[],u=t._rawExtent,f=0;f<e.length;f++)u[e[f]]=pi();for(var h=0;h<s;h++){for(var v=t.getRawIndex(h),c=0;c<o;c++)l[c]=n[e[c]][v];l[o]=h;var d=i&&i.apply(null,l);if(d!=null){typeof d!="object"&&(a[0]=d,d=a);for(var f=0;f<d.length;f++){var y=e[f],p=d[f],g=u[y],m=n[y];m&&(m[v]=p),p<g[0]&&(g[0]=p),p>g[1]&&(g[1]=p)}}}},r.prototype.lttbDownSample=function(t,e){var i=this.clone([t],!0),n=i._chunks,a=n[t],o=this.count(),s=0,l=Math.floor(1/e),u=this.getRawIndex(0),f,h,v,c=new(di(this._rawCount))(Math.min((Math.ceil(o/l)+2)*2,o));c[s++]=u;for(var d=1;d<o-1;d+=l){for(var y=Math.min(d+l,o-1),p=Math.min(d+l*2,o),g=(p+y)/2,m=0,_=y;_<p;_++){var S=this.getRawIndex(_),b=a[S];isNaN(b)||(m+=b)}m/=p-y;var w=d,x=Math.min(d+l,o),A=d-1,C=a[u];f=-1,v=w;for(var D=-1,T=0,_=w;_<x;_++){var S=this.getRawIndex(_),b=a[S];if(isNaN(b)){T++,D<0&&(D=S);continue}h=Math.abs((A-g)*(b-C)-(A-_)*(m-C)),h>f&&(f=h,v=S)}T>0&&T<x-w&&(c[s++]=Math.min(D,v),v=Math.max(D,v)),c[s++]=v,u=v}return c[s++]=this.getRawIndex(o-1),i._count=s,i._indices=c,i.getRawIndex=this._getRawIdx,i},r.prototype.minmaxDownSample=function(t,e){for(var i=this.clone([t],!0),n=i._chunks,a=Math.floor(1/e),o=n[t],s=this.count(),l=new(di(this._rawCount))(Math.ceil(s/a)*2),u=0,f=0;f<s;f+=a){var h=f,v=o[this.getRawIndex(h)],c=f,d=o[this.getRawIndex(c)],y=a;f+a>s&&(y=s-f);for(var p=0;p<y;p++){var g=this.getRawIndex(f+p),m=o[g];m<v&&(v=m,h=f+p),m>d&&(d=m,c=f+p)}var _=this.getRawIndex(h),S=this.getRawIndex(c);h<c?(l[u++]=_,l[u++]=S):(l[u++]=S,l[u++]=_)}return i._count=u,i._indices=l,i._updateGetRawIdx(),i},r.prototype.downSample=function(t,e,i,n){for(var a=this.clone([t],!0),o=a._chunks,s=[],l=Math.floor(1/e),u=o[t],f=this.count(),h=a._rawExtent[t]=pi(),v=new(di(this._rawCount))(Math.ceil(f/l)),c=0,d=0;d<f;d+=l){l>f-d&&(l=f-d,s.length=l);for(var y=0;y<l;y++){var p=this.getRawIndex(d+y);s[y]=u[p]}var g=i(s),m=this.getRawIndex(Math.min(d+n(s,g)||0,f-1));u[m]=g,g<h[0]&&(h[0]=g),g>h[1]&&(h[1]=g),v[c++]=m}return a._count=c,a._indices=v,a._updateGetRawIdx(),a},r.prototype.each=function(t,e){if(this._count)for(var i=t.length,n=this._chunks,a=0,o=this.count();a<o;a++){var s=this.getRawIndex(a);switch(i){case 0:e(a);break;case 1:e(n[t[0]][s],a);break;case 2:e(n[t[0]][s],n[t[1]][s],a);break;default:for(var l=0,u=[];l<i;l++)u[l]=n[t[l]][s];u[l]=a,e.apply(null,u)}}},r.prototype.getDataExtent=function(t){var e=this._chunks[t],i=pi();if(!e)return i;var n=this.count(),a=!this._indices,o;if(a)return this._rawExtent[t].slice();if(o=this._extent[t],o)return o.slice();o=i;for(var s=o[0],l=o[1],u=0;u<n;u++){var f=this.getRawIndex(u),h=e[f];h<s&&(s=h),h>l&&(l=h)}return o=[s,l],this._extent[t]=o,o},r.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var i=[],n=this._chunks,a=0;a<n.length;a++)i.push(n[a][e]);return i},r.prototype.clone=function(t,e){var i=new r,n=this._chunks,a=t&&Fi(t,function(s,l){return s[l]=!0,s},{});if(a)for(var o=0;o<n.length;o++)i._chunks[o]=a[o]?$b(n[o]):n[o];else i._chunks=n;return this._copyCommonProps(i),e||(i._indices=this._cloneIndices()),i._updateGetRawIdx(),i},r.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=Q(this._extent),t._rawExtent=Q(this._rawExtent)},r.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array){var i=this._indices.length;e=new t(i);for(var n=0;n<i;n++)e[n]=this._indices[n]}else e=new t(this._indices);return e}return null},r.prototype._getRawIdxIdentity=function(t){return t},r.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},r.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},r.internalField=function(){function t(e,i,n,a){return ro(e[a],this._dimensions[a])}qs={arrayRows:t,objectRows:function(e,i,n,a){return ro(e[i],this._dimensions[a])},keyedColumns:t,original:function(e,i,n,a){var o=e&&(e.value==null?e:e.value);return ro(o instanceof Array?o[a]:o,this._dimensions[a])},typedArray:function(e,i,n,a){return e[a]}}}(),r}(),Yb=function(){function r(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return r.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},r.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},r.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},r.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},r.prototype._createSource=function(){this._setLocalSource([],[]);var t=this._sourceHost,e=this._getUpstreamSourceManagers(),i=!!e.length,n,a;if(Ma(t)){var o=t,s=void 0,l=void 0,u=void 0;if(i){var f=e[0];f.prepareSource(),u=f.getSource(),s=u.data,l=u.sourceFormat,a=[f._getVersionSign()]}else s=o.get("data",!0),l=Xt(s)?ur:ce,a=[];var h=this._getSourceMetaRawOption()||{},v=u&&u.metaRawOption||{},c=Z(h.seriesLayoutBy,v.seriesLayoutBy)||null,d=Z(h.sourceHeader,v.sourceHeader),y=Z(h.dimensions,v.dimensions),p=c!==v.seriesLayoutBy||!!d!=!!v.sourceHeader||y;n=p?[Ql(s,{seriesLayoutBy:c,sourceHeader:d,dimensions:y},l)]:[]}else{var g=t;if(i){var m=this._applyTransform(e);n=m.sourceList,a=m.upstreamSignList}else{var _=g.get("source",!0);n=[Ql(_,this._getSourceMetaRawOption(),null)],a=[]}}this._setLocalSource(n,a)},r.prototype._applyTransform=function(t){var e=this._sourceHost,i=e.get("transform",!0),n=e.get("fromTransformResult",!0);if(n!=null){var a="";t.length!==1&&Ec(a)}var o,s=[],l=[];return M(t,function(u){u.prepareSource();var f=u.getSource(n||0),h="";n!=null&&!f&&Ec(h),s.push(f),l.push(u._getVersionSign())}),i?o=Gb(i,s,{datasetIndex:e.componentIndex}):n!=null&&(o=[Tb(s[0])]),{sourceList:o,upstreamSignList:l}},r.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var i=t[e];if(i._isDirty()||this._upstreamSignList[e]!==i._getVersionSign())return!0}},r.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var i=this._getUpstreamSourceManagers();return i[0]&&i[0].getSource(t)}return e},r.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},r.prototype._innerGetDataStore=function(t,e,i){var n=0,a=this._storeList,o=a[n];o||(o=a[n]={});var s=o[i];if(!s){var l=this._getUpstreamSourceManagers()[0];Ma(this._sourceHost)&&l?s=l._innerGetDataStore(t,e,i):(s=new Jl,s.initData(new Tg(e,t.length),t)),o[i]=s}return s},r.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(Ma(t)){var e=pg(t);return e?[e.getSourceManager()]:[]}else return V(qw(t),function(i){return i.getSourceManager()})},r.prototype._getSourceMetaRawOption=function(){var t=this._sourceHost,e,i,n;if(Ma(t))e=t.get("seriesLayoutBy",!0),i=t.get("sourceHeader",!0),n=t.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var a=t;e=a.get("seriesLayoutBy",!0),i=a.get("sourceHeader",!0),n=a.get("dimensions",!0)}return{seriesLayoutBy:e,sourceHeader:i,dimensions:n}},r}();function Ma(r){return r.mainType==="series"}function Ec(r){throw new Error(r)}var Xb="line-height:1";function Pg(r){var t=r.lineHeight;return t==null?Xb:"line-height:"+Vt(t+"")+"px"}function Rg(r,t){var e=r.color||"#6e7079",i=r.fontSize||12,n=r.fontWeight||"400",a=r.color||"#464646",o=r.fontSize||14,s=r.fontWeight||"900";return t==="html"?{nameStyle:"font-size:"+Vt(i+"")+"px;color:"+Vt(e)+";font-weight:"+Vt(n+""),valueStyle:"font-size:"+Vt(o+"")+"px;color:"+Vt(a)+";font-weight:"+Vt(s+"")}:{nameStyle:{fontSize:i,fill:e,fontWeight:n},valueStyle:{fontSize:o,fill:a,fontWeight:s}}}var Zb=[0,10,20,30],qb=["",`
`,`

`,`


`];function Wn(r,t){return t.type=r,t}function jl(r){return r.type==="section"}function Eg(r){return jl(r)?Kb:Qb}function kg(r){if(jl(r)){var t=0,e=r.blocks.length,i=e>1||e>0&&!r.noHeader;return M(r.blocks,function(n){var a=kg(n);a>=t&&(t=a+ +(i&&(!a||jl(n)&&!n.noHeader)))}),t}return 0}function Kb(r,t,e,i){var n=t.noHeader,a=Jb(kg(t)),o=[],s=t.blocks||[];Ge(!s||N(s)),s=s||[];var l=r.orderMode;if(t.sortBlocks&&l){s=s.slice();var u={valueAsc:"asc",valueDesc:"desc"};if(qr(u,l)){var f=new Eb(u[l],null);s.sort(function(y,p){return f.evaluate(y.sortParam,p.sortParam)})}else l==="seriesDesc"&&s.reverse()}M(s,function(y,p){var g=t.valueFormatter,m=Eg(y)(g?k(k({},r),{valueFormatter:g}):r,y,p>0?a.html:0,i);m!=null&&o.push(m)});var h=r.renderMode==="richText"?o.join(a.richText):tu(i,o.join(""),n?e:a.html);if(n)return h;var v=Kl(t.header,"ordinal",r.useUTC),c=Rg(i,r.renderMode).nameStyle,d=Pg(i);return r.renderMode==="richText"?Og(r,v,c)+a.richText+h:tu(i,'<div style="'+c+";"+d+';">'+Vt(v)+"</div>"+h,e)}function Qb(r,t,e,i){var n=r.renderMode,a=t.noName,o=t.noValue,s=!t.markerType,l=t.name,u=r.useUTC,f=t.valueFormatter||r.valueFormatter||function(S){return S=N(S)?S:[S],V(S,function(b,w){return Kl(b,N(c)?c[w]:c,u)})};if(!(a&&o)){var h=s?"":r.markupStyleCreator.makeTooltipMarker(t.markerType,t.markerColor||"#333",n),v=a?"":Kl(l,"ordinal",u),c=t.valueType,d=o?[]:f(t.value,t.dataIndex),y=!s||!a,p=!s&&a,g=Rg(i,n),m=g.nameStyle,_=g.valueStyle;return n==="richText"?(s?"":h)+(a?"":Og(r,v,m))+(o?"":ex(r,d,y,p,_)):tu(i,(s?"":h)+(a?"":jb(v,!s,m))+(o?"":tx(d,y,p,_)),e)}}function kc(r,t,e,i,n,a){if(r){var o=Eg(r),s={useUTC:n,renderMode:e,orderMode:i,markupStyleCreator:t,valueFormatter:r.valueFormatter};return o(s,r,0,a)}}function Jb(r){return{html:Zb[r],richText:qb[r]}}function tu(r,t,e){var i='<div style="clear:both"></div>',n="margin: "+e+"px 0 0",a=Pg(r);return'<div style="'+n+";"+a+';">'+t+i+"</div>"}function jb(r,t,e){var i=t?"margin-left:2px":"";return'<span style="'+e+";"+i+'">'+Vt(r)+"</span>"}function tx(r,t,e,i){var n=e?"10px":"20px",a=t?"float:right;margin-left:"+n:"";return r=N(r)?r:[r],'<span style="'+a+";"+i+'">'+V(r,function(o){return Vt(o)}).join("&nbsp;&nbsp;")+"</span>"}function Og(r,t,e){return r.markupStyleCreator.wrapRichTextStyle(t,e)}function ex(r,t,e,i,n){var a=[n],o=i?10:20;return e&&a.push({padding:[0,0,0,o],align:"right"}),r.markupStyleCreator.wrapRichTextStyle(N(t)?t.join("  "):t,a)}function rx(r,t){var e=r.getData().getItemVisual(t,"style"),i=e[r.visualDrawType];return ti(i)}function Bg(r,t){var e=r.get("padding");return e??(t==="richText"?[8,10]:10)}var Ks=function(){function r(){this.richTextStyles={},this._nextStyleNameId=Zd()}return r.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},r.prototype.makeTooltipMarker=function(t,e,i){var n=i==="richText"?this._generateStyleName():null,a=Gw({color:e,type:t,renderMode:i,markerId:n});return z(a)?a:(this.richTextStyles[n]=a.style,a.content)},r.prototype.wrapRichTextStyle=function(t,e){var i={};N(e)?M(e,function(a){return k(i,a)}):k(i,e);var n=this._generateStyleName();return this.richTextStyles[n]=i,"{"+n+"|"+t+"}"},r}();function ix(r){var t=r.series,e=r.dataIndex,i=r.multipleSeries,n=t.getData(),a=n.mapDimensionsAll("defaultedTooltip"),o=a.length,s=t.getRawValue(e),l=N(s),u=rx(t,e),f,h,v,c;if(o>1||l&&!o){var d=nx(s,t,e,a,u);f=d.inlineValues,h=d.inlineValueTypes,v=d.blocks,c=d.inlineValues[0]}else if(o){var y=n.getDimensionInfo(a[0]);c=f=Oi(n,e,a[0]),h=y.type}else c=f=l?s[0]:s;var p=zu(t),g=p&&t.name||"",m=n.getName(e),_=i?g:m;return Wn("section",{header:g,noHeader:i||!p,sortParam:c,blocks:[Wn("nameValue",{markerType:"item",markerColor:u,name:_,noName:!Ce(_),value:f,valueType:h,dataIndex:e})].concat(v||[])})}function nx(r,t,e,i,n){var a=t.getData(),o=Fi(r,function(h,v,c){var d=a.getDimensionInfo(c);return h=h||d&&d.tooltip!==!1&&d.displayName!=null},!1),s=[],l=[],u=[];i.length?M(i,function(h){f(Oi(a,e,h),h)}):M(r,f);function f(h,v){var c=a.getDimensionInfo(v);!c||c.otherDims.tooltip===!1||(o?u.push(Wn("nameValue",{markerType:"subItem",markerColor:n,name:c.displayName,value:h,valueType:c.type})):(s.push(h),l.push(c.type)))}return{inlineValues:s,inlineValueTypes:l,blocks:u}}var Ke=wt();function Aa(r,t){return r.getName(t)||r.getId(t)}var ax="__universalTransitionEnabled",Qo=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e._selectedDataIndicesMap={},e}return t.prototype.init=function(e,i,n){this.seriesIndex=this.componentIndex,this.dataTask=Ln({count:sx,reset:lx}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(e,n);var a=Ke(this).sourceManager=new Yb(this);a.prepareSource();var o=this.getInitialData(e,n);Bc(o,this),this.dataTask.context.data=o,Ke(this).dataBeforeProcessed=o,Oc(this),this._initSelectedMapFromData(o)},t.prototype.mergeDefaultAndTheme=function(e,i){var n=Gn(this),a=n?Zo(e):{},o=this.subType;dt.hasClass(o)&&(o+="Series"),et(e,i.getTheme().get(this.subType)),et(e,this.getDefaultOption()),yh(e,"label",["show"]),this.fillDataTextStyle(e.data),n&&ki(e,a,n)},t.prototype.mergeOption=function(e,i){e=et(this.option,e,!0),this.fillDataTextStyle(e.data);var n=Gn(this);n&&ki(this.option,e,n);var a=Ke(this).sourceManager;a.dirty(),a.prepareSource();var o=this.getInitialData(e,i);Bc(o,this),this.dataTask.dirty(),this.dataTask.context.data=o,Ke(this).dataBeforeProcessed=o,Oc(this),this._initSelectedMapFromData(o)},t.prototype.fillDataTextStyle=function(e){if(e&&!Xt(e))for(var i=["show"],n=0;n<e.length;n++)e[n]&&e[n].label&&yh(e[n],"label",i)},t.prototype.getInitialData=function(e,i){},t.prototype.appendData=function(e){var i=this.getRawData();i.appendData(e.data)},t.prototype.getData=function(e){var i=eu(this);if(i){var n=i.context.data;return e==null||!n.getLinkedData?n:n.getLinkedData(e)}else return Ke(this).data},t.prototype.getAllData=function(){var e=this.getData();return e&&e.getLinkedDataAll?e.getLinkedDataAll():[{data:e}]},t.prototype.setData=function(e){var i=eu(this);if(i){var n=i.context;n.outputData=e,i!==this.dataTask&&(n.data=e)}Ke(this).data=e},t.prototype.getEncode=function(){var e=this.get("encode",!0);if(e)return X(e)},t.prototype.getSourceManager=function(){return Ke(this).sourceManager},t.prototype.getSource=function(){return this.getSourceManager().getSource()},t.prototype.getRawData=function(){return Ke(this).dataBeforeProcessed},t.prototype.getColorBy=function(){var e=this.get("colorBy");return e||"series"},t.prototype.isColorBySeries=function(){return this.getColorBy()==="series"},t.prototype.getBaseAxis=function(){var e=this.coordinateSystem;return e&&e.getBaseAxis&&e.getBaseAxis()},t.prototype.formatTooltip=function(e,i,n){return ix({series:this,dataIndex:e,multipleSeries:i})},t.prototype.isAnimationEnabled=function(){var e=this.ecModel;if(Y.node&&!(e&&e.ssr))return!1;var i=this.getShallow("animation");return i&&this.getData().count()>this.getShallow("animationThreshold")&&(i=!1),!!i},t.prototype.restoreData=function(){this.dataTask.dirty()},t.prototype.getColorFromPalette=function(e,i,n){var a=this.ecModel,o=uf.prototype.getColorFromPalette.call(this,e,i,n);return o||(o=a.getColorFromPalette(e,i,n)),o},t.prototype.coordDimToDataDim=function(e){return this.getRawData().mapDimensionsAll(e)},t.prototype.getProgressive=function(){return this.get("progressive")},t.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},t.prototype.select=function(e,i){this._innerSelect(this.getData(i),e)},t.prototype.unselect=function(e,i){var n=this.option.selectedMap;if(n){var a=this.option.selectedMode,o=this.getData(i);if(a==="series"||n==="all"){this.option.selectedMap={},this._selectedDataIndicesMap={};return}for(var s=0;s<e.length;s++){var l=e[s],u=Aa(o,l);n[u]=!1,this._selectedDataIndicesMap[u]=-1}}},t.prototype.toggleSelect=function(e,i){for(var n=[],a=0;a<e.length;a++)n[0]=e[a],this.isSelected(e[a],i)?this.unselect(n,i):this.select(n,i)},t.prototype.getSelectedDataIndices=function(){if(this.option.selectedMap==="all")return[].slice.call(this.getData().getIndices());for(var e=this._selectedDataIndicesMap,i=gt(e),n=[],a=0;a<i.length;a++){var o=e[i[a]];o>=0&&n.push(o)}return n},t.prototype.isSelected=function(e,i){var n=this.option.selectedMap;if(!n)return!1;var a=this.getData(i);return(n==="all"||n[Aa(a,e)])&&!a.getItemModel(e).get(["select","disabled"])},t.prototype.isUniversalTransitionEnabled=function(){if(this[ax])return!0;var e=this.option.universalTransition;return e?e===!0?!0:e&&e.enabled:!1},t.prototype._innerSelect=function(e,i){var n,a,o=this.option,s=o.selectedMode,l=i.length;if(!(!s||!l)){if(s==="series")o.selectedMap="all";else if(s==="multiple"){H(o.selectedMap)||(o.selectedMap={});for(var u=o.selectedMap,f=0;f<l;f++){var h=i[f],v=Aa(e,h);u[v]=!0,this._selectedDataIndicesMap[v]=e.getRawIndex(h)}}else if(s==="single"||s===!0){var c=i[l-1],v=Aa(e,c);o.selectedMap=(n={},n[v]=!0,n),this._selectedDataIndicesMap=(a={},a[v]=e.getRawIndex(c),a)}}},t.prototype._initSelectedMapFromData=function(e){if(!this.option.selectedMap){var i=[];e.hasItemOption&&e.each(function(n){var a=e.getRawDataItem(n);a&&a.selected&&i.push(n)}),i.length>0&&this._innerSelect(e,i)}},t.registerClass=function(e){return dt.registerClass(e)},t.protoInitialize=function(){var e=t.prototype;e.type="series.__base__",e.seriesIndex=0,e.ignoreStyleOnData=!1,e.hasSymbolVisual=!1,e.defaultSymbol="circle",e.visualStyleAccessPath="itemStyle",e.visualDrawType="fill"}(),t}(dt);Le(Qo,Pb);Le(Qo,uf);tp(Qo,dt);function Oc(r){var t=r.name;zu(r)||(r.name=ox(r)||t)}function ox(r){var t=r.getRawData(),e=t.mapDimensionsAll("seriesName"),i=[];return M(e,function(n){var a=t.getDimensionInfo(n);a.displayName&&i.push(a.displayName)}),i.join(" ")}function sx(r){return r.model.getRawData().count()}function lx(r){var t=r.model;return t.setData(t.getRawData().cloneShallow()),ux}function ux(r,t){t.outputData&&r.end>t.outputData.count()&&t.model.getRawData().cloneShallow(t.outputData)}function Bc(r,t){M(t0(r.CHANGABLE_METHODS,r.DOWNSAMPLE_METHODS),function(e){r.wrapMethod(e,bt(fx,t))})}function fx(r,t){var e=eu(r);return e&&e.setOutputEnd((t||this).count()),t}function eu(r){var t=(r.ecModel||{}).scheduler,e=t&&t.getPipeline(r.uid);if(e){var i=e.currentTask;if(i){var n=i.agentStubMap;n&&(i=n.get(r.uid))}return i}}const Un=Qo;var pf=function(){function r(){this.group=new Ft,this.uid=Go("viewComponent")}return r.prototype.init=function(t,e){},r.prototype.render=function(t,e,i,n){},r.prototype.dispose=function(t,e){},r.prototype.updateView=function(t,e,i,n){},r.prototype.updateLayout=function(t,e,i,n){},r.prototype.updateVisual=function(t,e,i,n){},r.prototype.toggleBlurSeries=function(t,e,i){},r.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)},r}();Gu(pf);Eo(pf);const We=pf;function Ng(){var r=wt();return function(t){var e=r(t),i=t.pipelineContext,n=!!e.large,a=!!e.progressiveRender,o=e.large=!!(i&&i.large),s=e.progressiveRender=!!(i&&i.progressiveRender);return(n!==o||a!==s)&&"reset"}}var Fg=wt(),hx=Ng(),gf=function(){function r(){this.group=new Ft,this.uid=Go("viewChart"),this.renderTask=Ln({plan:cx,reset:vx}),this.renderTask.context={view:this}}return r.prototype.init=function(t,e){},r.prototype.render=function(t,e,i,n){},r.prototype.highlight=function(t,e,i,n){var a=t.getData(n&&n.dataType);a&&Fc(a,n,"emphasis")},r.prototype.downplay=function(t,e,i,n){var a=t.getData(n&&n.dataType);a&&Fc(a,n,"normal")},r.prototype.remove=function(t,e){this.group.removeAll()},r.prototype.dispose=function(t,e){},r.prototype.updateView=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.updateLayout=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.updateVisual=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.eachRendered=function(t){ju(this.group,t)},r.markUpdateMethod=function(t,e){Fg(t).updateMethod=e},r.protoInitialize=function(){var t=r.prototype;t.type="chart"}(),r}();function Nc(r,t,e){r&&Yl(r)&&(t==="emphasis"?go:yo)(r,e)}function Fc(r,t,e){var i=Qr(r,t),n=t&&t.highlightKey!=null?gS(t.highlightKey):null;i!=null?M(Nt(i),function(a){Nc(r.getItemGraphicEl(a),e,n)}):r.eachItemGraphicEl(function(a){Nc(a,e,n)})}Gu(gf);Eo(gf);function cx(r){return hx(r.model)}function vx(r){var t=r.model,e=r.ecModel,i=r.api,n=r.payload,a=t.pipelineContext.progressiveRender,o=r.view,s=n&&Fg(n).updateMethod,l=a?"incrementalPrepareRender":s&&o[s]?s:"render";return l!=="render"&&o[l](t,e,i,n),dx[l]}var dx={incrementalPrepareRender:{progress:function(r,t){t.view.incrementalRender(r,t.model,t.ecModel,t.api,t.payload)}},render:{forceFirstProgress:!0,progress:function(r,t){t.view.render(t.model,t.ecModel,t.api,t.payload)}}};const fr=gf;var xo="\0__throttleOriginMethod",zc="\0__throttleRate",Hc="\0__throttleType";function yf(r,t,e){var i,n=0,a=0,o=null,s,l,u,f;t=t||0;function h(){a=new Date().getTime(),o=null,r.apply(l,u||[])}var v=function(){for(var c=[],d=0;d<arguments.length;d++)c[d]=arguments[d];i=new Date().getTime(),l=this,u=c;var y=f||t,p=f||e;f=null,s=i-(p?n:a)-y,clearTimeout(o),p?o=setTimeout(h,y):s>=0?h():o=setTimeout(h,-s),n=i};return v.clear=function(){o&&(clearTimeout(o),o=null)},v.debounceNextCall=function(c){f=c},v}function zg(r,t,e,i){var n=r[t];if(n){var a=n[xo]||n,o=n[Hc],s=n[zc];if(s!==e||o!==i){if(e==null||!i)return r[t]=a;n=r[t]=yf(a,e,i==="debounce"),n[xo]=a,n[Hc]=i,n[zc]=e}return n}}function ru(r,t){var e=r[t];e&&e[xo]&&(e.clear&&e.clear(),r[t]=e[xo])}var Gc=wt(),Vc={itemStyle:Nn(Qp,!0),lineStyle:Nn(Kp,!0)},px={lineStyle:"stroke",itemStyle:"fill"};function Hg(r,t){var e=r.visualStyleMapper||Vc[t];return e||(console.warn("Unknown style type '"+t+"'."),Vc.itemStyle)}function Gg(r,t){var e=r.visualDrawType||px[t];return e||(console.warn("Unknown style type '"+t+"'."),"fill")}var gx={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData(),i=r.visualStyleAccessPath||"itemStyle",n=r.getModel(i),a=Hg(r,i),o=a(n),s=n.getShallow("decal");s&&(e.setVisual("decal",s),s.dirty=!0);var l=Gg(r,i),u=o[l],f=U(u)?u:null,h=o.fill==="auto"||o.stroke==="auto";if(!o[l]||f||h){var v=r.getColorFromPalette(r.name,null,t.getSeriesCount());o[l]||(o[l]=v,e.setVisual("colorFromPalette",!0)),o.fill=o.fill==="auto"||U(o.fill)?v:o.fill,o.stroke=o.stroke==="auto"||U(o.stroke)?v:o.stroke}if(e.setVisual("style",o),e.setVisual("drawType",l),!t.isSeriesFiltered(r)&&f)return e.setVisual("colorFromPalette",!1),{dataEach:function(c,d){var y=r.getDataParams(d),p=k({},o);p[l]=f(y),c.setItemVisual(d,"style",p)}}}},en=new Rt,yx={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){if(!(r.ignoreStyleOnData||t.isSeriesFiltered(r))){var e=r.getData(),i=r.visualStyleAccessPath||"itemStyle",n=Hg(r,i),a=e.getVisual("drawType");return{dataEach:e.hasItemOption?function(o,s){var l=o.getRawDataItem(s);if(l&&l[i]){en.option=l[i];var u=n(en),f=o.ensureUniqueItemVisual(s,"style");k(f,u),en.option.decal&&(o.setItemVisual(s,"decal",en.option.decal),en.option.decal.dirty=!0),a in u&&o.setItemVisual(s,"colorFromPalette",!1)}}:null}}}},mx={performRawSeries:!0,overallReset:function(r){var t=X();r.eachSeries(function(e){var i=e.getColorBy();if(!e.isColorBySeries()){var n=e.type+"-"+i,a=t.get(n);a||(a={},t.set(n,a)),Gc(e).scope=a}}),r.eachSeries(function(e){if(!(e.isColorBySeries()||r.isSeriesFiltered(e))){var i=e.getRawData(),n={},a=e.getData(),o=Gc(e).scope,s=e.visualStyleAccessPath||"itemStyle",l=Gg(e,s);a.each(function(u){var f=a.getRawIndex(u);n[f]=u}),i.each(function(u){var f=n[u],h=a.getItemVisual(f,"colorFromPalette");if(h){var v=a.ensureUniqueItemVisual(f,"style"),c=i.getName(u)||u+"",d=i.count();v[l]=e.getColorFromPalette(c,o,d)}})}})}},La=Math.PI;function _x(r,t){t=t||{},ot(t,{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var e=new Ft,i=new Ct({style:{fill:t.maskColor},zlevel:t.zlevel,z:1e4});e.add(i);var n=new zt({style:{text:t.text,fill:t.textColor,fontSize:t.fontSize,fontWeight:t.fontWeight,fontStyle:t.fontStyle,fontFamily:t.fontFamily},zlevel:t.zlevel,z:10001}),a=new Ct({style:{fill:"none"},textContent:n,textConfig:{position:"right",distance:10},zlevel:t.zlevel,z:10001});e.add(a);var o;return t.showSpinner&&(o=new Zu({shape:{startAngle:-La/2,endAngle:-La/2+.1,r:t.spinnerRadius},style:{stroke:t.color,lineCap:"round",lineWidth:t.lineWidth},zlevel:t.zlevel,z:10001}),o.animateShape(!0).when(1e3,{endAngle:La*3/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:La*3/2}).delay(300).start("circularInOut"),e.add(o)),e.resize=function(){var s=n.getBoundingRect().width,l=t.showSpinner?t.spinnerRadius:0,u=(r.getWidth()-l*2-(t.showSpinner&&s?10:0)-s)/2-(t.showSpinner&&s?0:5+s/2)+(t.showSpinner?0:s/2)+(s?0:l),f=r.getHeight()/2;t.showSpinner&&o.setShape({cx:u,cy:f}),a.setShape({x:u-l,y:f-l,width:l*2,height:l*2}),i.setShape({x:0,y:0,width:r.getWidth(),height:r.getHeight()})},e.resize(),e}var Sx=function(){function r(t,e,i,n){this._stageTaskMap=X(),this.ecInstance=t,this.api=e,i=this._dataProcessorHandlers=i.slice(),n=this._visualHandlers=n.slice(),this._allHandlers=i.concat(n)}return r.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(i){var n=i.overallTask;n&&n.dirty()})},r.prototype.getPerformArgs=function(t,e){if(t.__pipeline){var i=this._pipelineMap.get(t.__pipeline.id),n=i.context,a=!e&&i.progressiveEnabled&&(!n||n.progressiveRender)&&t.__idxInPipeline>i.blockIndex,o=a?i.step:null,s=n&&n.modDataCount,l=s!=null?Math.ceil(s/o):null;return{step:o,modBy:l,modDataCount:s}}},r.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},r.prototype.updateStreamModes=function(t,e){var i=this._pipelineMap.get(t.uid),n=t.getData(),a=n.count(),o=i.progressiveEnabled&&e.incrementalPrepareRender&&a>=i.threshold,s=t.get("large")&&a>=t.get("largeThreshold"),l=t.get("progressiveChunkMode")==="mod"?a:null;t.pipelineContext=i.context={progressiveRender:o,modDataCount:l,large:s}},r.prototype.restorePipelines=function(t){var e=this,i=e._pipelineMap=X();t.eachSeries(function(n){var a=n.getProgressive(),o=n.uid;i.set(o,{id:o,head:null,tail:null,threshold:n.getProgressiveThreshold(),progressiveEnabled:a&&!(n.preventIncremental&&n.preventIncremental()),blockIndex:-1,step:Math.round(a||700),count:0}),e._pipe(n,n.dataTask)})},r.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),i=this.api;M(this._allHandlers,function(n){var a=t.get(n.uid)||t.set(n.uid,{}),o="";Ge(!(n.reset&&n.overallReset),o),n.reset&&this._createSeriesStageTask(n,a,e,i),n.overallReset&&this._createOverallStageTask(n,a,e,i)},this)},r.prototype.prepareView=function(t,e,i,n){var a=t.renderTask,o=a.context;o.model=e,o.ecModel=i,o.api=n,a.__block=!t.incrementalPrepareRender,this._pipe(e,a)},r.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},r.prototype.performVisualTasks=function(t,e,i){this._performStageTasks(this._visualHandlers,t,e,i)},r.prototype._performStageTasks=function(t,e,i,n){n=n||{};var a=!1,o=this;M(t,function(l,u){if(!(n.visualType&&n.visualType!==l.visualType)){var f=o._stageTaskMap.get(l.uid),h=f.seriesTaskMap,v=f.overallTask;if(v){var c,d=v.agentStubMap;d.each(function(p){s(n,p)&&(p.dirty(),c=!0)}),c&&v.dirty(),o.updatePayload(v,i);var y=o.getPerformArgs(v,n.block);d.each(function(p){p.perform(y)}),v.perform(y)&&(a=!0)}else h&&h.each(function(p,g){s(n,p)&&p.dirty();var m=o.getPerformArgs(p,n.block);m.skip=!l.performRawSeries&&e.isSeriesFiltered(p.context.model),o.updatePayload(p,i),p.perform(m)&&(a=!0)})}});function s(l,u){return l.setDirty&&(!l.dirtyMap||l.dirtyMap.get(u.__pipeline.id))}this.unfinished=a||this.unfinished},r.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(i){e=i.dataTask.perform()||e}),this.unfinished=e||this.unfinished},r.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})},r.prototype.updatePayload=function(t,e){e!=="remain"&&(t.context.payload=e)},r.prototype._createSeriesStageTask=function(t,e,i,n){var a=this,o=e.seriesTaskMap,s=e.seriesTaskMap=X(),l=t.seriesType,u=t.getTargetSeries;t.createOnAllSeries?i.eachRawSeries(f):l?i.eachRawSeriesByType(l,f):u&&u(i,n).each(f);function f(h){var v=h.uid,c=s.set(v,o&&o.get(v)||Ln({plan:Dx,reset:Mx,count:Lx}));c.context={model:h,ecModel:i,api:n,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:a},a._pipe(h,c)}},r.prototype._createOverallStageTask=function(t,e,i,n){var a=this,o=e.overallTask=e.overallTask||Ln({reset:bx});o.context={ecModel:i,api:n,overallReset:t.overallReset,scheduler:a};var s=o.agentStubMap,l=o.agentStubMap=X(),u=t.seriesType,f=t.getTargetSeries,h=!0,v=!1,c="";Ge(!t.createOnAllSeries,c),u?i.eachRawSeriesByType(u,d):f?f(i,n).each(d):(h=!1,M(i.getSeries(),d));function d(y){var p=y.uid,g=l.set(p,s&&s.get(p)||(v=!0,Ln({reset:xx,onDirty:Cx})));g.context={model:y,overallProgress:h},g.agent=o,g.__block=h,a._pipe(y,g)}v&&o.dirty()},r.prototype._pipe=function(t,e){var i=t.uid,n=this._pipelineMap.get(i);!n.head&&(n.head=e),n.tail&&n.tail.pipe(e),n.tail=e,e.__idxInPipeline=n.count++,e.__pipeline=n},r.wrapStageHandler=function(t,e){return U(t)&&(t={overallReset:t,seriesType:Ix(t)}),t.uid=Go("stageHandler"),e&&(t.visualType=e),t},r}();function bx(r){r.overallReset(r.ecModel,r.api,r.payload)}function xx(r){return r.overallProgress&&Tx}function Tx(){this.agent.dirty(),this.getDownstream().dirty()}function Cx(){this.agent&&this.agent.dirty()}function Dx(r){return r.plan?r.plan(r.model,r.ecModel,r.api,r.payload):null}function Mx(r){r.useClearVisual&&r.data.clearAllVisual();var t=r.resetDefines=Nt(r.reset(r.model,r.ecModel,r.api,r.payload));return t.length>1?V(t,function(e,i){return Vg(i)}):Ax}var Ax=Vg(0);function Vg(r){return function(t,e){var i=e.data,n=e.resetDefines[r];if(n&&n.dataEach)for(var a=t.start;a<t.end;a++)n.dataEach(i,a);else n&&n.progress&&n.progress(t,i)}}function Lx(r){return r.data.count()}function Ix(r){To=null;try{r($n,Wg)}catch{}return To}var $n={},Wg={},To;Ug($n,mg);Ug(Wg,_g);$n.eachSeriesByType=$n.eachRawSeriesByType=function(r){To=r};$n.eachComponent=function(r){r.mainType==="series"&&r.subType&&(To=r.subType)};function Ug(r,t){for(var e in t.prototype)r[e]=$t}const $g=Sx;var Wc=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"];const Px={color:Wc,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],Wc]};var Lt="#B9B8CE",Uc="#100C2A",Ia=function(){return{axisLine:{lineStyle:{color:Lt}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},$c=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],Yg={darkMode:!0,color:$c,backgroundColor:Uc,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:Lt},pageTextStyle:{color:Lt}},textStyle:{color:Lt},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:Lt}},dataZoom:{borderColor:"#71708A",textStyle:{color:Lt},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:Lt}},timeline:{lineStyle:{color:Lt},label:{color:Lt},controlStyle:{color:Lt,borderColor:Lt}},calendar:{itemStyle:{color:Uc},dayLabel:{color:Lt},monthLabel:{color:Lt},yearLabel:{color:Lt}},timeAxis:Ia(),logAxis:Ia(),valueAxis:Ia(),categoryAxis:Ia(),line:{symbol:"circle"},graph:{color:$c},gauge:{title:{color:Lt},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:Lt},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};Yg.categoryAxis.splitLine.show=!1;const Rx=Yg;var Ex=function(){function r(){}return r.prototype.normalizeQuery=function(t){var e={},i={},n={};if(z(t)){var a=De(t);e.mainType=a.main||null,e.subType=a.sub||null}else{var o=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1};M(t,function(l,u){for(var f=!1,h=0;h<o.length;h++){var v=o[h],c=u.lastIndexOf(v);if(c>0&&c===u.length-v.length){var d=u.slice(0,c);d!=="data"&&(e.mainType=d,e[v.toLowerCase()]=l,f=!0)}}s.hasOwnProperty(u)&&(i[u]=l,f=!0),f||(n[u]=l)})}return{cptQuery:e,dataQuery:i,otherQuery:n}},r.prototype.filter=function(t,e){var i=this.eventInfo;if(!i)return!0;var n=i.targetEl,a=i.packedEvent,o=i.model,s=i.view;if(!o||!s)return!0;var l=e.cptQuery,u=e.dataQuery;return f(l,o,"mainType")&&f(l,o,"subType")&&f(l,o,"index","componentIndex")&&f(l,o,"name")&&f(l,o,"id")&&f(u,a,"name")&&f(u,a,"dataIndex")&&f(u,a,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,n,a));function f(h,v,c,d){return h[c]==null||v[d||c]===h[c]}},r.prototype.afterTrigger=function(){this.eventInfo=null},r}(),iu=["symbol","symbolSize","symbolRotate","symbolOffset"],Yc=iu.concat(["symbolKeepAspect"]),kx={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData();if(r.legendIcon&&e.setVisual("legendIcon",r.legendIcon),!r.hasSymbolVisual)return;for(var i={},n={},a=!1,o=0;o<iu.length;o++){var s=iu[o],l=r.get(s);U(l)?(a=!0,n[s]=l):i[s]=l}if(i.symbol=i.symbol||r.defaultSymbol,e.setVisual(k({legendIcon:r.legendIcon||i.symbol,symbolKeepAspect:r.get("symbolKeepAspect")},i)),t.isSeriesFiltered(r))return;var u=gt(n);function f(h,v){for(var c=r.getRawValue(v),d=r.getDataParams(v),y=0;y<u.length;y++){var p=u[y];h.setItemVisual(v,p,n[p](c,d))}}return{dataEach:a?f:null}}},Ox={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){if(!r.hasSymbolVisual||t.isSeriesFiltered(r))return;var e=r.getData();function i(n,a){for(var o=n.getItemModel(a),s=0;s<Yc.length;s++){var l=Yc[s],u=o.getShallow(l,!0);u!=null&&n.setItemVisual(a,l,u)}}return{dataEach:e.hasItemOption?i:null}}};function Bx(r,t,e){switch(e){case"color":var i=r.getItemVisual(t,"style");return i[r.getVisual("drawType")];case"opacity":return r.getItemVisual(t,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getItemVisual(t,e)}}function Nx(r,t){switch(t){case"color":var e=r.getVisual("style");return e[r.getVisual("drawType")];case"opacity":return r.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getVisual(t)}}function gi(r,t,e,i,n){var a=r+t;e.isSilent(a)||i.eachComponent({mainType:"series",subType:"pie"},function(o){for(var s=o.seriesIndex,l=o.option.selectedMap,u=n.selected,f=0;f<u.length;f++)if(u[f].seriesIndex===s){var h=o.getData(),v=Qr(h,n.fromActionPayload);e.trigger(a,{type:a,seriesId:o.id,name:N(v)?h.getName(v[0]):h.getName(v),selected:z(l)?l:k({},l)})}})}function Fx(r,t,e){r.on("selectchanged",function(i){var n=e.getModel();i.isFromClick?(gi("map","selectchanged",t,n,i),gi("pie","selectchanged",t,n,i)):i.fromAction==="select"?(gi("map","selected",t,n,i),gi("pie","selected",t,n,i)):i.fromAction==="unselect"&&(gi("map","unselected",t,n,i),gi("pie","unselected",t,n,i))})}function _n(r,t,e){for(var i;r&&!(t(r)&&(i=r,e));)r=r.__hostTarget||r.parent;return i}var zx=Math.round(Math.random()*9),Hx=typeof Object.defineProperty=="function",Gx=function(){function r(){this._id="__ec_inner_"+zx++}return r.prototype.get=function(t){return this._guard(t)[this._id]},r.prototype.set=function(t,e){var i=this._guard(t);return Hx?Object.defineProperty(i,this._id,{value:e,enumerable:!1,configurable:!0}):i[this._id]=e,this},r.prototype.delete=function(t){return this.has(t)?(delete this._guard(t)[this._id],!0):!1},r.prototype.has=function(t){return!!this._guard(t)[this._id]},r.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},r}();const Vx=Gx;var Wx=ft.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,t){var e=t.cx,i=t.cy,n=t.width/2,a=t.height/2;r.moveTo(e,i-a),r.lineTo(e+n,i+a),r.lineTo(e-n,i+a),r.closePath()}}),Ux=ft.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,t){var e=t.cx,i=t.cy,n=t.width/2,a=t.height/2;r.moveTo(e,i-a),r.lineTo(e+n,i),r.lineTo(e,i+a),r.lineTo(e-n,i),r.closePath()}}),$x=ft.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,t){var e=t.x,i=t.y,n=t.width/5*3,a=Math.max(n,t.height),o=n/2,s=o*o/(a-o),l=i-a+o+s,u=Math.asin(s/o),f=Math.cos(u)*o,h=Math.sin(u),v=Math.cos(u),c=o*.6,d=o*.7;r.moveTo(e-f,l+s),r.arc(e,l,o,Math.PI-u,Math.PI*2+u),r.bezierCurveTo(e+f-h*c,l+s+v*c,e,i-d,e,i),r.bezierCurveTo(e,i-d,e-f+h*c,l+s+v*c,e-f,l+s),r.closePath()}}),Yx=ft.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,t){var e=t.height,i=t.width,n=t.x,a=t.y,o=i/3*2;r.moveTo(n,a),r.lineTo(n+o,a+e),r.lineTo(n,a+e/4*3),r.lineTo(n-o,a+e),r.lineTo(n,a),r.closePath()}}),Xx={line:jr,rect:Ct,roundRect:Ct,square:Ct,circle:Yu,diamond:Ux,pin:$x,arrow:Yx,triangle:Wx},Zx={line:function(r,t,e,i,n){n.x1=r,n.y1=t+i/2,n.x2=r+e,n.y2=t+i/2},rect:function(r,t,e,i,n){n.x=r,n.y=t,n.width=e,n.height=i},roundRect:function(r,t,e,i,n){n.x=r,n.y=t,n.width=e,n.height=i,n.r=Math.min(e,i)/4},square:function(r,t,e,i,n){var a=Math.min(e,i);n.x=r,n.y=t,n.width=a,n.height=a},circle:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.r=Math.min(e,i)/2},diamond:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.width=e,n.height=i},pin:function(r,t,e,i,n){n.x=r+e/2,n.y=t+i/2,n.width=e,n.height=i},arrow:function(r,t,e,i,n){n.x=r+e/2,n.y=t+i/2,n.width=e,n.height=i},triangle:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.width=e,n.height=i}},nu={};M(Xx,function(r,t){nu[t]=new r});var qx=ft.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(r,t,e){var i=Hd(r,t,e),n=this.shape;return n&&n.symbolType==="pin"&&t.position==="inside"&&(i.y=e.y+e.height*.4),i},buildPath:function(r,t,e){var i=t.symbolType;if(i!=="none"){var n=nu[i];n||(i="rect",n=nu[i]),Zx[i](t.x,t.y,t.width,t.height,n.shape),n.buildPath(r,n.shape,e)}}});function Kx(r,t){if(this.type!=="image"){var e=this.style;this.__isEmptyBrush?(e.stroke=r,e.fill=t||"#fff",e.lineWidth=2):this.shape.symbolType==="line"?e.stroke=r:e.fill=r,this.markRedraw()}}function Bi(r,t,e,i,n,a,o){var s=r.indexOf("empty")===0;s&&(r=r.substr(5,1).toLowerCase()+r.substr(6));var l;return r.indexOf("image://")===0?l=Up(r.slice(8),new rt(t,e,i,n),o?"center":"cover"):r.indexOf("path://")===0?l=Ku(r.slice(7),{},new rt(t,e,i,n),o?"center":"cover"):l=new qx({shape:{symbolType:r,x:t,y:e,width:i,height:n}}),l.__isEmptyBrush=s,l.setColor=Kx,a&&l.setColor(a),l}function Qx(r){return N(r)||(r=[+r,+r]),[r[0]||0,r[1]||0]}function Xg(r,t){if(r!=null)return N(r)||(r=[r,r]),[Bt(r[0],t[0])||0,Bt(Z(r[1],r[0]),t[1])||0]}function Vr(r){return isFinite(r)}function Jx(r,t,e){var i=t.x==null?0:t.x,n=t.x2==null?1:t.x2,a=t.y==null?0:t.y,o=t.y2==null?0:t.y2;t.global||(i=i*e.width+e.x,n=n*e.width+e.x,a=a*e.height+e.y,o=o*e.height+e.y),i=Vr(i)?i:0,n=Vr(n)?n:1,a=Vr(a)?a:0,o=Vr(o)?o:0;var s=r.createLinearGradient(i,a,n,o);return s}function jx(r,t,e){var i=e.width,n=e.height,a=Math.min(i,n),o=t.x==null?.5:t.x,s=t.y==null?.5:t.y,l=t.r==null?.5:t.r;t.global||(o=o*i+e.x,s=s*n+e.y,l=l*a),o=Vr(o)?o:.5,s=Vr(s)?s:.5,l=l>=0&&Vr(l)?l:.5;var u=r.createRadialGradient(o,s,0,o,s,l);return u}function au(r,t,e){for(var i=t.type==="radial"?jx(r,t,e):Jx(r,t,e),n=t.colorStops,a=0;a<n.length;a++)i.addColorStop(n[a].offset,n[a].color);return i}function tT(r,t){if(r===t||!r&&!t)return!1;if(!r||!t||r.length!==t.length)return!0;for(var e=0;e<r.length;e++)if(r[e]!==t[e])return!0;return!1}function Pa(r){return parseInt(r,10)}function Ra(r,t,e){var i=["width","height"][t],n=["clientWidth","clientHeight"][t],a=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(e[i]!=null&&e[i]!=="auto")return parseFloat(e[i]);var s=document.defaultView.getComputedStyle(r);return(r[n]||Pa(s[i])||Pa(r.style[i]))-(Pa(s[a])||0)-(Pa(s[o])||0)|0}function eT(r,t){return!r||r==="solid"||!(t>0)?null:r==="dashed"?[4*t,2*t]:r==="dotted"?[t]:yt(r)?[r]:N(r)?r:null}function Zg(r){var t=r.style,e=t.lineDash&&t.lineWidth>0&&eT(t.lineDash,t.lineWidth),i=t.lineDashOffset;if(e){var n=t.strokeNoScale&&r.getLineScale?r.getLineScale():1;n&&n!==1&&(e=V(e,function(a){return a/n}),i/=n)}return[e,i]}var rT=new Jr(!0);function Co(r){var t=r.stroke;return!(t==null||t==="none"||!(r.lineWidth>0))}function Xc(r){return typeof r=="string"&&r!=="none"}function Do(r){var t=r.fill;return t!=null&&t!=="none"}function Zc(r,t){if(t.fillOpacity!=null&&t.fillOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.fillOpacity*t.opacity,r.fill(),r.globalAlpha=e}else r.fill()}function qc(r,t){if(t.strokeOpacity!=null&&t.strokeOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.strokeOpacity*t.opacity,r.stroke(),r.globalAlpha=e}else r.stroke()}function ou(r,t,e){var i=ep(t.image,t.__image,e);if(ko(i)){var n=r.createPattern(i,t.repeat||"repeat");if(typeof DOMMatrix=="function"&&n&&n.setTransform){var a=new DOMMatrix;a.translateSelf(t.x||0,t.y||0),a.rotateSelf(0,0,(t.rotation||0)*e0),a.scaleSelf(t.scaleX||1,t.scaleY||1),n.setTransform(a)}return n}}function iT(r,t,e,i){var n,a=Co(e),o=Do(e),s=e.strokePercent,l=s<1,u=!t.path;(!t.silent||l)&&u&&t.createPathProxy();var f=t.path||rT,h=t.__dirty;if(!i){var v=e.fill,c=e.stroke,d=o&&!!v.colorStops,y=a&&!!c.colorStops,p=o&&!!v.image,g=a&&!!c.image,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0;(d||y)&&(w=t.getBoundingRect()),d&&(m=h?au(r,v,w):t.__canvasFillGradient,t.__canvasFillGradient=m),y&&(_=h?au(r,c,w):t.__canvasStrokeGradient,t.__canvasStrokeGradient=_),p&&(S=h||!t.__canvasFillPattern?ou(r,v,t):t.__canvasFillPattern,t.__canvasFillPattern=S),g&&(b=h||!t.__canvasStrokePattern?ou(r,c,t):t.__canvasStrokePattern,t.__canvasStrokePattern=S),d?r.fillStyle=m:p&&(S?r.fillStyle=S:o=!1),y?r.strokeStyle=_:g&&(b?r.strokeStyle=b:a=!1)}var x=t.getGlobalScale();f.setScale(x[0],x[1],t.segmentIgnoreThreshold);var A,C;r.setLineDash&&e.lineDash&&(n=Zg(t),A=n[0],C=n[1]);var D=!0;(u||h&Si)&&(f.setDPR(r.dpr),l?f.setContext(null):(f.setContext(r),D=!1),f.reset(),t.buildPath(f,t.shape,i),f.toStatic(),t.pathUpdated()),D&&f.rebuildPath(r,l?s:1),A&&(r.setLineDash(A),r.lineDashOffset=C),i||(e.strokeFirst?(a&&qc(r,e),o&&Zc(r,e)):(o&&Zc(r,e),a&&qc(r,e))),A&&r.setLineDash([])}function nT(r,t,e){var i=t.__image=ep(e.image,t.__image,t,t.onload);if(!(!i||!ko(i))){var n=e.x||0,a=e.y||0,o=t.getWidth(),s=t.getHeight(),l=i.width/i.height;if(o==null&&s!=null?o=s*l:s==null&&o!=null?s=o/l:o==null&&s==null&&(o=i.width,s=i.height),e.sWidth&&e.sHeight){var u=e.sx||0,f=e.sy||0;r.drawImage(i,u,f,e.sWidth,e.sHeight,n,a,o,s)}else if(e.sx&&e.sy){var u=e.sx,f=e.sy,h=o-u,v=s-f;r.drawImage(i,u,f,h,v,n,a,o,s)}else r.drawImage(i,n,a,o,s)}}function aT(r,t,e){var i,n=e.text;if(n!=null&&(n+=""),n){r.font=e.font||Zr,r.textAlign=e.textAlign,r.textBaseline=e.textBaseline;var a=void 0,o=void 0;r.setLineDash&&e.lineDash&&(i=Zg(t),a=i[0],o=i[1]),a&&(r.setLineDash(a),r.lineDashOffset=o),e.strokeFirst?(Co(e)&&r.strokeText(n,e.x,e.y),Do(e)&&r.fillText(n,e.x,e.y)):(Do(e)&&r.fillText(n,e.x,e.y),Co(e)&&r.strokeText(n,e.x,e.y)),a&&r.setLineDash([])}}var Kc=["shadowBlur","shadowOffsetX","shadowOffsetY"],Qc=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function qg(r,t,e,i,n){var a=!1;if(!i&&(e=e||{},t===e))return!1;if(i||t.opacity!==e.opacity){Ut(r,n),a=!0;var o=Math.max(Math.min(t.opacity,1),0);r.globalAlpha=isNaN(o)?Ur.opacity:o}(i||t.blend!==e.blend)&&(a||(Ut(r,n),a=!0),r.globalCompositeOperation=t.blend||Ur.blend);for(var s=0;s<Kc.length;s++){var l=Kc[s];(i||t[l]!==e[l])&&(a||(Ut(r,n),a=!0),r[l]=r.dpr*(t[l]||0))}return(i||t.shadowColor!==e.shadowColor)&&(a||(Ut(r,n),a=!0),r.shadowColor=t.shadowColor||Ur.shadowColor),a}function Jc(r,t,e,i,n){var a=Yn(t,n.inHover),o=i?null:e&&Yn(e,n.inHover)||{};if(a===o)return!1;var s=qg(r,a,o,i,n);if((i||a.fill!==o.fill)&&(s||(Ut(r,n),s=!0),Xc(a.fill)&&(r.fillStyle=a.fill)),(i||a.stroke!==o.stroke)&&(s||(Ut(r,n),s=!0),Xc(a.stroke)&&(r.strokeStyle=a.stroke)),(i||a.opacity!==o.opacity)&&(s||(Ut(r,n),s=!0),r.globalAlpha=a.opacity==null?1:a.opacity),t.hasStroke()){var l=a.lineWidth,u=l/(a.strokeNoScale&&t.getLineScale?t.getLineScale():1);r.lineWidth!==u&&(s||(Ut(r,n),s=!0),r.lineWidth=u)}for(var f=0;f<Qc.length;f++){var h=Qc[f],v=h[0];(i||a[v]!==o[v])&&(s||(Ut(r,n),s=!0),r[v]=a[v]||h[1])}return s}function oT(r,t,e,i,n){return qg(r,Yn(t,n.inHover),e&&Yn(e,n.inHover),i,n)}function Kg(r,t){var e=t.transform,i=r.dpr||1;e?r.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):r.setTransform(i,0,0,i,0,0)}function sT(r,t,e){for(var i=!1,n=0;n<r.length;n++){var a=r[n];i=i||a.isZeroArea(),Kg(t,a),t.beginPath(),a.buildPath(t,a.shape),t.clip()}e.allClipped=i}function lT(r,t){return r&&t?r[0]!==t[0]||r[1]!==t[1]||r[2]!==t[2]||r[3]!==t[3]||r[4]!==t[4]||r[5]!==t[5]:!(!r&&!t)}var jc=1,tv=2,ev=3,rv=4;function uT(r){var t=Do(r),e=Co(r);return!(r.lineDash||!(+t^+e)||t&&typeof r.fill!="string"||e&&typeof r.stroke!="string"||r.strokePercent<1||r.strokeOpacity<1||r.fillOpacity<1)}function Ut(r,t){t.batchFill&&r.fill(),t.batchStroke&&r.stroke(),t.batchFill="",t.batchStroke=""}function Yn(r,t){return t&&r.__hoverStyle||r.style}function Qg(r,t){Wr(r,t,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function Wr(r,t,e,i){var n=t.transform;if(!t.shouldBePainted(e.viewWidth,e.viewHeight,!1,!1)){t.__dirty&=~jt,t.__isRendered=!1;return}var a=t.__clipPaths,o=e.prevElClipPaths,s=!1,l=!1;if((!o||tT(a,o))&&(o&&o.length&&(Ut(r,e),r.restore(),l=s=!0,e.prevElClipPaths=null,e.allClipped=!1,e.prevEl=null),a&&a.length&&(Ut(r,e),r.save(),sT(a,r,e),s=!0),e.prevElClipPaths=a),e.allClipped){t.__isRendered=!1;return}t.beforeBrush&&t.beforeBrush(),t.innerBeforeBrush();var u=e.prevEl;u||(l=s=!0);var f=t instanceof ft&&t.autoBatch&&uT(t.style);s||lT(n,u.transform)?(Ut(r,e),Kg(r,t)):f||Ut(r,e);var h=Yn(t,e.inHover);t instanceof ft?(e.lastDrawType!==jc&&(l=!0,e.lastDrawType=jc),Jc(r,t,u,l,e),(!f||!e.batchFill&&!e.batchStroke)&&r.beginPath(),iT(r,t,h,f),f&&(e.batchFill=h.fill||"",e.batchStroke=h.stroke||"")):t instanceof Gl?(e.lastDrawType!==ev&&(l=!0,e.lastDrawType=ev),Jc(r,t,u,l,e),aT(r,t,h)):t instanceof ei?(e.lastDrawType!==tv&&(l=!0,e.lastDrawType=tv),oT(r,t,u,l,e),nT(r,t,h)):t.getTemporalDisplayables&&(e.lastDrawType!==rv&&(l=!0,e.lastDrawType=rv),fT(r,t,e)),f&&i&&Ut(r,e),t.innerAfterBrush(),t.afterBrush&&t.afterBrush(),e.prevEl=t,t.__dirty=0,t.__isRendered=!0}function fT(r,t,e){var i=t.getDisplayables(),n=t.getTemporalDisplayables();r.save();var a={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:e.viewWidth,viewHeight:e.viewHeight,inHover:e.inHover},o,s;for(o=t.getCursor(),s=i.length;o<s;o++){var l=i[o];l.beforeBrush&&l.beforeBrush(),l.innerBeforeBrush(),Wr(r,l,a,o===s-1),l.innerAfterBrush(),l.afterBrush&&l.afterBrush(),a.prevEl=l}for(var u=0,f=n.length;u<f;u++){var l=n[u];l.beforeBrush&&l.beforeBrush(),l.innerBeforeBrush(),Wr(r,l,a,u===f-1),l.innerAfterBrush(),l.afterBrush&&l.afterBrush(),a.prevEl=l}t.clearTemporalDisplayables(),t.notClear=!0,r.restore()}var Qs=new Vx,iv=new Jn(100),nv=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function su(r,t){if(r==="none")return null;var e=t.getDevicePixelRatio(),i=t.getZr(),n=i.painter.type==="svg";r.dirty&&Qs.delete(r);var a=Qs.get(r);if(a)return a;var o=ot(r,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});o.backgroundColor==="none"&&(o.backgroundColor=null);var s={repeat:"repeat"};return l(s),s.rotation=o.rotation,s.scaleX=s.scaleY=n?1:1/e,Qs.set(r,s),r.dirty=!1,s;function l(u){for(var f=[e],h=!0,v=0;v<nv.length;++v){var c=o[nv[v]];if(c!=null&&!N(c)&&!z(c)&&!yt(c)&&typeof c!="boolean"){h=!1;break}f.push(c)}var d;if(h){d=f.join(",")+(n?"-svg":"");var y=iv.get(d);y&&(n?u.svgElement=y:u.image=y)}var p=jg(o.dashArrayX),g=hT(o.dashArrayY),m=Jg(o.symbol),_=cT(p),S=ty(g),b=!n&&Ni.createCanvas(),w=n&&{tag:"g",attrs:{},key:"dcl",children:[]},x=C(),A;b&&(b.width=x.width*e,b.height=x.height*e,A=b.getContext("2d")),D(),h&&iv.put(d,b||w),u.image=b,u.svgElement=w,u.svgWidth=x.width,u.svgHeight=x.height;function C(){for(var T=1,L=0,P=_.length;L<P;++L)T=ph(T,_[L]);for(var I=1,L=0,P=m.length;L<P;++L)I=ph(I,m[L].length);T*=I;var R=S*_.length*m.length;return{width:Math.max(1,Math.min(T,o.maxTileWidth)),height:Math.max(1,Math.min(R,o.maxTileHeight))}}function D(){A&&(A.clearRect(0,0,b.width,b.height),o.backgroundColor&&(A.fillStyle=o.backgroundColor,A.fillRect(0,0,b.width,b.height)));for(var T=0,L=0;L<g.length;++L)T+=g[L];if(T<=0)return;for(var P=-S,I=0,R=0,E=0;P<x.height;){if(I%2===0){for(var G=R/2%m.length,B=0,F=0,$=0;B<x.width*2;){for(var it=0,L=0;L<p[E].length;++L)it+=p[E][L];if(it<=0)break;if(F%2===0){var J=(1-o.symbolSize)*.5,st=B+p[E][F]*J,ht=P+g[I]*J,ct=p[E][F]*o.symbolSize,qt=g[I]*o.symbolSize,Re=$/2%m[G].length;vt(st,ht,ct,qt,m[G][Re])}B+=p[E][F],++$,++F,F===p[E].length&&(F=0)}++E,E===p.length&&(E=0)}P+=g[I],++R,++I,I===g.length&&(I=0)}function vt(Dt,xt,W,q,dr){var Et=n?1:e,If=Bi(dr,Dt*Et,xt*Et,W*Et,q*Et,o.color,o.symbolKeepAspect);if(n){var Pf=i.painter.renderOneToVNode(If);Pf&&w.children.push(Pf)}else Qg(A,If)}}}}function Jg(r){if(!r||r.length===0)return[["rect"]];if(z(r))return[[r]];for(var t=!0,e=0;e<r.length;++e)if(!z(r[e])){t=!1;break}if(t)return Jg([r]);for(var i=[],e=0;e<r.length;++e)z(r[e])?i.push([r[e]]):i.push(r[e]);return i}function jg(r){if(!r||r.length===0)return[[0,0]];if(yt(r)){var t=Math.ceil(r);return[[t,t]]}for(var e=!0,i=0;i<r.length;++i)if(!yt(r[i])){e=!1;break}if(e)return jg([r]);for(var n=[],i=0;i<r.length;++i)if(yt(r[i])){var t=Math.ceil(r[i]);n.push([t,t])}else{var t=V(r[i],function(s){return Math.ceil(s)});t.length%2===1?n.push(t.concat(t)):n.push(t)}return n}function hT(r){if(!r||typeof r=="object"&&r.length===0)return[0,0];if(yt(r)){var t=Math.ceil(r);return[t,t]}var e=V(r,function(i){return Math.ceil(i)});return r.length%2?e.concat(e):e}function cT(r){return V(r,function(t){return ty(t)})}function ty(r){for(var t=0,e=0;e<r.length;++e)t+=r[e];return r.length%2===1?t*2:t}function vT(r,t){r.eachRawSeries(function(e){if(!r.isSeriesFiltered(e)){var i=e.getData();i.hasItemVisual()&&i.each(function(o){var s=i.getItemVisual(o,"decal");if(s){var l=i.ensureUniqueItemVisual(o,"style");l.decal=su(s,t)}});var n=i.getVisual("decal");if(n){var a=i.getVisual("style");a.decal=su(n,t)}}})}var dT=new Ie;const ye=dT;var ey={};function pT(r,t){ey[r]=t}function gT(r){return ey[r]}var yT=1,mT=800,_T=900,ST=1e3,wT=2e3,bT=5e3,ry=1e3,xT=1100,mf=2e3,iy=3e3,TT=4e3,Jo=4500,CT=4600,DT=5e3,MT=6e3,ny=7e3,AT={PROCESSOR:{FILTER:ST,SERIES_FILTER:mT,STATISTIC:bT},VISUAL:{LAYOUT:ry,PROGRESSIVE_LAYOUT:xT,GLOBAL:mf,CHART:iy,POST_CHART_LAYOUT:CT,COMPONENT:TT,BRUSH:DT,CHART_ITEM:Jo,ARIA:MT,DECAL:ny}},At="__flagInMainProcess",Gt="__pendingUpdate",Js="__needsUpdateStatus",av=/^[a-zA-Z0-9_]+$/,js="__connectUpdateStatus",ov=0,LT=1,IT=2;function ay(r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(this.isDisposed()){this.id;return}return sy(this,r,t)}}function oy(r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return sy(this,r,t)}}function sy(r,t,e){return e[0]=e[0]&&e[0].toLowerCase(),Ie.prototype[t].apply(r,e)}var ly=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t}(Ie),uy=ly.prototype;uy.on=oy("on");uy.off=oy("off");var yi,tl,Ea,Qe,el,rl,il,rn,nn,sv,lv,nl,uv,ka,fv,fy,ee,hv,hy=function(r){O(t,r);function t(e,i,n){var a=r.call(this,new Ex)||this;a._chartsViews=[],a._chartsMap={},a._componentsViews=[],a._componentsMap={},a._pendingActions=[],n=n||{},z(i)&&(i=cy[i]),a._dom=e;var o="canvas",s="auto",l=!1;n.ssr;var u=a._zr=ch(e,{renderer:n.renderer||o,devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height,ssr:n.ssr,useDirtyRect:Z(n.useDirtyRect,l),useCoarsePointer:Z(n.useCoarsePointer,s),pointerSize:n.pointerSize});a._ssr=n.ssr,a._throttledZrFlush=yf(pt(u.flush,u),17),i=Q(i),i&&wg(i,!0),a._theme=i,a._locale=kw(n.locale||Jp),a._coordSysMgr=new ff;var f=a._api=fv(a);function h(v,c){return v.__prio-c.__prio}return $a(Ao,h),$a(lu,h),a._scheduler=new $g(a,f,lu,Ao),a._messageCenter=new ly,a._initEvents(),a.resize=pt(a.resize,a),u.animation.on("frame",a._onframe,a),sv(u,a),lv(u,a),wl(a),a}return t.prototype._onframe=function(){if(!this._disposed){hv(this);var e=this._scheduler;if(this[Gt]){var i=this[Gt].silent;this[At]=!0;try{yi(this),Qe.update.call(this,null,this[Gt].updateParams)}catch(l){throw this[At]=!1,this[Gt]=null,l}this._zr.flush(),this[At]=!1,this[Gt]=null,rn.call(this,i),nn.call(this,i)}else if(e.unfinished){var n=yT,a=this._model,o=this._api;e.unfinished=!1;do{var s=+new Date;e.performSeriesTasks(a),e.performDataProcessorTasks(a),rl(this,a),e.performVisualTasks(a),ka(this,this._model,o,"remain",{}),n-=+new Date-s}while(n>0&&e.unfinished);e.unfinished||this._zr.flush()}}},t.prototype.getDom=function(){return this._dom},t.prototype.getId=function(){return this.id},t.prototype.getZr=function(){return this._zr},t.prototype.isSSR=function(){return this._ssr},t.prototype.setOption=function(e,i,n){if(!this[At]){if(this._disposed){this.id;return}var a,o,s;if(H(i)&&(n=i.lazyUpdate,a=i.silent,o=i.replaceMerge,s=i.transition,i=i.notMerge),this[At]=!0,!this._model||i){var l=new db(this._api),u=this._theme,f=this._model=new mg;f.scheduler=this._scheduler,f.ssr=this._ssr,f.init(null,null,null,u,this._locale,l)}this._model.setOption(e,{replaceMerge:o},uu);var h={seriesTransition:s,optionChanged:!0};if(n)this[Gt]={silent:a,updateParams:h},this[At]=!1,this.getZr().wakeUp();else{try{yi(this),Qe.update.call(this,null,h)}catch(v){throw this[Gt]=null,this[At]=!1,v}this._ssr||this._zr.flush(),this[Gt]=null,this[At]=!1,rn.call(this,a),nn.call(this,a)}}},t.prototype.setTheme=function(){},t.prototype.getModel=function(){return this._model},t.prototype.getOption=function(){return this._model&&this._model.getOption()},t.prototype.getWidth=function(){return this._zr.getWidth()},t.prototype.getHeight=function(){return this._zr.getHeight()},t.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||Y.hasGlobalWindow&&window.devicePixelRatio||1},t.prototype.getRenderedCanvas=function(e){return this.renderToCanvas(e)},t.prototype.renderToCanvas=function(e){e=e||{};var i=this._zr.painter;return i.getRenderedCanvas({backgroundColor:e.backgroundColor||this._model.get("backgroundColor"),pixelRatio:e.pixelRatio||this.getDevicePixelRatio()})},t.prototype.renderToSVGString=function(e){e=e||{};var i=this._zr.painter;return i.renderToString({useViewBox:e.useViewBox})},t.prototype.getSvgDataURL=function(){if(Y.svgSupported){var e=this._zr,i=e.storage.getDisplayList();return M(i,function(n){n.stopAnimation(null,!0)}),e.painter.toDataURL()}},t.prototype.getDataURL=function(e){if(this._disposed){this.id;return}e=e||{};var i=e.excludeComponents,n=this._model,a=[],o=this;M(i,function(l){n.eachComponent({mainType:l},function(u){var f=o._componentsMap[u.__viewId];f.group.ignore||(a.push(f),f.group.ignore=!0)})});var s=this._zr.painter.getType()==="svg"?this.getSvgDataURL():this.renderToCanvas(e).toDataURL("image/"+(e&&e.type||"png"));return M(a,function(l){l.group.ignore=!1}),s},t.prototype.getConnectedDataURL=function(e){if(this._disposed){this.id;return}var i=e.type==="svg",n=this.group,a=Math.min,o=Math.max,s=1/0;if(cv[n]){var l=s,u=s,f=-s,h=-s,v=[],c=e&&e.pixelRatio||this.getDevicePixelRatio();M(Pn,function(_,S){if(_.group===n){var b=i?_.getZr().painter.getSvgDom().innerHTML:_.renderToCanvas(Q(e)),w=_.getDom().getBoundingClientRect();l=a(w.left,l),u=a(w.top,u),f=o(w.right,f),h=o(w.bottom,h),v.push({dom:b,left:w.left,top:w.top})}}),l*=c,u*=c,f*=c,h*=c;var d=f-l,y=h-u,p=Ni.createCanvas(),g=ch(p,{renderer:i?"svg":"canvas"});if(g.resize({width:d,height:y}),i){var m="";return M(v,function(_){var S=_.left-l,b=_.top-u;m+='<g transform="translate('+S+","+b+')">'+_.dom+"</g>"}),g.painter.getSvgRoot().innerHTML=m,e.connectedBackgroundColor&&g.painter.setBackgroundColor(e.connectedBackgroundColor),g.refreshImmediately(),g.painter.toDataURL()}else return e.connectedBackgroundColor&&g.add(new Ct({shape:{x:0,y:0,width:d,height:y},style:{fill:e.connectedBackgroundColor}})),M(v,function(_){var S=new ei({style:{x:_.left*c-l,y:_.top*c-u,image:_.dom}});g.add(S)}),g.refreshImmediately(),p.toDataURL("image/"+(e&&e.type||"png"))}else return this.getDataURL(e)},t.prototype.convertToPixel=function(e,i){return el(this,"convertToPixel",e,i)},t.prototype.convertFromPixel=function(e,i){return el(this,"convertFromPixel",e,i)},t.prototype.containPixel=function(e,i){if(this._disposed){this.id;return}var n=this._model,a,o=Ts(n,e);return M(o,function(s,l){l.indexOf("Models")>=0&&M(s,function(u){var f=u.coordinateSystem;if(f&&f.containPoint)a=a||!!f.containPoint(i);else if(l==="seriesModels"){var h=this._chartsMap[u.__viewId];h&&h.containPoint&&(a=a||h.containPoint(i,u))}},this)},this),!!a},t.prototype.getVisual=function(e,i){var n=this._model,a=Ts(n,e,{defaultMainType:"series"}),o=a.seriesModel,s=o.getData(),l=a.hasOwnProperty("dataIndexInside")?a.dataIndexInside:a.hasOwnProperty("dataIndex")?s.indexOfRawIndex(a.dataIndex):null;return l!=null?Bx(s,l,i):Nx(s,i)},t.prototype.getViewOfComponentModel=function(e){return this._componentsMap[e.__viewId]},t.prototype.getViewOfSeriesModel=function(e){return this._chartsMap[e.__viewId]},t.prototype._initEvents=function(){var e=this;M(PT,function(i){var n=function(a){var o=e.getModel(),s=a.target,l,u=i==="globalout";if(u?l={}:s&&_n(s,function(d){var y=at(d);if(y&&y.dataIndex!=null){var p=y.dataModel||o.getSeriesByIndex(y.seriesIndex);return l=p&&p.getDataParams(y.dataIndex,y.dataType,s)||{},!0}else if(y.eventData)return l=k({},y.eventData),!0},!0),l){var f=l.componentType,h=l.componentIndex;(f==="markLine"||f==="markPoint"||f==="markArea")&&(f="series",h=l.seriesIndex);var v=f&&h!=null&&o.getComponent(f,h),c=v&&e[v.mainType==="series"?"_chartsMap":"_componentsMap"][v.__viewId];l.event=a,l.type=i,e._$eventProcessor.eventInfo={targetEl:s,packedEvent:l,model:v,view:c},e.trigger(i,l)}};n.zrEventfulCallAtLast=!0,e._zr.on(i,n,e)}),M(In,function(i,n){e._messageCenter.on(n,function(a){this.trigger(n,a)},e)}),M(["selectchanged"],function(i){e._messageCenter.on(i,function(n){this.trigger(i,n)},e)}),Fx(this._messageCenter,this,this._api)},t.prototype.isDisposed=function(){return this._disposed},t.prototype.clear=function(){if(this._disposed){this.id;return}this.setOption({series:[]},!0)},t.prototype.dispose=function(){if(this._disposed){this.id;return}this._disposed=!0;var e=this.getDom();e&&Jd(this.getDom(),Sf,"");var i=this,n=i._api,a=i._model;M(i._componentsViews,function(o){o.dispose(a,n)}),M(i._chartsViews,function(o){o.dispose(a,n)}),i._zr.dispose(),i._dom=i._model=i._chartsMap=i._componentsMap=i._chartsViews=i._componentsViews=i._scheduler=i._api=i._zr=i._throttledZrFlush=i._theme=i._coordSysMgr=i._messageCenter=null,delete Pn[i.id]},t.prototype.resize=function(e){if(!this[At]){if(this._disposed){this.id;return}this._zr.resize(e);var i=this._model;if(this._loadingFX&&this._loadingFX.resize(),!!i){var n=i.resetOption("media"),a=e&&e.silent;this[Gt]&&(a==null&&(a=this[Gt].silent),n=!0,this[Gt]=null),this[At]=!0;try{n&&yi(this),Qe.update.call(this,{type:"resize",animation:k({duration:0},e&&e.animation)})}catch(o){throw this[At]=!1,o}this[At]=!1,rn.call(this,a),nn.call(this,a)}}},t.prototype.showLoading=function(e,i){if(this._disposed){this.id;return}if(H(e)&&(i=e,e=""),e=e||"default",this.hideLoading(),!!fu[e]){var n=fu[e](this._api,i),a=this._zr;this._loadingFX=n,a.add(n)}},t.prototype.hideLoading=function(){if(this._disposed){this.id;return}this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},t.prototype.makeActionFromEvent=function(e){var i=k({},e);return i.type=In[e.type],i},t.prototype.dispatchAction=function(e,i){if(this._disposed){this.id;return}if(H(i)||(i={silent:!!i}),!!Mo[e.type]&&this._model){if(this[At]){this._pendingActions.push(e);return}var n=i.silent;il.call(this,e,n);var a=i.flush;a?this._zr.flush():a!==!1&&Y.browser.weChat&&this._throttledZrFlush(),rn.call(this,n),nn.call(this,n)}},t.prototype.updateLabelLayout=function(){ye.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},t.prototype.appendData=function(e){if(this._disposed){this.id;return}var i=e.seriesIndex,n=this.getModel(),a=n.getSeriesByIndex(i);a.appendData(e),this._scheduler.unfinished=!0,this.getZr().wakeUp()},t.internalField=function(){yi=function(h){var v=h._scheduler;v.restorePipelines(h._model),v.prepareStageTasks(),tl(h,!0),tl(h,!1),v.plan()},tl=function(h,v){for(var c=h._model,d=h._scheduler,y=v?h._componentsViews:h._chartsViews,p=v?h._componentsMap:h._chartsMap,g=h._zr,m=h._api,_=0;_<y.length;_++)y[_].__alive=!1;v?c.eachComponent(function(w,x){w!=="series"&&S(x)}):c.eachSeries(S);function S(w){var x=w.__requireNewView;w.__requireNewView=!1;var A="_ec_"+w.id+"_"+w.type,C=!x&&p[A];if(!C){var D=De(w.type),T=v?We.getClass(D.main,D.sub):fr.getClass(D.sub);C=new T,C.init(c,m),p[A]=C,y.push(C),g.add(C.group)}w.__viewId=C.__id=A,C.__alive=!0,C.__model=w,C.group.__ecComponentInfo={mainType:w.mainType,index:w.componentIndex},!v&&d.prepareView(C,w,c,m)}for(var _=0;_<y.length;){var b=y[_];b.__alive?_++:(!v&&b.renderTask.dispose(),g.remove(b.group),b.dispose(c,m),y.splice(_,1),p[b.__id]===b&&delete p[b.__id],b.__id=b.group.__ecComponentInfo=null)}},Ea=function(h,v,c,d,y){var p=h._model;if(p.setUpdatePayload(c),!d){M([].concat(h._componentsViews).concat(h._chartsViews),b);return}var g={};g[d+"Id"]=c[d+"Id"],g[d+"Index"]=c[d+"Index"],g[d+"Name"]=c[d+"Name"];var m={mainType:d,query:g};y&&(m.subType=y);var _=c.excludeSeriesId,S;_!=null&&(S=X(),M(Nt(_),function(w){var x=Me(w,null);x!=null&&S.set(x,!0)})),p&&p.eachComponent(m,function(w){var x=S&&S.get(w.id)!=null;if(!x)if($h(c))if(w instanceof Un)c.type===$r&&!c.notBlur&&!w.get(["emphasis","disabled"])&&lS(w,c,h._api);else{var A=$u(w.mainType,w.componentIndex,c.name,h._api),C=A.focusSelf,D=A.dispatchers;c.type===$r&&C&&!c.notBlur&&Wl(w.mainType,w.componentIndex,h._api),D&&M(D,function(T){c.type===$r?go(T):yo(T)})}else Xl(c)&&w instanceof Un&&(hS(w,c,h._api),Vh(w),ee(h))},h),p&&p.eachComponent(m,function(w){var x=S&&S.get(w.id)!=null;x||b(h[d==="series"?"_chartsMap":"_componentsMap"][w.__viewId])},h);function b(w){w&&w.__alive&&w[v]&&w[v](w.__model,p,h._api,c)}},Qe={prepareAndUpdate:function(h){yi(this),Qe.update.call(this,h,{optionChanged:h.newOption!=null})},update:function(h,v){var c=this._model,d=this._api,y=this._zr,p=this._coordSysMgr,g=this._scheduler;if(c){c.setUpdatePayload(h),g.restoreData(c,h),g.performSeriesTasks(c),p.create(c,d),g.performDataProcessorTasks(c,h),rl(this,c),p.update(c,d),e(c),g.performVisualTasks(c,h),nl(this,c,d,h,v);var m=c.get("backgroundColor")||"transparent",_=c.get("darkMode");y.setBackgroundColor(m),_!=null&&_!=="auto"&&y.setDarkMode(_),ye.trigger("afterupdate",c,d)}},updateTransform:function(h){var v=this,c=this._model,d=this._api;if(c){c.setUpdatePayload(h);var y=[];c.eachComponent(function(g,m){if(g!=="series"){var _=v.getViewOfComponentModel(m);if(_&&_.__alive)if(_.updateTransform){var S=_.updateTransform(m,c,d,h);S&&S.update&&y.push(_)}else y.push(_)}});var p=X();c.eachSeries(function(g){var m=v._chartsMap[g.__viewId];if(m.updateTransform){var _=m.updateTransform(g,c,d,h);_&&_.update&&p.set(g.uid,1)}else p.set(g.uid,1)}),e(c),this._scheduler.performVisualTasks(c,h,{setDirty:!0,dirtyMap:p}),ka(this,c,d,h,{},p),ye.trigger("afterupdate",c,d)}},updateView:function(h){var v=this._model;v&&(v.setUpdatePayload(h),fr.markUpdateMethod(h,"updateView"),e(v),this._scheduler.performVisualTasks(v,h,{setDirty:!0}),nl(this,v,this._api,h,{}),ye.trigger("afterupdate",v,this._api))},updateVisual:function(h){var v=this,c=this._model;c&&(c.setUpdatePayload(h),c.eachSeries(function(d){d.getData().clearAllVisual()}),fr.markUpdateMethod(h,"updateVisual"),e(c),this._scheduler.performVisualTasks(c,h,{visualType:"visual",setDirty:!0}),c.eachComponent(function(d,y){if(d!=="series"){var p=v.getViewOfComponentModel(y);p&&p.__alive&&p.updateVisual(y,c,v._api,h)}}),c.eachSeries(function(d){var y=v._chartsMap[d.__viewId];y.updateVisual(d,c,v._api,h)}),ye.trigger("afterupdate",c,this._api))},updateLayout:function(h){Qe.update.call(this,h)}},el=function(h,v,c,d){if(h._disposed){h.id;return}for(var y=h._model,p=h._coordSysMgr.getCoordinateSystems(),g,m=Ts(y,c),_=0;_<p.length;_++){var S=p[_];if(S[v]&&(g=S[v](y,m,d))!=null)return g}},rl=function(h,v){var c=h._chartsMap,d=h._scheduler;v.eachSeries(function(y){d.updateStreamModes(y,c[y.__viewId])})},il=function(h,v){var c=this,d=this.getModel(),y=h.type,p=h.escapeConnect,g=Mo[y],m=g.actionInfo,_=(m.update||"update").split(":"),S=_.pop(),b=_[0]!=null&&De(_[0]);this[At]=!0;var w=[h],x=!1;h.batch&&(x=!0,w=V(h.batch,function(I){return I=ot(k({},I),h),I.batch=null,I}));var A=[],C,D=Xl(h),T=$h(h);if(T&&wp(this._api),M(w,function(I){if(C=g.action(I,c._model,c._api),C=C||k({},I),C.type=m.event||C.type,A.push(C),T){var R=Hu(h),E=R.queryOptionMap,G=R.mainTypeSpecified,B=G?E.keys()[0]:"series";Ea(c,S,I,B),ee(c)}else D?(Ea(c,S,I,"series"),ee(c)):b&&Ea(c,S,I,b.main,b.sub)}),S!=="none"&&!T&&!D&&!b)try{this[Gt]?(yi(this),Qe.update.call(this,h),this[Gt]=null):Qe[S].call(this,h)}catch(I){throw this[At]=!1,I}if(x?C={type:m.event||y,escapeConnect:p,batch:A}:C=A[0],this[At]=!1,!v){var L=this._messageCenter;if(L.trigger(C.type,C),D){var P={type:"selectchanged",escapeConnect:p,selected:cS(d),isFromClick:h.isFromClick||!1,fromAction:h.type,fromActionPayload:h};L.trigger(P.type,P)}}},rn=function(h){for(var v=this._pendingActions;v.length;){var c=v.shift();il.call(this,c,h)}},nn=function(h){!h&&this.trigger("updated")},sv=function(h,v){h.on("rendered",function(c){v.trigger("rendered",c),h.animation.isFinished()&&!v[Gt]&&!v._scheduler.unfinished&&!v._pendingActions.length&&v.trigger("finished")})},lv=function(h,v){h.on("mouseover",function(c){var d=c.target,y=_n(d,Yl);y&&(uS(y,c,v._api),ee(v))}).on("mouseout",function(c){var d=c.target,y=_n(d,Yl);y&&(fS(y,c,v._api),ee(v))}).on("click",function(c){var d=c.target,y=_n(d,function(m){return at(m).dataIndex!=null},!0);if(y){var p=y.selected?"unselect":"select",g=at(y);v._api.dispatchAction({type:p,dataType:g.dataType,dataIndexInside:g.dataIndex,seriesIndex:g.seriesIndex,isFromClick:!0})}})};function e(h){h.clearColorPalette(),h.eachSeries(function(v){v.clearColorPalette()})}function i(h){var v=[],c=[],d=!1;if(h.eachComponent(function(m,_){var S=_.get("zlevel")||0,b=_.get("z")||0,w=_.getZLevelKey();d=d||!!w,(m==="series"?c:v).push({zlevel:S,z:b,idx:_.componentIndex,type:m,key:w})}),d){var y=v.concat(c),p,g;$a(y,function(m,_){return m.zlevel===_.zlevel?m.z-_.z:m.zlevel-_.zlevel}),M(y,function(m){var _=h.getComponent(m.type,m.idx),S=m.zlevel,b=m.key;p!=null&&(S=Math.max(p,S)),b?(S===p&&b!==g&&S++,g=b):g&&(S===p&&S++,g=""),p=S,_.setZLevel(S)})}}nl=function(h,v,c,d,y){i(v),uv(h,v,c,d,y),M(h._chartsViews,function(p){p.__alive=!1}),ka(h,v,c,d,y),M(h._chartsViews,function(p){p.__alive||p.remove(v,c)})},uv=function(h,v,c,d,y,p){M(p||h._componentsViews,function(g){var m=g.__model;u(m,g),g.render(m,v,c,d),s(m,g),f(m,g)})},ka=function(h,v,c,d,y,p){var g=h._scheduler;y=k(y||{},{updatedSeries:v.getSeries()}),ye.trigger("series:beforeupdate",v,c,y);var m=!1;v.eachSeries(function(_){var S=h._chartsMap[_.__viewId];S.__alive=!0;var b=S.renderTask;g.updatePayload(b,d),u(_,S),p&&p.get(_.uid)&&b.dirty(),b.perform(g.getPerformArgs(b))&&(m=!0),S.group.silent=!!_.get("silent"),o(_,S),Vh(_)}),g.unfinished=m||g.unfinished,ye.trigger("series:layoutlabels",v,c,y),ye.trigger("series:transition",v,c,y),v.eachSeries(function(_){var S=h._chartsMap[_.__viewId];s(_,S),f(_,S)}),a(h,v),ye.trigger("series:afterupdate",v,c,y)},ee=function(h){h[Js]=!0,h.getZr().wakeUp()},hv=function(h){h[Js]&&(h.getZr().storage.traverse(function(v){Dn(v)||n(v)}),h[Js]=!1)};function n(h){for(var v=[],c=h.currentStates,d=0;d<c.length;d++){var y=c[d];y==="emphasis"||y==="blur"||y==="select"||v.push(y)}h.selected&&h.states.select&&v.push("select"),h.hoverState===No&&h.states.emphasis?v.push("emphasis"):h.hoverState===Bo&&h.states.blur&&v.push("blur"),h.useStates(v)}function a(h,v){var c=h._zr,d=c.storage,y=0;d.traverse(function(p){p.isGroup||y++}),y>v.get("hoverLayerThreshold")&&!Y.node&&!Y.worker&&v.eachSeries(function(p){if(!p.preventUsingHoverLayer){var g=h._chartsMap[p.__viewId];g.__alive&&g.eachRendered(function(m){m.states.emphasis&&(m.states.emphasis.hoverLayer=!0)})}})}function o(h,v){var c=h.get("blendMode")||null;v.eachRendered(function(d){d.isGroup||(d.style.blend=c)})}function s(h,v){if(!h.preventAutoZ){var c=h.get("z")||0,d=h.get("zlevel")||0;v.eachRendered(function(y){return l(y,c,d,-1/0),!0})}}function l(h,v,c,d){var y=h.getTextContent(),p=h.getTextGuideLine(),g=h.isGroup;if(g)for(var m=h.childrenRef(),_=0;_<m.length;_++)d=Math.max(l(m[_],v,c,d),d);else h.z=v,h.zlevel=c,d=Math.max(h.z2,d);if(y&&(y.z=v,y.zlevel=c,isFinite(d)&&(y.z2=d+2)),p){var S=h.textGuideLineConfig;p.z=v,p.zlevel=c,isFinite(d)&&(p.z2=d+(S&&S.showAbove?1:-1))}return d}function u(h,v){v.eachRendered(function(c){if(!Dn(c)){var d=c.getTextContent(),y=c.getTextGuideLine();c.stateTransition&&(c.stateTransition=null),d&&d.stateTransition&&(d.stateTransition=null),y&&y.stateTransition&&(y.stateTransition=null),c.hasState()?(c.prevStates=c.currentStates,c.clearStates()):c.prevStates&&(c.prevStates=null)}})}function f(h,v){var c=h.getModel("stateAnimation"),d=h.isAnimationEnabled(),y=c.get("duration"),p=y>0?{duration:y,delay:c.get("delay"),easing:c.get("easing")}:null;v.eachRendered(function(g){if(g.states&&g.states.emphasis){if(Dn(g))return;if(g instanceof ft&&yS(g),g.__dirty){var m=g.prevStates;m&&g.useStates(m)}if(d){g.stateTransition=p;var _=g.getTextContent(),S=g.getTextGuideLine();_&&(_.stateTransition=p),S&&(S.stateTransition=p)}g.__dirty&&n(g)}})}fv=function(h){return new(function(v){O(c,v);function c(){return v!==null&&v.apply(this,arguments)||this}return c.prototype.getCoordinateSystems=function(){return h._coordSysMgr.getCoordinateSystems()},c.prototype.getComponentByElement=function(d){for(;d;){var y=d.__ecComponentInfo;if(y!=null)return h._model.getComponent(y.mainType,y.index);d=d.parent}},c.prototype.enterEmphasis=function(d,y){go(d,y),ee(h)},c.prototype.leaveEmphasis=function(d,y){yo(d,y),ee(h)},c.prototype.enterBlur=function(d){sS(d),ee(h)},c.prototype.leaveBlur=function(d){yp(d),ee(h)},c.prototype.enterSelect=function(d){mp(d),ee(h)},c.prototype.leaveSelect=function(d){_p(d),ee(h)},c.prototype.getModel=function(){return h.getModel()},c.prototype.getViewOfComponentModel=function(d){return h.getViewOfComponentModel(d)},c.prototype.getViewOfSeriesModel=function(d){return h.getViewOfSeriesModel(d)},c}(_g))(h)},fy=function(h){function v(c,d){for(var y=0;y<c.length;y++){var p=c[y];p[js]=d}}M(In,function(c,d){h._messageCenter.on(d,function(y){if(cv[h.group]&&h[js]!==ov){if(y&&y.escapeConnect)return;var p=h.makeActionFromEvent(y),g=[];M(Pn,function(m){m!==h&&m.group===h.group&&g.push(m)}),v(g,ov),M(g,function(m){m[js]!==LT&&m.dispatchAction(p)}),v(g,IT)}})})}}(),t}(Ie),_f=hy.prototype;_f.on=ay("on");_f.off=ay("off");_f.one=function(r,t,e){var i=this;function n(){for(var a=[],o=0;o<arguments.length;o++)a[o]=arguments[o];t&&t.apply&&t.apply(this,a),i.off(r,n)}this.on.call(this,r,n,e)};var PT=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];var Mo={},In={},lu=[],uu=[],Ao=[],cy={},fu={},Pn={},cv={},RT=+new Date-0,Sf="_echarts_instance_";function ET(r,t,e){var i=!(e&&e.ssr);if(i){var n=kT(r);if(n)return n}var a=new hy(r,t,e);return a.id="ec_"+RT++,Pn[a.id]=a,i&&Jd(r,Sf,a.id),fy(a),ye.trigger("afterinit",a),a}function kT(r){return Pn[X_(r,Sf)]}function vy(r,t){cy[r]=t}function dy(r){lt(uu,r)<0&&uu.push(r)}function py(r,t){bf(lu,r,t,wT)}function OT(r){wf("afterinit",r)}function BT(r){wf("afterupdate",r)}function wf(r,t){ye.on(r,t)}function Vi(r,t,e){U(t)&&(e=t,t="");var i=H(r)?r.type:[r,r={event:t}][0];r.event=(r.event||i).toLowerCase(),t=r.event,!In[t]&&(Ge(av.test(i)&&av.test(t)),Mo[i]||(Mo[i]={action:e,actionInfo:r}),In[t]=i)}function NT(r,t){ff.register(r,t)}function FT(r,t){bf(Ao,r,t,ry,"layout")}function ii(r,t){bf(Ao,r,t,iy,"visual")}var vv=[];function bf(r,t,e,i,n){if((U(t)||H(t))&&(e=t,t=i),!(lt(vv,e)>=0)){vv.push(e);var a=$g.wrapStageHandler(e,n);a.__prio=t,a.__raw=e,r.push(a)}}function gy(r,t){fu[r]=t}function zT(r,t,e){var i=gT("registerMap");i&&i(r,t,e)}var HT=Hb;ii(mf,gx);ii(Jo,yx);ii(Jo,mx);ii(mf,kx);ii(Jo,Ox);ii(ny,vT);dy(wg);py(_T,bb);gy("default",_x);Vi({type:$r,event:$r,update:$r},$t);Vi({type:Ja,event:Ja,update:Ja},$t);Vi({type:xn,event:xn,update:xn},$t);Vi({type:ja,event:ja,update:ja},$t);Vi({type:Tn,event:Tn,update:Tn},$t);vy("light",Px);vy("dark",Rx);function an(r){return r==null?0:r.length||1}function dv(r){return r}var GT=function(){function r(t,e,i,n,a,o){this._old=t,this._new=e,this._oldKeyGetter=i||dv,this._newKeyGetter=n||dv,this.context=a,this._diffModeMultiple=o==="multiple"}return r.prototype.add=function(t){return this._add=t,this},r.prototype.update=function(t){return this._update=t,this},r.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},r.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},r.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},r.prototype.remove=function(t){return this._remove=t,this},r.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},r.prototype._executeOneToOne=function(){var t=this._old,e=this._new,i={},n=new Array(t.length),a=new Array(e.length);this._initIndexMap(t,null,n,"_oldKeyGetter"),this._initIndexMap(e,i,a,"_newKeyGetter");for(var o=0;o<t.length;o++){var s=n[o],l=i[s],u=an(l);if(u>1){var f=l.shift();l.length===1&&(i[s]=l[0]),this._update&&this._update(f,o)}else u===1?(i[s]=null,this._update&&this._update(l,o)):this._remove&&this._remove(o)}this._performRestAdd(a,i)},r.prototype._executeMultiple=function(){var t=this._old,e=this._new,i={},n={},a=[],o=[];this._initIndexMap(t,i,a,"_oldKeyGetter"),this._initIndexMap(e,n,o,"_newKeyGetter");for(var s=0;s<a.length;s++){var l=a[s],u=i[l],f=n[l],h=an(u),v=an(f);if(h>1&&v===1)this._updateManyToOne&&this._updateManyToOne(f,u),n[l]=null;else if(h===1&&v>1)this._updateOneToMany&&this._updateOneToMany(f,u),n[l]=null;else if(h===1&&v===1)this._update&&this._update(f,u),n[l]=null;else if(h>1&&v>1)this._updateManyToMany&&this._updateManyToMany(f,u),n[l]=null;else if(h>1)for(var c=0;c<h;c++)this._remove&&this._remove(u[c]);else this._remove&&this._remove(u)}this._performRestAdd(o,n)},r.prototype._performRestAdd=function(t,e){for(var i=0;i<t.length;i++){var n=t[i],a=e[n],o=an(a);if(o>1)for(var s=0;s<o;s++)this._add&&this._add(a[s]);else o===1&&this._add&&this._add(a);e[n]=null}},r.prototype._initIndexMap=function(t,e,i,n){for(var a=this._diffModeMultiple,o=0;o<t.length;o++){var s="_ec_"+this[n](t[o],o);if(a||(i[o]=s),!!e){var l=e[s],u=an(l);u===0?(e[s]=o,a&&i.push(s)):u===1?e[s]=[l,o]:l.push(o)}}},r}();const VT=GT;var WT=function(){function r(t,e){this._encode=t,this._schema=e}return r.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},r.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},r}();function UT(r,t){var e={},i=e.encode={},n=X(),a=[],o=[],s={};M(r.dimensions,function(v){var c=r.getDimensionInfo(v),d=c.coordDim;if(d){var y=c.coordDimIndex;al(i,d)[y]=v,c.isExtraCoord||(n.set(d,1),YT(c.type)&&(a[0]=v),al(s,d)[y]=r.getDimensionIndex(c.name)),c.defaultTooltip&&o.push(v)}cg.each(function(p,g){var m=al(i,g),_=c.otherDims[g];_!=null&&_!==!1&&(m[_]=c.name)})});var l=[],u={};n.each(function(v,c){var d=i[c];u[c]=d[0],l=l.concat(d)}),e.dataDimsOnCoord=l,e.dataDimIndicesOnCoord=V(l,function(v){return r.getDimensionInfo(v).storeDimIndex}),e.encodeFirstDimNotExtra=u;var f=i.label;f&&f.length&&(a=f.slice());var h=i.tooltip;return h&&h.length?o=h.slice():o.length||(o=a.slice()),i.defaultedLabel=a,i.defaultedTooltip=o,e.userOutput=new WT(s,t),e}function al(r,t){return r.hasOwnProperty(t)||(r[t]=[]),r[t]}function $T(r){return r==="category"?"ordinal":r==="time"?"time":"float"}function YT(r){return!(r==="ordinal"||r==="time")}var XT=function(){function r(t){this.otherDims={},t!=null&&k(this,t)}return r}();const io=XT;var ZT=wt(),qT={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},yy=function(){function r(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return r.prototype.isDimensionOmitted=function(){return this._dimOmitted},r.prototype._updateDimOmitted=function(t){this._dimOmitted=t,t&&(this._dimNameMap||(this._dimNameMap=Sy(this.source)))},r.prototype.getSourceDimensionIndex=function(t){return Z(this._dimNameMap.get(t),-1)},r.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},r.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=xg(this.source),i=!wy(t),n="",a=[],o=0,s=0;o<t;o++){var l=void 0,u=void 0,f=void 0,h=this.dimensions[s];if(h&&h.storeDimIndex===o)l=e?h.name:null,u=h.type,f=h.ordinalMeta,s++;else{var v=this.getSourceDimension(o);v&&(l=e?v.name:null,u=v.type)}a.push({property:l,type:u,ordinalMeta:f}),e&&l!=null&&(!h||!h.isCalculationCoord)&&(n+=i?l.replace(/\`/g,"`1").replace(/\$/g,"`2"):l),n+="$",n+=qT[u]||"f",f&&(n+=f.uid),n+="$"}var c=this.source,d=[c.seriesLayoutBy,c.startIndex,n].join("$$");return{dimensions:a,hash:d}},r.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,i=0;e<this._fullDimCount;e++){var n=void 0,a=this.dimensions[i];if(a&&a.storeDimIndex===e)a.isCalculationCoord||(n=a.name),i++;else{var o=this.getSourceDimension(e);o&&(n=o.name)}t.push(n)}return t},r.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},r}();function my(r){return r instanceof yy}function _y(r){for(var t=X(),e=0;e<(r||[]).length;e++){var i=r[e],n=H(i)?i.name:i;n!=null&&t.get(n)==null&&t.set(n,e)}return t}function Sy(r){var t=ZT(r);return t.dimNameMap||(t.dimNameMap=_y(r.dimensionsDefine))}function wy(r){return r>30}var on=H,Je=V,KT=typeof Int32Array>"u"?Array:Int32Array,QT="e\0\0",pv=-1,JT=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],jT=["_approximateExtent"],gv,Oa,sn,ln,ol,un,sl,tC=function(){function r(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","minmaxDownSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","minmaxDownSample","lttbDownSample"];var i,n=!1;my(t)?(i=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(n=!0,i=t),i=i||["x","y"];for(var a={},o=[],s={},l=!1,u={},f=0;f<i.length;f++){var h=i[f],v=z(h)?new io({name:h}):h instanceof io?h:new io(h),c=v.name;v.type=v.type||"float",v.coordDim||(v.coordDim=c,v.coordDimIndex=0);var d=v.otherDims=v.otherDims||{};o.push(c),a[c]=v,u[c]!=null&&(l=!0),v.createInvertedIndices&&(s[c]=[]),d.itemName===0&&(this._nameDimIdx=f),d.itemId===0&&(this._idDimIdx=f),n&&(v.storeDimIndex=f)}if(this.dimensions=o,this._dimInfos=a,this._initGetDimensionInfo(l),this.hostModel=e,this._invertedIndicesMap=s,this._dimOmitted){var y=this._dimIdxToName=X();M(o,function(p){y.set(a[p].storeDimIndex,p)})}}return r.prototype.getDimension=function(t){var e=this._recognizeDimIndex(t);if(e==null)return t;if(e=t,!this._dimOmitted)return this.dimensions[e];var i=this._dimIdxToName.get(e);if(i!=null)return i;var n=this._schema.getSourceDimension(e);if(n)return n.name},r.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);if(e!=null)return e;if(t==null)return-1;var i=this._getDimInfo(t);return i?i.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},r.prototype._recognizeDimIndex=function(t){if(yt(t)||t!=null&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},r.prototype._getStoreDimIndex=function(t){var e=this.getDimensionIndex(t);return e},r.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},r.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(i){return e.hasOwnProperty(i)?e[i]:void 0}:function(i){return e[i]}},r.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},r.prototype.mapDimension=function(t,e){var i=this._dimSummary;if(e==null)return i.encodeFirstDimNotExtra[t];var n=i.encode[t];return n?n[e]:null},r.prototype.mapDimensionsAll=function(t){var e=this._dimSummary,i=e.encode[t];return(i||[]).slice()},r.prototype.getStore=function(){return this._store},r.prototype.initData=function(t,e,i){var n=this,a;if(t instanceof Jl&&(a=t),!a){var o=this.dimensions,s=hf(t)||Yt(t)?new Tg(t,o.length):t;a=new Jl;var l=Je(o,function(u){return{type:n._dimInfos[u].type,property:u}});a.initData(s,l,i)}this._store=a,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,a.count()),this._dimSummary=UT(this,this._schema),this.userOutput=this._dimSummary.userOutput},r.prototype.appendData=function(t){var e=this._store.appendData(t);this._doInit(e[0],e[1])},r.prototype.appendValues=function(t,e){var i=this._store.appendValues(t,e&&e.length),n=i.start,a=i.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var s=n;s<a;s++){var l=s-n;this._nameList[s]=e[l],o&&sl(this,s)}},r.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,i=0;i<e.length;i++){var n=this._dimInfos[e[i]];n.ordinalMeta&&t.collectOrdinalMeta(n.storeDimIndex,n.ordinalMeta)}},r.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return this._idDimIdx==null&&t.getSource().sourceFormat!==ur&&!t.fillStorage},r.prototype._doInit=function(t,e){if(!(t>=e)){var i=this._store,n=i.getProvider();this._updateOrdinalMeta();var a=this._nameList,o=this._idList,s=n.getSource().sourceFormat,l=s===ce;if(l&&!n.pure)for(var u=[],f=t;f<e;f++){var h=n.getItem(f,u);if(!this.hasItemOption&&B_(h)&&(this.hasItemOption=!0),h){var v=h.name;a[f]==null&&v!=null&&(a[f]=Me(v,null));var c=h.id;o[f]==null&&c!=null&&(o[f]=Me(c,null))}}if(this._shouldMakeIdFromName())for(var f=t;f<e;f++)sl(this,f);gv(this)}},r.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},r.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},r.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},r.prototype.setCalculationInfo=function(t,e){on(t)?k(this._calculationInfo,t):this._calculationInfo[t]=e},r.prototype.getName=function(t){var e=this.getRawIndex(t),i=this._nameList[e];return i==null&&this._nameDimIdx!=null&&(i=sn(this,this._nameDimIdx,e)),i==null&&(i=""),i},r.prototype._getCategory=function(t,e){var i=this._store.get(t,e),n=this._store.getOrdinalMeta(t);return n?n.categories[i]:i},r.prototype.getId=function(t){return Oa(this,this.getRawIndex(t))},r.prototype.count=function(){return this._store.count()},r.prototype.get=function(t,e){var i=this._store,n=this._dimInfos[t];if(n)return i.get(n.storeDimIndex,e)},r.prototype.getByRawIndex=function(t,e){var i=this._store,n=this._dimInfos[t];if(n)return i.getByRawIndex(n.storeDimIndex,e)},r.prototype.getIndices=function(){return this._store.getIndices()},r.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},r.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},r.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},r.prototype.getValues=function(t,e){var i=this,n=this._store;return N(t)?n.getValues(Je(t,function(a){return i._getStoreDimIndex(a)}),e):n.getValues(t)},r.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,i=0,n=e.length;i<n;i++)if(isNaN(this._store.get(e[i],t)))return!1;return!0},r.prototype.indexOfName=function(t){for(var e=0,i=this._store.count();e<i;e++)if(this.getName(e)===t)return e;return-1},r.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},r.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},r.prototype.rawIndexOf=function(t,e){var i=t&&this._invertedIndicesMap[t],n=i&&i[e];return n==null||isNaN(n)?pv:n},r.prototype.indicesOfNearest=function(t,e,i){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,i)},r.prototype.each=function(t,e,i){U(t)&&(i=e,e=t,t=[]);var n=i||this,a=Je(ln(t),this._getStoreDimIndex,this);this._store.each(a,n?pt(e,n):e)},r.prototype.filterSelf=function(t,e,i){U(t)&&(i=e,e=t,t=[]);var n=i||this,a=Je(ln(t),this._getStoreDimIndex,this);return this._store=this._store.filter(a,n?pt(e,n):e),this},r.prototype.selectRange=function(t){var e=this,i={},n=gt(t);return M(n,function(a){var o=e._getStoreDimIndex(a);i[o]=t[a]}),this._store=this._store.selectRange(i),this},r.prototype.mapArray=function(t,e,i){U(t)&&(i=e,e=t,t=[]),i=i||this;var n=[];return this.each(t,function(){n.push(e&&e.apply(this,arguments))},i),n},r.prototype.map=function(t,e,i,n){var a=i||n||this,o=Je(ln(t),this._getStoreDimIndex,this),s=un(this);return s._store=this._store.map(o,a?pt(e,a):e),s},r.prototype.modify=function(t,e,i,n){var a=i||n||this,o=Je(ln(t),this._getStoreDimIndex,this);this._store.modify(o,a?pt(e,a):e)},r.prototype.downSample=function(t,e,i,n){var a=un(this);return a._store=this._store.downSample(this._getStoreDimIndex(t),e,i,n),a},r.prototype.minmaxDownSample=function(t,e){var i=un(this);return i._store=this._store.minmaxDownSample(this._getStoreDimIndex(t),e),i},r.prototype.lttbDownSample=function(t,e){var i=un(this);return i._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),i},r.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},r.prototype.getItemModel=function(t){var e=this.hostModel,i=this.getRawDataItem(t);return new Rt(i,e,e&&e.ecModel)},r.prototype.diff=function(t){var e=this;return new VT(t?t.getStore().getIndices():[],this.getStore().getIndices(),function(i){return Oa(t,i)},function(i){return Oa(e,i)})},r.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},r.prototype.setVisual=function(t,e){this._visual=this._visual||{},on(t)?k(this._visual,t):this._visual[t]=e},r.prototype.getItemVisual=function(t,e){var i=this._itemVisuals[t],n=i&&i[e];return n??this.getVisual(e)},r.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},r.prototype.ensureUniqueItemVisual=function(t,e){var i=this._itemVisuals,n=i[t];n||(n=i[t]={});var a=n[e];return a==null&&(a=this.getVisual(e),N(a)?a=a.slice():on(a)&&(a=k({},a)),n[e]=a),a},r.prototype.setItemVisual=function(t,e,i){var n=this._itemVisuals[t]||{};this._itemVisuals[t]=n,on(e)?k(n,e):n[e]=i},r.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},r.prototype.setLayout=function(t,e){on(t)?k(this._layout,t):this._layout[t]=e},r.prototype.getLayout=function(t){return this._layout[t]},r.prototype.getItemLayout=function(t){return this._itemLayouts[t]},r.prototype.setItemLayout=function(t,e,i){this._itemLayouts[t]=i?k(this._itemLayouts[t]||{},e):e},r.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},r.prototype.setItemGraphicEl=function(t,e){var i=this.hostModel&&this.hostModel.seriesIndex;Q1(i,this.dataType,t,e),this._graphicEls[t]=e},r.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},r.prototype.eachItemGraphicEl=function(t,e){M(this._graphicEls,function(i,n){i&&t&&t.call(e,i,n)})},r.prototype.cloneShallow=function(t){return t||(t=new r(this._schema?this._schema:Je(this.dimensions,this._getDimInfo,this),this.hostModel)),ol(t,this),t._store=this._store,t},r.prototype.wrapMethod=function(t,e){var i=this[t];U(i)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var n=i.apply(this,arguments);return e.apply(this,[n].concat(Au(arguments)))})},r.internalField=function(){gv=function(t){var e=t._invertedIndicesMap;M(e,function(i,n){var a=t._dimInfos[n],o=a.ordinalMeta,s=t._store;if(o){i=e[n]=new KT(o.categories.length);for(var l=0;l<i.length;l++)i[l]=pv;for(var l=0;l<s.count();l++)i[s.get(a.storeDimIndex,l)]=l}})},sn=function(t,e,i){return Me(t._getCategory(e,i),null)},Oa=function(t,e){var i=t._idList[e];return i==null&&t._idDimIdx!=null&&(i=sn(t,t._idDimIdx,e)),i==null&&(i=QT+e),i},ln=function(t){return N(t)||(t=t!=null?[t]:[]),t},un=function(t){var e=new r(t._schema?t._schema:Je(t.dimensions,t._getDimInfo,t),t.hostModel);return ol(e,t),e},ol=function(t,e){M(JT.concat(e.__wrappedMethods||[]),function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t.__wrappedMethods=e.__wrappedMethods,M(jT,function(i){t[i]=Q(e[i])}),t._calculationInfo=k({},e._calculationInfo)},sl=function(t,e){var i=t._nameList,n=t._idList,a=t._nameDimIdx,o=t._idDimIdx,s=i[e],l=n[e];if(s==null&&a!=null&&(i[e]=s=sn(t,a,e)),l==null&&o!=null&&(n[e]=l=sn(t,o,e)),l==null&&s!=null){var u=t._nameRepeatCount,f=u[s]=(u[s]||0)+1;l=s,f>1&&(l+="__ec__"+f),n[e]=l}}}(),r}();const eC=tC;function rC(r,t){hf(r)||(r=cf(r)),t=t||{};var e=t.coordDimensions||[],i=t.dimensionsDefine||r.dimensionsDefine||[],n=X(),a=[],o=nC(r,e,i,t.dimensionsCount),s=t.canOmitUnusedDimensions&&wy(o),l=i===r.dimensionsDefine,u=l?Sy(r):_y(i),f=t.encodeDefine;!f&&t.encodeDefaulter&&(f=t.encodeDefaulter(r,o));for(var h=X(f),v=new Lg(o),c=0;c<v.length;c++)v[c]=-1;function d(C){var D=v[C];if(D<0){var T=i[C],L=H(T)?T:{name:T},P=new io,I=L.name;I!=null&&u.get(I)!=null&&(P.name=P.displayName=I),L.type!=null&&(P.type=L.type),L.displayName!=null&&(P.displayName=L.displayName);var R=a.length;return v[C]=R,P.storeDimIndex=C,a.push(P),P}return a[D]}if(!s)for(var c=0;c<o;c++)d(c);h.each(function(C,D){var T=Nt(C).slice();if(T.length===1&&!z(T[0])&&T[0]<0){h.set(D,!1);return}var L=h.set(D,[]);M(T,function(P,I){var R=z(P)?u.get(P):P;R!=null&&R<o&&(L[I]=R,p(d(R),D,I))})});var y=0;M(e,function(C){var D,T,L,P;if(z(C))D=C,P={};else{P=C,D=P.name;var I=P.ordinalMeta;P.ordinalMeta=null,P=k({},P),P.ordinalMeta=I,T=P.dimsDef,L=P.otherDims,P.name=P.coordDim=P.coordDimIndex=P.dimsDef=P.otherDims=null}var R=h.get(D);if(R!==!1){if(R=Nt(R),!R.length)for(var E=0;E<(T&&T.length||1);E++){for(;y<o&&d(y).coordDim!=null;)y++;y<o&&R.push(y++)}M(R,function(G,B){var F=d(G);if(l&&P.type!=null&&(F.type=P.type),p(ot(F,P),D,B),F.name==null&&T){var $=T[B];!H($)&&($={name:$}),F.name=F.displayName=$.name,F.defaultTooltip=$.defaultTooltip}L&&ot(F.otherDims,L)})}});function p(C,D,T){cg.get(D)!=null?C.otherDims[D]=T:(C.coordDim=D,C.coordDimIndex=T,n.set(D,!0))}var g=t.generateCoord,m=t.generateCoordCount,_=m!=null;m=g?m||1:0;var S=g||"value";function b(C){C.name==null&&(C.name=C.coordDim)}if(s)M(a,function(C){b(C)}),a.sort(function(C,D){return C.storeDimIndex-D.storeDimIndex});else for(var w=0;w<o;w++){var x=d(w),A=x.coordDim;A==null&&(x.coordDim=aC(S,n,_),x.coordDimIndex=0,(!g||m<=0)&&(x.isExtraCoord=!0),m--),b(x),x.type==null&&(gg(r,w)===Jt.Must||x.isExtraCoord&&(x.otherDims.itemName!=null||x.otherDims.seriesName!=null))&&(x.type="ordinal")}return iC(a),new yy({source:r,dimensions:a,fullDimensionCount:o,dimensionOmitted:s})}function iC(r){for(var t=X(),e=0;e<r.length;e++){var i=r[e],n=i.name,a=t.get(n)||0;a>0&&(i.name=n+(a-1)),a++,t.set(n,a)}}function nC(r,t,e,i){var n=Math.max(r.dimensionsDetectedCount||1,t.length,e.length,i||0);return M(t,function(a){var o;H(a)&&(o=a.dimsDef)&&(n=Math.max(n,o.length))}),n}function aC(r,t,e){if(e||t.hasKey(r)){for(var i=0;t.hasKey(r+i);)i++;r+=i}return t.set(r,!0),r}var oC=function(){function r(t){this.coordSysDims=[],this.axisMap=X(),this.categoryAxisMap=X(),this.coordSysName=t}return r}();function sC(r){var t=r.get("coordinateSystem"),e=new oC(t),i=lC[t];if(i)return i(r,e,e.axisMap,e.categoryAxisMap),e}var lC={cartesian2d:function(r,t,e,i){var n=r.getReferringComponents("xAxis",me).models[0],a=r.getReferringComponents("yAxis",me).models[0];t.coordSysDims=["x","y"],e.set("x",n),e.set("y",a),mi(n)&&(i.set("x",n),t.firstCategoryDimIndex=0),mi(a)&&(i.set("y",a),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=1))},singleAxis:function(r,t,e,i){var n=r.getReferringComponents("singleAxis",me).models[0];t.coordSysDims=["single"],e.set("single",n),mi(n)&&(i.set("single",n),t.firstCategoryDimIndex=0)},polar:function(r,t,e,i){var n=r.getReferringComponents("polar",me).models[0],a=n.findAxisModel("radiusAxis"),o=n.findAxisModel("angleAxis");t.coordSysDims=["radius","angle"],e.set("radius",a),e.set("angle",o),mi(a)&&(i.set("radius",a),t.firstCategoryDimIndex=0),mi(o)&&(i.set("angle",o),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=1))},geo:function(r,t,e,i){t.coordSysDims=["lng","lat"]},parallel:function(r,t,e,i){var n=r.ecModel,a=n.getComponent("parallel",r.get("parallelIndex")),o=t.coordSysDims=a.dimensions.slice();M(a.parallelAxisIndex,function(s,l){var u=n.getComponent("parallelAxis",s),f=o[l];e.set(f,u),mi(u)&&(i.set(f,u),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=l))})}};function mi(r){return r.get("type")==="category"}function uC(r,t,e){e=e||{};var i=e.byIndex,n=e.stackedCoordDimension,a,o,s;fC(t)?a=t:(o=t.schema,a=o.dimensions,s=t.store);var l=!!(r&&r.get("stack")),u,f,h,v;if(M(a,function(m,_){z(m)&&(a[_]=m={name:m}),l&&!m.isExtraCoord&&(!i&&!u&&m.ordinalMeta&&(u=m),!f&&m.type!=="ordinal"&&m.type!=="time"&&(!n||n===m.coordDim)&&(f=m))}),f&&!i&&!u&&(i=!0),f){h="__\0ecstackresult_"+r.id,v="__\0ecstackedover_"+r.id,u&&(u.createInvertedIndices=!0);var c=f.coordDim,d=f.type,y=0;M(a,function(m){m.coordDim===c&&y++});var p={name:h,coordDim:c,coordDimIndex:y,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:a.length},g={name:v,coordDim:v,coordDimIndex:y+1,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:a.length+1};o?(s&&(p.storeDimIndex=s.ensureCalculationDimension(v,d),g.storeDimIndex=s.ensureCalculationDimension(h,d)),o.appendCalculationDimension(p),o.appendCalculationDimension(g)):(a.push(p),a.push(g))}return{stackedDimension:f&&f.name,stackedByDimension:u&&u.name,isStackedByIndex:i,stackedOverDimension:v,stackResultDimension:h}}function fC(r){return!my(r.schema)}function Xn(r,t){return!!t&&t===r.getCalculationInfo("stackedDimension")}function hC(r,t){return Xn(r,t)?r.getCalculationInfo("stackResultDimension"):t}function cC(r,t){var e=r.get("coordinateSystem"),i=ff.get(e),n;return t&&t.coordSysDims&&(n=V(t.coordSysDims,function(a){var o={name:a},s=t.axisMap.get(a);if(s){var l=s.get("type");o.type=$T(l)}return o})),n||(n=i&&(i.getDimensionsInfo?i.getDimensionsInfo():i.dimensions.slice())||["x","y"]),n}function vC(r,t,e){var i,n;return e&&M(r,function(a,o){var s=a.coordDim,l=e.categoryAxisMap.get(s);l&&(i==null&&(i=o),a.ordinalMeta=l.getOrdinalMeta(),t&&(a.createInvertedIndices=!0)),a.otherDims.itemName!=null&&(n=!0)}),!n&&i!=null&&(r[i].otherDims.itemName=0),i}function dC(r,t,e){e=e||{};var i=t.getSourceManager(),n,a=!1;r?(a=!0,n=cf(r)):(n=i.getSource(),a=n.sourceFormat===ce);var o=sC(t),s=cC(t,o),l=e.useEncodeDefaulter,u=U(l)?l:l?bt(Zw,s,t):null,f={coordDimensions:s,generateCoord:e.generateCoord,encodeDefine:t.getEncode(),encodeDefaulter:u,canOmitUnusedDimensions:!a},h=rC(n,f),v=vC(h.dimensions,e.createInvertedIndices,o),c=a?null:i.getSharedDataStore(h),d=uC(t,{schema:h,store:c}),y=new eC(h,t);y.setCalculationInfo(d);var p=v!=null&&pC(n)?function(g,m,_,S){return S===v?_:this.defaultDimValueGetter(g,m,_,S)}:null;return y.hasItemOption=!1,y.initData(a?n:c,null,p),y}function pC(r){if(r.sourceFormat===ce){var t=gC(r.data||[]);return!N(jn(t))}}function gC(r){for(var t=0;t<r.length&&r[t]==null;)t++;return r[t]}var by=function(){function r(t){this._setting=t||{},this._extent=[1/0,-1/0]}return r.prototype.getSetting=function(t){return this._setting[t]},r.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},r.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.setExtent=function(t,e){var i=this._extent;isNaN(t)||(i[0]=t),isNaN(e)||(i[1]=e)},r.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},r.prototype.isBlank=function(){return this._isBlank},r.prototype.setBlank=function(t){this._isBlank=t},r}();Eo(by);const Ye=by;var yC=0,mC=function(){function r(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++yC}return r.createByAxisModel=function(t){var e=t.option,i=e.data,n=i&&V(i,_C);return new r({categories:n,needCollect:!n,deduplication:e.dedplication!==!1})},r.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},r.prototype.parseAndCollect=function(t){var e,i=this._needCollect;if(!z(t)&&!i)return t;if(i&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var n=this._getOrCreateMap();return e=n.get(t),e==null&&(i?(e=this.categories.length,this.categories[e]=t,n.set(t,e)):e=NaN),e},r.prototype._getOrCreateMap=function(){return this._map||(this._map=X(this.categories))},r}();function _C(r){return H(r)&&r.value!=null?r.value:r+""}const hu=mC;function cu(r){return r.type==="interval"||r.type==="log"}function SC(r,t,e,i){var n={},a=r[1]-r[0],o=n.interval=Xd(a/t,!0);e!=null&&o<e&&(o=n.interval=e),i!=null&&o>i&&(o=n.interval=i);var s=n.intervalPrecision=xy(o),l=n.niceTickExtent=[_t(Math.ceil(r[0]/o)*o,s),_t(Math.floor(r[1]/o)*o,s)];return wC(l,r),n}function ll(r){var t=Math.pow(10,Fu(r)),e=r/t;return e?e===2?e=3:e===3?e=5:e*=2:e=1,_t(e*t)}function xy(r){return Ne(r)+2}function yv(r,t,e){r[t]=Math.max(Math.min(r[t],e[1]),e[0])}function wC(r,t){!isFinite(r[0])&&(r[0]=t[0]),!isFinite(r[1])&&(r[1]=t[1]),yv(r,0,t),yv(r,1,t),r[0]>r[1]&&(r[0]=r[1])}function jo(r,t){return r>=t[0]&&r<=t[1]}function ts(r,t){return t[1]===t[0]?.5:(r-t[0])/(t[1]-t[0])}function es(r,t){return r*(t[1]-t[0])+t[0]}var Ty=function(r){O(t,r);function t(e){var i=r.call(this,e)||this;i.type="ordinal";var n=i.getSetting("ordinalMeta");return n||(n=new hu({})),N(n)&&(n=new hu({categories:V(n,function(a){return H(a)?a.value:a})})),i._ordinalMeta=n,i._extent=i.getSetting("extent")||[0,n.categories.length-1],i}return t.prototype.parse=function(e){return e==null?NaN:z(e)?this._ordinalMeta.getOrdinal(e):Math.round(e)},t.prototype.contain=function(e){return e=this.parse(e),jo(e,this._extent)&&this._ordinalMeta.categories[e]!=null},t.prototype.normalize=function(e){return e=this._getTickNumber(this.parse(e)),ts(e,this._extent)},t.prototype.scale=function(e){return e=Math.round(es(e,this._extent)),this.getRawOrdinalNumber(e)},t.prototype.getTicks=function(){for(var e=[],i=this._extent,n=i[0];n<=i[1];)e.push({value:n}),n++;return e},t.prototype.getMinorTicks=function(e){},t.prototype.setSortInfo=function(e){if(e==null){this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;return}for(var i=e.ordinalNumbers,n=this._ordinalNumbersByTick=[],a=this._ticksByOrdinalNumber=[],o=0,s=this._ordinalMeta.categories.length,l=Math.min(s,i.length);o<l;++o){var u=i[o];n[o]=u,a[u]=o}for(var f=0;o<s;++o){for(;a[f]!=null;)f++;n.push(f),a[f]=o}},t.prototype._getTickNumber=function(e){var i=this._ticksByOrdinalNumber;return i&&e>=0&&e<i.length?i[e]:e},t.prototype.getRawOrdinalNumber=function(e){var i=this._ordinalNumbersByTick;return i&&e>=0&&e<i.length?i[e]:e},t.prototype.getLabel=function(e){if(!this.isBlank()){var i=this.getRawOrdinalNumber(e.value),n=this._ordinalMeta.categories[i];return n==null?"":n+""}},t.prototype.count=function(){return this._extent[1]-this._extent[0]+1},t.prototype.unionExtentFromData=function(e,i){this.unionExtent(e.getApproximateExtent(i))},t.prototype.isInExtentRange=function(e){return e=this._getTickNumber(e),this._extent[0]<=e&&this._extent[1]>=e},t.prototype.getOrdinalMeta=function(){return this._ordinalMeta},t.prototype.calcNiceTicks=function(){},t.prototype.calcNiceExtent=function(){},t.type="ordinal",t}(Ye);Ye.registerClass(Ty);const Cy=Ty;var Nr=_t,Dy=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="interval",e._interval=0,e._intervalPrecision=2,e}return t.prototype.parse=function(e){return e},t.prototype.contain=function(e){return jo(e,this._extent)},t.prototype.normalize=function(e){return ts(e,this._extent)},t.prototype.scale=function(e){return es(e,this._extent)},t.prototype.setExtent=function(e,i){var n=this._extent;isNaN(e)||(n[0]=parseFloat(e)),isNaN(i)||(n[1]=parseFloat(i))},t.prototype.unionExtent=function(e){var i=this._extent;e[0]<i[0]&&(i[0]=e[0]),e[1]>i[1]&&(i[1]=e[1]),this.setExtent(i[0],i[1])},t.prototype.getInterval=function(){return this._interval},t.prototype.setInterval=function(e){this._interval=e,this._niceExtent=this._extent.slice(),this._intervalPrecision=xy(e)},t.prototype.getTicks=function(e){var i=this._interval,n=this._extent,a=this._niceExtent,o=this._intervalPrecision,s=[];if(!i)return s;var l=1e4;n[0]<a[0]&&(e?s.push({value:Nr(a[0]-i,o)}):s.push({value:n[0]}));for(var u=a[0];u<=a[1]&&(s.push({value:u}),u=Nr(u+i,o),u!==s[s.length-1].value);)if(s.length>l)return[];var f=s.length?s[s.length-1].value:a[1];return n[1]>f&&(e?s.push({value:Nr(f+i,o)}):s.push({value:n[1]})),s},t.prototype.getMinorTicks=function(e){for(var i=this.getTicks(!0),n=[],a=this.getExtent(),o=1;o<i.length;o++){for(var s=i[o],l=i[o-1],u=0,f=[],h=s.value-l.value,v=h/e;u<e-1;){var c=Nr(l.value+(u+1)*v);c>a[0]&&c<a[1]&&f.push(c),u++}n.push(f)}return n},t.prototype.getLabel=function(e,i){if(e==null)return"";var n=i&&i.precision;n==null?n=Ne(e.value)||0:n==="auto"&&(n=this._intervalPrecision);var a=Nr(e.value,n,!0);return lg(a)},t.prototype.calcNiceTicks=function(e,i,n){e=e||5;var a=this._extent,o=a[1]-a[0];if(isFinite(o)){o<0&&(o=-o,a.reverse());var s=SC(a,e,i,n);this._intervalPrecision=s.intervalPrecision,this._interval=s.interval,this._niceExtent=s.niceTickExtent}},t.prototype.calcNiceExtent=function(e){var i=this._extent;if(i[0]===i[1])if(i[0]!==0){var n=Math.abs(i[0]);e.fixMax||(i[1]+=n/2),i[0]-=n/2}else i[1]=1;var a=i[1]-i[0];isFinite(a)||(i[0]=0,i[1]=1),this.calcNiceTicks(e.splitNumber,e.minInterval,e.maxInterval);var o=this._interval;e.fixMin||(i[0]=Nr(Math.floor(i[0]/o)*o)),e.fixMax||(i[1]=Nr(Math.ceil(i[1]/o)*o))},t.prototype.setNiceExtent=function(e,i){this._niceExtent=[e,i]},t.type="interval",t}(Ye);Ye.registerClass(Dy);const ia=Dy;var My=typeof Float32Array<"u",bC=My?Float32Array:Array;function Di(r){return N(r)?My?new Float32Array(r):r:new bC(r)}var xC="__ec_stack_";function Ay(r){return r.get("stack")||xC+r.seriesIndex}function Ly(r){return r.dim+r.index}function TC(r,t){var e=[];return t.eachSeriesByType(r,function(i){LC(i)&&e.push(i)}),e}function CC(r){var t={};M(r,function(l){var u=l.coordinateSystem,f=u.getBaseAxis();if(!(f.type!=="time"&&f.type!=="value"))for(var h=l.getData(),v=f.dim+"_"+f.index,c=h.getDimensionIndex(h.mapDimension(f.dim)),d=h.getStore(),y=0,p=d.count();y<p;++y){var g=d.get(c,y);t[v]?t[v].push(g):t[v]=[g]}});var e={};for(var i in t)if(t.hasOwnProperty(i)){var n=t[i];if(n){n.sort(function(l,u){return l-u});for(var a=null,o=1;o<n.length;++o){var s=n[o]-n[o-1];s>0&&(a=a===null?s:Math.min(a,s))}e[i]=a}}return e}function DC(r){var t=CC(r),e=[];return M(r,function(i){var n=i.coordinateSystem,a=n.getBaseAxis(),o=a.getExtent(),s;if(a.type==="category")s=a.getBandWidth();else if(a.type==="value"||a.type==="time"){var l=a.dim+"_"+a.index,u=t[l],f=Math.abs(o[1]-o[0]),h=a.scale.getExtent(),v=Math.abs(h[1]-h[0]);s=u?f/v*u:f}else{var c=i.getData();s=Math.abs(o[1]-o[0])/c.count()}var d=Bt(i.get("barWidth"),s),y=Bt(i.get("barMaxWidth"),s),p=Bt(i.get("barMinWidth")||(IC(i)?.5:1),s),g=i.get("barGap"),m=i.get("barCategoryGap");e.push({bandWidth:s,barWidth:d,barMaxWidth:y,barMinWidth:p,barGap:g,barCategoryGap:m,axisKey:Ly(a),stackId:Ay(i)})}),MC(e)}function MC(r){var t={};M(r,function(i,n){var a=i.axisKey,o=i.bandWidth,s=t[a]||{bandWidth:o,remainedWidth:o,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},l=s.stacks;t[a]=s;var u=i.stackId;l[u]||s.autoWidthCount++,l[u]=l[u]||{width:0,maxWidth:0};var f=i.barWidth;f&&!l[u].width&&(l[u].width=f,f=Math.min(s.remainedWidth,f),s.remainedWidth-=f);var h=i.barMaxWidth;h&&(l[u].maxWidth=h);var v=i.barMinWidth;v&&(l[u].minWidth=v);var c=i.barGap;c!=null&&(s.gap=c);var d=i.barCategoryGap;d!=null&&(s.categoryGap=d)});var e={};return M(t,function(i,n){e[n]={};var a=i.stacks,o=i.bandWidth,s=i.categoryGap;if(s==null){var l=gt(a).length;s=Math.max(35-l*4,15)+"%"}var u=Bt(s,o),f=Bt(i.gap,1),h=i.remainedWidth,v=i.autoWidthCount,c=(h-u)/(v+(v-1)*f);c=Math.max(c,0),M(a,function(g){var m=g.maxWidth,_=g.minWidth;if(g.width){var S=g.width;m&&(S=Math.min(S,m)),_&&(S=Math.max(S,_)),g.width=S,h-=S+f*S,v--}else{var S=c;m&&m<S&&(S=Math.min(m,h)),_&&_>S&&(S=_),S!==c&&(g.width=S,h-=S+f*S,v--)}}),c=(h-u)/(v+(v-1)*f),c=Math.max(c,0);var d=0,y;M(a,function(g,m){g.width||(g.width=c),y=g,d+=g.width*(1+f)}),y&&(d-=y.width*f);var p=-d/2;M(a,function(g,m){e[n][m]=e[n][m]||{bandWidth:o,offset:p,width:g.width},p+=g.width*(1+f)})}),e}function AC(r,t,e){if(r&&t){var i=r[Ly(t)];return i!=null&&e!=null?i[Ay(e)]:i}}function LC(r){return r.coordinateSystem&&r.coordinateSystem.type==="cartesian2d"}function IC(r){return r.pipelineContext&&r.pipelineContext.large}var PC=function(r,t,e,i){for(;e<i;){var n=e+i>>>1;r[n][1]<t?e=n+1:i=n}return e},Iy=function(r){O(t,r);function t(e){var i=r.call(this,e)||this;return i.type="time",i}return t.prototype.getLabel=function(e){var i=this.getSetting("useUTC");return Vo(e.value,lc[Fw(Ri(this._minLevelUnit))]||lc.second,i,this.getSetting("locale"))},t.prototype.getFormattedLabel=function(e,i,n){var a=this.getSetting("useUTC"),o=this.getSetting("locale");return zw(e,i,n,o,a)},t.prototype.getTicks=function(){var e=this._interval,i=this._extent,n=[];if(!e)return n;n.push({value:i[0],level:0});var a=this.getSetting("useUTC"),o=FC(this._minLevelUnit,this._approxInterval,a,i);return n=n.concat(o),n.push({value:i[1],level:0}),n},t.prototype.calcNiceExtent=function(e){var i=this._extent;if(i[0]===i[1]&&(i[0]-=fe,i[1]+=fe),i[1]===-1/0&&i[0]===1/0){var n=new Date;i[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),i[0]=i[1]-fe}this.calcNiceTicks(e.splitNumber,e.minInterval,e.maxInterval)},t.prototype.calcNiceTicks=function(e,i,n){e=e||10;var a=this._extent,o=a[1]-a[0];this._approxInterval=o/e,i!=null&&this._approxInterval<i&&(this._approxInterval=i),n!=null&&this._approxInterval>n&&(this._approxInterval=n);var s=Ba.length,l=Math.min(PC(Ba,this._approxInterval,0,s),s-1);this._interval=Ba[l][1],this._minLevelUnit=Ba[Math.max(l-1,0)][0]},t.prototype.parse=function(e){return yt(e)?e:+Ve(e)},t.prototype.contain=function(e){return jo(this.parse(e),this._extent)},t.prototype.normalize=function(e){return ts(this.parse(e),this._extent)},t.prototype.scale=function(e){return es(e,this._extent)},t.type="time",t}(ia),Ba=[["second",af],["minute",of],["hour",Mn],["quarter-day",Mn*6],["half-day",Mn*12],["day",fe*1.2],["half-week",fe*3.5],["week",fe*7],["month",fe*31],["quarter",fe*95],["half-year",sc/2],["year",sc]];function RC(r,t,e,i){var n=Ve(t),a=Ve(e),o=function(d){return uc(n,d,i)===uc(a,d,i)},s=function(){return o("year")},l=function(){return s()&&o("month")},u=function(){return l()&&o("day")},f=function(){return u()&&o("hour")},h=function(){return f()&&o("minute")},v=function(){return h()&&o("second")},c=function(){return v()&&o("millisecond")};switch(r){case"year":return s();case"month":return l();case"day":return u();case"hour":return f();case"minute":return h();case"second":return v();case"millisecond":return c()}}function EC(r,t){return r/=fe,r>16?16:r>7.5?7:r>3.5?4:r>1.5?2:1}function kC(r){var t=30*fe;return r/=t,r>6?6:r>3?3:r>2?2:1}function OC(r){return r/=Mn,r>12?12:r>6?6:r>3.5?4:r>2?2:1}function mv(r,t){return r/=t?of:af,r>30?30:r>20?20:r>15?15:r>10?10:r>5?5:r>2?2:1}function BC(r){return Xd(r,!0)}function NC(r,t,e){var i=new Date(r);switch(Ri(t)){case"year":case"month":i[rg(e)](0);case"day":i[ig(e)](1);case"hour":i[ng(e)](0);case"minute":i[ag(e)](0);case"second":i[og(e)](0),i[sg(e)](0)}return i.getTime()}function FC(r,t,e,i){var n=1e4,a=tg,o=0;function s(D,T,L,P,I,R,E){for(var G=new Date(T),B=T,F=G[P]();B<L&&B<=i[1];)E.push({value:B}),F+=D,G[I](F),B=G.getTime();E.push({value:B,notAdd:!0})}function l(D,T,L){var P=[],I=!T.length;if(!RC(Ri(D),i[0],i[1],e)){I&&(T=[{value:NC(new Date(i[0]),D,e)},{value:i[1]}]);for(var R=0;R<T.length-1;R++){var E=T[R].value,G=T[R+1].value;if(E!==G){var B=void 0,F=void 0,$=void 0,it=!1;switch(D){case"year":B=Math.max(1,Math.round(t/fe/365)),F=sf(e),$=Hw(e);break;case"half-year":case"quarter":case"month":B=kC(t),F=Ei(e),$=rg(e);break;case"week":case"half-week":case"day":B=EC(t),F=Wo(e),$=ig(e),it=!0;break;case"half-day":case"quarter-day":case"hour":B=OC(t),F=zn(e),$=ng(e);break;case"minute":B=mv(t,!0),F=Uo(e),$=ag(e);break;case"second":B=mv(t,!1),F=$o(e),$=og(e);break;case"millisecond":B=BC(t),F=Yo(e),$=sg(e);break}s(B,E,G,F,$,it,P),D==="year"&&L.length>1&&R===0&&L.unshift({value:L[0].value-B})}}for(var R=0;R<P.length;R++)L.push(P[R]);return P}}for(var u=[],f=[],h=0,v=0,c=0;c<a.length&&o++<n;++c){var d=Ri(a[c]);if(Nw(a[c])){l(a[c],u[u.length-1]||[],f);var y=a[c+1]?Ri(a[c+1]):null;if(d!==y){if(f.length){v=h,f.sort(function(D,T){return D.value-T.value});for(var p=[],g=0;g<f.length;++g){var m=f[g].value;(g===0||f[g-1].value!==m)&&(p.push(f[g]),m>=i[0]&&m<=i[1]&&h++)}var _=(i[1]-i[0])/t;if(h>_*1.5&&v>_/1.5||(u.push(p),h>_||r===a[c]))break}f=[]}}}for(var S=Tt(V(u,function(D){return Tt(D,function(T){return T.value>=i[0]&&T.value<=i[1]&&!T.notAdd})}),function(D){return D.length>0}),b=[],w=S.length-1,c=0;c<S.length;++c)for(var x=S[c],A=0;A<x.length;++A)b.push({value:x[A].value,level:w-c});b.sort(function(D,T){return D.value-T.value});for(var C=[],c=0;c<b.length;++c)(c===0||b[c].value!==b[c-1].value)&&C.push(b[c]);return C}Ye.registerClass(Iy);const zC=Iy;var _v=Ye.prototype,Rn=ia.prototype,HC=_t,GC=Math.floor,VC=Math.ceil,Na=Math.pow,de=Math.log,xf=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="log",e.base=10,e._originalScale=new ia,e._interval=0,e}return t.prototype.getTicks=function(e){var i=this._originalScale,n=this._extent,a=i.getExtent(),o=Rn.getTicks.call(this,e);return V(o,function(s){var l=s.value,u=_t(Na(this.base,l));return u=l===n[0]&&this._fixMin?Fa(u,a[0]):u,u=l===n[1]&&this._fixMax?Fa(u,a[1]):u,{value:u}},this)},t.prototype.setExtent=function(e,i){var n=de(this.base);e=de(Math.max(0,e))/n,i=de(Math.max(0,i))/n,Rn.setExtent.call(this,e,i)},t.prototype.getExtent=function(){var e=this.base,i=_v.getExtent.call(this);i[0]=Na(e,i[0]),i[1]=Na(e,i[1]);var n=this._originalScale,a=n.getExtent();return this._fixMin&&(i[0]=Fa(i[0],a[0])),this._fixMax&&(i[1]=Fa(i[1],a[1])),i},t.prototype.unionExtent=function(e){this._originalScale.unionExtent(e);var i=this.base;e[0]=de(e[0])/de(i),e[1]=de(e[1])/de(i),_v.unionExtent.call(this,e)},t.prototype.unionExtentFromData=function(e,i){this.unionExtent(e.getApproximateExtent(i))},t.prototype.calcNiceTicks=function(e){e=e||10;var i=this._extent,n=i[1]-i[0];if(!(n===1/0||n<=0)){var a=E_(n),o=e/n*a;for(o<=.5&&(a*=10);!isNaN(a)&&Math.abs(a)<1&&Math.abs(a)>0;)a*=10;var s=[_t(VC(i[0]/a)*a),_t(GC(i[1]/a)*a)];this._interval=a,this._niceExtent=s}},t.prototype.calcNiceExtent=function(e){Rn.calcNiceExtent.call(this,e),this._fixMin=e.fixMin,this._fixMax=e.fixMax},t.prototype.parse=function(e){return e},t.prototype.contain=function(e){return e=de(e)/de(this.base),jo(e,this._extent)},t.prototype.normalize=function(e){return e=de(e)/de(this.base),ts(e,this._extent)},t.prototype.scale=function(e){return e=es(e,this._extent),Na(this.base,e)},t.type="log",t}(Ye),Py=xf.prototype;Py.getMinorTicks=Rn.getMinorTicks;Py.getLabel=Rn.getLabel;function Fa(r,t){return HC(r,Ne(t))}Ye.registerClass(xf);const WC=xf;var UC=function(){function r(t,e,i){this._prepareParams(t,e,i)}return r.prototype._prepareParams=function(t,e,i){i[1]<i[0]&&(i=[NaN,NaN]),this._dataMin=i[0],this._dataMax=i[1];var n=this._isOrdinal=t.type==="ordinal";this._needCrossZero=t.type==="interval"&&e.getNeedCrossZero&&e.getNeedCrossZero();var a=e.get("min",!0);a==null&&(a=e.get("startValue",!0));var o=this._modelMinRaw=a;U(o)?this._modelMinNum=za(t,o({min:i[0],max:i[1]})):o!=="dataMin"&&(this._modelMinNum=za(t,o));var s=this._modelMaxRaw=e.get("max",!0);if(U(s)?this._modelMaxNum=za(t,s({min:i[0],max:i[1]})):s!=="dataMax"&&(this._modelMaxNum=za(t,s)),n)this._axisDataLen=e.getCategories().length;else{var l=e.get("boundaryGap"),u=N(l)?l:[l||0,l||0];typeof u[0]=="boolean"||typeof u[1]=="boolean"?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[Kr(u[0],1),Kr(u[1],1)]}},r.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,i=this._dataMax,n=this._axisDataLen,a=this._boundaryGapInner,o=t?null:i-e||Math.abs(e),s=this._modelMinRaw==="dataMin"?e:this._modelMinNum,l=this._modelMaxRaw==="dataMax"?i:this._modelMaxNum,u=s!=null,f=l!=null;s==null&&(s=t?n?0:NaN:e-a[0]*o),l==null&&(l=t?n?n-1:NaN:i+a[1]*o),(s==null||!isFinite(s))&&(s=NaN),(l==null||!isFinite(l))&&(l=NaN);var h=oo(s)||oo(l)||t&&!n;this._needCrossZero&&(s>0&&l>0&&!u&&(s=0),s<0&&l<0&&!f&&(l=0));var v=this._determinedMin,c=this._determinedMax;return v!=null&&(s=v,u=!0),c!=null&&(l=c,f=!0),{min:s,max:l,minFixed:u,maxFixed:f,isBlank:h}},r.prototype.modifyDataMinMax=function(t,e){this[YC[t]]=e},r.prototype.setDeterminedMinMax=function(t,e){var i=$C[t];this[i]=e},r.prototype.freeze=function(){this.frozen=!0},r}(),$C={min:"_determinedMin",max:"_determinedMax"},YC={min:"_dataMin",max:"_dataMax"};function XC(r,t,e){var i=r.rawExtentInfo;return i||(i=new UC(r,t,e),r.rawExtentInfo=i,i)}function za(r,t){return t==null?null:oo(t)?NaN:r.parse(t)}function Ry(r,t){var e=r.type,i=XC(r,t,r.getExtent()).calculate();r.setBlank(i.isBlank);var n=i.min,a=i.max,o=t.ecModel;if(o&&e==="time"){var s=TC("bar",o),l=!1;if(M(s,function(h){l=l||h.getBaseAxis()===t.axis}),l){var u=DC(s),f=ZC(n,a,t,u);n=f.min,a=f.max}}return{extent:[n,a],fixMin:i.minFixed,fixMax:i.maxFixed}}function ZC(r,t,e,i){var n=e.axis.getExtent(),a=Math.abs(n[1]-n[0]),o=AC(i,e.axis);if(o===void 0)return{min:r,max:t};var s=1/0;M(o,function(c){s=Math.min(c.offset,s)});var l=-1/0;M(o,function(c){l=Math.max(c.offset+c.width,l)}),s=Math.abs(s),l=Math.abs(l);var u=s+l,f=t-r,h=1-(s+l)/a,v=f/h-f;return t+=v*(l/u),r-=v*(s/u),{min:r,max:t}}function Sv(r,t){var e=t,i=Ry(r,e),n=i.extent,a=e.get("splitNumber");r instanceof WC&&(r.base=e.get("logBase"));var o=r.type,s=e.get("interval"),l=o==="interval"||o==="time";r.setExtent(n[0],n[1]),r.calcNiceExtent({splitNumber:a,fixMin:i.fixMin,fixMax:i.fixMax,minInterval:l?e.get("minInterval"):null,maxInterval:l?e.get("maxInterval"):null}),s!=null&&r.setInterval&&r.setInterval(s)}function qC(r,t){if(t=t||r.get("type"),t)switch(t){case"category":return new Cy({ordinalMeta:r.getOrdinalMeta?r.getOrdinalMeta():r.getCategories(),extent:[1/0,-1/0]});case"time":return new zC({locale:r.ecModel.getLocaleModel(),useUTC:r.ecModel.get("useUTC")});default:return new(Ye.getClass(t)||ia)}}function KC(r){var t=r.scale.getExtent(),e=t[0],i=t[1];return!(e>0&&i>0||e<0&&i<0)}function Wi(r){var t=r.getLabelModel().get("formatter"),e=r.type==="category"?r.scale.getExtent()[0]:null;return r.scale.type==="time"?function(i){return function(n,a){return r.scale.getFormattedLabel(n,a,i)}}(t):z(t)?function(i){return function(n){var a=r.scale.getLabel(n),o=i.replace("{value}",a??"");return o}}(t):U(t)?function(i){return function(n,a){return e!=null&&(a=n.value-e),i(Tf(r,n),a,n.level!=null?{level:n.level}:null)}}(t):function(i){return r.scale.getLabel(i)}}function Tf(r,t){return r.type==="category"?r.scale.getLabel(t):t.value}function QC(r){var t=r.model,e=r.scale;if(!(!t.get(["axisLabel","show"])||e.isBlank())){var i,n,a=e.getExtent();e instanceof Cy?n=e.count():(i=e.getTicks(),n=i.length);var o=r.getLabelModel(),s=Wi(r),l,u=1;n>40&&(u=Math.ceil(n/40));for(var f=0;f<n;f+=u){var h=i?i[f]:{value:a[0]+f},v=s(h,f),c=o.getTextRect(v),d=JC(c,o.get("rotate")||0);l?l.union(d):l=d}return l}}function JC(r,t){var e=t*Math.PI/180,i=r.width,n=r.height,a=i*Math.abs(Math.cos(e))+Math.abs(n*Math.sin(e)),o=i*Math.abs(Math.sin(e))+Math.abs(n*Math.cos(e)),s=new rt(r.x,r.y,a,o);return s}function Cf(r){var t=r.get("interval");return t??"auto"}function Ey(r){return r.type==="category"&&Cf(r.getLabelModel())===0}function jC(r,t){var e={};return M(r.mapDimensionsAll(t),function(i){e[hC(r,i)]=!0}),gt(e)}var tD=function(){function r(){}return r.prototype.getNeedCrossZero=function(){var t=this.option;return!t.scale},r.prototype.getCoordSysModel=function(){},r}(),wv=[],eD={registerPreprocessor:dy,registerProcessor:py,registerPostInit:OT,registerPostUpdate:BT,registerUpdateLifecycle:wf,registerAction:Vi,registerCoordinateSystem:NT,registerLayout:FT,registerVisual:ii,registerTransform:HT,registerLoading:gy,registerMap:zT,registerImpl:pT,PRIORITY:AT,ComponentModel:dt,ComponentView:We,SeriesModel:Un,ChartView:fr,registerComponentModel:function(r){dt.registerClass(r)},registerComponentView:function(r){We.registerClass(r)},registerSeriesModel:function(r){Un.registerClass(r)},registerChartView:function(r){fr.registerClass(r)},registerSubTypeDefaulter:function(r,t){dt.registerSubTypeDefaulter(r,t)},registerPainter:function(r,t){M_(r,t)}};function vr(r){if(N(r)){M(r,function(t){vr(t)});return}lt(wv,r)>=0||(wv.push(r),U(r)&&(r={install:r}),r.install(eD))}var Zn=wt();function ky(r,t){var e=V(t,function(i){return r.scale.parse(i)});return r.type==="time"&&e.length>0&&(e.sort(),e.unshift(e[0]),e.push(e[e.length-1])),e}function rD(r){var t=r.getLabelModel().get("customValues");if(t){var e=Wi(r),i=r.scale.getExtent(),n=ky(r,t),a=Tt(n,function(o){return o>=i[0]&&o<=i[1]});return{labels:V(a,function(o){var s={value:o};return{formattedLabel:e(s),rawLabel:r.scale.getLabel(s),tickValue:o}})}}return r.type==="category"?nD(r):oD(r)}function iD(r,t){var e=r.getTickModel().get("customValues");if(e){var i=r.scale.getExtent(),n=ky(r,e);return{ticks:Tt(n,function(a){return a>=i[0]&&a<=i[1]})}}return r.type==="category"?aD(r,t):{ticks:V(r.scale.getTicks(),function(a){return a.value})}}function nD(r){var t=r.getLabelModel(),e=Oy(r,t);return!t.get("show")||r.scale.isBlank()?{labels:[],labelCategoryInterval:e.labelCategoryInterval}:e}function Oy(r,t){var e=By(r,"labels"),i=Cf(t),n=Ny(e,i);if(n)return n;var a,o;return U(i)?a=Hy(r,i):(o=i==="auto"?sD(r):i,a=zy(r,o)),Fy(e,i,{labels:a,labelCategoryInterval:o})}function aD(r,t){var e=By(r,"ticks"),i=Cf(t),n=Ny(e,i);if(n)return n;var a,o;if((!t.get("show")||r.scale.isBlank())&&(a=[]),U(i))a=Hy(r,i,!0);else if(i==="auto"){var s=Oy(r,r.getLabelModel());o=s.labelCategoryInterval,a=V(s.labels,function(l){return l.tickValue})}else o=i,a=zy(r,o,!0);return Fy(e,i,{ticks:a,tickCategoryInterval:o})}function oD(r){var t=r.scale.getTicks(),e=Wi(r);return{labels:V(t,function(i,n){return{level:i.level,formattedLabel:e(i,n),rawLabel:r.scale.getLabel(i),tickValue:i.value}})}}function By(r,t){return Zn(r)[t]||(Zn(r)[t]=[])}function Ny(r,t){for(var e=0;e<r.length;e++)if(r[e].key===t)return r[e].value}function Fy(r,t,e){return r.push({key:t,value:e}),e}function sD(r){var t=Zn(r).autoInterval;return t??(Zn(r).autoInterval=r.calculateCategoryInterval())}function lD(r){var t=uD(r),e=Wi(r),i=(t.axisRotate-t.labelRotate)/180*Math.PI,n=r.scale,a=n.getExtent(),o=n.count();if(a[1]-a[0]<1)return 0;var s=1;o>40&&(s=Math.max(1,Math.floor(o/40)));for(var l=a[0],u=r.dataToCoord(l+1)-r.dataToCoord(l),f=Math.abs(u*Math.cos(i)),h=Math.abs(u*Math.sin(i)),v=0,c=0;l<=a[1];l+=s){var d=0,y=0,p=Ou(e({value:l}),t.font,"center","top");d=p.width*1.3,y=p.height*1.3,v=Math.max(v,d,7),c=Math.max(c,y,7)}var g=v/f,m=c/h;isNaN(g)&&(g=1/0),isNaN(m)&&(m=1/0);var _=Math.max(0,Math.floor(Math.min(g,m))),S=Zn(r.model),b=r.getExtent(),w=S.lastAutoInterval,x=S.lastTickCount;return w!=null&&x!=null&&Math.abs(w-_)<=1&&Math.abs(x-o)<=1&&w>_&&S.axisExtent0===b[0]&&S.axisExtent1===b[1]?_=w:(S.lastTickCount=o,S.lastAutoInterval=_,S.axisExtent0=b[0],S.axisExtent1=b[1]),_}function uD(r){var t=r.getLabelModel();return{axisRotate:r.getRotate?r.getRotate():r.isHorizontal&&!r.isHorizontal()?90:0,labelRotate:t.get("rotate")||0,font:t.getFont()}}function zy(r,t,e){var i=Wi(r),n=r.scale,a=n.getExtent(),o=r.getLabelModel(),s=[],l=Math.max((t||0)+1,1),u=a[0],f=n.count();u!==0&&l>1&&f/l>2&&(u=Math.round(Math.ceil(u/l)*l));var h=Ey(r),v=o.get("showMinLabel")||h,c=o.get("showMaxLabel")||h;v&&u!==a[0]&&y(a[0]);for(var d=u;d<=a[1];d+=l)y(d);c&&d-l!==a[1]&&y(a[1]);function y(p){var g={value:p};s.push(e?p:{formattedLabel:i(g),rawLabel:n.getLabel(g),tickValue:p})}return s}function Hy(r,t,e){var i=r.scale,n=Wi(r),a=[];return M(i.getTicks(),function(o){var s=i.getLabel(o),l=o.value;t(o.value,s)&&a.push(e?l:{formattedLabel:n(o),rawLabel:s,tickValue:l})}),a}var bv=[0,1],fD=function(){function r(t,e,i){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=i||[0,0]}return r.prototype.contain=function(t){var e=this._extent,i=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]);return t>=i&&t<=n},r.prototype.containData=function(t){return this.scale.contain(t)},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.getPixelPrecision=function(t){return I_(t||this.scale.getExtent(),this._extent)},r.prototype.setExtent=function(t,e){var i=this._extent;i[0]=t,i[1]=e},r.prototype.dataToCoord=function(t,e){var i=this._extent,n=this.scale;return t=n.normalize(t),this.onBand&&n.type==="ordinal"&&(i=i.slice(),xv(i,n.count())),dh(t,bv,i,e)},r.prototype.coordToData=function(t,e){var i=this._extent,n=this.scale;this.onBand&&n.type==="ordinal"&&(i=i.slice(),xv(i,n.count()));var a=dh(t,i,bv,e);return this.scale.scale(a)},r.prototype.pointToData=function(t,e){},r.prototype.getTicksCoords=function(t){t=t||{};var e=t.tickModel||this.getTickModel(),i=iD(this,e),n=i.ticks,a=V(n,function(s){return{coord:this.dataToCoord(this.scale.type==="ordinal"?this.scale.getRawOrdinalNumber(s):s),tickValue:s}},this),o=e.get("alignWithLabel");return hD(this,a,o,t.clamp),a},r.prototype.getMinorTicksCoords=function(){if(this.scale.type==="ordinal")return[];var t=this.model.getModel("minorTick"),e=t.get("splitNumber");e>0&&e<100||(e=5);var i=this.scale.getMinorTicks(e),n=V(i,function(a){return V(a,function(o){return{coord:this.dataToCoord(o),tickValue:o}},this)},this);return n},r.prototype.getViewLabels=function(){return rD(this).labels},r.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},r.prototype.getTickModel=function(){return this.model.getModel("axisTick")},r.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),i=e[1]-e[0]+(this.onBand?1:0);i===0&&(i=1);var n=Math.abs(t[1]-t[0]);return Math.abs(n)/i},r.prototype.calculateCategoryInterval=function(){return lD(this)},r}();function xv(r,t){var e=r[1]-r[0],i=t,n=e/i/2;r[0]+=n,r[1]-=n}function hD(r,t,e,i){var n=t.length;if(!r.onBand||e||!n)return;var a=r.getExtent(),o,s;if(n===1)t[0].coord=a[0],o=t[1]={coord:a[1],tickValue:t[0].tickValue};else{var l=t[n-1].tickValue-t[0].tickValue,u=(t[n-1].coord-t[0].coord)/l;M(t,function(c){c.coord-=u/2});var f=r.scale.getExtent();s=1+f[1]-t[n-1].tickValue,o={coord:t[n-1].coord+u*s,tickValue:f[1]+1},t.push(o)}var h=a[0]>a[1];v(t[0].coord,a[0])&&(i?t[0].coord=a[0]:t.shift()),i&&v(a[0],t[0].coord)&&t.unshift({coord:a[0]}),v(a[1],o.coord)&&(i?o.coord=a[1]:t.pop()),i&&v(o.coord,a[1])&&t.push({coord:a[1]});function v(c,d){return c=_t(c),d=_t(d),h?c>d:c<d}}const cD=fD;function vD(r){for(var t=[],e=0;e<r.length;e++){var i=r[e];if(!i.defaultAttr.ignore){var n=i.label,a=n.getComputedTransform(),o=n.getBoundingRect(),s=!a||a[1]<1e-5&&a[2]<1e-5,l=n.style.margin||0,u=o.clone();u.applyTransform(a),u.x-=l/2,u.y-=l/2,u.width+=l,u.height+=l;var f=s?new mo(o,a):null;t.push({label:n,labelLine:i.labelLine,rect:u,localRect:o,obb:f,priority:i.priority,defaultAttr:i.defaultAttr,layoutOption:i.computedLayoutOption,axisAligned:s,transform:a})}}return t}function dD(r){var t=[];r.sort(function(y,p){return p.priority-y.priority});var e=new rt(0,0,0,0);function i(y){if(!y.ignore){var p=y.ensureState("emphasis");p.ignore==null&&(p.ignore=!1)}y.ignore=!0}for(var n=0;n<r.length;n++){var a=r[n],o=a.axisAligned,s=a.localRect,l=a.transform,u=a.label,f=a.labelLine;e.copy(a.rect),e.width-=.1,e.height-=.1,e.x+=.05,e.y+=.05;for(var h=a.obb,v=!1,c=0;c<t.length;c++){var d=t[c];if(e.intersect(d.rect)){if(o&&d.axisAligned){v=!0;break}if(d.obb||(d.obb=new mo(d.localRect,d.transform)),h||(h=new mo(s,l)),h.intersect(d.obb)){v=!0;break}}}v?(i(u),f&&i(f)):(u.attr("ignore",a.defaultAttr.ignore),f&&f.attr("ignore",a.defaultAttr.labelGuideIgnore),t.push(a))}}function Tv(r,t,e){var i=Ni.createCanvas(),n=t.getWidth(),a=t.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=n+"px",o.height=a+"px",i.setAttribute("data-zr-dom-id",r)),i.width=n*e,i.height=a*e,i}var pD=function(r){O(t,r);function t(e,i,n){var a=r.call(this)||this;a.motionBlur=!1,a.lastFrameAlpha=.7,a.dpr=1,a.virtual=!1,a.config={},a.incremental=!1,a.zlevel=0,a.maxRepaintRectCount=5,a.__dirty=!0,a.__firstTimePaint=!0,a.__used=!1,a.__drawIndex=0,a.__startIndex=0,a.__endIndex=0,a.__prevStartIndex=null,a.__prevEndIndex=null;var o;n=n||co,typeof e=="string"?o=Tv(e,i,n):H(e)&&(o=e,e=o.id),a.id=e,a.dom=o;var s=o.style;return s&&(wd(o),o.onselectstart=function(){return!1},s.padding="0",s.margin="0",s.borderWidth="0"),a.painter=i,a.dpr=n,a}return t.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},t.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},t.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},t.prototype.setUnpainted=function(){this.__firstTimePaint=!0},t.prototype.createBackBuffer=function(){var e=this.dpr;this.domBack=Tv("back-"+this.id,this.painter,e),this.ctxBack=this.domBack.getContext("2d"),e!==1&&this.ctxBack.scale(e,e)},t.prototype.createRepaintRects=function(e,i,n,a){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var o=[],s=this.maxRepaintRectCount,l=!1,u=new rt(0,0,0,0);function f(m){if(!(!m.isFinite()||m.isZero()))if(o.length===0){var _=new rt(0,0,0,0);_.copy(m),o.push(_)}else{for(var S=!1,b=1/0,w=0,x=0;x<o.length;++x){var A=o[x];if(A.intersect(m)){var C=new rt(0,0,0,0);C.copy(A),C.union(m),o[x]=C,S=!0;break}else if(l){u.copy(m),u.union(A);var D=m.width*m.height,T=A.width*A.height,L=u.width*u.height,P=L-D-T;P<b&&(b=P,w=x)}}if(l&&(o[w].union(m),S=!0),!S){var _=new rt(0,0,0,0);_.copy(m),o.push(_)}l||(l=o.length>=s)}}for(var h=this.__startIndex;h<this.__endIndex;++h){var v=e[h];if(v){var c=v.shouldBePainted(n,a,!0,!0),d=v.__isRendered&&(v.__dirty&jt||!c)?v.getPrevPaintRect():null;d&&f(d);var y=c&&(v.__dirty&jt||!v.__isRendered)?v.getPaintRect():null;y&&f(y)}}for(var h=this.__prevStartIndex;h<this.__prevEndIndex;++h){var v=i[h],c=v&&v.shouldBePainted(n,a,!0,!0);if(v&&(!c||!v.__zr)&&v.__isRendered){var d=v.getPrevPaintRect();d&&f(d)}}var p;do{p=!1;for(var h=0;h<o.length;){if(o[h].isZero()){o.splice(h,1);continue}for(var g=h+1;g<o.length;)o[h].intersect(o[g])?(p=!0,o[h].union(o[g]),o.splice(g,1)):g++;h++}}while(p);return this._paintRects=o,o},t.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},t.prototype.resize=function(e,i){var n=this.dpr,a=this.dom,o=a.style,s=this.domBack;o&&(o.width=e+"px",o.height=i+"px"),a.width=e*n,a.height=i*n,s&&(s.width=e*n,s.height=i*n,n!==1&&this.ctxBack.scale(n,n))},t.prototype.clear=function(e,i,n){var a=this.dom,o=this.ctx,s=a.width,l=a.height;i=i||this.clearColor;var u=this.motionBlur&&!e,f=this.lastFrameAlpha,h=this.dpr,v=this;u&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(a,0,0,s/h,l/h));var c=this.domBack;function d(y,p,g,m){if(o.clearRect(y,p,g,m),i&&i!=="transparent"){var _=void 0;if(Io(i)){var S=i.global||i.__width===g&&i.__height===m;_=S&&i.__canvasGradient||au(o,i,{x:0,y:0,width:g,height:m}),i.__canvasGradient=_,i.__width=g,i.__height=m}else Km(i)&&(i.scaleX=i.scaleX||h,i.scaleY=i.scaleY||h,_=ou(o,i,{dirty:function(){v.setUnpainted(),v.painter.refresh()}}));o.save(),o.fillStyle=_||i,o.fillRect(y,p,g,m),o.restore()}u&&(o.save(),o.globalAlpha=f,o.drawImage(c,y,p,g,m),o.restore())}!n||u?d(0,0,s,l):n.length&&M(n,function(y){d(y.x*h,y.y*h,y.width*h,y.height*h)})},t}(Ie);const ul=pD;var Cv=1e5,Fr=314159,Ha=.01,gD=.001;function yD(r){return r?r.__builtin__?!0:!(typeof r.resize!="function"||typeof r.refresh!="function"):!1}function mD(r,t){var e=document.createElement("div");return e.style.cssText=["position:relative","width:"+r+"px","height:"+t+"px","padding:0","margin:0","border-width:0"].join(";")+";",e}var _D=function(){function r(t,e,i,n){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var a=!t.nodeName||t.nodeName.toUpperCase()==="CANVAS";this._opts=i=k({},i||{}),this.dpr=i.devicePixelRatio||co,this._singleCanvas=a,this.root=t;var o=t.style;o&&(wd(t),t.innerHTML=""),this.storage=e;var s=this._zlevelList;this._prevDisplayList=[];var l=this._layers;if(a){var f=t,h=f.width,v=f.height;i.width!=null&&(h=i.width),i.height!=null&&(v=i.height),this.dpr=i.devicePixelRatio||1,f.width=h*this.dpr,f.height=v*this.dpr,this._width=h,this._height=v;var c=new ul(f,this,this.dpr);c.__builtin__=!0,c.initContext(),l[Fr]=c,c.zlevel=Fr,s.push(Fr),this._domRoot=t}else{this._width=Ra(t,0,i),this._height=Ra(t,1,i);var u=this._domRoot=mD(this._width,this._height);t.appendChild(u)}}return r.prototype.getType=function(){return"canvas"},r.prototype.isSingleCanvas=function(){return this._singleCanvas},r.prototype.getViewportRoot=function(){return this._domRoot},r.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},r.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),i=this._prevDisplayList,n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,i,t,this._redrawId);for(var a=0;a<n.length;a++){var o=n[a],s=this._layers[o];if(!s.__builtin__&&s.refresh){var l=a===0?this._backgroundColor:null;s.refresh(l)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},r.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},r.prototype._paintHoverList=function(t){var e=t.length,i=this._hoverlayer;if(i&&i.clear(),!!e){for(var n={inHover:!0,viewWidth:this._width,viewHeight:this._height},a,o=0;o<e;o++){var s=t[o];s.__inHover&&(i||(i=this._hoverlayer=this.getLayer(Cv)),a||(a=i.ctx,a.save()),Wr(a,s,n,o===e-1))}a&&a.restore()}},r.prototype.getHoverLayer=function(){return this.getLayer(Cv)},r.prototype.paintOne=function(t,e){Qg(t,e)},r.prototype._paintList=function(t,e,i,n){if(this._redrawId===n){i=i||!1,this._updateLayerStatus(t);var a=this._doPaintList(t,e,i),o=a.finished,s=a.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),s&&this._paintHoverList(t),o)this.eachLayer(function(u){u.afterBrush&&u.afterBrush()});else{var l=this;Ml(function(){l._paintList(t,e,i,n)})}}},r.prototype._compositeManually=function(){var t=this.getLayer(Fr).ctx,e=this._domRoot.width,i=this._domRoot.height;t.clearRect(0,0,e,i),this.eachBuiltinLayer(function(n){n.virtual&&t.drawImage(n.dom,0,0,e,i)})},r.prototype._doPaintList=function(t,e,i){for(var n=this,a=[],o=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var l=this._zlevelList[s],u=this._layers[l];u.__builtin__&&u!==this._hoverlayer&&(u.__dirty||i)&&a.push(u)}for(var f=!0,h=!1,v=function(y){var p=a[y],g=p.ctx,m=o&&p.createRepaintRects(t,e,c._width,c._height),_=i?p.__startIndex:p.__drawIndex,S=!i&&p.incremental&&Date.now,b=S&&Date.now(),w=p.zlevel===c._zlevelList[0]?c._backgroundColor:null;if(p.__startIndex===p.__endIndex)p.clear(!1,w,m);else if(_===p.__startIndex){var x=t[_];(!x.incremental||!x.notClear||i)&&p.clear(!1,w,m)}_===-1&&(console.error("For some unknown reason. drawIndex is -1"),_=p.__startIndex);var A,C=function(P){var I={inHover:!1,allClipped:!1,prevEl:null,viewWidth:n._width,viewHeight:n._height};for(A=_;A<p.__endIndex;A++){var R=t[A];if(R.__inHover&&(h=!0),n._doPaintEl(R,p,o,P,I,A===p.__endIndex-1),S){var E=Date.now()-b;if(E>15)break}}I.prevElClipPaths&&g.restore()};if(m)if(m.length===0)A=p.__endIndex;else for(var D=c.dpr,T=0;T<m.length;++T){var L=m[T];g.save(),g.beginPath(),g.rect(L.x*D,L.y*D,L.width*D,L.height*D),g.clip(),C(L),g.restore()}else g.save(),C(),g.restore();p.__drawIndex=A,p.__drawIndex<p.__endIndex&&(f=!1)},c=this,d=0;d<a.length;d++)v(d);return Y.wxa&&M(this._layers,function(y){y&&y.ctx&&y.ctx.draw&&y.ctx.draw()}),{finished:f,needsRefreshHover:h}},r.prototype._doPaintEl=function(t,e,i,n,a,o){var s=e.ctx;if(i){var l=t.getPaintRect();(!n||l&&l.intersect(n))&&(Wr(s,t,a,o),t.setPrevPaintRect(l))}else Wr(s,t,a,o)},r.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=Fr);var i=this._layers[t];return i||(i=new ul("zr_"+t,this,this.dpr),i.zlevel=t,i.__builtin__=!0,this._layerConfig[t]?et(i,this._layerConfig[t],!0):this._layerConfig[t-Ha]&&et(i,this._layerConfig[t-Ha],!0),e&&(i.virtual=e),this.insertLayer(t,i),i.initContext()),i},r.prototype.insertLayer=function(t,e){var i=this._layers,n=this._zlevelList,a=n.length,o=this._domRoot,s=null,l=-1;if(!i[t]&&yD(e)){if(a>0&&t>n[0]){for(l=0;l<a-1&&!(n[l]<t&&n[l+1]>t);l++);s=i[n[l]]}if(n.splice(l+1,0,t),i[t]=e,!e.virtual)if(s){var u=s.dom;u.nextSibling?o.insertBefore(e.dom,u.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.painter||(e.painter=this)}},r.prototype.eachLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n];t.call(e,this._layers[a],a)}},r.prototype.eachBuiltinLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],o=this._layers[a];o.__builtin__&&t.call(e,o,a)}},r.prototype.eachOtherLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],o=this._layers[a];o.__builtin__||t.call(e,o,a)}},r.prototype.getLayers=function(){return this._layers},r.prototype._updateLayerStatus=function(t){this.eachBuiltinLayer(function(h,v){h.__dirty=h.__used=!1});function e(h){a&&(a.__endIndex!==h&&(a.__dirty=!0),a.__endIndex=h)}if(this._singleCanvas)for(var i=1;i<t.length;i++){var n=t[i];if(n.zlevel!==t[i-1].zlevel||n.incremental){this._needsManuallyCompositing=!0;break}}var a=null,o=0,s,l;for(l=0;l<t.length;l++){var n=t[l],u=n.zlevel,f=void 0;s!==u&&(s=u,o=0),n.incremental?(f=this.getLayer(u+gD,this._needsManuallyCompositing),f.incremental=!0,o=1):f=this.getLayer(u+(o>0?Ha:0),this._needsManuallyCompositing),f.__builtin__||Mu("ZLevel "+u+" has been used by unkown layer "+f.id),f!==a&&(f.__used=!0,f.__startIndex!==l&&(f.__dirty=!0),f.__startIndex=l,f.incremental?f.__drawIndex=-1:f.__drawIndex=l,e(l),a=f),n.__dirty&jt&&!n.__inHover&&(f.__dirty=!0,f.incremental&&f.__drawIndex<0&&(f.__drawIndex=l))}e(l),this.eachBuiltinLayer(function(h,v){!h.__used&&h.getElementCount()>0&&(h.__dirty=!0,h.__startIndex=h.__endIndex=h.__drawIndex=0),h.__dirty&&h.__drawIndex<0&&(h.__drawIndex=h.__startIndex)})},r.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},r.prototype._clearLayer=function(t){t.clear()},r.prototype.setBackgroundColor=function(t){this._backgroundColor=t,M(this._layers,function(e){e.setUnpainted()})},r.prototype.configLayer=function(t,e){if(e){var i=this._layerConfig;i[t]?et(i[t],e,!0):i[t]=e;for(var n=0;n<this._zlevelList.length;n++){var a=this._zlevelList[n];if(a===t||a===t+Ha){var o=this._layers[a];et(o,i[t],!0)}}}},r.prototype.delLayer=function(t){var e=this._layers,i=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],i.splice(lt(i,t),1))},r.prototype.resize=function(t,e){if(this._domRoot.style){var i=this._domRoot;i.style.display="none";var n=this._opts,a=this.root;if(t!=null&&(n.width=t),e!=null&&(n.height=e),t=Ra(a,0,n),e=Ra(a,1,n),i.style.display="",this._width!==t||e!==this._height){i.style.width=t+"px",i.style.height=e+"px";for(var o in this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(t==null||e==null)return;this._width=t,this._height=e,this.getLayer(Fr).resize(t,e)}return this},r.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},r.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},r.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[Fr].dom;var e=new ul("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var i=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,a=e.dom.height;this.eachLayer(function(h){h.__builtin__?i.drawImage(h.dom,0,0,n,a):h.renderToCanvas&&(i.save(),h.renderToCanvas(i),i.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},s=this.storage.getDisplayList(!0),l=0,u=s.length;l<u;l++){var f=s[l];Wr(i,f,o,l===u-1)}return e.dom},r.prototype.getWidth=function(){return this._width},r.prototype.getHeight=function(){return this._height},r}();const SD=_D;function wD(r){r.registerPainter("canvas",SD)}var bD=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.hasSymbolVisual=!0,e}return t.prototype.getInitialData=function(e){return dC(null,this,{useEncodeDefaulter:!0})},t.prototype.getLegendIcon=function(e){var i=new Ft,n=Bi("line",0,e.itemHeight/2,e.itemWidth,0,e.lineStyle.stroke,!1);i.add(n),n.setStyle(e.lineStyle);var a=this.getData().getVisual("symbol"),o=this.getData().getVisual("symbolRotate"),s=a==="none"?"circle":a,l=e.itemHeight*.8,u=Bi(s,(e.itemWidth-l)/2,(e.itemHeight-l)/2,l,l,e.itemStyle.fill);i.add(u),u.setStyle(e.itemStyle);var f=e.iconRotate==="inherit"?o:e.iconRotate||0;return u.rotation=f*Math.PI/180,u.setOrigin([e.itemWidth/2,e.itemHeight/2]),s.indexOf("empty")>-1&&(u.style.stroke=u.style.fill,u.style.fill="#fff",u.style.lineWidth=2),i},t.type="series.line",t.dependencies=["grid","polar"],t.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1},t}(Un);const xD=bD;function Gy(r,t){var e=r.mapDimensionsAll("defaultedLabel"),i=e.length;if(i===1){var n=Oi(r,t,e[0]);return n!=null?n+"":null}else if(i){for(var a=[],o=0;o<e.length;o++)a.push(Oi(r,t,e[o]));return a.join(" ")}}function TD(r,t){var e=r.mapDimensionsAll("defaultedLabel");if(!N(t))return t+"";for(var i=[],n=0;n<e.length;n++){var a=r.getDimensionIndex(e[n]);a>=0&&i.push(t[a])}return i.join(" ")}var CD=function(r){O(t,r);function t(e,i,n,a){var o=r.call(this)||this;return o.updateData(e,i,n,a),o}return t.prototype._createSymbol=function(e,i,n,a,o){this.removeAll();var s=Bi(e,-1,-1,2,2,null,o);s.attr({z2:100,culling:!0,scaleX:a[0]/2,scaleY:a[1]/2}),s.drift=DD,this._symbolType=e,this.add(s)},t.prototype.stopSymbolAnimation=function(e){this.childAt(0).stopAnimation(null,e)},t.prototype.getSymbolType=function(){return this._symbolType},t.prototype.getSymbolPath=function(){return this.childAt(0)},t.prototype.highlight=function(){go(this.childAt(0))},t.prototype.downplay=function(){yo(this.childAt(0))},t.prototype.setZ=function(e,i){var n=this.childAt(0);n.zlevel=e,n.z=i},t.prototype.setDraggable=function(e,i){var n=this.childAt(0);n.draggable=e,n.cursor=!i&&e?"move":n.cursor},t.prototype.updateData=function(e,i,n,a){this.silent=!1;var o=e.getItemVisual(i,"symbol")||"circle",s=e.hostModel,l=t.getSymbolSize(e,i),u=o!==this._symbolType,f=a&&a.disableAnimation;if(u){var h=e.getItemVisual(i,"symbolKeepAspect");this._createSymbol(o,e,i,l,h)}else{var v=this.childAt(0);v.silent=!1;var c={scaleX:l[0]/2,scaleY:l[1]/2};f?v.attr(c):hr(v,c,s,i),ew(v)}if(this._updateCommon(e,i,l,n,a),u){var v=this.childAt(0);if(!f){var c={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:v.style.opacity}};v.scaleX=v.scaleY=0,v.style.opacity=0,ra(v,c,s,i)}}f&&this.childAt(0).stopAnimation("leave")},t.prototype._updateCommon=function(e,i,n,a,o){var s=this.childAt(0),l=e.hostModel,u,f,h,v,c,d,y,p,g;if(a&&(u=a.emphasisItemStyle,f=a.blurItemStyle,h=a.selectItemStyle,v=a.focus,c=a.blurScope,y=a.labelStatesModels,p=a.hoverScale,g=a.cursorStyle,d=a.emphasisDisabled),!a||e.hasItemOption){var m=a&&a.itemModel?a.itemModel:e.getItemModel(i),_=m.getModel("emphasis");u=_.getModel("itemStyle").getItemStyle(),h=m.getModel(["select","itemStyle"]).getItemStyle(),f=m.getModel(["blur","itemStyle"]).getItemStyle(),v=_.get("focus"),c=_.get("blurScope"),d=_.get("disabled"),y=ef(m),p=_.getShallow("scale"),g=m.getShallow("cursor")}var S=e.getItemVisual(i,"symbolRotate");s.attr("rotation",(S||0)*Math.PI/180||0);var b=Xg(e.getItemVisual(i,"symbolOffset"),n);b&&(s.x=b[0],s.y=b[1]),g&&s.attr("cursor",g);var w=e.getItemVisual(i,"style"),x=w.fill;if(s instanceof ei){var A=s.style;s.useStyle(k({image:A.image,x:A.x,y:A.y,width:A.width,height:A.height},w))}else s.__isEmptyBrush?s.useStyle(k({},w)):s.useStyle(w),s.style.decal=null,s.setColor(x,o&&o.symbolInnerColor),s.style.strokeNoScale=!0;var C=e.getItemVisual(i,"liftZ"),D=this._z2;C!=null?D==null&&(this._z2=s.z2,s.z2+=C):D!=null&&(s.z2=D,this._z2=null);var T=o&&o.useNameLabel;tf(s,y,{labelFetcher:l,labelDataIndex:i,defaultText:L,inheritColor:x,defaultOpacity:w.opacity});function L(R){return T?e.getName(R):Gy(e,R)}this._sizeX=n[0]/2,this._sizeY=n[1]/2;var P=s.ensureState("emphasis");P.style=u,s.ensureState("select").style=h,s.ensureState("blur").style=f;var I=p==null||p===!0?Math.max(1.1,3/this._sizeY):isFinite(p)&&p>0?+p:1;P.scaleX=this._sizeX*I,P.scaleY=this._sizeY*I,this.setSymbolScale(1),$l(this,v,c,d)},t.prototype.setSymbolScale=function(e){this.scaleX=this.scaleY=e},t.prototype.fadeOut=function(e,i,n){var a=this.childAt(0),o=at(this).dataIndex,s=n&&n.animation;if(this.silent=a.silent=!0,n&&n.fadeLabel){var l=a.getTextContent();l&&_o(l,{style:{opacity:0}},i,{dataIndex:o,removeOpt:s,cb:function(){a.removeTextContent()}})}else a.removeTextContent();_o(a,{style:{opacity:0},scaleX:0,scaleY:0},i,{dataIndex:o,cb:e,removeOpt:s})},t.getSymbolSize=function(e,i){return Qx(e.getItemVisual(i,"symbolSize"))},t}(Ft);function DD(r,t){this.parent.drift(r,t)}const Df=CD;function fl(r,t,e,i){return t&&!isNaN(t[0])&&!isNaN(t[1])&&!(i.isIgnore&&i.isIgnore(e))&&!(i.clipShape&&!i.clipShape.contain(t[0],t[1]))&&r.getItemVisual(e,"symbol")!=="none"}function Dv(r){return r!=null&&!H(r)&&(r={isIgnore:r}),r||{}}function Mv(r){var t=r.hostModel,e=t.getModel("emphasis");return{emphasisItemStyle:e.getModel("itemStyle").getItemStyle(),blurItemStyle:t.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:t.getModel(["select","itemStyle"]).getItemStyle(),focus:e.get("focus"),blurScope:e.get("blurScope"),emphasisDisabled:e.get("disabled"),hoverScale:e.get("scale"),labelStatesModels:ef(t),cursorStyle:t.get("cursor")}}var MD=function(){function r(t){this.group=new Ft,this._SymbolCtor=t||Df}return r.prototype.updateData=function(t,e){this._progressiveEls=null,e=Dv(e);var i=this.group,n=t.hostModel,a=this._data,o=this._SymbolCtor,s=e.disableAnimation,l=Mv(t),u={disableAnimation:s},f=e.getSymbolPoint||function(h){return t.getItemLayout(h)};a||i.removeAll(),t.diff(a).add(function(h){var v=f(h);if(fl(t,v,h,e)){var c=new o(t,h,l,u);c.setPosition(v),t.setItemGraphicEl(h,c),i.add(c)}}).update(function(h,v){var c=a.getItemGraphicEl(v),d=f(h);if(!fl(t,d,h,e)){i.remove(c);return}var y=t.getItemVisual(h,"symbol")||"circle",p=c&&c.getSymbolType&&c.getSymbolType();if(!c||p&&p!==y)i.remove(c),c=new o(t,h,l,u),c.setPosition(d);else{c.updateData(t,h,l,u);var g={x:d[0],y:d[1]};s?c.attr(g):hr(c,g,n)}i.add(c),t.setItemGraphicEl(h,c)}).remove(function(h){var v=a.getItemGraphicEl(h);v&&v.fadeOut(function(){i.remove(v)},n)}).execute(),this._getSymbolPoint=f,this._data=t},r.prototype.updateLayout=function(){var t=this,e=this._data;e&&e.eachItemGraphicEl(function(i,n){var a=t._getSymbolPoint(n);i.setPosition(a),i.markRedraw()})},r.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=Mv(t),this._data=null,this.group.removeAll()},r.prototype.incrementalUpdate=function(t,e,i){this._progressiveEls=[],i=Dv(i);function n(l){l.isGroup||(l.incremental=!0,l.ensureState("emphasis").hoverLayer=!0)}for(var a=t.start;a<t.end;a++){var o=e.getItemLayout(a);if(fl(e,o,a,i)){var s=new this._SymbolCtor(e,a,this._seriesScope);s.traverse(n),s.setPosition(o),this.group.add(s),e.setItemGraphicEl(a,s),this._progressiveEls.push(s)}}},r.prototype.eachRendered=function(t){ju(this._progressiveEls||this.group,t)},r.prototype.remove=function(t){var e=this.group,i=this._data;i&&t?i.eachItemGraphicEl(function(n){n.fadeOut(function(){e.remove(n)},i.hostModel)}):e.removeAll()},r}();const AD=MD;function Vy(r,t,e){var i=r.getBaseAxis(),n=r.getOtherAxis(i),a=LD(n,e),o=i.dim,s=n.dim,l=t.mapDimension(s),u=t.mapDimension(o),f=s==="x"||s==="radius"?1:0,h=V(r.dimensions,function(d){return t.mapDimension(d)}),v=!1,c=t.getCalculationInfo("stackResultDimension");return Xn(t,h[0])&&(v=!0,h[0]=c),Xn(t,h[1])&&(v=!0,h[1]=c),{dataDimsForPoint:h,valueStart:a,valueAxisDim:s,baseAxisDim:o,stacked:!!v,valueDim:l,baseDim:u,baseDataOffset:f,stackedOverDimension:t.getCalculationInfo("stackedOverDimension")}}function LD(r,t){var e=0,i=r.scale.getExtent();return t==="start"?e=i[0]:t==="end"?e=i[1]:yt(t)&&!isNaN(t)?e=t:i[0]>0?e=i[0]:i[1]<0&&(e=i[1]),e}function Wy(r,t,e,i){var n=NaN;r.stacked&&(n=e.get(e.getCalculationInfo("stackedOverDimension"),i)),isNaN(n)&&(n=r.valueStart);var a=r.baseDataOffset,o=[];return o[a]=e.get(r.baseDim,i),o[1-a]=n,t.dataToPoint(o)}function ID(r,t){var e=[];return t.diff(r).add(function(i){e.push({cmd:"+",idx:i})}).update(function(i,n){e.push({cmd:"=",idx:n,idx1:i})}).remove(function(i){e.push({cmd:"-",idx:i})}).execute(),e}function PD(r,t,e,i,n,a,o,s){for(var l=ID(r,t),u=[],f=[],h=[],v=[],c=[],d=[],y=[],p=Vy(n,t,o),g=r.getLayout("points")||[],m=t.getLayout("points")||[],_=0;_<l.length;_++){var S=l[_],b=!0,w=void 0,x=void 0;switch(S.cmd){case"=":w=S.idx*2,x=S.idx1*2;var A=g[w],C=g[w+1],D=m[x],T=m[x+1];(isNaN(A)||isNaN(C))&&(A=D,C=T),u.push(A,C),f.push(D,T),h.push(e[w],e[w+1]),v.push(i[x],i[x+1]),y.push(t.getRawIndex(S.idx1));break;case"+":var L=S.idx,P=p.dataDimsForPoint,I=n.dataToPoint([t.get(P[0],L),t.get(P[1],L)]);x=L*2,u.push(I[0],I[1]),f.push(m[x],m[x+1]);var R=Wy(p,n,t,L);h.push(R[0],R[1]),v.push(i[x],i[x+1]),y.push(t.getRawIndex(L));break;case"-":b=!1}b&&(c.push(S),d.push(d.length))}d.sort(function(ct,qt){return y[ct]-y[qt]});for(var E=u.length,G=Di(E),B=Di(E),F=Di(E),$=Di(E),it=[],_=0;_<d.length;_++){var J=d[_],st=_*2,ht=J*2;G[st]=u[ht],G[st+1]=u[ht+1],B[st]=f[ht],B[st+1]=f[ht+1],F[st]=h[ht],F[st+1]=h[ht+1],$[st]=v[ht],$[st+1]=v[ht+1],it[_]=c[J]}return{current:G,next:B,stackedOnCurrent:F,stackedOnNext:$,status:it}}var je=Math.min,tr=Math.max;function Yr(r,t){return isNaN(r)||isNaN(t)}function vu(r,t,e,i,n,a,o,s,l){for(var u,f,h,v,c,d,y=e,p=0;p<i;p++){var g=t[y*2],m=t[y*2+1];if(y>=n||y<0)break;if(Yr(g,m)){if(l){y+=a;continue}break}if(y===e)r[a>0?"moveTo":"lineTo"](g,m),h=g,v=m;else{var _=g-u,S=m-f;if(_*_+S*S<.5){y+=a;continue}if(o>0){for(var b=y+a,w=t[b*2],x=t[b*2+1];w===g&&x===m&&p<i;)p++,b+=a,y+=a,w=t[b*2],x=t[b*2+1],g=t[y*2],m=t[y*2+1],_=g-u,S=m-f;var A=p+1;if(l)for(;Yr(w,x)&&A<i;)A++,b+=a,w=t[b*2],x=t[b*2+1];var C=.5,D=0,T=0,L=void 0,P=void 0;if(A>=i||Yr(w,x))c=g,d=m;else{D=w-u,T=x-f;var I=g-u,R=w-g,E=m-f,G=x-m,B=void 0,F=void 0;if(s==="x"){B=Math.abs(I),F=Math.abs(R);var $=D>0?1:-1;c=g-$*B*o,d=m,L=g+$*F*o,P=m}else if(s==="y"){B=Math.abs(E),F=Math.abs(G);var it=T>0?1:-1;c=g,d=m-it*B*o,L=g,P=m+it*F*o}else B=Math.sqrt(I*I+E*E),F=Math.sqrt(R*R+G*G),C=F/(F+B),c=g-D*o*(1-C),d=m-T*o*(1-C),L=g+D*o*C,P=m+T*o*C,L=je(L,tr(w,g)),P=je(P,tr(x,m)),L=tr(L,je(w,g)),P=tr(P,je(x,m)),D=L-g,T=P-m,c=g-D*B/F,d=m-T*B/F,c=je(c,tr(u,g)),d=je(d,tr(f,m)),c=tr(c,je(u,g)),d=tr(d,je(f,m)),D=g-c,T=m-d,L=g+D*F/B,P=m+T*F/B}r.bezierCurveTo(h,v,c,d,g,m),h=L,v=P}else r.lineTo(g,m)}u=g,f=m,y+=a}return p}var Uy=function(){function r(){this.smooth=0,this.smoothConstraint=!0}return r}(),RD=function(r){O(t,r);function t(e){var i=r.call(this,e)||this;return i.type="ec-polyline",i}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Uy},t.prototype.buildPath=function(e,i){var n=i.points,a=0,o=n.length/2;if(i.connectNulls){for(;o>0&&Yr(n[o*2-2],n[o*2-1]);o--);for(;a<o&&Yr(n[a*2],n[a*2+1]);a++);}for(;a<o;)a+=vu(e,n,a,o,o,1,i.smooth,i.smoothMonotone,i.connectNulls)+1},t.prototype.getPointOn=function(e,i){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var n=this.path,a=n.data,o=Jr.CMD,s,l,u=i==="x",f=[],h=0;h<a.length;){var v=a[h++],c=void 0,d=void 0,y=void 0,p=void 0,g=void 0,m=void 0,_=void 0;switch(v){case o.M:s=a[h++],l=a[h++];break;case o.L:if(c=a[h++],d=a[h++],_=u?(e-s)/(c-s):(e-l)/(d-l),_<=1&&_>=0){var S=u?(d-l)*_+l:(c-s)*_+s;return u?[e,S]:[S,e]}s=c,l=d;break;case o.C:c=a[h++],d=a[h++],y=a[h++],p=a[h++],g=a[h++],m=a[h++];var b=u?lo(s,c,y,g,e,f):lo(l,d,p,m,e,f);if(b>0)for(var w=0;w<b;w++){var x=f[w];if(x<=1&&x>=0){var S=u?St(l,d,p,m,x):St(s,c,y,g,x);return u?[e,S]:[S,e]}}s=g,l=m;break}}},t}(ft),ED=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t}(Uy),kD=function(r){O(t,r);function t(e){var i=r.call(this,e)||this;return i.type="ec-polygon",i}return t.prototype.getDefaultShape=function(){return new ED},t.prototype.buildPath=function(e,i){var n=i.points,a=i.stackedOnPoints,o=0,s=n.length/2,l=i.smoothMonotone;if(i.connectNulls){for(;s>0&&Yr(n[s*2-2],n[s*2-1]);s--);for(;o<s&&Yr(n[o*2],n[o*2+1]);o++);}for(;o<s;){var u=vu(e,n,o,s,s,1,i.smooth,l,i.connectNulls);vu(e,a,o+u-1,u,s,-1,i.stackedOnSmooth,l,i.connectNulls),o+=u+1,e.closePath()}},t}(ft);function OD(r,t,e,i,n){var a=r.getArea(),o=a.x,s=a.y,l=a.width,u=a.height,f=e.get(["lineStyle","width"])||0;o-=f/2,s-=f/2,l+=f,u+=f,l=Math.ceil(l),o!==Math.floor(o)&&(o=Math.floor(o),l++);var h=new Ct({shape:{x:o,y:s,width:l,height:u}});if(t){var v=r.getBaseAxis(),c=v.isHorizontal(),d=v.inverse;c?(d&&(h.shape.x+=l),h.shape.width=0):(d||(h.shape.y+=u),h.shape.height=0);var y=U(n)?function(p){n(p,h)}:null;ra(h,{shape:{width:l,height:u,x:o,y:s}},e,null,i,y)}return h}function BD(r,t,e){var i=r.getArea(),n=_t(i.r0,1),a=_t(i.r,1),o=new Xu({shape:{cx:_t(r.cx,1),cy:_t(r.cy,1),r0:n,r:a,startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}});if(t){var s=r.getBaseAxis().dim==="angle";s?o.shape.endAngle=i.startAngle:o.shape.r=n,ra(o,{shape:{endAngle:i.endAngle,r:a}},e)}return o}function ND(r,t){return r.type===t}function Av(r,t){if(r.length===t.length){for(var e=0;e<r.length;e++)if(r[e]!==t[e])return;return!0}}function Lv(r){for(var t=1/0,e=1/0,i=-1/0,n=-1/0,a=0;a<r.length;){var o=r[a++],s=r[a++];isNaN(o)||(t=Math.min(o,t),i=Math.max(o,i)),isNaN(s)||(e=Math.min(s,e),n=Math.max(s,n))}return[[t,e],[i,n]]}function Iv(r,t){var e=Lv(r),i=e[0],n=e[1],a=Lv(t),o=a[0],s=a[1];return Math.max(Math.abs(i[0]-o[0]),Math.abs(i[1]-o[1]),Math.abs(n[0]-s[0]),Math.abs(n[1]-s[1]))}function Pv(r){return yt(r)?r:r?.5:0}function FD(r,t,e){if(!e.valueDim)return[];for(var i=t.count(),n=Di(i*2),a=0;a<i;a++){var o=Wy(e,r,t,a);n[a*2]=o[0],n[a*2+1]=o[1]}return n}function er(r,t,e,i,n){var a=e.getBaseAxis(),o=a.dim==="x"||a.dim==="radius"?0:1,s=[],l=0,u=[],f=[],h=[],v=[];if(n){for(l=0;l<r.length;l+=2){var c=t||r;!isNaN(c[l])&&!isNaN(c[l+1])&&v.push(r[l],r[l+1])}r=v}for(l=0;l<r.length-2;l+=2)switch(h[0]=r[l+2],h[1]=r[l+3],f[0]=r[l],f[1]=r[l+1],s.push(f[0],f[1]),i){case"end":u[o]=h[o],u[1-o]=f[1-o],s.push(u[0],u[1]);break;case"middle":var d=(f[o]+h[o])/2,y=[];u[o]=y[o]=d,u[1-o]=f[1-o],y[1-o]=h[1-o],s.push(u[0],u[1]),s.push(y[0],y[1]);break;default:u[o]=f[o],u[1-o]=h[1-o],s.push(u[0],u[1])}return s.push(r[l++],r[l++]),s}function zD(r,t){var e=[],i=r.length,n,a;function o(f,h,v){var c=f.coord,d=(v-c)/(h.coord-c),y=K0(d,[f.color,h.color]);return{coord:v,color:y}}for(var s=0;s<i;s++){var l=r[s],u=l.coord;if(u<0)n=l;else if(u>t){a?e.push(o(a,l,t)):n&&e.push(o(n,l,0),o(n,l,t));break}else n&&(e.push(o(n,l,0)),n=null),e.push(l),a=l}return e}function HD(r,t,e){var i=r.getVisual("visualMeta");if(!(!i||!i.length||!r.count())&&t.type==="cartesian2d"){for(var n,a,o=i.length-1;o>=0;o--){var s=r.getDimensionInfo(i[o].dimension);if(n=s&&s.coordDim,n==="x"||n==="y"){a=i[o];break}}if(a){var l=t.getAxis(n),u=V(a.stops,function(_){return{coord:l.toGlobalCoord(l.dataToCoord(_.value)),color:_.color}}),f=u.length,h=a.outerColors.slice();f&&u[0].coord>u[f-1].coord&&(u.reverse(),h.reverse());var v=zD(u,n==="x"?e.getWidth():e.getHeight()),c=v.length;if(!c&&f)return u[0].coord<0?h[1]?h[1]:u[f-1].color:h[0]?h[0]:u[0].color;var d=10,y=v[0].coord-d,p=v[c-1].coord+d,g=p-y;if(g<.001)return"transparent";M(v,function(_){_.offset=(_.coord-y)/g}),v.push({offset:c?v[c-1].offset:.5,color:h[1]||"transparent"}),v.unshift({offset:c?v[0].offset:.5,color:h[0]||"transparent"});var m=new Wp(0,0,0,0,v,!0);return m[n]=y,m[n+"2"]=p,m}}}function GD(r,t,e){var i=r.get("showAllSymbol"),n=i==="auto";if(!(i&&!n)){var a=e.getAxesByScale("ordinal")[0];if(a&&!(n&&VD(a,t))){var o=t.mapDimension(a.dim),s={};return M(a.getViewLabels(),function(l){var u=a.scale.getRawOrdinalNumber(l.tickValue);s[u]=1}),function(l){return!s.hasOwnProperty(t.get(o,l))}}}}function VD(r,t){var e=r.getExtent(),i=Math.abs(e[1]-e[0])/r.scale.count();isNaN(i)&&(i=0);for(var n=t.count(),a=Math.max(1,Math.round(n/5)),o=0;o<n;o+=a)if(Df.getSymbolSize(t,o)[r.isHorizontal()?1:0]*1.5>i)return!1;return!0}function WD(r,t){return isNaN(r)||isNaN(t)}function UD(r){for(var t=r.length/2;t>0&&WD(r[t*2-2],r[t*2-1]);t--);return t-1}function Rv(r,t){return[r[t*2],r[t*2+1]]}function $D(r,t,e){for(var i=r.length/2,n=e==="x"?0:1,a,o,s=0,l=-1,u=0;u<i;u++)if(o=r[u*2+n],!(isNaN(o)||isNaN(r[u*2+1-n]))){if(u===0){a=o;continue}if(a<=t&&o>=t||a>=t&&o<=t){l=u;break}s=u,a=o}return{range:[s,l],t:(t-a)/(o-a)}}function $y(r){if(r.get(["endLabel","show"]))return!0;for(var t=0;t<Ae.length;t++)if(r.get([Ae[t],"endLabel","show"]))return!0;return!1}function hl(r,t,e,i){if(ND(t,"cartesian2d")){var n=i.getModel("endLabel"),a=n.get("valueAnimation"),o=i.getData(),s={lastFrameIndex:0},l=$y(i)?function(c,d){r._endLabelOnDuring(c,d,o,s,a,n,t)}:null,u=t.getBaseAxis().isHorizontal(),f=OD(t,e,i,function(){var c=r._endLabel;c&&e&&s.originalX!=null&&c.attr({x:s.originalX,y:s.originalY})},l);if(!i.get("clip",!0)){var h=f.shape,v=Math.max(h.width,h.height);u?(h.y-=v,h.height+=v*2):(h.x-=v,h.width+=v*2)}return l&&l(1,f),f}else return BD(t,e,i)}function YD(r,t){var e=t.getBaseAxis(),i=e.isHorizontal(),n=e.inverse,a=i?n?"right":"left":"center",o=i?"middle":n?"top":"bottom";return{normal:{align:r.get("align")||a,verticalAlign:r.get("verticalAlign")||o}}}var XD=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.init=function(){var e=new Ft,i=new AD;this.group.add(i.group),this._symbolDraw=i,this._lineGroup=e,this._changePolyState=pt(this._changePolyState,this)},t.prototype.render=function(e,i,n){var a=e.coordinateSystem,o=this.group,s=e.getData(),l=e.getModel("lineStyle"),u=e.getModel("areaStyle"),f=s.getLayout("points")||[],h=a.type==="polar",v=this._coordSys,c=this._symbolDraw,d=this._polyline,y=this._polygon,p=this._lineGroup,g=!i.ssr&&e.get("animation"),m=!u.isEmpty(),_=u.get("origin"),S=Vy(a,s,_),b=m&&FD(a,s,S),w=e.get("showSymbol"),x=e.get("connectNulls"),A=w&&!h&&GD(e,s,a),C=this._data;C&&C.eachItemGraphicEl(function(ct,qt){ct.__temp&&(o.remove(ct),C.setItemGraphicEl(qt,null))}),w||c.remove(),o.add(p);var D=h?!1:e.get("step"),T;a&&a.getArea&&e.get("clip",!0)&&(T=a.getArea(),T.width!=null?(T.x-=.1,T.y-=.1,T.width+=.2,T.height+=.2):T.r0&&(T.r0-=.5,T.r+=.5)),this._clipShapeForSymbol=T;var L=HD(s,a,n)||s.getVisual("style")[s.getVisual("drawType")];if(!(d&&v.type===a.type&&D===this._step))w&&c.updateData(s,{isIgnore:A,clipShape:T,disableAnimation:!0,getSymbolPoint:function(ct){return[f[ct*2],f[ct*2+1]]}}),g&&this._initSymbolLabelAnimation(s,a,T),D&&(b&&(b=er(b,f,a,D,x)),f=er(f,null,a,D,x)),d=this._newPolyline(f),m?y=this._newPolygon(f,b):y&&(p.remove(y),y=this._polygon=null),h||this._initOrUpdateEndLabel(e,a,ti(L)),p.setClipPath(hl(this,a,!0,e));else{m&&!y?y=this._newPolygon(f,b):y&&!m&&(p.remove(y),y=this._polygon=null),h||this._initOrUpdateEndLabel(e,a,ti(L));var P=p.getClipPath();if(P){var I=hl(this,a,!1,e);ra(P,{shape:I.shape},e)}else p.setClipPath(hl(this,a,!0,e));w&&c.updateData(s,{isIgnore:A,clipShape:T,disableAnimation:!0,getSymbolPoint:function(ct){return[f[ct*2],f[ct*2+1]]}}),(!Av(this._stackedOnPoints,b)||!Av(this._points,f))&&(g?this._doUpdateAnimation(s,b,a,n,D,_,x):(D&&(b&&(b=er(b,f,a,D,x)),f=er(f,null,a,D,x)),d.setShape({points:f}),y&&y.setShape({points:f,stackedOnPoints:b})))}var R=e.getModel("emphasis"),E=R.get("focus"),G=R.get("blurScope"),B=R.get("disabled");if(d.useStyle(ot(l.getLineStyle(),{fill:"none",stroke:L,lineJoin:"bevel"})),Uh(d,e,"lineStyle"),d.style.lineWidth>0&&e.get(["emphasis","lineStyle","width"])==="bolder"){var F=d.getState("emphasis").style;F.lineWidth=+d.style.lineWidth+1}at(d).seriesIndex=e.seriesIndex,$l(d,E,G,B);var $=Pv(e.get("smooth")),it=e.get("smoothMonotone");if(d.setShape({smooth:$,smoothMonotone:it,connectNulls:x}),y){var J=s.getCalculationInfo("stackedOnSeries"),st=0;y.useStyle(ot(u.getAreaStyle(),{fill:L,opacity:.7,lineJoin:"bevel",decal:s.getVisual("style").decal})),J&&(st=Pv(J.get("smooth"))),y.setShape({smooth:$,stackedOnSmooth:st,smoothMonotone:it,connectNulls:x}),Uh(y,e,"areaStyle"),at(y).seriesIndex=e.seriesIndex,$l(y,E,G,B)}var ht=this._changePolyState;s.eachItemGraphicEl(function(ct){ct&&(ct.onHoverStateChange=ht)}),this._polyline.onHoverStateChange=ht,this._data=s,this._coordSys=a,this._stackedOnPoints=b,this._points=f,this._step=D,this._valueOrigin=_,e.get("triggerLineEvent")&&(this.packEventData(e,d),y&&this.packEventData(e,y))},t.prototype.packEventData=function(e,i){at(i).eventData={componentType:"series",componentSubType:"line",componentIndex:e.componentIndex,seriesIndex:e.seriesIndex,seriesName:e.name,seriesType:"line"}},t.prototype.highlight=function(e,i,n,a){var o=e.getData(),s=Qr(o,a);if(this._changePolyState("emphasis"),!(s instanceof Array)&&s!=null&&s>=0){var l=o.getLayout("points"),u=o.getItemGraphicEl(s);if(!u){var f=l[s*2],h=l[s*2+1];if(isNaN(f)||isNaN(h)||this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(f,h))return;var v=e.get("zlevel")||0,c=e.get("z")||0;u=new Df(o,s),u.x=f,u.y=h,u.setZ(v,c);var d=u.getSymbolPath().getTextContent();d&&(d.zlevel=v,d.z=c,d.z2=this._polyline.z2+1),u.__temp=!0,o.setItemGraphicEl(s,u),u.stopSymbolAnimation(!0),this.group.add(u)}u.highlight()}else fr.prototype.highlight.call(this,e,i,n,a)},t.prototype.downplay=function(e,i,n,a){var o=e.getData(),s=Qr(o,a);if(this._changePolyState("normal"),s!=null&&s>=0){var l=o.getItemGraphicEl(s);l&&(l.__temp?(o.setItemGraphicEl(s,null),this.group.remove(l)):l.downplay())}else fr.prototype.downplay.call(this,e,i,n,a)},t.prototype._changePolyState=function(e){var i=this._polygon;zh(this._polyline,e),i&&zh(i,e)},t.prototype._newPolyline=function(e){var i=this._polyline;return i&&this._lineGroup.remove(i),i=new RD({shape:{points:e},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(i),this._polyline=i,i},t.prototype._newPolygon=function(e,i){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new kD({shape:{points:e,stackedOnPoints:i},segmentIgnoreThreshold:2}),this._lineGroup.add(n),this._polygon=n,n},t.prototype._initSymbolLabelAnimation=function(e,i,n){var a,o,s=i.getBaseAxis(),l=s.inverse;i.type==="cartesian2d"?(a=s.isHorizontal(),o=!1):i.type==="polar"&&(a=s.dim==="angle",o=!0);var u=e.hostModel,f=u.get("animationDuration");U(f)&&(f=f(null));var h=u.get("animationDelay")||0,v=U(h)?h(null):h;e.eachItemGraphicEl(function(c,d){var y=c;if(y){var p=[c.x,c.y],g=void 0,m=void 0,_=void 0;if(n)if(o){var S=n,b=i.pointToCoord(p);a?(g=S.startAngle,m=S.endAngle,_=-b[1]/180*Math.PI):(g=S.r0,m=S.r,_=b[0])}else{var w=n;a?(g=w.x,m=w.x+w.width,_=c.x):(g=w.y+w.height,m=w.y,_=c.y)}var x=m===g?0:(_-g)/(m-g);l&&(x=1-x);var A=U(h)?h(d):f*x+v,C=y.getSymbolPath(),D=C.getTextContent();y.attr({scaleX:0,scaleY:0}),y.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:A}),D&&D.animateFrom({style:{opacity:0}},{duration:300,delay:A}),C.disableLabelAnimation=!0}})},t.prototype._initOrUpdateEndLabel=function(e,i,n){var a=e.getModel("endLabel");if($y(e)){var o=e.getData(),s=this._polyline,l=o.getLayout("points");if(!l){s.removeTextContent(),this._endLabel=null;return}var u=this._endLabel;u||(u=this._endLabel=new zt({z2:200}),u.ignoreClip=!0,s.setTextContent(this._endLabel),s.disableLabelAnimation=!0);var f=UD(l);f>=0&&(tf(s,ef(e,"endLabel"),{inheritColor:n,labelFetcher:e,labelDataIndex:f,defaultText:function(h,v,c){return c!=null?TD(o,c):Gy(o,h)},enableTextSetter:!0},YD(a,i)),s.textConfig.position=null)}else this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},t.prototype._endLabelOnDuring=function(e,i,n,a,o,s,l){var u=this._endLabel,f=this._polyline;if(u){e<1&&a.originalX==null&&(a.originalX=u.x,a.originalY=u.y);var h=n.getLayout("points"),v=n.hostModel,c=v.get("connectNulls"),d=s.get("precision"),y=s.get("distance")||0,p=l.getBaseAxis(),g=p.isHorizontal(),m=p.inverse,_=i.shape,S=m?g?_.x:_.y+_.height:g?_.x+_.width:_.y,b=(g?y:0)*(m?-1:1),w=(g?0:-y)*(m?-1:1),x=g?"x":"y",A=$D(h,S,x),C=A.range,D=C[1]-C[0],T=void 0;if(D>=1){if(D>1&&!c){var L=Rv(h,C[0]);u.attr({x:L[0]+b,y:L[1]+w}),o&&(T=v.getRawValue(C[0]))}else{var L=f.getPointOn(S,x);L&&u.attr({x:L[0]+b,y:L[1]+w});var P=v.getRawValue(C[0]),I=v.getRawValue(C[1]);o&&(T=q_(n,d,P,I,A.t))}a.lastFrameIndex=C[0]}else{var R=e===1||a.lastFrameIndex>0?C[0]:0,L=Rv(h,R);o&&(T=v.getRawValue(R)),u.attr({x:L[0]+b,y:L[1]+w})}if(o){var E=qp(u);typeof E.setLabelText=="function"&&E.setLabelText(T)}}},t.prototype._doUpdateAnimation=function(e,i,n,a,o,s,l){var u=this._polyline,f=this._polygon,h=e.hostModel,v=PD(this._data,e,this._stackedOnPoints,i,this._coordSys,n,this._valueOrigin),c=v.current,d=v.stackedOnCurrent,y=v.next,p=v.stackedOnNext;if(o&&(d=er(v.stackedOnCurrent,v.current,n,o,l),c=er(v.current,null,n,o,l),p=er(v.stackedOnNext,v.next,n,o,l),y=er(v.next,null,n,o,l)),Iv(c,y)>3e3||f&&Iv(d,p)>3e3){u.stopAnimation(),u.setShape({points:y}),f&&(f.stopAnimation(),f.setShape({points:y,stackedOnPoints:p}));return}u.shape.__points=v.current,u.shape.points=c;var g={shape:{points:y}};v.current!==c&&(g.shape.__points=v.next),u.stopAnimation(),hr(u,g,h),f&&(f.setShape({points:c,stackedOnPoints:d}),f.stopAnimation(),hr(f,{shape:{stackedOnPoints:p}},h),u.shape.points!==f.shape.points&&(f.shape.points=u.shape.points));for(var m=[],_=v.status,S=0;S<_.length;S++){var b=_[S].cmd;if(b==="="){var w=e.getItemGraphicEl(_[S].idx1);w&&m.push({el:w,ptIdx:S})}}u.animators&&u.animators.length&&u.animators[0].during(function(){f&&f.dirtyShape();for(var x=u.shape.__points,A=0;A<m.length;A++){var C=m[A].el,D=m[A].ptIdx*2;C.x=x[D],C.y=x[D+1],C.markRedraw()}})},t.prototype.remove=function(e){var i=this.group,n=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),n&&n.eachItemGraphicEl(function(a,o){a.__temp&&(i.remove(a),n.setItemGraphicEl(o,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},t.type="line",t}(fr);const ZD=XD;function qD(r,t){return{seriesType:r,plan:Ng(),reset:function(e){var i=e.getData(),n=e.coordinateSystem,a=e.pipelineContext,o=t||a.large;if(n){var s=V(n.dimensions,function(c){return i.mapDimension(c)}).slice(0,2),l=s.length,u=i.getCalculationInfo("stackResultDimension");Xn(i,s[0])&&(s[0]=u),Xn(i,s[1])&&(s[1]=u);var f=i.getStore(),h=i.getDimensionIndex(s[0]),v=i.getDimensionIndex(s[1]);return l&&{progress:function(c,d){for(var y=c.end-c.start,p=o&&Di(y*l),g=[],m=[],_=c.start,S=0;_<c.end;_++){var b=void 0;if(l===1){var w=f.get(h,_);b=n.dataToPoint(w,null,m)}else g[0]=f.get(h,_),g[1]=f.get(v,_),b=n.dataToPoint(g,null,m);o?(p[S++]=b[0],p[S++]=b[1]):d.setItemLayout(_,b.slice())}o&&d.setLayout("points",p)}}}}}}var KD={average:function(r){for(var t=0,e=0,i=0;i<r.length;i++)isNaN(r[i])||(t+=r[i],e++);return e===0?NaN:t/e},sum:function(r){for(var t=0,e=0;e<r.length;e++)t+=r[e]||0;return t},max:function(r){for(var t=-1/0,e=0;e<r.length;e++)r[e]>t&&(t=r[e]);return isFinite(t)?t:NaN},min:function(r){for(var t=1/0,e=0;e<r.length;e++)r[e]<t&&(t=r[e]);return isFinite(t)?t:NaN},nearest:function(r){return r[0]}},QD=function(r){return Math.round(r.length/2)};function JD(r){return{seriesType:r,reset:function(t,e,i){var n=t.getData(),a=t.get("sampling"),o=t.coordinateSystem,s=n.count();if(s>10&&o.type==="cartesian2d"&&a){var l=o.getBaseAxis(),u=o.getOtherAxis(l),f=l.getExtent(),h=i.getDevicePixelRatio(),v=Math.abs(f[1]-f[0])*(h||1),c=Math.round(s/v);if(isFinite(c)&&c>1){a==="lttb"?t.setData(n.lttbDownSample(n.mapDimension(u.dim),1/c)):a==="minmax"&&t.setData(n.minmaxDownSample(n.mapDimension(u.dim),1/c));var d=void 0;z(a)?d=KD[a]:U(a)&&(d=a),d&&t.setData(n.downSample(n.mapDimension(u.dim),1/c,d,QD))}}}}}function jD(r){r.registerChartView(ZD),r.registerSeriesModel(xD),r.registerLayout(qD("line",!0)),r.registerVisual({seriesType:"line",reset:function(t){var e=t.getData(),i=t.getModel("lineStyle").getLineStyle();i&&!i.stroke&&(i.stroke=e.getVisual("style").fill),e.setVisual("legendLineStyle",i)}}),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,JD("line"))}var tM=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.type="grid",t.dependencies=["xAxis","yAxis"],t.layoutMode="box",t.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"},t}(dt);const eM=tM;var du=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",me).models[0]},t.type="cartesian2dAxis",t}(dt);Le(du,tD);var Yy={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,showMinLine:!0,showMaxLine:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},rM=et({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},Yy),Mf=et({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},Yy),iM=et({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},Mf),nM=ot({logBase:10},Mf);const aM={category:rM,value:Mf,time:iM,log:nM};var oM={value:1,category:1,time:1,log:1};function Ev(r,t,e,i){M(oM,function(n,a){var o=et(et({},aM[a],!0),i,!0),s=function(l){O(u,l);function u(){var f=l!==null&&l.apply(this,arguments)||this;return f.type=t+"Axis."+a,f}return u.prototype.mergeDefaultAndTheme=function(f,h){var v=Gn(this),c=v?Zo(f):{},d=h.getTheme();et(f,d.get(a+"Axis")),et(f,this.getDefaultOption()),f.type=kv(f),v&&ki(f,c,v)},u.prototype.optionUpdated=function(){var f=this.option;f.type==="category"&&(this.__ordinalMeta=hu.createByAxisModel(this))},u.prototype.getCategories=function(f){var h=this.option;if(h.type==="category")return f?h.data:this.__ordinalMeta.categories},u.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},u.type=t+"Axis."+a,u.defaultOption=o,u}(e);r.registerComponentModel(s)}),r.registerSubTypeDefaulter(t+"Axis",kv)}function kv(r){return r.type||(r.data?"category":"value")}var sM=function(){function r(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}return r.prototype.getAxis=function(t){return this._axes[t]},r.prototype.getAxes=function(){return V(this._dimList,function(t){return this._axes[t]},this)},r.prototype.getAxesByScale=function(t){return t=t.toLowerCase(),Tt(this.getAxes(),function(e){return e.scale.type===t})},r.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},r}();const lM=sM;var pu=["x","y"];function Ov(r){return r.type==="interval"||r.type==="time"}var uM=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="cartesian2d",e.dimensions=pu,e}return t.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var e=this.getAxis("x").scale,i=this.getAxis("y").scale;if(!(!Ov(e)||!Ov(i))){var n=e.getExtent(),a=i.getExtent(),o=this.dataToPoint([n[0],a[0]]),s=this.dataToPoint([n[1],a[1]]),l=n[1]-n[0],u=a[1]-a[0];if(!(!l||!u)){var f=(s[0]-o[0])/l,h=(s[1]-o[1])/u,v=o[0]-n[0]*f,c=o[1]-a[0]*h,d=this._transform=[f,0,0,h,v,c];this._invTransform=Ru([],d)}}},t.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},t.prototype.containPoint=function(e){var i=this.getAxis("x"),n=this.getAxis("y");return i.contain(i.toLocalCoord(e[0]))&&n.contain(n.toLocalCoord(e[1]))},t.prototype.containData=function(e){return this.getAxis("x").containData(e[0])&&this.getAxis("y").containData(e[1])},t.prototype.containZone=function(e,i){var n=this.dataToPoint(e),a=this.dataToPoint(i),o=this.getArea(),s=new rt(n[0],n[1],a[0]-n[0],a[1]-n[1]);return o.intersect(s)},t.prototype.dataToPoint=function(e,i,n){n=n||[];var a=e[0],o=e[1];if(this._transform&&a!=null&&isFinite(a)&&o!=null&&isFinite(o))return he(n,e,this._transform);var s=this.getAxis("x"),l=this.getAxis("y");return n[0]=s.toGlobalCoord(s.dataToCoord(a,i)),n[1]=l.toGlobalCoord(l.dataToCoord(o,i)),n},t.prototype.clampData=function(e,i){var n=this.getAxis("x").scale,a=this.getAxis("y").scale,o=n.getExtent(),s=a.getExtent(),l=n.parse(e[0]),u=a.parse(e[1]);return i=i||[],i[0]=Math.min(Math.max(Math.min(o[0],o[1]),l),Math.max(o[0],o[1])),i[1]=Math.min(Math.max(Math.min(s[0],s[1]),u),Math.max(s[0],s[1])),i},t.prototype.pointToData=function(e,i){var n=[];if(this._invTransform)return he(n,e,this._invTransform);var a=this.getAxis("x"),o=this.getAxis("y");return n[0]=a.coordToData(a.toLocalCoord(e[0]),i),n[1]=o.coordToData(o.toLocalCoord(e[1]),i),n},t.prototype.getOtherAxis=function(e){return this.getAxis(e.dim==="x"?"y":"x")},t.prototype.getArea=function(e){e=e||0;var i=this.getAxis("x").getGlobalExtent(),n=this.getAxis("y").getGlobalExtent(),a=Math.min(i[0],i[1])-e,o=Math.min(n[0],n[1])-e,s=Math.max(i[0],i[1])-a+e,l=Math.max(n[0],n[1])-o+e;return new rt(a,o,s,l)},t}(lM),fM=function(r){O(t,r);function t(e,i,n,a,o){var s=r.call(this,e,i,n)||this;return s.index=0,s.type=a||"value",s.position=o||"bottom",s}return t.prototype.isHorizontal=function(){var e=this.position;return e==="top"||e==="bottom"},t.prototype.getGlobalExtent=function(e){var i=this.getExtent();return i[0]=this.toGlobalCoord(i[0]),i[1]=this.toGlobalCoord(i[1]),e&&i[0]>i[1]&&i.reverse(),i},t.prototype.pointToData=function(e,i){return this.coordToData(this.toLocalCoord(e[this.dim==="x"?0:1]),i)},t.prototype.setCategorySortInfo=function(e){if(this.type!=="category")return!1;this.model.option.categorySortInfo=e,this.scale.setSortInfo(e)},t}(cD);const hM=fM;function gu(r,t,e){e=e||{};var i=r.coordinateSystem,n=t.axis,a={},o=n.getAxesOnZeroOf()[0],s=n.position,l=o?"onZero":s,u=n.dim,f=i.getRect(),h=[f.x,f.x+f.width,f.y,f.y+f.height],v={left:0,right:1,top:0,bottom:1,onZero:2},c=t.get("offset")||0,d=u==="x"?[h[2]-c,h[3]+c]:[h[0]-c,h[1]+c];if(o){var y=o.toGlobalCoord(o.dataToCoord(0));d[v.onZero]=Math.max(Math.min(y,d[1]),d[0])}a.position=[u==="y"?d[v[l]]:h[0],u==="x"?d[v[l]]:h[3]],a.rotation=Math.PI/2*(u==="x"?0:1);var p={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=p[s],a.labelOffset=o?d[v[s]]-d[v.onZero]:0,t.get(["axisTick","inside"])&&(a.tickDirection=-a.tickDirection),kn(e.labelInside,t.get(["axisLabel","inside"]))&&(a.labelDirection=-a.labelDirection);var g=t.get(["axisLabel","rotate"]);return a.labelRotate=l==="top"?-g:g,a.z2=1,a}function Bv(r){return r.get("coordinateSystem")==="cartesian2d"}function Nv(r){var t={xAxisModel:null,yAxisModel:null};return M(t,function(e,i){var n=i.replace(/Model$/,""),a=r.getReferringComponents(n,me).models[0];t[i]=a}),t}var cl=Math.log;function cM(r,t,e){var i=ia.prototype,n=i.getTicks.call(e),a=i.getTicks.call(e,!0),o=n.length-1,s=i.getInterval.call(e),l=Ry(r,t),u=l.extent,f=l.fixMin,h=l.fixMax;if(r.type==="log"){var v=cl(r.base);u=[cl(u[0])/v,cl(u[1])/v]}r.setExtent(u[0],u[1]),r.calcNiceExtent({splitNumber:o,fixMin:f,fixMax:h});var c=i.getExtent.call(r);f&&(u[0]=c[0]),h&&(u[1]=c[1]);var d=i.getInterval.call(r),y=u[0],p=u[1];if(f&&h)d=(p-y)/o;else if(f)for(p=u[0]+d*o;p<u[1]&&isFinite(p)&&isFinite(u[1]);)d=ll(d),p=u[0]+d*o;else if(h)for(y=u[1]-d*o;y>u[0]&&isFinite(y)&&isFinite(u[0]);)d=ll(d),y=u[1]-d*o;else{var g=r.getTicks().length-1;g>o&&(d=ll(d));var m=d*o;p=Math.ceil(u[1]/d)*d,y=_t(p-m),y<0&&u[0]>=0?(y=0,p=_t(m)):p>0&&u[1]<=0&&(p=0,y=-_t(m))}var _=(n[0].value-a[0].value)/s,S=(n[o].value-a[o].value)/s;i.setExtent.call(r,y+d*_,p+d*S),i.setInterval.call(r,d),(_||S)&&i.setNiceExtent.call(r,y+d,p-d)}var vM=function(){function r(t,e,i){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=pu,this._initCartesian(t,e,i),this.model=t}return r.prototype.getRect=function(){return this._rect},r.prototype.update=function(t,e){var i=this._axesMap;this._updateScale(t,this.model);function n(o){var s,l=gt(o),u=l.length;if(u){for(var f=[],h=u-1;h>=0;h--){var v=+l[h],c=o[v],d=c.model,y=c.scale;cu(y)&&d.get("alignTicks")&&d.get("interval")==null?f.push(c):(Sv(y,d),cu(y)&&(s=c))}f.length&&(s||(s=f.pop(),Sv(s.scale,s.model)),M(f,function(p){cM(p.scale,p.model,s.scale)}))}}n(i.x),n(i.y);var a={};M(i.x,function(o){Fv(i,"y",o,a)}),M(i.y,function(o){Fv(i,"x",o,a)}),this.resize(this.model,e)},r.prototype.resize=function(t,e,i){var n=t.getBoxLayoutParams(),a=!i&&t.get("containLabel"),o=Hn(n,{width:e.getWidth(),height:e.getHeight()});this._rect=o;var s=this._axesList;l(),a&&(M(s,function(u){if(!u.model.get(["axisLabel","inside"])){var f=QC(u);if(f){var h=u.isHorizontal()?"height":"width",v=u.model.get(["axisLabel","margin"]);o[h]-=f[h]+v,u.position==="top"?o.y+=f.height+v:u.position==="left"&&(o.x+=f.width+v)}}}),l()),M(this._coordsList,function(u){u.calcAffineTransform()});function l(){M(s,function(u){var f=u.isHorizontal(),h=f?[0,o.width]:[0,o.height],v=u.inverse?1:0;u.setExtent(h[v],h[1-v]),dM(u,f?o.x:o.y)})}},r.prototype.getAxis=function(t,e){var i=this._axesMap[t];if(i!=null)return i[e||0]},r.prototype.getAxes=function(){return this._axesList.slice()},r.prototype.getCartesian=function(t,e){if(t!=null&&e!=null){var i="x"+t+"y"+e;return this._coordsMap[i]}H(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,a=this._coordsList;n<a.length;n++)if(a[n].getAxis("x").index===t||a[n].getAxis("y").index===e)return a[n]},r.prototype.getCartesians=function(){return this._coordsList.slice()},r.prototype.convertToPixel=function(t,e,i){var n=this._findConvertTarget(e);return n.cartesian?n.cartesian.dataToPoint(i):n.axis?n.axis.toGlobalCoord(n.axis.dataToCoord(i)):null},r.prototype.convertFromPixel=function(t,e,i){var n=this._findConvertTarget(e);return n.cartesian?n.cartesian.pointToData(i):n.axis?n.axis.coordToData(n.axis.toLocalCoord(i)):null},r.prototype._findConvertTarget=function(t){var e=t.seriesModel,i=t.xAxisModel||e&&e.getReferringComponents("xAxis",me).models[0],n=t.yAxisModel||e&&e.getReferringComponents("yAxis",me).models[0],a=t.gridModel,o=this._coordsList,s,l;if(e)s=e.coordinateSystem,lt(o,s)<0&&(s=null);else if(i&&n)s=this.getCartesian(i.componentIndex,n.componentIndex);else if(i)l=this.getAxis("x",i.componentIndex);else if(n)l=this.getAxis("y",n.componentIndex);else if(a){var u=a.coordinateSystem;u===this&&(s=this._coordsList[0])}return{cartesian:s,axis:l}},r.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},r.prototype._initCartesian=function(t,e,i){var n=this,a=this,o={left:!1,right:!1,top:!1,bottom:!1},s={x:{},y:{}},l={x:0,y:0};if(e.eachComponent("xAxis",u("x"),this),e.eachComponent("yAxis",u("y"),this),!l.x||!l.y){this._axesMap={},this._axesList=[];return}this._axesMap=s,M(s.x,function(f,h){M(s.y,function(v,c){var d="x"+h+"y"+c,y=new uM(d);y.master=n,y.model=t,n._coordsMap[d]=y,n._coordsList.push(y),y.addAxis(f),y.addAxis(v)})});function u(f){return function(h,v){if(vl(h,t)){var c=h.get("position");f==="x"?c!=="top"&&c!=="bottom"&&(c=o.bottom?"top":"bottom"):c!=="left"&&c!=="right"&&(c=o.left?"right":"left"),o[c]=!0;var d=new hM(f,qC(h),[0,0],h.get("type"),c),y=d.type==="category";d.onBand=y&&h.get("boundaryGap"),d.inverse=h.get("inverse"),h.axis=d,d.model=h,d.grid=a,d.index=v,a._axesList.push(d),s[f][v]=d,l[f]++}}}},r.prototype._updateScale=function(t,e){M(this._axesList,function(n){if(n.scale.setExtent(1/0,-1/0),n.type==="category"){var a=n.model.get("categorySortInfo");n.scale.setSortInfo(a)}}),t.eachSeries(function(n){if(Bv(n)){var a=Nv(n),o=a.xAxisModel,s=a.yAxisModel;if(!vl(o,e)||!vl(s,e))return;var l=this.getCartesian(o.componentIndex,s.componentIndex),u=n.getData(),f=l.getAxis("x"),h=l.getAxis("y");i(u,f),i(u,h)}},this);function i(n,a){M(jC(n,a.dim),function(o){a.scale.unionExtentFromData(n,o)})}},r.prototype.getTooltipAxes=function(t){var e=[],i=[];return M(this.getCartesians(),function(n){var a=t!=null&&t!=="auto"?n.getAxis(t):n.getBaseAxis(),o=n.getOtherAxis(a);lt(e,a)<0&&e.push(a),lt(i,o)<0&&i.push(o)}),{baseAxes:e,otherAxes:i}},r.create=function(t,e){var i=[];return t.eachComponent("grid",function(n,a){var o=new r(n,t,e);o.name="grid_"+a,o.resize(n,e,!0),n.coordinateSystem=o,i.push(o)}),t.eachSeries(function(n){if(Bv(n)){var a=Nv(n),o=a.xAxisModel,s=a.yAxisModel,l=o.getCoordSysModel(),u=l.coordinateSystem;n.coordinateSystem=u.getCartesian(o.componentIndex,s.componentIndex)}}),i},r.dimensions=pu,r}();function vl(r,t){return r.getCoordSysModel()===t}function Fv(r,t,e,i){e.getAxesOnZeroOf=function(){return a?[a]:[]};var n=r[t],a,o=e.model,s=o.get(["axisLine","onZero"]),l=o.get(["axisLine","onZeroAxisIndex"]);if(!s)return;if(l!=null)zv(n[l])&&(a=n[l]);else for(var u in n)if(n.hasOwnProperty(u)&&zv(n[u])&&!i[f(n[u])]){a=n[u];break}a&&(i[f(a)]=!0);function f(h){return h.dim+"_"+h.index}}function zv(r){return r&&r.type!=="category"&&r.type!=="time"&&KC(r)}function dM(r,t){var e=r.getExtent(),i=e[0]+e[1];r.toGlobalCoord=r.dim==="x"?function(n){return n+t}:function(n){return i-n+t},r.toLocalCoord=r.dim==="x"?function(n){return n-t}:function(n){return i-n+t}}const pM=vM;var or=Math.PI,Xr=function(){function r(t,e){this.group=new Ft,this.opt=e,this.axisModel=t,ot(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var i=new Ft({x:e.position[0],y:e.position[1],rotation:e.rotation});i.updateTransform(),this._transformGroup=i}return r.prototype.hasBuilder=function(t){return!!Hv[t]},r.prototype.add=function(t){Hv[t](this.opt,this.axisModel,this.group,this._transformGroup)},r.prototype.getGroup=function(){return this.group},r.innerTextLayout=function(t,e,i){var n=Yd(e-t),a,o;return vo(n)?(o=i>0?"top":"bottom",a="center"):vo(n-or)?(o=i>0?"bottom":"top",a="center"):(o="middle",n>0&&n<or?a=i>0?"right":"left":a=i>0?"left":"right"),{rotation:n,textAlign:a,textVerticalAlign:o}},r.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},r.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},r}(),Hv={axisLine:function(r,t,e,i){var n=t.get(["axisLine","show"]);if(n==="auto"&&r.handleAutoShown&&(n=r.handleAutoShown("axisLine")),!!n){var a=t.axis.getExtent(),o=i.transform,s=[a[0],0],l=[a[1],0],u=s[0]>l[0];o&&(he(s,s,o),he(l,l,o));var f=k({lineCap:"round"},t.getModel(["axisLine","lineStyle"]).getLineStyle()),h=new jr({shape:{x1:s[0],y1:s[1],x2:l[0],y2:l[1]},style:f,strokeContainThreshold:r.strokeContainThreshold||5,silent:!0,z2:1});Fn(h.shape,h.style.lineWidth),h.anid="line",e.add(h);var v=t.get(["axisLine","symbol"]);if(v!=null){var c=t.get(["axisLine","symbolSize"]);z(v)&&(v=[v,v]),(z(c)||yt(c))&&(c=[c,c]);var d=Xg(t.get(["axisLine","symbolOffset"])||0,c),y=c[0],p=c[1];M([{rotate:r.rotation+Math.PI/2,offset:d[0],r:0},{rotate:r.rotation-Math.PI/2,offset:d[1],r:Math.sqrt((s[0]-l[0])*(s[0]-l[0])+(s[1]-l[1])*(s[1]-l[1]))}],function(g,m){if(v[m]!=="none"&&v[m]!=null){var _=Bi(v[m],-y/2,-p/2,y,p,f.stroke,!0),S=g.r+g.offset,b=u?l:s;_.attr({rotation:g.rotate,x:b[0]+S*Math.cos(r.rotation),y:b[1]-S*Math.sin(r.rotation),silent:!0,z2:11}),e.add(_)}})}}},axisTickLabel:function(r,t,e,i){var n=mM(e,i,t,r),a=SM(e,i,t,r);if(yM(t,a,n),_M(e,i,t,r.tickDirection),t.get(["axisLabel","hideOverlap"])){var o=vD(V(a,function(s){return{label:s,priority:s.z2,defaultAttr:{ignore:s.ignore}}}));dD(o)}},axisName:function(r,t,e,i){var n=kn(r.axisName,t.get("name"));if(n){var a=t.get("nameLocation"),o=r.nameDirection,s=t.getModel("nameTextStyle"),l=t.get("nameGap")||0,u=t.axis.getExtent(),f=u[0]>u[1]?-1:1,h=[a==="start"?u[0]-f*l:a==="end"?u[1]+f*l:(u[0]+u[1])/2,Vv(a)?r.labelOffset+o*l:0],v,c=t.get("nameRotate");c!=null&&(c=c*or/180);var d;Vv(a)?v=Xr.innerTextLayout(r.rotation,c??r.rotation,o):(v=gM(r.rotation,a,c||0,u),d=r.axisNameAvailableWidth,d!=null&&(d=Math.abs(d/Math.sin(v.rotation)),!isFinite(d)&&(d=null)));var y=s.getFont(),p=t.get("nameTruncate",!0)||{},g=p.ellipsis,m=kn(r.nameTruncateMaxWidth,p.maxWidth,d),_=new zt({x:h[0],y:h[1],rotation:v.rotation,silent:Xr.isLabelSilent(t),style:cr(s,{text:n,font:y,overflow:"truncate",width:m,ellipsis:g,fill:s.getTextColor()||t.get(["axisLine","lineStyle","color"]),align:s.get("align")||v.textAlign,verticalAlign:s.get("verticalAlign")||v.textVerticalAlign}),z2:1});if(zo({el:_,componentModel:t,itemName:n}),_.__fullText=n,_.anid="name",t.get("triggerEvent")){var S=Xr.makeAxisEventDataBase(t);S.targetType="axisName",S.name=n,at(_).eventData=S}i.add(_),_.updateTransform(),e.add(_),_.decomposeTransform()}}};function gM(r,t,e,i){var n=Yd(e-r),a,o,s=i[0]>i[1],l=t==="start"&&!s||t!=="start"&&s;return vo(n-or/2)?(o=l?"bottom":"top",a="center"):vo(n-or*1.5)?(o=l?"top":"bottom",a="center"):(o="middle",n<or*1.5&&n>or/2?a=l?"left":"right":a=l?"right":"left"),{rotation:n,textAlign:a,textVerticalAlign:o}}function yM(r,t,e){if(!Ey(r.axis)){var i=r.get(["axisLabel","showMinLabel"]),n=r.get(["axisLabel","showMaxLabel"]);t=t||[],e=e||[];var a=t[0],o=t[1],s=t[t.length-1],l=t[t.length-2],u=e[0],f=e[1],h=e[e.length-1],v=e[e.length-2];i===!1?(re(a),re(u)):Gv(a,o)&&(i?(re(o),re(f)):(re(a),re(u))),n===!1?(re(s),re(h)):Gv(l,s)&&(n?(re(l),re(v)):(re(s),re(h)))}}function re(r){r&&(r.ignore=!0)}function Gv(r,t){var e=r&&r.getBoundingRect().clone(),i=t&&t.getBoundingRect().clone();if(!(!e||!i)){var n=Iu([]);return Pu(n,n,-r.rotation),e.applyTransform(Li([],n,r.getLocalTransform())),i.applyTransform(Li([],n,t.getLocalTransform())),e.intersect(i)}}function Vv(r){return r==="middle"||r==="center"}function Xy(r,t,e,i,n){for(var a=[],o=[],s=[],l=0;l<r.length;l++){var u=r[l].coord;o[0]=u,o[1]=0,s[0]=u,s[1]=e,t&&(he(o,o,t),he(s,s,t));var f=new jr({shape:{x1:o[0],y1:o[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0});Fn(f.shape,f.style.lineWidth),f.anid=n+"_"+r[l].tickValue,a.push(f)}return a}function mM(r,t,e,i){var n=e.axis,a=e.getModel("axisTick"),o=a.get("show");if(o==="auto"&&i.handleAutoShown&&(o=i.handleAutoShown("axisTick")),!(!o||n.scale.isBlank())){for(var s=a.getModel("lineStyle"),l=i.tickDirection*a.get("length"),u=n.getTicksCoords(),f=Xy(u,t.transform,l,ot(s.getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])}),"ticks"),h=0;h<f.length;h++)r.add(f[h]);return f}}function _M(r,t,e,i){var n=e.axis,a=e.getModel("minorTick");if(!(!a.get("show")||n.scale.isBlank())){var o=n.getMinorTicksCoords();if(o.length)for(var s=a.getModel("lineStyle"),l=i*a.get("length"),u=ot(s.getLineStyle(),ot(e.getModel("axisTick").getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])})),f=0;f<o.length;f++)for(var h=Xy(o[f],t.transform,l,u,"minorticks_"+f),v=0;v<h.length;v++)r.add(h[v])}}function SM(r,t,e,i){var n=e.axis,a=kn(i.axisLabelShow,e.get(["axisLabel","show"]));if(!(!a||n.scale.isBlank())){var o=e.getModel("axisLabel"),s=o.get("margin"),l=n.getViewLabels(),u=(kn(i.labelRotate,o.get("rotate"))||0)*or/180,f=Xr.innerTextLayout(i.rotation,u,i.labelDirection),h=e.getCategories&&e.getCategories(!0),v=[],c=Xr.isLabelSilent(e),d=e.get("triggerEvent");return M(l,function(y,p){var g=n.scale.type==="ordinal"?n.scale.getRawOrdinalNumber(y.tickValue):y.tickValue,m=y.formattedLabel,_=y.rawLabel,S=o;if(h&&h[g]){var b=h[g];H(b)&&b.textStyle&&(S=new Rt(b.textStyle,o,e.ecModel))}var w=S.getTextColor()||e.get(["axisLine","lineStyle","color"]),x=n.dataToCoord(g),A=S.getShallow("align",!0)||f.textAlign,C=Z(S.getShallow("alignMinLabel",!0),A),D=Z(S.getShallow("alignMaxLabel",!0),A),T=S.getShallow("verticalAlign",!0)||S.getShallow("baseline",!0)||f.textVerticalAlign,L=Z(S.getShallow("verticalAlignMinLabel",!0),T),P=Z(S.getShallow("verticalAlignMaxLabel",!0),T),I=new zt({x,y:i.labelOffset+i.labelDirection*s,rotation:f.rotation,silent:c,z2:10+(y.level||0),style:cr(S,{text:m,align:p===0?C:p===l.length-1?D:A,verticalAlign:p===0?L:p===l.length-1?P:T,fill:U(w)?w(n.type==="category"?_:n.type==="value"?g+"":g,p):w})});if(I.anid="label_"+g,zo({el:I,componentModel:e,itemName:m,formatterParamsExtra:{isTruncated:function(){return I.isTruncated},value:_,tickIndex:p}}),d){var R=Xr.makeAxisEventDataBase(e);R.targetType="axisLabel",R.value=_,R.tickIndex=p,n.type==="category"&&(R.dataIndex=g),at(I).eventData=R}t.add(I),I.updateTransform(),v.push(I),r.add(I),I.decomposeTransform()}),v}}const Zy=Xr;function wM(r,t){var e={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return bM(e,r,t),e.seriesInvolved&&TM(e,r),e}function bM(r,t,e){var i=t.getComponent("tooltip"),n=t.getComponent("axisPointer"),a=n.get("link",!0)||[],o=[];M(e.getCoordinateSystems(),function(s){if(!s.axisPointerEnabled)return;var l=qn(s.model),u=r.coordSysAxesInfo[l]={};r.coordSysMap[l]=s;var f=s.model,h=f.getModel("tooltip",i);if(M(s.getAxes(),bt(y,!1,null)),s.getTooltipAxes&&i&&h.get("show")){var v=h.get("trigger")==="axis",c=h.get(["axisPointer","type"])==="cross",d=s.getTooltipAxes(h.get(["axisPointer","axis"]));(v||c)&&M(d.baseAxes,bt(y,c?"cross":!0,v)),c&&M(d.otherAxes,bt(y,"cross",!1))}function y(p,g,m){var _=m.model.getModel("axisPointer",n),S=_.get("show");if(!(!S||S==="auto"&&!p&&!yu(_))){g==null&&(g=_.get("triggerTooltip")),_=p?xM(m,h,n,t,p,g):_;var b=_.get("snap"),w=_.get("triggerEmphasis"),x=qn(m.model),A=g||b||m.type==="category",C=r.axesInfo[x]={key:x,axis:m,coordSys:s,axisPointerModel:_,triggerTooltip:g,triggerEmphasis:w,involveSeries:A,snap:b,useHandle:yu(_),seriesModels:[],linkGroup:null};u[x]=C,r.seriesInvolved=r.seriesInvolved||A;var D=CM(a,m);if(D!=null){var T=o[D]||(o[D]={axesInfo:{}});T.axesInfo[x]=C,T.mapper=a[D].mapper,C.linkGroup=T}}}})}function xM(r,t,e,i,n,a){var o=t.getModel("axisPointer"),s=["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],l={};M(s,function(v){l[v]=Q(o.get(v))}),l.snap=r.type!=="category"&&!!a,o.get("type")==="cross"&&(l.type="line");var u=l.label||(l.label={});if(u.show==null&&(u.show=!1),n==="cross"){var f=o.get(["label","show"]);if(u.show=f??!0,!a){var h=l.lineStyle=o.get("crossStyle");h&&ot(u,h.textStyle)}}return r.model.getModel("axisPointer",new Rt(l,e,i))}function TM(r,t){t.eachSeries(function(e){var i=e.coordinateSystem,n=e.get(["tooltip","trigger"],!0),a=e.get(["tooltip","show"],!0);!i||n==="none"||n===!1||n==="item"||a===!1||e.get(["axisPointer","show"],!0)===!1||M(r.coordSysAxesInfo[qn(i.model)],function(o){var s=o.axis;i.getAxis(s.dim)===s&&(o.seriesModels.push(e),o.seriesDataCount==null&&(o.seriesDataCount=0),o.seriesDataCount+=e.getData().count())})})}function CM(r,t){for(var e=t.model,i=t.dim,n=0;n<r.length;n++){var a=r[n]||{};if(dl(a[i+"AxisId"],e.id)||dl(a[i+"AxisIndex"],e.componentIndex)||dl(a[i+"AxisName"],e.name))return n}}function dl(r,t){return r==="all"||N(r)&&lt(r,t)>=0||r===t}function DM(r){var t=Af(r);if(t){var e=t.axisPointerModel,i=t.axis.scale,n=e.option,a=e.get("status"),o=e.get("value");o!=null&&(o=i.parse(o));var s=yu(e);a==null&&(n.status=s?"show":"hide");var l=i.getExtent().slice();l[0]>l[1]&&l.reverse(),(o==null||o>l[1])&&(o=l[1]),o<l[0]&&(o=l[0]),n.value=o,s&&(n.status=t.axis.scale.isBlank()?"hide":"show")}}function Af(r){var t=(r.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return t&&t.axesInfo[qn(r)]}function MM(r){var t=Af(r);return t&&t.axisPointerModel}function yu(r){return!!r.get(["handle","show"])}function qn(r){return r.type+"||"+r.id}var Wv={},AM=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n,a){this.axisPointerClass&&DM(e),r.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(e,n,!0)},t.prototype.updateAxisPointer=function(e,i,n,a){this._doUpdateAxisPointerClass(e,n,!1)},t.prototype.remove=function(e,i){var n=this._axisPointer;n&&n.remove(i)},t.prototype.dispose=function(e,i){this._disposeAxisPointer(i),r.prototype.dispose.apply(this,arguments)},t.prototype._doUpdateAxisPointerClass=function(e,i,n){var a=t.getAxisPointerClass(this.axisPointerClass);if(a){var o=MM(e);o?(this._axisPointer||(this._axisPointer=new a)).render(e,o,i,n):this._disposeAxisPointer(i)}},t.prototype._disposeAxisPointer=function(e){this._axisPointer&&this._axisPointer.dispose(e),this._axisPointer=null},t.registerAxisPointerClass=function(e,i){Wv[e]=i},t.getAxisPointerClass=function(e){return e&&Wv[e]},t.type="axis",t}(We);const qy=AM;var mu=wt();function LM(r,t,e,i){var n=e.axis;if(!n.scale.isBlank()){var a=e.getModel("splitArea"),o=a.getModel("areaStyle"),s=o.get("color"),l=i.coordinateSystem.getRect(),u=n.getTicksCoords({tickModel:a,clamp:!0});if(u.length){var f=s.length,h=mu(r).splitAreaColors,v=X(),c=0;if(h)for(var d=0;d<u.length;d++){var y=h.get(u[d].tickValue);if(y!=null){c=(y+(f-1)*d)%f;break}}var p=n.toGlobalCoord(u[0].coord),g=o.getAreaStyle();s=N(s)?s:[s];for(var d=1;d<u.length;d++){var m=n.toGlobalCoord(u[d].coord),_=void 0,S=void 0,b=void 0,w=void 0;n.isHorizontal()?(_=p,S=l.y,b=m-_,w=l.height,p=_+b):(_=l.x,S=p,b=l.width,w=m-S,p=S+w);var x=u[d-1].tickValue;x!=null&&v.set(x,c),t.add(new Ct({anid:x!=null?"area_"+x:null,shape:{x:_,y:S,width:b,height:w},style:ot({fill:s[c]},g),autoBatch:!0,silent:!0})),c=(c+1)%f}mu(r).splitAreaColors=v}}}function IM(r){mu(r).splitAreaColors=null}var PM=["axisLine","axisTickLabel","axisName"],RM=["splitArea","splitLine","minorSplitLine"],Ky=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.axisPointerClass="CartesianAxisPointer",e}return t.prototype.render=function(e,i,n,a){this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new Ft,this.group.add(this._axisGroup),!!e.get("show")){var s=e.getCoordSysModel(),l=gu(s,e),u=new Zy(e,k({handleAutoShown:function(h){for(var v=s.coordinateSystem.getCartesians(),c=0;c<v.length;c++)if(cu(v[c].getOtherAxis(e.axis).scale))return!0;return!1}},l));M(PM,u.add,u),this._axisGroup.add(u.getGroup()),M(RM,function(h){e.get([h,"show"])&&EM[h](this,this._axisGroup,e,s)},this);var f=a&&a.type==="changeAxisOrder"&&a.isInitSort;f||Xp(o,this._axisGroup,e),r.prototype.render.call(this,e,i,n,a)}},t.prototype.remove=function(){IM(this)},t.type="cartesianAxis",t}(qy),EM={splitLine:function(r,t,e,i){var n=e.axis;if(!n.scale.isBlank()){var a=e.getModel("splitLine"),o=a.getModel("lineStyle"),s=o.get("color"),l=a.get("showMinLine")!==!1,u=a.get("showMaxLine")!==!1;s=N(s)?s:[s];for(var f=i.coordinateSystem.getRect(),h=n.isHorizontal(),v=0,c=n.getTicksCoords({tickModel:a}),d=[],y=[],p=o.getLineStyle(),g=0;g<c.length;g++){var m=n.toGlobalCoord(c[g].coord);if(!(g===0&&!l||g===c.length-1&&!u)){var _=c[g].tickValue;h?(d[0]=m,d[1]=f.y,y[0]=m,y[1]=f.y+f.height):(d[0]=f.x,d[1]=m,y[0]=f.x+f.width,y[1]=m);var S=v++%s.length,b=new jr({anid:_!=null?"line_"+_:null,autoBatch:!0,shape:{x1:d[0],y1:d[1],x2:y[0],y2:y[1]},style:ot({stroke:s[S]},p),silent:!0});Fn(b.shape,p.lineWidth),t.add(b)}}}},minorSplitLine:function(r,t,e,i){var n=e.axis,a=e.getModel("minorSplitLine"),o=a.getModel("lineStyle"),s=i.coordinateSystem.getRect(),l=n.isHorizontal(),u=n.getMinorTicksCoords();if(u.length)for(var f=[],h=[],v=o.getLineStyle(),c=0;c<u.length;c++)for(var d=0;d<u[c].length;d++){var y=n.toGlobalCoord(u[c][d].coord);l?(f[0]=y,f[1]=s.y,h[0]=y,h[1]=s.y+s.height):(f[0]=s.x,f[1]=y,h[0]=s.x+s.width,h[1]=y);var p=new jr({anid:"minor_line_"+u[c][d].tickValue,autoBatch:!0,shape:{x1:f[0],y1:f[1],x2:h[0],y2:h[1]},style:v,silent:!0});Fn(p.shape,v.lineWidth),t.add(p)}},splitArea:function(r,t,e,i){LM(r,t,e,i)}},Qy=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="xAxis",t}(Ky),kM=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=Qy.type,e}return t.type="yAxis",t}(Ky),OM=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="grid",e}return t.prototype.render=function(e,i){this.group.removeAll(),e.get("show")&&this.group.add(new Ct({shape:e.coordinateSystem.getRect(),style:ot({fill:e.get("backgroundColor")},e.getItemStyle()),silent:!0,z2:-1}))},t.type="grid",t}(We),Uv={offset:0};function BM(r){r.registerComponentView(OM),r.registerComponentModel(eM),r.registerCoordinateSystem("cartesian2d",pM),Ev(r,"x",du,Uv),Ev(r,"y",du,Uv),r.registerComponentView(Qy),r.registerComponentView(kM),r.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})}var Hr=wt(),$v=Q,pl=pt,NM=function(){function r(){this._dragging=!1,this.animationThreshold=15}return r.prototype.render=function(t,e,i,n){var a=e.get("value"),o=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=i,!(!n&&this._lastValue===a&&this._lastStatus===o)){this._lastValue=a,this._lastStatus=o;var s=this._group,l=this._handle;if(!o||o==="hide"){s&&s.hide(),l&&l.hide();return}s&&s.show(),l&&l.show();var u={};this.makeElOption(u,a,t,e,i);var f=u.graphicKey;f!==this._lastGraphicKey&&this.clear(i),this._lastGraphicKey=f;var h=this._moveAnimation=this.determineAnimation(t,e);if(!s)s=this._group=new Ft,this.createPointerEl(s,u,t,e),this.createLabelEl(s,u,t,e),i.getZr().add(s);else{var v=bt(Yv,e,h);this.updatePointerEl(s,u,v),this.updateLabelEl(s,u,v,e)}Zv(s,e,!0),this._renderHandle(a)}},r.prototype.remove=function(t){this.clear(t)},r.prototype.dispose=function(t){this.clear(t)},r.prototype.determineAnimation=function(t,e){var i=e.get("animation"),n=t.axis,a=n.type==="category",o=e.get("snap");if(!o&&!a)return!1;if(i==="auto"||i==null){var s=this.animationThreshold;if(a&&n.getBandWidth()>s)return!0;if(o){var l=Af(t).seriesDataCount,u=n.getExtent();return Math.abs(u[0]-u[1])/l>s}return!1}return i===!0},r.prototype.makeElOption=function(t,e,i,n,a){},r.prototype.createPointerEl=function(t,e,i,n){var a=e.pointer;if(a){var o=Hr(t).pointerEl=new gw[a.type]($v(e.pointer));t.add(o)}},r.prototype.createLabelEl=function(t,e,i,n){if(e.label){var a=Hr(t).labelEl=new zt($v(e.label));t.add(a),Xv(a,n)}},r.prototype.updatePointerEl=function(t,e,i){var n=Hr(t).pointerEl;n&&e.pointer&&(n.setStyle(e.pointer.style),i(n,{shape:e.pointer.shape}))},r.prototype.updateLabelEl=function(t,e,i,n){var a=Hr(t).labelEl;a&&(a.setStyle(e.label.style),i(a,{x:e.label.x,y:e.label.y}),Xv(a,n))},r.prototype._renderHandle=function(t){if(!(this._dragging||!this.updateHandleTransform)){var e=this._axisPointerModel,i=this._api.getZr(),n=this._handle,a=e.getModel("handle"),o=e.get("status");if(!a.get("show")||!o||o==="hide"){n&&i.remove(n),this._handle=null;return}var s;this._handle||(s=!0,n=this._handle=Ju(a.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(u){xd(u.event)},onmousedown:pl(this._onHandleDragMove,this,0,0),drift:pl(this._onHandleDragMove,this),ondragend:pl(this._onHandleDragEnd,this)}),i.add(n)),Zv(n,e,!1),n.setStyle(a.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var l=a.get("size");N(l)||(l=[l,l]),n.scaleX=l[0]/2,n.scaleY=l[1]/2,zg(this,"_doDispatchAxisPointer",a.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,s)}},r.prototype._moveHandleToValue=function(t,e){Yv(this._axisPointerModel,!e&&this._moveAnimation,this._handle,gl(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},r.prototype._onHandleDragMove=function(t,e){var i=this._handle;if(i){this._dragging=!0;var n=this.updateHandleTransform(gl(i),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=n,i.stopAnimation(),i.attr(gl(n)),Hr(i).lastProp=null,this._doDispatchAxisPointer()}},r.prototype._doDispatchAxisPointer=function(){var t=this._handle;if(t){var e=this._payloadInfo,i=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:i.axis.dim,axisIndex:i.componentIndex}]})}},r.prototype._onHandleDragEnd=function(){this._dragging=!1;var t=this._handle;if(t){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},r.prototype.clear=function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),i=this._group,n=this._handle;e&&i&&(this._lastGraphicKey=null,i&&e.remove(i),n&&e.remove(n),this._group=null,this._handle=null,this._payloadInfo=null),ru(this,"_doDispatchAxisPointer")},r.prototype.doClear=function(){},r.prototype.buildLabel=function(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}},r}();function Yv(r,t,e,i){Jy(Hr(e).lastProp,i)||(Hr(e).lastProp=i,t?hr(e,i,r):(e.stopAnimation(),e.attr(i)))}function Jy(r,t){if(H(r)&&H(t)){var e=!0;return M(t,function(i,n){e=e&&Jy(r[n],i)}),!!e}else return r===t}function Xv(r,t){r[t.get(["label","show"])?"show":"hide"]()}function gl(r){return{x:r.x||0,y:r.y||0,rotation:r.rotation||0}}function Zv(r,t,e){var i=t.get("z"),n=t.get("zlevel");r&&r.traverse(function(a){a.type!=="group"&&(i!=null&&(a.z=i),n!=null&&(a.zlevel=n),a.silent=e)})}const FM=NM;function zM(r){var t=r.get("type"),e=r.getModel(t+"Style"),i;return t==="line"?(i=e.getLineStyle(),i.fill=null):t==="shadow"&&(i=e.getAreaStyle(),i.stroke=null),i}function HM(r,t,e,i,n){var a=e.get("value"),o=jy(a,t.axis,t.ecModel,e.get("seriesDataIndices"),{precision:e.get(["label","precision"]),formatter:e.get(["label","formatter"])}),s=e.getModel("label"),l=Xo(s.get("padding")||0),u=s.getFont(),f=Ou(o,u),h=n.position,v=f.width+l[1]+l[3],c=f.height+l[0]+l[2],d=n.align;d==="right"&&(h[0]-=v),d==="center"&&(h[0]-=v/2);var y=n.verticalAlign;y==="bottom"&&(h[1]-=c),y==="middle"&&(h[1]-=c/2),GM(h,v,c,i);var p=s.get("backgroundColor");(!p||p==="auto")&&(p=t.get(["axisLine","lineStyle","color"])),r.label={x:h[0],y:h[1],style:cr(s,{text:o,font:u,fill:s.getTextColor(),padding:l,backgroundColor:p}),z2:10}}function GM(r,t,e,i){var n=i.getWidth(),a=i.getHeight();r[0]=Math.min(r[0]+t,n)-t,r[1]=Math.min(r[1]+e,a)-e,r[0]=Math.max(r[0],0),r[1]=Math.max(r[1],0)}function jy(r,t,e,i,n){r=t.scale.parse(r);var a=t.scale.getLabel({value:r},{precision:n.precision}),o=n.formatter;if(o){var s={value:Tf(t,{value:r}),axisDimension:t.dim,axisIndex:t.index,seriesData:[]};M(i,function(l){var u=e.getSeriesByIndex(l.seriesIndex),f=l.dataIndexInside,h=u&&u.getDataParams(f);h&&s.seriesData.push(h)}),z(o)?a=o.replace("{value}",a):U(o)&&(a=o(s))}return a}function tm(r,t,e){var i=Ai();return Pu(i,i,e.rotation),Dl(i,i,e.position),Qu([r.dataToCoord(t),(e.labelOffset||0)+(e.labelDirection||1)*(e.labelMargin||0)],i)}function VM(r,t,e,i,n,a){var o=Zy.innerTextLayout(e.rotation,0,e.labelDirection);e.labelMargin=n.get(["label","margin"]),HM(t,i,n,a,{position:tm(i.axis,r,e),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function WM(r,t,e){return e=e||0,{x1:r[e],y1:r[1-e],x2:t[e],y2:t[1-e]}}function UM(r,t,e){return e=e||0,{x:r[e],y:r[1-e],width:t[e],height:t[1-e]}}var $M=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.makeElOption=function(e,i,n,a,o){var s=n.axis,l=s.grid,u=a.get("type"),f=qv(l,s).getOtherAxis(s).getGlobalExtent(),h=s.toGlobalCoord(s.dataToCoord(i,!0));if(u&&u!=="none"){var v=zM(a),c=YM[u](s,h,f);c.style=v,e.graphicKey=c.type,e.pointer=c}var d=gu(l.model,n);VM(i,e,d,n,a,o)},t.prototype.getHandleTransform=function(e,i,n){var a=gu(i.axis.grid.model,i,{labelInside:!1});a.labelMargin=n.get(["handle","margin"]);var o=tm(i.axis,e,a);return{x:o[0],y:o[1],rotation:a.rotation+(a.labelDirection<0?Math.PI:0)}},t.prototype.updateHandleTransform=function(e,i,n,a){var o=n.axis,s=o.grid,l=o.getGlobalExtent(!0),u=qv(s,o).getOtherAxis(o).getGlobalExtent(),f=o.dim==="x"?0:1,h=[e.x,e.y];h[f]+=i[f],h[f]=Math.min(l[1],h[f]),h[f]=Math.max(l[0],h[f]);var v=(u[1]+u[0])/2,c=[v,v];c[f]=h[f];var d=[{verticalAlign:"middle"},{align:"center"}];return{x:h[0],y:h[1],rotation:e.rotation,cursorPoint:c,tooltipOption:d[f]}},t}(FM);function qv(r,t){var e={};return e[t.dim+"AxisIndex"]=t.index,r.getCartesian(e)}var YM={line:function(r,t,e){var i=WM([t,e[0]],[t,e[1]],Kv(r));return{type:"Line",subPixelOptimize:!0,shape:i}},shadow:function(r,t,e){var i=Math.max(1,r.getBandWidth()),n=e[1]-e[0];return{type:"Rect",shape:UM([t-i/2,e[0]],[i,n],Kv(r))}}};function Kv(r){return r.dim==="x"?0:1}const XM=$M;var ZM=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="axisPointer",t.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}},t}(dt);const qM=ZM;var Fe=wt(),KM=M;function em(r,t,e){if(!Y.node){var i=t.getZr();Fe(i).records||(Fe(i).records={}),QM(i,t);var n=Fe(i).records[r]||(Fe(i).records[r]={});n.handler=e}}function QM(r,t){if(Fe(r).initialized)return;Fe(r).initialized=!0,e("click",bt(Qv,"click")),e("mousemove",bt(Qv,"mousemove")),e("globalout",jM);function e(i,n){r.on(i,function(a){var o=tA(t);KM(Fe(r).records,function(s){s&&n(s,a,o.dispatchAction)}),JM(o.pendings,t)})}}function JM(r,t){var e=r.showTip.length,i=r.hideTip.length,n;e?n=r.showTip[e-1]:i&&(n=r.hideTip[i-1]),n&&(n.dispatchAction=null,t.dispatchAction(n))}function jM(r,t,e){r.handler("leave",null,e)}function Qv(r,t,e,i){t.handler(r,e,i)}function tA(r){var t={showTip:[],hideTip:[]},e=function(i){var n=t[i.type];n?n.push(i):(i.dispatchAction=e,r.dispatchAction(i))};return{dispatchAction:e,pendings:t}}function _u(r,t){if(!Y.node){var e=t.getZr(),i=(Fe(e).records||{})[r];i&&(Fe(e).records[r]=null)}}var eA=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n){var a=i.getComponent("tooltip"),o=e.get("triggerOn")||a&&a.get("triggerOn")||"mousemove|click";em("axisPointer",n,function(s,l,u){o!=="none"&&(s==="leave"||o.indexOf(s)>=0)&&u({type:"updateAxisPointer",currTrigger:s,x:l&&l.offsetX,y:l&&l.offsetY})})},t.prototype.remove=function(e,i){_u("axisPointer",i)},t.prototype.dispose=function(e,i){_u("axisPointer",i)},t.type="axisPointer",t}(We);const rA=eA;function rm(r,t){var e=[],i=r.seriesIndex,n;if(i==null||!(n=t.getSeriesByIndex(i)))return{point:[]};var a=n.getData(),o=Qr(a,r);if(o==null||o<0||N(o))return{point:[]};var s=a.getItemGraphicEl(o),l=n.coordinateSystem;if(n.getTooltipPosition)e=n.getTooltipPosition(o)||[];else if(l&&l.dataToPoint)if(r.isStacked){var u=l.getBaseAxis(),f=l.getOtherAxis(u),h=f.dim,v=u.dim,c=h==="x"||h==="radius"?1:0,d=a.mapDimension(v),y=[];y[c]=a.get(d,o),y[1-c]=a.get(a.getCalculationInfo("stackResultDimension"),o),e=l.dataToPoint(y)||[]}else e=l.dataToPoint(a.getValues(V(l.dimensions,function(g){return a.mapDimension(g)}),o))||[];else if(s){var p=s.getBoundingRect().clone();p.applyTransform(s.transform),e=[p.x+p.width/2,p.y+p.height/2]}return{point:e,el:s}}var Jv=wt();function iA(r,t,e){var i=r.currTrigger,n=[r.x,r.y],a=r,o=r.dispatchAction||pt(e.dispatchAction,e),s=t.getComponent("axisPointer").coordSysAxesInfo;if(s){no(n)&&(n=rm({seriesIndex:a.seriesIndex,dataIndex:a.dataIndex},t).point);var l=no(n),u=a.axesInfo,f=s.axesInfo,h=i==="leave"||no(n),v={},c={},d={list:[],map:{}},y={showPointer:bt(aA,c),showTooltip:bt(oA,d)};M(s.coordSysMap,function(g,m){var _=l||g.containPoint(n);M(s.coordSysAxesInfo[m],function(S,b){var w=S.axis,x=fA(u,S);if(!h&&_&&(!u||x)){var A=x&&x.value;A==null&&!l&&(A=w.pointToData(n)),A!=null&&jv(S,A,y,!1,v)}})});var p={};return M(f,function(g,m){var _=g.linkGroup;_&&!c[m]&&M(_.axesInfo,function(S,b){var w=c[b];if(S!==g&&w){var x=w.value;_.mapper&&(x=g.axis.scale.parse(_.mapper(x,td(S),td(g)))),p[g.key]=x}})}),M(p,function(g,m){jv(f[m],g,y,!0,v)}),sA(c,f,v),lA(d,n,r,o),uA(f,o,e),v}}function jv(r,t,e,i,n){var a=r.axis;if(!(a.scale.isBlank()||!a.containData(t))){if(!r.involveSeries){e.showPointer(r,t);return}var o=nA(t,r),s=o.payloadBatch,l=o.snapToValue;s[0]&&n.seriesIndex==null&&k(n,s[0]),!i&&r.snap&&a.containData(l)&&l!=null&&(t=l),e.showPointer(r,t,s),e.showTooltip(r,o,l)}}function nA(r,t){var e=t.axis,i=e.dim,n=r,a=[],o=Number.MAX_VALUE,s=-1;return M(t.seriesModels,function(l,u){var f=l.getData().mapDimensionsAll(i),h,v;if(l.getAxisTooltipData){var c=l.getAxisTooltipData(f,r,e);v=c.dataIndices,h=c.nestestValue}else{if(v=l.getData().indicesOfNearest(f[0],r,e.type==="category"?.5:null),!v.length)return;h=l.getData().get(f[0],v[0])}if(!(h==null||!isFinite(h))){var d=r-h,y=Math.abs(d);y<=o&&((y<o||d>=0&&s<0)&&(o=y,s=d,n=h,a.length=0),M(v,function(p){a.push({seriesIndex:l.seriesIndex,dataIndexInside:p,dataIndex:l.getData().getRawIndex(p)})}))}}),{payloadBatch:a,snapToValue:n}}function aA(r,t,e,i){r[t.key]={value:e,payloadBatch:i}}function oA(r,t,e,i){var n=e.payloadBatch,a=t.axis,o=a.model,s=t.axisPointerModel;if(!(!t.triggerTooltip||!n.length)){var l=t.coordSys.model,u=qn(l),f=r.map[u];f||(f=r.map[u]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},r.list.push(f)),f.dataByAxis.push({axisDim:a.dim,axisIndex:o.componentIndex,axisType:o.type,axisId:o.id,value:i,valueLabelOpt:{precision:s.get(["label","precision"]),formatter:s.get(["label","formatter"])},seriesDataIndices:n.slice()})}}function sA(r,t,e){var i=e.axesInfo=[];M(t,function(n,a){var o=n.axisPointerModel.option,s=r[a];s?(!n.useHandle&&(o.status="show"),o.value=s.value,o.seriesDataIndices=(s.payloadBatch||[]).slice()):!n.useHandle&&(o.status="hide"),o.status==="show"&&i.push({axisDim:n.axis.dim,axisIndex:n.axis.model.componentIndex,value:o.value})})}function lA(r,t,e,i){if(no(t)||!r.list.length){i({type:"hideTip"});return}var n=((r.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:t[0],y:t[1],tooltipOption:e.tooltipOption,position:e.position,dataIndexInside:n.dataIndexInside,dataIndex:n.dataIndex,seriesIndex:n.seriesIndex,dataByCoordSys:r.list})}function uA(r,t,e){var i=e.getZr(),n="axisPointerLastHighlights",a=Jv(i)[n]||{},o=Jv(i)[n]={};M(r,function(u,f){var h=u.axisPointerModel.option;h.status==="show"&&u.triggerEmphasis&&M(h.seriesDataIndices,function(v){var c=v.seriesIndex+" | "+v.dataIndex;o[c]=v})});var s=[],l=[];M(a,function(u,f){!o[f]&&l.push(u)}),M(o,function(u,f){!a[f]&&s.push(u)}),l.length&&e.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:l}),s.length&&e.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:s})}function fA(r,t){for(var e=0;e<(r||[]).length;e++){var i=r[e];if(t.axis.dim===i.axisDim&&t.axis.model.componentIndex===i.axisIndex)return i}}function td(r){var t=r.axis.model,e={},i=e.axisDim=r.axis.dim;return e.axisIndex=e[i+"AxisIndex"]=t.componentIndex,e.axisName=e[i+"AxisName"]=t.name,e.axisId=e[i+"AxisId"]=t.id,e}function no(r){return!r||r[0]==null||isNaN(r[0])||r[1]==null||isNaN(r[1])}function im(r){qy.registerAxisPointerClass("CartesianAxisPointer",XM),r.registerComponentModel(qM),r.registerComponentView(rA),r.registerPreprocessor(function(t){if(t){(!t.axisPointer||t.axisPointer.length===0)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!N(e)&&(t.axisPointer.link=[e])}}),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=wM(t,e)}),r.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},iA)}function hA(r){vr(BM),vr(im)}function cA(r,t){var e=Xo(t.get("padding")),i=t.getItemStyle(["color","opacity"]);return i.fill=t.get("backgroundColor"),r=new Ct({shape:{x:r.x-e[3],y:r.y-e[0],width:r.width+e[1]+e[3],height:r.height+e[0]+e[2],r:t.get("borderRadius")},style:i,silent:!0,z2:-1}),r}var vA=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="tooltip",t.dependencies=["axisPointer"],t.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}},t}(dt);const dA=vA;function nm(r){var t=r.get("confine");return t!=null?!!t:r.get("renderMode")==="richText"}function am(r){if(Y.domSupported){for(var t=document.documentElement.style,e=0,i=r.length;e<i;e++)if(r[e]in t)return r[e]}}var om=am(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),pA=am(["webkitTransition","transition","OTransition","MozTransition","msTransition"]);function sm(r,t){if(!r)return t;t=ug(t,!0);var e=r.indexOf(t);return r=e===-1?t:"-"+r.slice(0,e)+"-"+t,r.toLowerCase()}function gA(r,t){var e=r.currentStyle||document.defaultView&&document.defaultView.getComputedStyle(r);return e?t?e[t]:e:null}var yA=sm(pA,"transition"),Lf=sm(om,"transform"),mA="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+(Y.transform3dSupported?"will-change:transform;":"");function _A(r){return r=r==="left"?"right":r==="right"?"left":r==="top"?"bottom":"top",r}function SA(r,t,e){if(!z(e)||e==="inside")return"";var i=r.get("backgroundColor"),n=r.get("borderWidth");t=ti(t);var a=_A(e),o=Math.max(Math.round(n)*1.5,6),s="",l=Lf+":",u;lt(["left","right"],a)>-1?(s+="top:50%",l+="translateY(-50%) rotate("+(u=a==="left"?-225:-45)+"deg)"):(s+="left:50%",l+="translateX(-50%) rotate("+(u=a==="top"?225:45)+"deg)");var f=u*Math.PI/180,h=o+n,v=h*Math.abs(Math.cos(f))+h*Math.abs(Math.sin(f)),c=Math.round(((v-Math.SQRT2*n)/2+Math.SQRT2*n-(v-h)/2)*100)/100;s+=";"+a+":-"+c+"px";var d=t+" solid "+n+"px;",y=["position:absolute;width:"+o+"px;height:"+o+"px;z-index:-1;",s+";"+l+";","border-bottom:"+d,"border-right:"+d,"background-color:"+i+";"];return'<div style="'+y.join("")+'"></div>'}function wA(r,t){var e="cubic-bezier(0.23,1,0.32,1)",i=" "+r/2+"s "+e,n="opacity"+i+",visibility"+i;return t||(i=" "+r+"s "+e,n+=Y.transformSupported?","+Lf+i:",left"+i+",top"+i),yA+":"+n}function ed(r,t,e){var i=r.toFixed(0)+"px",n=t.toFixed(0)+"px";if(!Y.transformSupported)return e?"top:"+n+";left:"+i+";":[["top",n],["left",i]];var a=Y.transform3dSupported,o="translate"+(a?"3d":"")+"("+i+","+n+(a?",0":"")+")";return e?"top:0;left:0;"+Lf+":"+o+";":[["top",0],["left",0],[om,o]]}function bA(r){var t=[],e=r.get("fontSize"),i=r.getTextColor();i&&t.push("color:"+i),t.push("font:"+r.getFont());var n=Z(r.get("lineHeight"),Math.round(e*3/2));e&&t.push("line-height:"+n+"px");var a=r.get("textShadowColor"),o=r.get("textShadowBlur")||0,s=r.get("textShadowOffsetX")||0,l=r.get("textShadowOffsetY")||0;return a&&o&&t.push("text-shadow:"+s+"px "+l+"px "+o+"px "+a),M(["decoration","align"],function(u){var f=r.get(u);f&&t.push("text-"+u+":"+f)}),t.join(";")}function xA(r,t,e){var i=[],n=r.get("transitionDuration"),a=r.get("backgroundColor"),o=r.get("shadowBlur"),s=r.get("shadowColor"),l=r.get("shadowOffsetX"),u=r.get("shadowOffsetY"),f=r.getModel("textStyle"),h=Bg(r,"html"),v=l+"px "+u+"px "+o+"px "+s;return i.push("box-shadow:"+v),t&&n&&i.push(wA(n,e)),a&&i.push("background-color:"+a),M(["width","color","radius"],function(c){var d="border-"+c,y=ug(d),p=r.get(y);p!=null&&i.push(d+":"+p+(c==="color"?"":"px"))}),i.push(bA(f)),h!=null&&i.push("padding:"+Xo(h).join("px ")+"px"),i.join(";")+";"}function rd(r,t,e,i,n){var a=t&&t.painter;if(e){var o=a&&a.getViewportRoot();o&&v0(r,o,e,i,n)}else{r[0]=i,r[1]=n;var s=a&&a.getViewportRootOffset();s&&(r[0]+=s.offsetLeft,r[1]+=s.offsetTop)}r[2]=r[0]/t.getWidth(),r[3]=r[1]/t.getHeight()}var TA=function(){function r(t,e){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._alwaysShowContent=!1,this._firstShow=!0,this._longHide=!0,Y.wxa)return null;var i=document.createElement("div");i.domBelongToZr=!0,this.el=i;var n=this._zr=t.getZr(),a=e.appendTo,o=a&&(z(a)?document.querySelector(a):En(a)?a:U(a)&&a(t.getDom()));rd(this._styleCoord,n,o,t.getWidth()/2,t.getHeight()/2),(o||t.getDom()).appendChild(i),this._api=t,this._container=o;var s=this;i.onmouseenter=function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0},i.onmousemove=function(l){if(l=l||window.event,!s._enterable){var u=n.handler,f=n.painter.getViewportRoot();ne(f,l,!0),u.dispatch("mousemove",l)}},i.onmouseleave=function(){s._inContent=!1,s._enterable&&s._show&&s.hideLater(s._hideDelay)}}return r.prototype.update=function(t){if(!this._container){var e=this._api.getDom(),i=gA(e,"position"),n=e.style;n.position!=="absolute"&&i!=="absolute"&&(n.position="relative")}var a=t.get("alwaysShowContent");a&&this._moveIfResized(),this._alwaysShowContent=a,this.el.className=t.get("className")||""},r.prototype.show=function(t,e){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var i=this.el,n=i.style,a=this._styleCoord;i.innerHTML?n.cssText=mA+xA(t,!this._firstShow,this._longHide)+ed(a[0],a[1],!0)+("border-color:"+ti(e)+";")+(t.get("extraCssText")||"")+(";pointer-events:"+(this._enterable?"auto":"none")):n.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},r.prototype.setContent=function(t,e,i,n,a){var o=this.el;if(t==null){o.innerHTML="";return}var s="";if(z(a)&&i.get("trigger")==="item"&&!nm(i)&&(s=SA(i,n,a)),z(t))o.innerHTML=t+s;else if(t){o.innerHTML="",N(t)||(t=[t]);for(var l=0;l<t.length;l++)En(t[l])&&t[l].parentNode!==o&&o.appendChild(t[l]);if(s&&o.childNodes.length){var u=document.createElement("div");u.innerHTML=s,o.appendChild(u)}}},r.prototype.setEnterable=function(t){this._enterable=t},r.prototype.getSize=function(){var t=this.el;return t?[t.offsetWidth,t.offsetHeight]:[0,0]},r.prototype.moveTo=function(t,e){if(this.el){var i=this._styleCoord;if(rd(i,this._zr,this._container,t,e),i[0]!=null&&i[1]!=null){var n=this.el.style,a=ed(i[0],i[1]);M(a,function(o){n[o[0]]=o[1]})}}},r.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},r.prototype.hide=function(){var t=this,e=this.el.style;e.visibility="hidden",e.opacity="0",Y.transform3dSupported&&(e.willChange=""),this._show=!1,this._longHideTimeout=setTimeout(function(){return t._longHide=!0},500)},r.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(pt(this.hide,this),t)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var t=this.el.parentNode;t&&t.removeChild(this.el),this.el=this._container=null},r}();const CA=TA;var DA=function(){function r(t){this._show=!1,this._styleCoord=[0,0,0,0],this._alwaysShowContent=!1,this._enterable=!0,this._zr=t.getZr(),nd(this._styleCoord,this._zr,t.getWidth()/2,t.getHeight()/2)}return r.prototype.update=function(t){var e=t.get("alwaysShowContent");e&&this._moveIfResized(),this._alwaysShowContent=e},r.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},r.prototype.setContent=function(t,e,i,n,a){var o=this;H(t)&&Wt(""),this.el&&this._zr.remove(this.el);var s=i.getModel("textStyle");this.el=new zt({style:{rich:e.richTextStyles,text:t,lineHeight:22,borderWidth:1,borderColor:n,textShadowColor:s.get("textShadowColor"),fill:i.get(["textStyle","color"]),padding:Bg(i,"richText"),verticalAlign:"top",align:"left"},z:i.get("z")}),M(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],function(u){o.el.style[u]=i.get(u)}),M(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],function(u){o.el.style[u]=s.get(u)||0}),this._zr.add(this.el);var l=this;this.el.on("mouseover",function(){l._enterable&&(clearTimeout(l._hideTimeout),l._show=!0),l._inContent=!0}),this.el.on("mouseout",function(){l._enterable&&l._show&&l.hideLater(l._hideDelay),l._inContent=!1})},r.prototype.setEnterable=function(t){this._enterable=t},r.prototype.getSize=function(){var t=this.el,e=this.el.getBoundingRect(),i=id(t.style);return[e.width+i.left+i.right,e.height+i.top+i.bottom]},r.prototype.moveTo=function(t,e){var i=this.el;if(i){var n=this._styleCoord;nd(n,this._zr,t,e),t=n[0],e=n[1];var a=i.style,o=nr(a.borderWidth||0),s=id(a);i.x=t+o+s.left,i.y=e+o+s.top,i.markRedraw()}},r.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},r.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},r.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(pt(this.hide,this),t)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){this._zr.remove(this.el)},r}();function nr(r){return Math.max(0,r)}function id(r){var t=nr(r.shadowBlur||0),e=nr(r.shadowOffsetX||0),i=nr(r.shadowOffsetY||0);return{left:nr(t-e),right:nr(t+e),top:nr(t-i),bottom:nr(t+i)}}function nd(r,t,e,i){r[0]=e,r[1]=i,r[2]=r[0]/t.getWidth(),r[3]=r[1]/t.getHeight()}const MA=DA;var AA=new Ct({shape:{x:-1,y:-1,width:2,height:2}}),LA=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.init=function(e,i){if(!(Y.node||!i.getDom())){var n=e.getComponent("tooltip"),a=this._renderMode=Z_(n.get("renderMode"));this._tooltipContent=a==="richText"?new MA(i):new CA(i,{appendTo:n.get("appendToBody",!0)?"body":n.get("appendTo",!0)})}},t.prototype.render=function(e,i,n){if(!(Y.node||!n.getDom())){this.group.removeAll(),this._tooltipModel=e,this._ecModel=i,this._api=n;var a=this._tooltipContent;a.update(e),a.setEnterable(e.get("enterable")),this._initGlobalListener(),this._keepShow(),this._renderMode!=="richText"&&e.get("transitionDuration")?zg(this,"_updatePosition",50,"fixRate"):ru(this,"_updatePosition")}},t.prototype._initGlobalListener=function(){var e=this._tooltipModel,i=e.get("triggerOn");em("itemTooltip",this._api,pt(function(n,a,o){i!=="none"&&(i.indexOf(n)>=0?this._tryShow(a,o):n==="leave"&&this._hide(o))},this))},t.prototype._keepShow=function(){var e=this._tooltipModel,i=this._ecModel,n=this._api,a=e.get("triggerOn");if(this._lastX!=null&&this._lastY!=null&&a!=="none"&&a!=="click"){var o=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){!n.isDisposed()&&o.manuallyShowTip(e,i,n,{x:o._lastX,y:o._lastY,dataByCoordSys:o._lastDataByCoordSys})})}},t.prototype.manuallyShowTip=function(e,i,n,a){if(!(a.from===this.uid||Y.node||!n.getDom())){var o=ad(a,n);this._ticket="";var s=a.dataByCoordSys,l=EA(a,i,n);if(l){var u=l.el.getBoundingRect().clone();u.applyTransform(l.el.transform),this._tryShow({offsetX:u.x+u.width/2,offsetY:u.y+u.height/2,target:l.el,position:a.position,positionDefault:"bottom"},o)}else if(a.tooltip&&a.x!=null&&a.y!=null){var f=AA;f.x=a.x,f.y=a.y,f.update(),at(f).tooltipConfig={name:null,option:a.tooltip},this._tryShow({offsetX:a.x,offsetY:a.y,target:f},o)}else if(s)this._tryShow({offsetX:a.x,offsetY:a.y,position:a.position,dataByCoordSys:s,tooltipOption:a.tooltipOption},o);else if(a.seriesIndex!=null){if(this._manuallyAxisShowTip(e,i,n,a))return;var h=rm(a,i),v=h.point[0],c=h.point[1];v!=null&&c!=null&&this._tryShow({offsetX:v,offsetY:c,target:h.el,position:a.position,positionDefault:"bottom"},o)}else a.x!=null&&a.y!=null&&(n.dispatchAction({type:"updateAxisPointer",x:a.x,y:a.y}),this._tryShow({offsetX:a.x,offsetY:a.y,position:a.position,target:n.getZr().findHover(a.x,a.y).target},o))}},t.prototype.manuallyHideTip=function(e,i,n,a){var o=this._tooltipContent;this._tooltipModel&&o.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,a.from!==this.uid&&this._hide(ad(a,n))},t.prototype._manuallyAxisShowTip=function(e,i,n,a){var o=a.seriesIndex,s=a.dataIndex,l=i.getComponent("axisPointer").coordSysAxesInfo;if(!(o==null||s==null||l==null)){var u=i.getSeriesByIndex(o);if(u){var f=u.getData(),h=fn([f.getItemModel(s),u,(u.coordinateSystem||{}).model],this._tooltipModel);if(h.get("trigger")==="axis")return n.dispatchAction({type:"updateAxisPointer",seriesIndex:o,dataIndex:s,position:a.position}),!0}}},t.prototype._tryShow=function(e,i){var n=e.target,a=this._tooltipModel;if(a){this._lastX=e.offsetX,this._lastY=e.offsetY;var o=e.dataByCoordSys;if(o&&o.length)this._showAxisTooltip(o,e);else if(n){var s=at(n);if(s.ssrType==="legend")return;this._lastDataByCoordSys=null;var l,u;_n(n,function(f){if(at(f).dataIndex!=null)return l=f,!0;if(at(f).tooltipConfig!=null)return u=f,!0},!0),l?this._showSeriesItemTooltip(e,l,i):u?this._showComponentItemTooltip(e,u,i):this._hide(i)}else this._lastDataByCoordSys=null,this._hide(i)}},t.prototype._showOrMove=function(e,i){var n=e.get("showDelay");i=pt(i,this),clearTimeout(this._showTimout),n>0?this._showTimout=setTimeout(i,n):i()},t.prototype._showAxisTooltip=function(e,i){var n=this._ecModel,a=this._tooltipModel,o=[i.offsetX,i.offsetY],s=fn([i.tooltipOption],a),l=this._renderMode,u=[],f=Wn("section",{blocks:[],noHeader:!0}),h=[],v=new Ks;M(e,function(m){M(m.dataByAxis,function(_){var S=n.getComponent(_.axisDim+"Axis",_.axisIndex),b=_.value;if(!(!S||b==null)){var w=jy(b,S.axis,n,_.seriesDataIndices,_.valueLabelOpt),x=Wn("section",{header:w,noHeader:!Ce(w),sortBlocks:!0,blocks:[]});f.blocks.push(x),M(_.seriesDataIndices,function(A){var C=n.getSeriesByIndex(A.seriesIndex),D=A.dataIndexInside,T=C.getDataParams(D);if(!(T.dataIndex<0)){T.axisDim=_.axisDim,T.axisIndex=_.axisIndex,T.axisType=_.axisType,T.axisId=_.axisId,T.axisValue=Tf(S.axis,{value:b}),T.axisValueLabel=w,T.marker=v.makeTooltipMarker("item",ti(T.color),l);var L=Lc(C.formatTooltip(D,!0,null)),P=L.frag;if(P){var I=fn([C],a).get("valueFormatter");x.blocks.push(I?k({valueFormatter:I},P):P)}L.text&&h.push(L.text),u.push(T)}})}})}),f.blocks.reverse(),h.reverse();var c=i.position,d=s.get("order"),y=kc(f,v,l,d,n.get("useUTC"),s.get("textStyle"));y&&h.unshift(y);var p=l==="richText"?`

`:"<br/>",g=h.join(p);this._showOrMove(s,function(){this._updateContentNotChangedOnAxis(e,u)?this._updatePosition(s,c,o[0],o[1],this._tooltipContent,u):this._showTooltipContent(s,g,u,Math.random()+"",o[0],o[1],c,null,v)})},t.prototype._showSeriesItemTooltip=function(e,i,n){var a=this._ecModel,o=at(i),s=o.seriesIndex,l=a.getSeriesByIndex(s),u=o.dataModel||l,f=o.dataIndex,h=o.dataType,v=u.getData(h),c=this._renderMode,d=e.positionDefault,y=fn([v.getItemModel(f),u,l&&(l.coordinateSystem||{}).model],this._tooltipModel,d?{position:d}:null),p=y.get("trigger");if(!(p!=null&&p!=="item")){var g=u.getDataParams(f,h),m=new Ks;g.marker=m.makeTooltipMarker("item",ti(g.color),c);var _=Lc(u.formatTooltip(f,!1,h)),S=y.get("order"),b=y.get("valueFormatter"),w=_.frag,x=w?kc(b?k({valueFormatter:b},w):w,m,c,S,a.get("useUTC"),y.get("textStyle")):_.text,A="item_"+u.name+"_"+f;this._showOrMove(y,function(){this._showTooltipContent(y,x,g,A,e.offsetX,e.offsetY,e.position,e.target,m)}),n({type:"showTip",dataIndexInside:f,dataIndex:v.getRawIndex(f),seriesIndex:s,from:this.uid})}},t.prototype._showComponentItemTooltip=function(e,i,n){var a=this._renderMode==="html",o=at(i),s=o.tooltipConfig,l=s.option||{},u=l.encodeHTMLContent;if(z(l)){var f=l;l={content:f,formatter:f},u=!0}u&&a&&l.content&&(l=Q(l),l.content=Vt(l.content));var h=[l],v=this._ecModel.getComponent(o.componentMainType,o.componentIndex);v&&h.push(v),h.push({formatter:l.content});var c=e.positionDefault,d=fn(h,this._tooltipModel,c?{position:c}:null),y=d.get("content"),p=Math.random()+"",g=new Ks;this._showOrMove(d,function(){var m=Q(d.get("formatterParams")||{});this._showTooltipContent(d,y,m,p,e.offsetX,e.offsetY,e.position,i,g)}),n({type:"showTip",from:this.uid})},t.prototype._showTooltipContent=function(e,i,n,a,o,s,l,u,f){if(this._ticket="",!(!e.get("showContent")||!e.get("show"))){var h=this._tooltipContent;h.setEnterable(e.get("enterable"));var v=e.get("formatter");l=l||e.get("position");var c=i,d=this._getNearestPoint([o,s],n,e.get("trigger"),e.get("borderColor")),y=d.color;if(v)if(z(v)){var p=e.ecModel.get("useUTC"),g=N(n)?n[0]:n,m=g&&g.axisType&&g.axisType.indexOf("time")>=0;c=v,m&&(c=Vo(g.axisValue,c,p)),c=fg(c,n,!0)}else if(U(v)){var _=pt(function(S,b){S===this._ticket&&(h.setContent(b,f,e,y,l),this._updatePosition(e,l,o,s,h,n,u))},this);this._ticket=a,c=v(n,a,_)}else c=v;h.setContent(c,f,e,y,l),h.show(e,y),this._updatePosition(e,l,o,s,h,n,u)}},t.prototype._getNearestPoint=function(e,i,n,a){if(n==="axis"||N(i))return{color:a||(this._renderMode==="html"?"#fff":"none")};if(!N(i))return{color:a||i.color||i.borderColor}},t.prototype._updatePosition=function(e,i,n,a,o,s,l){var u=this._api.getWidth(),f=this._api.getHeight();i=i||e.get("position");var h=o.getSize(),v=e.get("align"),c=e.get("verticalAlign"),d=l&&l.getBoundingRect().clone();if(l&&d.applyTransform(l.transform),U(i)&&(i=i([n,a],s,o.el,d,{viewSize:[u,f],contentSize:h.slice()})),N(i))n=Bt(i[0],u),a=Bt(i[1],f);else if(H(i)){var y=i;y.width=h[0],y.height=h[1];var p=Hn(y,{width:u,height:f});n=p.x,a=p.y,v=null,c=null}else if(z(i)&&l){var g=RA(i,d,h,e.get("borderWidth"));n=g[0],a=g[1]}else{var g=IA(n,a,o,u,f,v?null:20,c?null:20);n=g[0],a=g[1]}if(v&&(n-=od(v)?h[0]/2:v==="right"?h[0]:0),c&&(a-=od(c)?h[1]/2:c==="bottom"?h[1]:0),nm(e)){var g=PA(n,a,o,u,f);n=g[0],a=g[1]}o.moveTo(n,a)},t.prototype._updateContentNotChangedOnAxis=function(e,i){var n=this._lastDataByCoordSys,a=this._cbParamsList,o=!!n&&n.length===e.length;return o&&M(n,function(s,l){var u=s.dataByAxis||[],f=e[l]||{},h=f.dataByAxis||[];o=o&&u.length===h.length,o&&M(u,function(v,c){var d=h[c]||{},y=v.seriesDataIndices||[],p=d.seriesDataIndices||[];o=o&&v.value===d.value&&v.axisType===d.axisType&&v.axisId===d.axisId&&y.length===p.length,o&&M(y,function(g,m){var _=p[m];o=o&&g.seriesIndex===_.seriesIndex&&g.dataIndex===_.dataIndex}),a&&M(v.seriesDataIndices,function(g){var m=g.seriesIndex,_=i[m],S=a[m];_&&S&&S.data!==_.data&&(o=!1)})})}),this._lastDataByCoordSys=e,this._cbParamsList=i,!!o},t.prototype._hide=function(e){this._lastDataByCoordSys=null,e({type:"hideTip",from:this.uid})},t.prototype.dispose=function(e,i){Y.node||!i.getDom()||(ru(this,"_updatePosition"),this._tooltipContent.dispose(),_u("itemTooltip",i))},t.type="tooltip",t}(We);function fn(r,t,e){var i=t.ecModel,n;e?(n=new Rt(e,i,i),n=new Rt(t.option,n,i)):n=t;for(var a=r.length-1;a>=0;a--){var o=r[a];o&&(o instanceof Rt&&(o=o.get("tooltip",!0)),z(o)&&(o={formatter:o}),o&&(n=new Rt(o,n,i)))}return n}function ad(r,t){return r.dispatchAction||pt(t.dispatchAction,t)}function IA(r,t,e,i,n,a,o){var s=e.getSize(),l=s[0],u=s[1];return a!=null&&(r+l+a+2>i?r-=l+a:r+=a),o!=null&&(t+u+o>n?t-=u+o:t+=o),[r,t]}function PA(r,t,e,i,n){var a=e.getSize(),o=a[0],s=a[1];return r=Math.min(r+o,i)-o,t=Math.min(t+s,n)-s,r=Math.max(r,0),t=Math.max(t,0),[r,t]}function RA(r,t,e,i){var n=e[0],a=e[1],o=Math.ceil(Math.SQRT2*i)+8,s=0,l=0,u=t.width,f=t.height;switch(r){case"inside":s=t.x+u/2-n/2,l=t.y+f/2-a/2;break;case"top":s=t.x+u/2-n/2,l=t.y-a-o;break;case"bottom":s=t.x+u/2-n/2,l=t.y+f+o;break;case"left":s=t.x-n-o,l=t.y+f/2-a/2;break;case"right":s=t.x+u+o,l=t.y+f/2-a/2}return[s,l]}function od(r){return r==="center"||r==="middle"}function EA(r,t,e){var i=Hu(r).queryOptionMap,n=i.keys()[0];if(!(!n||n==="series")){var a=ta(t,n,i.get(n),{useDefault:!1,enableAll:!1,enableNone:!1}),o=a.models[0];if(o){var s=e.getViewOfComponentModel(o),l;if(s.group.traverse(function(u){var f=at(u).tooltipConfig;if(f&&f.name===r.name)return l=u,!0}),l)return{componentMainType:n,componentIndex:o.componentIndex,el:l}}}}const kA=LA;function OA(r){vr(im),r.registerComponentModel(dA),r.registerComponentView(kA),r.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},$t),r.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},$t)}var BA=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.layoutMode={type:"box",ignoreSize:!0},e}return t.type="title",t.defaultOption={z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bold",color:"#464646"},subtextStyle:{fontSize:12,color:"#6E7079"}},t}(dt),NA=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n){if(this.group.removeAll(),!!e.get("show")){var a=this.group,o=e.getModel("textStyle"),s=e.getModel("subtextStyle"),l=e.get("textAlign"),u=Z(e.get("textBaseline"),e.get("textVerticalAlign")),f=new zt({style:cr(o,{text:e.get("text"),fill:o.getTextColor()},{disableBox:!0}),z2:10}),h=f.getBoundingRect(),v=e.get("subtext"),c=new zt({style:cr(s,{text:v,fill:s.getTextColor(),y:h.height+e.get("itemGap"),verticalAlign:"top"},{disableBox:!0}),z2:10}),d=e.get("link"),y=e.get("sublink"),p=e.get("triggerEvent",!0);f.silent=!d&&!p,c.silent=!y&&!p,d&&f.on("click",function(){hc(d,"_"+e.get("target"))}),y&&c.on("click",function(){hc(y,"_"+e.get("subtarget"))}),at(f).eventData=at(c).eventData=p?{componentType:"title",componentIndex:e.componentIndex}:null,a.add(f),v&&a.add(c);var g=a.getBoundingRect(),m=e.getBoxLayoutParams();m.width=g.width,m.height=g.height;var _=Hn(m,{width:n.getWidth(),height:n.getHeight()},e.get("padding"));l||(l=e.get("left")||e.get("right"),l==="middle"&&(l="center"),l==="right"?_.x+=_.width:l==="center"&&(_.x+=_.width/2)),u||(u=e.get("top")||e.get("bottom"),u==="center"&&(u="middle"),u==="bottom"?_.y+=_.height:u==="middle"&&(_.y+=_.height/2),u=u||"top"),a.x=_.x,a.y=_.y,a.markRedraw();var S={align:l,verticalAlign:u};f.setStyle(S),c.setStyle(S),g=a.getBoundingRect();var b=_.margin,w=e.getItemStyle(["color","opacity"]);w.fill=e.get("backgroundColor");var x=new Ct({shape:{x:g.x-b[3],y:g.y-b[0],width:g.width+b[1]+b[3],height:g.height+b[0]+b[2],r:e.get("borderRadius")},style:w,subPixelOptimize:!0,silent:!0});a.add(x)}},t.type="title",t}(We);function FA(r){r.registerComponentModel(BA),r.registerComponentView(NA)}var zA=function(r,t){if(t==="all")return{type:"all",title:r.getLocaleModel().get(["legend","selector","all"])};if(t==="inverse")return{type:"inverse",title:r.getLocaleModel().get(["legend","selector","inverse"])}},HA=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.layoutMode={type:"box",ignoreSize:!0},e}return t.prototype.init=function(e,i,n){this.mergeDefaultAndTheme(e,n),e.selected=e.selected||{},this._updateSelector(e)},t.prototype.mergeOption=function(e,i){r.prototype.mergeOption.call(this,e,i),this._updateSelector(e)},t.prototype._updateSelector=function(e){var i=e.selector,n=this.ecModel;i===!0&&(i=e.selector=["all","inverse"]),N(i)&&M(i,function(a,o){z(a)&&(a={type:a}),i[o]=et(a,zA(n,a.type))})},t.prototype.optionUpdated=function(){this._updateData(this.ecModel);var e=this._data;if(e[0]&&this.get("selectedMode")==="single"){for(var i=!1,n=0;n<e.length;n++){var a=e[n].get("name");if(this.isSelected(a)){this.select(a),i=!0;break}}!i&&this.select(e[0].get("name"))}},t.prototype._updateData=function(e){var i=[],n=[];e.eachRawSeries(function(l){var u=l.name;n.push(u);var f;if(l.legendVisualProvider){var h=l.legendVisualProvider,v=h.getAllNames();e.isSeriesFiltered(l)||(n=n.concat(v)),v.length?i=i.concat(v):f=!0}else f=!0;f&&zu(l)&&i.push(l.name)}),this._availableNames=n;var a=this.get("data")||i,o=X(),s=V(a,function(l){return(z(l)||yt(l))&&(l={name:l}),o.get(l.name)?null:(o.set(l.name,!0),new Rt(l,this,this.ecModel))},this);this._data=Tt(s,function(l){return!!l})},t.prototype.getData=function(){return this._data},t.prototype.select=function(e){var i=this.option.selected,n=this.get("selectedMode");if(n==="single"){var a=this._data;M(a,function(o){i[o.get("name")]=!1})}i[e]=!0},t.prototype.unSelect=function(e){this.get("selectedMode")!=="single"&&(this.option.selected[e]=!1)},t.prototype.toggleSelected=function(e){var i=this.option.selected;i.hasOwnProperty(e)||(i[e]=!0),this[i[e]?"unSelect":"select"](e)},t.prototype.allSelect=function(){var e=this._data,i=this.option.selected;M(e,function(n){i[n.get("name",!0)]=!0})},t.prototype.inverseSelect=function(){var e=this._data,i=this.option.selected;M(e,function(n){var a=n.get("name",!0);i.hasOwnProperty(a)||(i[a]=!0),i[a]=!i[a]})},t.prototype.isSelected=function(e){var i=this.option.selected;return!(i.hasOwnProperty(e)&&!i[e])&&lt(this._availableNames,e)>=0},t.prototype.getOrient=function(){return this.get("orient")==="vertical"?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},t.type="legend.plain",t.dependencies=["series"],t.defaultOption={z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,symbolRotate:"inherit",symbolKeepAspect:!0,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",inactiveBorderWidth:"auto",itemStyle:{color:"inherit",opacity:"inherit",borderColor:"inherit",borderWidth:"auto",borderCap:"inherit",borderJoin:"inherit",borderDashOffset:"inherit",borderMiterLimit:"inherit"},lineStyle:{width:"auto",color:"inherit",inactiveColor:"#ccc",inactiveWidth:2,opacity:"inherit",type:"inherit",cap:"inherit",join:"inherit",dashOffset:"inherit",miterLimit:"inherit"},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:"sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}},t}(dt);const Su=HA;var _i=bt,wu=M,Ga=Ft,GA=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.newlineDisabled=!1,e}return t.prototype.init=function(){this.group.add(this._contentGroup=new Ga),this.group.add(this._selectorGroup=new Ga),this._isFirstRender=!0},t.prototype.getContentGroup=function(){return this._contentGroup},t.prototype.getSelectorGroup=function(){return this._selectorGroup},t.prototype.render=function(e,i,n){var a=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),!!e.get("show",!0)){var o=e.get("align"),s=e.get("orient");(!o||o==="auto")&&(o=e.get("left")==="right"&&s==="vertical"?"right":"left");var l=e.get("selector",!0),u=e.get("selectorPosition",!0);l&&(!u||u==="auto")&&(u=s==="horizontal"?"end":"start"),this.renderInner(o,e,i,n,l,s,u);var f=e.getBoxLayoutParams(),h={width:n.getWidth(),height:n.getHeight()},v=e.get("padding"),c=Hn(f,h,v),d=this.layoutInner(e,o,c,a,l,u),y=Hn(ot({width:d.width,height:d.height},f),h,v);this.group.x=y.x-d.x,this.group.y=y.y-d.y,this.group.markRedraw(),this.group.add(this._backgroundEl=cA(d,e))}},t.prototype.resetInner=function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},t.prototype.renderInner=function(e,i,n,a,o,s,l){var u=this.getContentGroup(),f=X(),h=i.get("selectedMode"),v=[];n.eachRawSeries(function(c){!c.get("legendHoverLink")&&v.push(c.id)}),wu(i.getData(),function(c,d){var y=c.get("name");if(!this.newlineDisabled&&(y===""||y===`
`)){var p=new Ga;p.newline=!0,u.add(p);return}var g=n.getSeriesByName(y)[0];if(!f.get(y))if(g){var m=g.getData(),_=m.getVisual("legendLineStyle")||{},S=m.getVisual("legendIcon"),b=m.getVisual("style"),w=this._createItem(g,y,d,c,i,e,_,b,S,h,a);w.on("click",_i(sd,y,null,a,v)).on("mouseover",_i(bu,g.name,null,a,v)).on("mouseout",_i(xu,g.name,null,a,v)),n.ssr&&w.eachChild(function(x){var A=at(x);A.seriesIndex=g.seriesIndex,A.dataIndex=d,A.ssrType="legend"}),f.set(y,!0)}else n.eachRawSeries(function(x){if(!f.get(y)&&x.legendVisualProvider){var A=x.legendVisualProvider;if(!A.containName(y))return;var C=A.indexOfName(y),D=A.getItemVisual(C,"style"),T=A.getItemVisual(C,"legendIcon"),L=ze(D.fill);L&&L[3]===0&&(L[3]=.2,D=k(k({},D),{fill:Ro(L,"rgba")}));var P=this._createItem(x,y,d,c,i,e,{},D,T,h,a);P.on("click",_i(sd,null,y,a,v)).on("mouseover",_i(bu,null,y,a,v)).on("mouseout",_i(xu,null,y,a,v)),n.ssr&&P.eachChild(function(I){var R=at(I);R.seriesIndex=x.seriesIndex,R.dataIndex=d,R.ssrType="legend"}),f.set(y,!0)}},this)},this),o&&this._createSelector(o,i,a,s,l)},t.prototype._createSelector=function(e,i,n,a,o){var s=this.getSelectorGroup();wu(e,function(u){var f=u.type,h=new zt({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){n.dispatchAction({type:f==="all"?"legendAllSelect":"legendInverseSelect",legendId:i.id})}});s.add(h);var v=i.getModel("selectorLabel"),c=i.getModel(["emphasis","selectorLabel"]);tf(h,{normal:v,emphasis:c},{defaultText:u.title}),Ul(h)})},t.prototype._createItem=function(e,i,n,a,o,s,l,u,f,h,v){var c=e.visualDrawType,d=o.get("itemWidth"),y=o.get("itemHeight"),p=o.isSelected(i),g=a.get("symbolRotate"),m=a.get("symbolKeepAspect"),_=a.get("icon");f=_||f||"roundRect";var S=VA(f,a,l,u,c,p,v),b=new Ga,w=a.getModel("textStyle");if(U(e.getLegendIcon)&&(!_||_==="inherit"))b.add(e.getLegendIcon({itemWidth:d,itemHeight:y,icon:f,iconRotate:g,itemStyle:S.itemStyle,lineStyle:S.lineStyle,symbolKeepAspect:m}));else{var x=_==="inherit"&&e.getData().getVisual("symbol")?g==="inherit"?e.getData().getVisual("symbolRotate"):g:0;b.add(WA({itemWidth:d,itemHeight:y,icon:f,iconRotate:x,itemStyle:S.itemStyle,lineStyle:S.lineStyle,symbolKeepAspect:m}))}var A=s==="left"?d+5:-5,C=s,D=o.get("formatter"),T=i;z(D)&&D?T=D.replace("{name}",i??""):U(D)&&(T=D(i));var L=p?w.getTextColor():a.get("inactiveColor");b.add(new zt({style:cr(w,{text:T,x:A,y:y/2,fill:L,align:C,verticalAlign:"middle"},{inheritColor:L})}));var P=new Ct({shape:b.getBoundingRect(),style:{fill:"transparent"}}),I=a.getModel("tooltip");return I.get("show")&&zo({el:P,componentModel:o,itemName:i,itemTooltipOption:I.option}),b.add(P),b.eachChild(function(R){R.silent=!0}),P.silent=!h,this.getContentGroup().add(b),Ul(b),b.__legendDataIndex=n,b},t.prototype.layoutInner=function(e,i,n,a,o,s){var l=this.getContentGroup(),u=this.getSelectorGroup();An(e.get("orient"),l,e.get("itemGap"),n.width,n.height);var f=l.getBoundingRect(),h=[-f.x,-f.y];if(u.markRedraw(),l.markRedraw(),o){An("horizontal",u,e.get("selectorItemGap",!0));var v=u.getBoundingRect(),c=[-v.x,-v.y],d=e.get("selectorButtonGap",!0),y=e.getOrient().index,p=y===0?"width":"height",g=y===0?"height":"width",m=y===0?"y":"x";s==="end"?c[y]+=f[p]+d:h[y]+=v[p]+d,c[1-y]+=f[g]/2-v[g]/2,u.x=c[0],u.y=c[1],l.x=h[0],l.y=h[1];var _={x:0,y:0};return _[p]=f[p]+d+v[p],_[g]=Math.max(f[g],v[g]),_[m]=Math.min(0,v[m]+c[1-y]),_}else return l.x=h[0],l.y=h[1],this.group.getBoundingRect()},t.prototype.remove=function(){this.getContentGroup().removeAll(),this._isFirstRender=!0},t.type="legend.plain",t}(We);function VA(r,t,e,i,n,a,o){function s(p,g){p.lineWidth==="auto"&&(p.lineWidth=g.lineWidth>0?2:0),wu(p,function(m,_){p[_]==="inherit"&&(p[_]=g[_])})}var l=t.getModel("itemStyle"),u=l.getItemStyle(),f=r.lastIndexOf("empty",0)===0?"fill":"stroke",h=l.getShallow("decal");u.decal=!h||h==="inherit"?i.decal:su(h,o),u.fill==="inherit"&&(u.fill=i[n]),u.stroke==="inherit"&&(u.stroke=i[f]),u.opacity==="inherit"&&(u.opacity=(n==="fill"?i:e).opacity),s(u,i);var v=t.getModel("lineStyle"),c=v.getLineStyle();if(s(c,e),u.fill==="auto"&&(u.fill=i.fill),u.stroke==="auto"&&(u.stroke=i.fill),c.stroke==="auto"&&(c.stroke=i.fill),!a){var d=t.get("inactiveBorderWidth"),y=u[f];u.lineWidth=d==="auto"?i.lineWidth>0&&y?2:0:u.lineWidth,u.fill=t.get("inactiveColor"),u.stroke=t.get("inactiveBorderColor"),c.stroke=v.get("inactiveColor"),c.lineWidth=v.get("inactiveWidth")}return{itemStyle:u,lineStyle:c}}function WA(r){var t=r.icon||"roundRect",e=Bi(t,0,0,r.itemWidth,r.itemHeight,r.itemStyle.fill,r.symbolKeepAspect);return e.setStyle(r.itemStyle),e.rotation=(r.iconRotate||0)*Math.PI/180,e.setOrigin([r.itemWidth/2,r.itemHeight/2]),t.indexOf("empty")>-1&&(e.style.stroke=e.style.fill,e.style.fill="#fff",e.style.lineWidth=2),e}function sd(r,t,e,i){xu(r,t,e,i),e.dispatchAction({type:"legendToggleSelect",name:r??t}),bu(r,t,e,i)}function lm(r){for(var t=r.getZr().storage.getDisplayList(),e,i=0,n=t.length;i<n&&!(e=t[i].states.emphasis);)i++;return e&&e.hoverLayer}function bu(r,t,e,i){lm(e)||e.dispatchAction({type:"highlight",seriesName:r,name:t,excludeSeriesId:i})}function xu(r,t,e,i){lm(e)||e.dispatchAction({type:"downplay",seriesName:r,name:t,excludeSeriesId:i})}const um=GA;function UA(r){var t=r.findComponents({mainType:"legend"});t&&t.length&&r.filterSeries(function(e){for(var i=0;i<t.length;i++)if(!t[i].isSelected(e.name))return!1;return!0})}function hn(r,t,e){var i=r==="allSelect"||r==="inverseSelect",n={},a=[];e.eachComponent({mainType:"legend",query:t},function(s){i?s[r]():s[r](t.name),ld(s,n),a.push(s.componentIndex)});var o={};return e.eachComponent("legend",function(s){M(n,function(l,u){s[l?"select":"unSelect"](u)}),ld(s,o)}),i?{selected:o,legendIndex:a}:{name:t.name,selected:o}}function ld(r,t){var e=t||{};return M(r.getData(),function(i){var n=i.get("name");if(!(n===`
`||n==="")){var a=r.isSelected(n);qr(e,n)?e[n]=e[n]&&a:e[n]=a}}),e}function $A(r){r.registerAction("legendToggleSelect","legendselectchanged",bt(hn,"toggleSelected")),r.registerAction("legendAllSelect","legendselectall",bt(hn,"allSelect")),r.registerAction("legendInverseSelect","legendinverseselect",bt(hn,"inverseSelect")),r.registerAction("legendSelect","legendselected",bt(hn,"select")),r.registerAction("legendUnSelect","legendunselected",bt(hn,"unSelect"))}function fm(r){r.registerComponentModel(Su),r.registerComponentView(um),r.registerProcessor(r.PRIORITY.PROCESSOR.SERIES_FILTER,UA),r.registerSubTypeDefaulter("legend",function(){return"plain"}),$A(r)}var YA=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.setScrollDataIndex=function(e){this.option.scrollDataIndex=e},t.prototype.init=function(e,i,n){var a=Zo(e);r.prototype.init.call(this,e,i,n),ud(this,e,a)},t.prototype.mergeOption=function(e,i){r.prototype.mergeOption.call(this,e,i),ud(this,this.option,e)},t.type="legend.scroll",t.defaultOption=Pw(Su.defaultOption,{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800}),t}(Su);function ud(r,t,e){var i=r.getOrient(),n=[1,1];n[i.index]=0,ki(t,e,{type:"box",ignoreSize:!!n})}const XA=YA;var fd=Ft,yl=["width","height"],ml=["x","y"],ZA=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.newlineDisabled=!0,e._currentIndex=0,e}return t.prototype.init=function(){r.prototype.init.call(this),this.group.add(this._containerGroup=new fd),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new fd)},t.prototype.resetInner=function(){r.prototype.resetInner.call(this),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},t.prototype.renderInner=function(e,i,n,a,o,s,l){var u=this;r.prototype.renderInner.call(this,e,i,n,a,o,s,l);var f=this._controllerGroup,h=i.get("pageIconSize",!0),v=N(h)?h:[h,h];d("pagePrev",0);var c=i.getModel("pageTextStyle");f.add(new zt({name:"pageText",style:{text:"xx/xx",fill:c.getTextColor(),font:c.getFont(),verticalAlign:"middle",align:"center"},silent:!0})),d("pageNext",1);function d(y,p){var g=y+"DataIndex",m=Ju(i.get("pageIcons",!0)[i.getOrient().name][p],{onclick:pt(u._pageGo,u,g,i,a)},{x:-v[0]/2,y:-v[1]/2,width:v[0],height:v[1]});m.name=y,f.add(m)}},t.prototype.layoutInner=function(e,i,n,a,o,s){var l=this.getSelectorGroup(),u=e.getOrient().index,f=yl[u],h=ml[u],v=yl[1-u],c=ml[1-u];o&&An("horizontal",l,e.get("selectorItemGap",!0));var d=e.get("selectorButtonGap",!0),y=l.getBoundingRect(),p=[-y.x,-y.y],g=Q(n);o&&(g[f]=n[f]-y[f]-d);var m=this._layoutContentAndController(e,a,g,u,f,v,c,h);if(o){if(s==="end")p[u]+=m[f]+d;else{var _=y[f]+d;p[u]-=_,m[h]-=_}m[f]+=y[f]+d,p[1-u]+=m[c]+m[v]/2-y[v]/2,m[v]=Math.max(m[v],y[v]),m[c]=Math.min(m[c],y[c]+p[1-u]),l.x=p[0],l.y=p[1],l.markRedraw()}return m},t.prototype._layoutContentAndController=function(e,i,n,a,o,s,l,u){var f=this.getContentGroup(),h=this._containerGroup,v=this._controllerGroup;An(e.get("orient"),f,e.get("itemGap"),a?n.width:null,a?null:n.height),An("horizontal",v,e.get("pageButtonItemGap",!0));var c=f.getBoundingRect(),d=v.getBoundingRect(),y=this._showController=c[o]>n[o],p=[-c.x,-c.y];i||(p[a]=f[u]);var g=[0,0],m=[-d.x,-d.y],_=Z(e.get("pageButtonGap",!0),e.get("itemGap",!0));if(y){var S=e.get("pageButtonPosition",!0);S==="end"?m[a]+=n[o]-d[o]:g[a]+=d[o]+_}m[1-a]+=c[s]/2-d[s]/2,f.setPosition(p),h.setPosition(g),v.setPosition(m);var b={x:0,y:0};if(b[o]=y?n[o]:c[o],b[s]=Math.max(c[s],d[s]),b[l]=Math.min(0,d[l]+m[1-a]),h.__rectSize=n[o],y){var w={x:0,y:0};w[o]=Math.max(n[o]-d[o]-_,0),w[s]=b[s],h.setClipPath(new Ct({shape:w})),h.__rectSize=w[o]}else v.eachChild(function(A){A.attr({invisible:!0,silent:!0})});var x=this._getPageInfo(e);return x.pageIndex!=null&&hr(f,{x:x.contentPosition[0],y:x.contentPosition[1]},y?e:null),this._updatePageInfoView(e,x),b},t.prototype._pageGo=function(e,i,n){var a=this._getPageInfo(i)[e];a!=null&&n.dispatchAction({type:"legendScroll",scrollDataIndex:a,legendId:i.id})},t.prototype._updatePageInfoView=function(e,i){var n=this._controllerGroup;M(["pagePrev","pageNext"],function(f){var h=f+"DataIndex",v=i[h]!=null,c=n.childOfName(f);c&&(c.setStyle("fill",v?e.get("pageIconColor",!0):e.get("pageIconInactiveColor",!0)),c.cursor=v?"pointer":"default")});var a=n.childOfName("pageText"),o=e.get("pageFormatter"),s=i.pageIndex,l=s!=null?s+1:0,u=i.pageCount;a&&o&&a.setStyle("text",z(o)?o.replace("{current}",l==null?"":l+"").replace("{total}",u==null?"":u+""):o({current:l,total:u}))},t.prototype._getPageInfo=function(e){var i=e.get("scrollDataIndex",!0),n=this.getContentGroup(),a=this._containerGroup.__rectSize,o=e.getOrient().index,s=yl[o],l=ml[o],u=this._findTargetItemIndex(i),f=n.children(),h=f[u],v=f.length,c=v?1:0,d={contentPosition:[n.x,n.y],pageCount:c,pageIndex:c-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!h)return d;var y=S(h);d.contentPosition[o]=-y.s;for(var p=u+1,g=y,m=y,_=null;p<=v;++p)_=S(f[p]),(!_&&m.e>g.s+a||_&&!b(_,g.s))&&(m.i>g.i?g=m:g=_,g&&(d.pageNextDataIndex==null&&(d.pageNextDataIndex=g.i),++d.pageCount)),m=_;for(var p=u-1,g=y,m=y,_=null;p>=-1;--p)_=S(f[p]),(!_||!b(m,_.s))&&g.i<m.i&&(m=g,d.pagePrevDataIndex==null&&(d.pagePrevDataIndex=g.i),++d.pageCount,++d.pageIndex),g=_;return d;function S(w){if(w){var x=w.getBoundingRect(),A=x[l]+w[l];return{s:A,e:A+x[s],i:w.__legendDataIndex}}}function b(w,x){return w.e>=x&&w.s<=x+a}},t.prototype._findTargetItemIndex=function(e){if(!this._showController)return 0;var i,n=this.getContentGroup(),a;return n.eachChild(function(o,s){var l=o.__legendDataIndex;a==null&&l!=null&&(a=s),l===e&&(i=s)}),i??a},t.type="legend.scroll",t}(um);const qA=ZA;function KA(r){r.registerAction("legendScroll","legendscroll",function(t,e){var i=t.scrollDataIndex;i!=null&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(n){n.setScrollDataIndex(i)})})}function QA(r){vr(fm),r.registerComponentModel(XA),r.registerComponentView(qA),KA(r)}function JA(r){vr(fm),vr(QA)}const jA=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function tL(r){function t(i){return(...n)=>{if(!r.value)throw new Error("ECharts is not initialized yet.");return r.value[i].apply(r.value,n)}}function e(){const i=Object.create(null);return jA.forEach(n=>{i[n]=t(n)}),i}return e()}function eL(r,t,e){Wa([e,r,t],([i,n,a],o,s)=>{let l=null;if(i&&n&&a){const{offsetWidth:u,offsetHeight:f}=i,h=a===!0?{}:a,{throttle:v=100,onResize:c}=h;let d=!1;const y=()=>{n.resize(),c==null||c()},p=v?yf(y,v):y;l=new ResizeObserver(()=>{!d&&(d=!0,i.offsetWidth===u&&i.offsetHeight===f)||p()}),l.observe(i)}s(()=>{l&&(l.disconnect(),l=null)})})}const rL={autoresize:[Boolean,Object]},iL=/^on[^a-z]/,hm=r=>iL.test(r);function nL(r){const t={};for(const e in r)hm(e)||(t[e]=r[e]);return t}function ao(r,t){const e=mm(r)?zr(r):r;return e&&typeof e=="object"&&"value"in e?e.value||t:e||t}const aL="ecLoadingOptions";function oL(r,t,e){const i=Va(aL,{}),n=Be(()=>({...ao(i,{}),...e==null?void 0:e.value}));cd(()=>{const a=r.value;a&&(t.value?a.showLoading(n.value):a.hideLoading())})}const sL={loading:Boolean,loadingOptions:Object};let cn=null;const cm="x-vue-echarts";function lL(){if(cn!=null)return cn;if(typeof HTMLElement>"u"||typeof customElements>"u")return cn=!1;try{new Function("tag","class EChartsElement extends HTMLElement{__dispose=null;disconnectedCallback(){this.__dispose&&(this.__dispose(),this.__dispose=null)}}customElements.get(tag)==null&&customElements.define(tag,EChartsElement);")(cm)}catch{return cn=!1}return cn=!0}document.head.appendChild(document.createElement("style")).textContent=`x-vue-echarts{display:block;width:100%;height:100%;min-width:0}
`;const uL=lL(),fL="ecTheme",hL="ecInitOptions",cL="ecUpdateOptions",hd=/(^&?~?!?)native:/;var vm=dm({name:"echarts",props:{option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean,...rL,...sL},emits:{},inheritAttrs:!1,setup(r,{attrs:t}){const e=rs(),i=rs(),n=rs(),a=Va(fL,null),o=Va(hL,null),s=Va(cL,null),{autoresize:l,manualUpdate:u,loading:f,loadingOptions:h}=pm(r),v=Be(()=>n.value||r.option||null),c=Be(()=>r.theme||ao(a,{})),d=Be(()=>r.initOptions||ao(o,{})),y=Be(()=>r.updateOptions||ao(s,{})),p=Be(()=>nL(t)),g={},m=gm().proxy.$listeners,_={};m?Object.keys(m).forEach(C=>{hd.test(C)?g[C.replace(hd,"$1")]=m[C]:_[C]=m[C]}):Object.keys(t).filter(C=>hm(C)).forEach(C=>{let D=C.charAt(2).toLowerCase()+C.slice(3);if(D.indexOf("native:")===0){const T=`on${D.charAt(7).toUpperCase()}${D.slice(8)}`;g[T]=t[C];return}D.substring(D.length-4)==="Once"&&(D=`~${D.substring(0,D.length-4)}`),_[D]=t[C]});function S(C){if(!e.value)return;const D=i.value=ET(e.value,c.value,d.value);r.group&&(D.group=r.group),Object.keys(_).forEach(P=>{let I=_[P];if(!I)return;let R=P.toLowerCase();R.charAt(0)==="~"&&(R=R.substring(1),I.__once__=!0);let E=D;if(R.indexOf("zr:")===0&&(E=D.getZr(),R=R.substring(3)),I.__once__){delete I.__once__;const G=I;I=(...B)=>{G(...B),E.off(R,I)}}E.on(R,I)});function T(){D&&!D.isDisposed()&&D.resize()}function L(){const P=C||v.value;P&&D.setOption(P,y.value)}l.value?_m(()=>{T(),L()}):L()}function b(C,D){r.manualUpdate&&(n.value=C),i.value?i.value.setOption(C,D||{}):S(C)}function w(){i.value&&(i.value.dispose(),i.value=void 0)}let x=null;Wa(u,C=>{typeof x=="function"&&(x(),x=null),C||(x=Wa(()=>r.option,(D,T)=>{D&&(i.value?i.value.setOption(D,{notMerge:D!==T,...y.value}):S())},{deep:!0}))},{immediate:!0}),Wa([c,d],()=>{w(),S()},{deep:!0}),cd(()=>{r.group&&i.value&&(i.value.group=r.group)});const A=tL(i);return oL(i,f,h),eL(i,l,e),vd(()=>{S()}),dd(()=>{uL&&e.value?e.value.__dispose=w:w()}),{chart:i,root:e,setOption:b,nonEventAttrs:p,nativeListeners:g,...A}},render(){const r={...this.nonEventAttrs,...this.nativeListeners};return r.ref="root",r.class=r.class?["echarts"].concat(r.class):"echarts",ym(cm,r)}});const vL={class:"home-container"},dL={class:"header-content"},pL={class:"user-info"},gL={class:"el-dropdown-link"},yL={class:"container"},mL={class:"top-section"},_L={class:"list-container"},SL=["onClick"],wL={class:"item-info"},bL={class:"item-date"},xL={class:"progress-container"},TL={key:0,class:"progress-empty"},CL={key:1,class:"progress-loading"},DL={key:2,class:"progress-chart"},ML={class:"bottom-section"},AL={class:"system-content",style:{background:"rgba(255,255,255,0.85)","border-radius":"8px",padding:"24px 0"}},LL={class:"system-icon"},IL={class:"system-name"},PL={class:"system-desc"},RL={class:"system-action"},EL={components:{VChart:vm}},kL=Object.assign(EL,{__name:"Home",setup(r){vr([wD,jD,FA,OA,JA,hA]);const t=Sm(),e=Am(),i=Be(()=>e.userInfo),n=Be(()=>e.isLoggedIn),a=na([]),o=na([]),s=na(!1),l=na(null),u=[{key:"0",name:"创建简历",index:0},{key:"1",name:"知识学习",index:1},{key:"2",name:"HR模拟面试",index:2},{key:"3",name:"技术模拟面试",index:3},{key:"4",name:"正式面试",index:4}];let f=null;const h=Be(()=>{if(!l.value)return d();const T=v(),L=c(T);return{title:{text:"求职进度",left:"center",textStyle:{fontSize:16,fontWeight:"bold",color:"#333"}},tooltip:{trigger:"item",formatter:function(P){const I=u[P.dataIndex];let R="未完成";return P.dataIndex<T?R="已完成":P.dataIndex===T&&(R="当前进度"),`${I.name}<br/>状态: ${R}`}},grid:{left:"10%",right:"10%",top:"20%",bottom:"15%"},xAxis:{type:"category",data:u.map(P=>P.name),axisLabel:{interval:0,rotate:0,fontSize:12}},yAxis:{type:"value",show:!1,min:0,max:100},series:[{name:"进度",type:"line",data:L,lineStyle:{width:4},symbol:"circle",symbolSize:12,itemStyle:{color:function(P){return P.dataIndex<T?"#67C23A":P.dataIndex===T?"#E6A23C":"#C0C4CC"}},lineStyle:{color:"#409EFF",width:3}}]}}),v=()=>{if(!l.value||!l.value.currentStage)return 0;const T=u.find(L=>L.key===l.value.currentStage);return T?T.index:0},c=T=>u.map((L,P)=>P<T?100:P===T?50:0),d=()=>({title:{text:"求职进度",left:"center",textStyle:{fontSize:16,fontWeight:"bold",color:"#333"}},grid:{left:"10%",right:"10%",top:"20%",bottom:"15%"},xAxis:{type:"category",data:u.map(T=>T.name),axisLabel:{interval:0,rotate:0,fontSize:12}},yAxis:{type:"value",show:!1,min:0,max:100},series:[{name:"进度",type:"line",data:[0,0,0,0,0],lineStyle:{width:4,color:"#C0C4CC"},symbol:"circle",symbolSize:12,itemStyle:{color:"#C0C4CC"}}]}),y=async()=>{if(e.isLoggedIn)try{await Kn({url:"/sso/validateToken",method:"get"}),console.log("Token验证成功，继续保持登录状态")}catch(T){console.error("Token验证失败:",T),T.message&&(T.message.includes("token")||T.message.includes("登录"))&&(e.logout(),Ui.warning("登录已过期，请重新登录"),a.value=[])}},p=T=>{const L={墨仔简历系统:"Document",墨仔面试系统:"Headset",面试喵:"Notebook",默认:"Monitor"};return L[T]||L.默认},g=async()=>{try{const T=await Pm();a.value=T.data||[]}catch(T){console.error("获取待办列表失败:",T),T.response&&T.response.status===401&&(a.value=[])}},m=async()=>{if(e.isLoggedIn)try{s.value=!0;const T=await km();l.value=T.data||null}catch(T){console.error("获取用户进度失败:",T),l.value=null}finally{s.value=!1}},_=async()=>{try{const T=await Em();o.value=T.data||[]}catch(T){console.error("获取系统列表失败:",T)}},S=T=>{kf.confirm("您确定已完成相关知识的学习了吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then(()=>{b(T)}).catch(()=>{})},b=async T=>{try{await Rm(T.id),Ui.success(`已完成待办事项：${T.content}`),await g()}catch(L){console.error("完成待办事项失败:",L),Ui.error("操作失败，请重试")}},w=T=>{window.open(T.url,"_blank")},x=T=>{T.url&&window.open(T.url,"_blank")},A=()=>{kf.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{localStorage.setItem("logoutFlag","1"),await Lm(),e.logout(),Ui.success("已退出登录")}catch(T){console.error("退出登录失败:",T),e.logout(),Ui.warning("退出登录失败，但已清除本地登录状态")}}).catch(()=>{})},C=()=>{t.push({path:"/login",query:{clientId:"sso_client"}})},D=()=>{t.push("/profile")};return vd(()=>{y(),f=setInterval(y,5*60*1e3),e.isLoggedIn?(g(),m()):a.value=[],_()}),dd(()=>{f&&(clearInterval(f),f=null)}),(T,L)=>{const P=kt("el-avatar"),I=kt("el-icon"),R=kt("el-dropdown-item"),E=kt("el-dropdown-menu"),G=kt("el-dropdown"),B=kt("el-button"),F=kt("el-header"),$=kt("el-empty"),it=kt("el-tag"),J=kt("el-card"),st=kt("el-col"),ht=kt("el-skeleton"),ct=kt("el-row"),qt=kt("el-main"),Re=kt("el-footer");return Kt(),pr("div",vL,[j(F,{class:"header"},{default:nt(()=>[K("div",dL,[L[3]||(L[3]=K("div",{class:"logo"},[K("i",{class:"el-icon-platform-eleme logo-icon"}),Qt(" 笔墨屋统一认证服务平台 ")],-1)),K("div",pL,[n.value?(Kt(),ni(G,{key:0,trigger:"click"},{dropdown:nt(()=>[j(E,null,{default:nt(()=>[j(R,{onClick:D},{default:nt(()=>[j(I,null,{default:nt(()=>[j(zr(Of))]),_:1}),L[0]||(L[0]=Qt(" 个人中心 "))]),_:1,__:[0]}),j(R,{onClick:A},{default:nt(()=>[j(I,null,{default:nt(()=>[j(zr(wm))]),_:1}),L[1]||(L[1]=Qt(" 退出登录 "))]),_:1,__:[1]})]),_:1})]),default:nt(()=>[K("span",gL,[j(P,{size:32,class:"user-avatar"},{default:nt(()=>[Qt(gr(i.value.nickname?i.value.nickname.substring(0,1):"U"),1)]),_:1}),Qt(" 欢迎，"+gr(i.value.nickname)+" ",1),j(I,{class:"el-icon--right"},{default:nt(()=>[j(zr(bm))]),_:1})])]),_:1})):(Kt(),ni(B,{key:1,type:"primary",onClick:C,class:"login-button"},{default:nt(()=>[j(I,null,{default:nt(()=>[j(zr(Of))]),_:1}),L[2]||(L[2]=Qt(" 登录 "))]),_:1,__:[2]}))])])]),_:1}),j(qt,{class:"main"},{default:nt(()=>[L[9]||(L[9]=K("div",{class:"banner"},[K("div",{class:"banner-content"},[K("h1",{class:"banner-title"},"欢迎使用笔墨屋统一认证服务平台"),K("p",{class:"banner-desc"},"一站式登录，畅享所有系统服务")])],-1)),K("div",yL,[K("div",mL,[j(ct,{gutter:20},{default:nt(()=>[j(st,{span:12},{default:nt(()=>[j(J,{class:"card",shadow:"hover"},{header:nt(()=>L[4]||(L[4]=[K("div",{class:"card-header"},[K("span",null,[K("i",{class:"el-icon-document-checked card-icon"}),Qt(" 待办事项")])],-1)])),default:nt(()=>[K("div",_L,[a.value.length===0?(Kt(),ni($,{key:0,description:"暂无待办事项"})):(Kt(!0),pr(Rf,{key:1},Ef(a.value,vt=>(Kt(),pr("div",{class:"list-item",key:vt.id},[K("div",{class:"item-title",onClick:Dt=>x(vt)},[j(I,null,{default:nt(()=>[j(zr(xm))]),_:1}),Qt(" "+gr(vt.content),1)],8,SL),K("div",wL,[K("span",bL,gr(vt.date||"今天"),1),j(it,{size:"small",effect:"plain",type:"info"},{default:nt(()=>[Qt(gr(vt.status||"待处理"),1)]),_:2},1024),vt.todoType===1?(Kt(),ni(B,{key:0,size:"small",type:"success",onClick:Dt=>S(vt)},{default:nt(()=>L[5]||(L[5]=[Qt("完成")])),_:2,__:[5]},1032,["onClick"])):Tm("",!0)])]))),128))])]),_:1})]),_:1}),j(st,{span:12},{default:nt(()=>[j(J,{class:"card",shadow:"hover"},{header:nt(()=>L[6]||(L[6]=[K("div",{class:"card-header"},[K("span",null,[K("i",{class:"el-icon-trend-charts card-icon"}),Qt(" 我的进度")])],-1)])),default:nt(()=>[K("div",xL,[n.value?s.value?(Kt(),pr("div",CL,[j(ht,{rows:3,animated:""})])):(Kt(),pr("div",DL,[j(zr(vm),{class:"chart",option:h.value,autoresize:""},null,8,["option"])])):(Kt(),pr("div",TL,[j($,{description:"请先登录查看进度"})]))])]),_:1})]),_:1})]),_:1})]),K("div",ML,[L[8]||(L[8]=K("div",{class:"section-header"},[K("h2",{class:"section-title"},[K("i",{class:"el-icon-menu"}),Qt(" 系统导航")]),K("div",{class:"section-divider"})],-1)),j(ct,{gutter:30},{default:nt(()=>[(Kt(!0),pr(Rf,null,Ef(o.value,vt=>(Kt(),ni(st,{xs:24,sm:12,md:8,lg:6,key:vt.id},{default:nt(()=>[j(J,{class:Cm(["system-card",{"resume-system":vt.name==="墨仔简历系统","interview-system":vt.name==="墨仔面试系统"}]),shadow:"hover",onClick:Dt=>w(vt),style:Dm(vt.bgImage?`background-image: url(${vt.bgImage}); background-size: cover; background-position: center;`:"")},{default:nt(()=>[K("div",AL,[K("div",LL,[j(I,{size:32},{default:nt(()=>[(Kt(),ni(Mm(p(vt.name))))]),_:2},1024)]),K("div",IL,gr(vt.name),1),K("div",PL,gr(vt.description),1),K("div",RL,[j(B,{type:"primary",size:"small",round:""},{default:nt(()=>L[7]||(L[7]=[Qt("进入系统")])),_:1,__:[7]})])])]),_:2},1032,["onClick","style","class"])]),_:2},1024))),128))]),_:1})])])]),_:1,__:[9]}),j(Re,{class:"footer"},{default:nt(()=>L[10]||(L[10]=[K("div",{class:"footer-content"},[K("div",{class:"footer-copyright"}," © 2025 笔墨屋统一认证服务平台 - 版权所有 ")],-1)])),_:1,__:[10]})])}}}),FL=Im(kL,[["__scopeId","data-v-a9301c3b"]]);export{FL as default};
