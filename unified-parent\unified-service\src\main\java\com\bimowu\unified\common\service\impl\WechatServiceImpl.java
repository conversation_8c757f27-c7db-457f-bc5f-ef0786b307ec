package com.bimowu.unified.common.service.impl;

import com.bimowu.unified.common.service.WechatService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
public class WechatServiceImpl implements WechatService {
    @Value("${wechat.token}")
    private String token;

    @Value("${wechat.appid}")
    private String appid;

    @Value("${wechat.appsecret}")
    private String appsecret;
    
    // 微信接口URL
    private static final String ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
    private static final String CREATE_QRCODE_URL = "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=%s";
    private static final String SHOW_QRCODE_URL = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=%s";
    
    // 缓存access_token
    private String accessToken;
    private long accessTokenExpireTime;
    
    // RestTemplate用于发送HTTP请求
    private final RestTemplate restTemplate = new RestTemplate();
    
    // ObjectMapper用于JSON处理
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 模拟实现，实际项目中应该从微信服务器获取
    private static final Map<String, String> MOCK_ACCESS_TOKEN = new ConcurrentHashMap<>();
    
    // 是否使用模拟数据（开发环境使用）
    @Value("${wechat.mock}")
    private boolean useMock;

    /**
     * 初始化模拟数据
     */
    @PostConstruct
    public void init() {
        if (useMock) {
            log.info("初始化微信模拟数据");
            MOCK_ACCESS_TOKEN.put("access_token", "mock_access_token_" + System.currentTimeMillis());
            MOCK_ACCESS_TOKEN.put("expires_in", "7200");
        }
    }

    @Override
    public boolean checkSignature(String signature, String timestamp, String nonce) {
        // 1. 将token、timestamp、nonce三个参数进行字典序排序
        String[] arr = new String[]{token, timestamp, nonce};
        Arrays.sort(arr);

        // 2. 将三个参数字符串拼接成一个字符串进行sha1加密
        StringBuilder content = new StringBuilder();
        for (String str : arr) {
            content.append(str);
        }

        String tmpStr = null;
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            byte[] digest = md.digest(content.toString().getBytes());
            tmpStr = byteToStr(digest);
        } catch (Exception e) {
            log.error("验证签名异常", e);
        }

        // 3. 将sha1加密后的字符串与signature对比，标识该请求来源于微信
        return tmpStr != null && tmpStr.equals(signature.toUpperCase());
    }

    @Override
    public Map<String, String> parseXml(HttpServletRequest request) throws Exception {
        Map<String, String> map = new HashMap<>();

        SAXReader reader = new SAXReader();
        Document document = reader.read(request.getInputStream());
        Element root = document.getRootElement();
        List<Element> elementList = root.elements();

        for (Element e : elementList) {
            map.put(e.getName(), e.getText());
        }

        return map;
    }

    @Override
    public String buildTextResponseXml(String toUserName, String fromUserName, String content) {
        return "<xml>" +
                "<ToUserName><![CDATA[" + toUserName + "]]></ToUserName>" +
                "<FromUserName><![CDATA[" + fromUserName + "]]></FromUserName>" +
                "<CreateTime>" + System.currentTimeMillis() / 1000 + "</CreateTime>" +
                "<MsgType><![CDATA[text]]></MsgType>" +
                "<Content><![CDATA[" + content + "]]></Content>" +
                "</xml>";
    }

    @Override
    public Map<String, String> getUserInfo(String openid) {
        log.info("获取微信用户信息, openid={}", openid);
        Map<String, String> userInfo = new HashMap<>();
        
        try {
            if (!useMock) {
                // 获取access_token
                String accessToken = getAccessToken();
                
                // 构建请求URL
                String userInfoUrl = String.format("https://api.weixin.qq.com/cgi-bin/user/info?access_token=%s&openid=%s&lang=zh_CN", 
                                                 accessToken, openid);
                log.info("调用微信获取用户信息接口: {}", userInfoUrl);
                
                // 发送请求
                ResponseEntity<Map> response = restTemplate.getForEntity(userInfoUrl, Map.class);
                Map<String, Object> responseBody = response.getBody();
                
                if (responseBody == null) {
                    log.error("微信获取用户信息接口返回为空");
                    throw new RuntimeException("微信获取用户信息接口返回为空");
                }
                
                log.info("微信获取用户信息接口返回: {}", objectMapper.writeValueAsString(responseBody));
                
                // 检查是否有错误
                if (responseBody.containsKey("errcode") && !responseBody.get("errcode").equals(0)) {
                    log.error("微信获取用户信息失败: errcode={}, errmsg={}", 
                        responseBody.get("errcode"), responseBody.get("errmsg"));
                    throw new RuntimeException("微信获取用户信息失败: " + responseBody.get("errmsg"));
                }
                
                // 提取用户信息
                userInfo.put("subscribe", String.valueOf(responseBody.getOrDefault("subscribe", "0")));
                userInfo.put("openid", (String) responseBody.getOrDefault("openid", ""));
                userInfo.put("nickname", (String) responseBody.getOrDefault("nickname", ""));
                userInfo.put("sex", String.valueOf(responseBody.getOrDefault("sex", "0")));
                userInfo.put("language", (String) responseBody.getOrDefault("language", ""));
                userInfo.put("city", (String) responseBody.getOrDefault("city", ""));
                userInfo.put("province", (String) responseBody.getOrDefault("province", ""));
                userInfo.put("country", (String) responseBody.getOrDefault("country", ""));
                userInfo.put("headimgurl", (String) responseBody.getOrDefault("headimgurl", ""));
                userInfo.put("subscribe_time", String.valueOf(responseBody.getOrDefault("subscribe_time", "")));
                userInfo.put("unionid", (String) responseBody.getOrDefault("unionid", ""));
                userInfo.put("remark", (String) responseBody.getOrDefault("remark", ""));
                userInfo.put("groupid", String.valueOf(responseBody.getOrDefault("groupid", "0")));
                userInfo.put("subscribe_scene", (String) responseBody.getOrDefault("subscribe_scene", ""));
                
                log.info("获取微信用户信息成功: nickname={}, openid={}", userInfo.get("nickname"), userInfo.get("openid"));
            } else {
                // 模拟数据
                userInfo.put("subscribe", "1");
                userInfo.put("openid", openid);
                userInfo.put("nickname", "墨仔用户");
                userInfo.put("sex", "1");
                userInfo.put("language", "zh_CN");
                userInfo.put("city", "深圳");
                userInfo.put("province", "广东");
                userInfo.put("country", "中国");
                userInfo.put("headimgurl", "http://thirdwx.qlogo.cn/mmopen/sample");
                userInfo.put("subscribe_time", String.valueOf(System.currentTimeMillis() / 1000));
                userInfo.put("unionid", "");
                userInfo.put("remark", "");
                userInfo.put("groupid", "0");
                userInfo.put("subscribe_scene", "ADD_SCENE_QR_CODE");
                
                log.info("使用模拟微信用户信息: nickname={}, openid={}", userInfo.get("nickname"), openid);
            }
        } catch (Exception e) {
            log.error("获取微信用户信息异常", e);
            if (useMock) {
                // 使用模拟数据
                userInfo.put("nickname", "墨仔用户");
                userInfo.put("openid", openid);
                userInfo.put("headimgurl", "");
                userInfo.put("subscribe", "1");
                log.info("异常情况下使用模拟微信用户信息");
            } else {
                throw new RuntimeException("获取微信用户信息异常", e);
            }
        }
        
        return userInfo;
    }
    
    @Override
    public Map<String, String> createTempQrcode(String sceneStr, int expireSeconds) {
        log.info("创建临时二维码, sceneStr={}, expireSeconds={}", sceneStr, expireSeconds);
        Map<String, String> result = new HashMap<>();
        
        try {
            // 获取access_token
            String accessToken = getAccessToken();
            
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("expire_seconds", expireSeconds);
            params.put("action_name", "QR_STR_SCENE");
            
            Map<String, Object> actionInfo = new HashMap<>();
            Map<String, Object> scene = new HashMap<>();
            scene.put("scene_str", sceneStr);
            actionInfo.put("scene", scene);
            params.put("action_info", actionInfo);
            
            // 发送请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
            
            Map<String, Object> responseBody;
            if (!useMock) {
                // 实际调用微信接口
                ResponseEntity<Map> response = restTemplate.postForEntity(
                    String.format(CREATE_QRCODE_URL, accessToken),
                    requestEntity,
                    Map.class
                );
                responseBody = response.getBody();
                log.info("微信创建二维码接口返回: {}", objectMapper.writeValueAsString(responseBody));
                
                // 检查是否有错误
                if (responseBody != null && responseBody.containsKey("errcode") && !responseBody.get("errcode").equals(0)) {
                    log.error("微信创建二维码失败: {}", responseBody.get("errmsg"));
                    throw new RuntimeException("微信创建二维码失败: " + responseBody.get("errmsg"));
                }
            } else {
                // 模拟微信接口返回
                responseBody = new HashMap<>();
                responseBody.put("ticket", "mock_ticket_" + sceneStr);
                responseBody.put("expire_seconds", expireSeconds);
                responseBody.put("url", "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=mock_ticket_" + sceneStr);
            }
            
            // 提取结果
            String ticket = (String) responseBody.get("ticket");
            // URL编码ticket
            String encodedTicket = java.net.URLEncoder.encode(ticket, "UTF-8");
            String url = String.format(SHOW_QRCODE_URL, encodedTicket);
            
            result.put("ticket", ticket);
            result.put("url", url);
            
            log.info("创建临时二维码成功, ticket={}, url={}", ticket, url);
        } catch (Exception e) {
            log.error("创建临时二维码异常", e);
            if (useMock) {
                // 模拟返回
                result.put("ticket", "mock_error_ticket");
                result.put("url", "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=mock_error_ticket");
            } else {
                throw new RuntimeException("创建临时二维码异常", e);
            }
        }
        
        return result;
    }
    
    /**
     * 获取微信access_token
     */
    private String getAccessToken() {
        // 检查缓存的access_token是否有效
        long now = System.currentTimeMillis();
        if (accessToken != null && now < accessTokenExpireTime) {
            return accessToken;
        }
        
        try {
            Map<String, Object> responseBody;
            if (!useMock) {
                // 实际调用微信接口
                log.info("开始获取微信access_token, appid={}", appid);
                String requestUrl = String.format(ACCESS_TOKEN_URL, appid, appsecret);
                
                ResponseEntity<Map> response = restTemplate.getForEntity(requestUrl, Map.class);
                responseBody = response.getBody();
                
                if (responseBody == null) {
                    log.error("微信获取access_token接口返回为空");
                    throw new RuntimeException("微信获取access_token接口返回为空");
                }
                
                log.info("微信获取access_token接口返回: {}", objectMapper.writeValueAsString(responseBody));
                
                // 检查是否有错误
                if (responseBody.containsKey("errcode") && !responseBody.get("errcode").equals(0)) {
                    Integer errcode = (Integer) responseBody.get("errcode");
                    String errmsg = (String) responseBody.get("errmsg");
                    log.error("微信获取access_token失败: errcode={}, errmsg={}", errcode, errmsg);
                    throw new RuntimeException("微信获取access_token失败: " + errmsg);
                }
            } else {
                // 模拟微信接口返回
                log.info("使用模拟access_token");
                responseBody = new HashMap<>();
                // 如果模拟数据未初始化，则初始化
                if (!MOCK_ACCESS_TOKEN.containsKey("access_token")) {
                    MOCK_ACCESS_TOKEN.put("access_token", "mock_access_token_" + System.currentTimeMillis());
                    MOCK_ACCESS_TOKEN.put("expires_in", "7200");
                }
                responseBody.put("access_token", MOCK_ACCESS_TOKEN.get("access_token"));
                responseBody.put("expires_in", Integer.parseInt(MOCK_ACCESS_TOKEN.get("expires_in")));
            }
            
            // 提取结果
            accessToken = (String) responseBody.get("access_token");
            int expiresIn = (int) responseBody.get("expires_in");
            accessTokenExpireTime = now + (expiresIn - 200) * 1000L; // 提前200秒过期
            
            log.info("获取access_token成功, 有效期至: {}", new java.util.Date(accessTokenExpireTime));
            return accessToken;
        } catch (Exception e) {
            log.error("获取access_token异常", e);
            if (useMock) {
                // 如果模拟数据未初始化，则初始化
                if (!MOCK_ACCESS_TOKEN.containsKey("access_token")) {
                    MOCK_ACCESS_TOKEN.put("access_token", "mock_access_token_" + System.currentTimeMillis());
                    MOCK_ACCESS_TOKEN.put("expires_in", "7200");
                }
                return MOCK_ACCESS_TOKEN.get("access_token"); // 模拟返回
            } else {
                throw new RuntimeException("获取access_token异常", e);
            }
        }
    }

    /**
     * 将字节数组转换为十六进制字符串
     */
    private String byteToStr(byte[] byteArray) {
        StringBuilder strDigest = new StringBuilder();
        for (byte b : byteArray) {
            strDigest.append(byteToHexStr(b));
        }
        return strDigest.toString();
    }

    /**
     * 将字节转换为十六进制字符串
     */
    private String byteToHexStr(byte mByte) {
        char[] Digit = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        char[] tempArr = new char[2];
        tempArr[0] = Digit[(mByte >>> 4) & 0X0F];
        tempArr[1] = Digit[mByte & 0X0F];
        return new String(tempArr);
    }
}
