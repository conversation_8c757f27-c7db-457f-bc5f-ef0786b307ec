package com.bimowu.unified.common.dao;

import com.bimowu.unified.common.model.SysSystem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * SysSystem数据库操作接口类
 */
@Repository
public interface SysSystemDao {

    /**
     * 查询（根据主键ID查询）
     */
    SysSystem selectByPrimaryKey(@Param("id") Long id);

    /**
     * 查询所有启用的系统，按排序字段排序
     */
    List<SysSystem> selectAllEnabled();

    /**
     * 查询所有系统
     */
    List<SysSystem> selectAll();

    /**
     * 删除（根据主键ID删除）
     */
    int deleteByPrimaryKey(@Param("id") Long id);

    /**
     * 添加
     */
    int insert(SysSystem record);

    /**
     * 修改（匹配有值的字段）
     */
    int updateByPrimaryKeySelective(SysSystem record);
} 