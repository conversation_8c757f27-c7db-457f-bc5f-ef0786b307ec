<template>
  <div class="demo-container">
    <div class="demo-header">
      <h1>我的进度 - 演示页面</h1>
      <p>展示不同阶段的求职进度效果</p>
    </div>

    <div class="demo-content">
      <el-row :gutter="20">
        <!-- 阶段选择器 -->
        <el-col :span="24">
          <el-card class="stage-selector">
            <template #header>
              <div class="card-header">
                <span>选择当前进度阶段</span>
              </div>
            </template>
            <div class="stage-buttons">
              <el-button 
                v-for="stage in progressStages" 
                :key="stage.key"
                :type="currentStage === stage.key ? 'primary' : 'default'"
                @click="setCurrentStage(stage.key)"
                class="stage-btn"
              >
                {{ stage.name }}
              </el-button>
            </div>
          </el-card>
        </el-col>

        <!-- 进度图表展示 -->
        <el-col :span="24">
          <el-card class="progress-card">
            <template #header>
              <div class="card-header">
                <span>进度图表展示</span>
              </div>
            </template>
            <div class="chart-container">
              <ProgressChart 
                :user-progress="mockProgress" 
                :loading="false"
              />
            </div>
          </el-card>
        </el-col>

        <!-- 进度说明 -->
        <el-col :span="24">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <span>进度说明</span>
              </div>
            </template>
            <div class="progress-info">
              <div class="info-item">
                <h4>当前阶段：{{ getCurrentStageName() }}</h4>
                <p>{{ getCurrentStageDescription() }}</p>
              </div>
              
              <div class="color-legend">
                <h4>颜色说明：</h4>
                <div class="legend-items">
                  <div class="legend-item">
                    <span class="color-dot green"></span>
                    <span>绿色 - 已完成的阶段</span>
                  </div>
                  <div class="legend-item">
                    <span class="color-dot yellow"></span>
                    <span>黄色 - 当前进行的阶段</span>
                  </div>
                  <div class="legend-item">
                    <span class="color-dot gray"></span>
                    <span>灰色 - 未开始的阶段</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import ProgressChart from '../components/ProgressChart.vue'

// 进度阶段定义
const progressStages = [
  { key: '0', name: '创建简历', index: 0, description: '完善个人简历信息，准备求职材料' },
  { key: '1', name: '知识学习', index: 1, description: '学习相关技能和知识，提升专业能力' },
  { key: '2', name: 'HR模拟面试', index: 2, description: '进行HR面试模拟，熟悉面试流程' },
  { key: '3', name: '技术模拟面试', index: 3, description: '进行技术面试模拟，检验专业技能' },
  { key: '4', name: '正式面试', index: 4, description: '参加正式面试，展示个人能力' }
]

// 当前选择的阶段
const currentStage = ref('1') // 默认选择知识学习阶段

// 模拟的进度数据
const mockProgress = computed(() => ({
  currentStage: currentStage.value,
  userId: 1,
  createTime: new Date(),
  updateTime: new Date()
}))

// 设置当前阶段
const setCurrentStage = (stageKey) => {
  currentStage.value = stageKey
}

// 获取当前阶段名称
const getCurrentStageName = () => {
  const stage = progressStages.find(s => s.key === currentStage.value)
  return stage ? stage.name : '未知阶段'
}

// 获取当前阶段描述
const getCurrentStageDescription = () => {
  const stage = progressStages.find(s => s.key === currentStage.value)
  return stage ? stage.description : '暂无描述'
}
</script>

<style scoped>
.demo-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.demo-header p {
  color: #666;
  font-size: 16px;
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
}

.stage-selector {
  margin-bottom: 20px;
}

.stage-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
}

.stage-btn {
  min-width: 120px;
}

.progress-card {
  margin-bottom: 20px;
}

.chart-container {
  height: 400px;
  padding: 20px;
}

.info-card .progress-info {
  padding: 20px;
}

.info-item {
  margin-bottom: 30px;
}

.info-item h4 {
  color: #333;
  margin-bottom: 10px;
}

.info-item p {
  color: #666;
  line-height: 1.6;
}

.color-legend h4 {
  color: #333;
  margin-bottom: 15px;
}

.legend-items {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.color-dot.green {
  background-color: #67C23A;
}

.color-dot.yellow {
  background-color: #E6A23C;
}

.color-dot.gray {
  background-color: #C0C4CC;
}

.card-header {
  font-weight: 600;
  color: #333;
}

@media (max-width: 768px) {
  .demo-container {
    padding: 10px;
  }
  
  .stage-buttons {
    justify-content: center;
  }
  
  .stage-btn {
    min-width: 100px;
    margin-bottom: 10px;
  }
  
  .legend-items {
    flex-direction: column;
    gap: 15px;
  }
  
  .chart-container {
    height: 300px;
    padding: 10px;
  }
}
</style>
