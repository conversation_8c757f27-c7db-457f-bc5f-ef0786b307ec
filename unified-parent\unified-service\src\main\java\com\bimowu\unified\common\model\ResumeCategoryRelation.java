package com.bimowu.unified.common.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 简历与职位类别关联实体类
 */
@Data
public class ResumeCategoryRelation {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 简历ID
     */
    private Long resumeId;
    
    /**
     * 职位类别ID
     */
    private Long catId;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 删除标识(0-未删除,1-已删除)
     */
    private Integer isDelete;
}
