import{y as G,z as J,r as u,b as B,A as K,E as i,B as O,k as m,l as F,C as M,D as l,F as t,G as s,H as Q,I as _,J as W}from"./vendor-477e13dd.js";import{u as X,v as P,l as Y,a as Z,g as ee}from"./index-23439adc.js";import{_ as te}from"./index-9768669e.js";const oe={class:"login-container"},ae={class:"login-content"},re={class:"login-right"},le={class:"login-form-container"},se={key:0,class:"captcha-row"},ne=["src"],ie={class:"form-actions"},ce={class:"captcha-row"},de=["src"],ue={class:"verify-code-container"},me={class:"login-actions"},pe={__name:"Login",setup(fe){const C=G(),R=J(),h=X(),k=u(null),I=u(null),p=u(!1),S=u("password"),b=u(!1),w=u(!1),a=B({username:"",password:"",clientId:"sso_client",redirectUrl:"",captcha:"",captchaUrl:""}),r=B({mobile:"",verifyCode:"",captcha:"",clientId:"sso_client",redirectUrl:"",captchaUrl:""}),x=u(!1),y=u("获取验证码");let U=null,f=60;const $={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能小于6位",trigger:"blur"}],captcha:[{required:!1,message:"请输入验证码",trigger:"blur"}]},L={mobile:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],verifyCode:[{required:!0,message:"请输入验证码",trigger:"blur"},{len:6,message:"验证码长度应为6位",trigger:"blur"}],captcha:[{required:!0,message:"请输入图片验证码",trigger:"blur"}]},V=()=>{r.captchaUrl=`/unified/sso/captcha?t=${new Date().getTime()}`};K(()=>{const o=localStorage.getItem("rememberedUsername"),e=localStorage.getItem("rememberedPassword");o&&e&&(a.username=o,a.password=e,w.value=!0);const c=R.query.clientId;c&&(a.clientId=c,r.clientId=c);const d=R.query.redirectUrl;d&&(a.redirectUrl=d,r.redirectUrl=d),b.value=!0,g(),V()});const g=()=>{a.captchaUrl=`/unified/sso/captcha?t=${new Date().getTime()}`,i({message:"验证码已刷新",type:"info",duration:1e3})},D=async()=>{if(k.value)try{if(await k.value.validate(),b.value){if(!a.captcha){i.warning("请输入验证码");return}try{if((await P(a.captcha)).code!==0){i.error("验证码错误"),g();return}}catch(e){console.error("验证码验证失败:",e),i.error("验证码验证失败"),g();return}}p.value=!0;const o=await Y(a);h.setToken(o.data.token),h.setUserInfo(o.data.userInfo),w.value?(localStorage.setItem("rememberedUsername",a.username),localStorage.setItem("rememberedPassword",a.password)):(localStorage.removeItem("rememberedUsername"),localStorage.removeItem("rememberedPassword")),i.success("登录成功"),o.data.redirectUrl?window.location.href=o.data.redirectUrl:a.redirectUrl?window.location.href=a.redirectUrl:C.push("/home")}catch(o){console.error("登录失败:",o),b.value=!0,g()}finally{p.value=!1}},N=async()=>{if(I.value)try{await I.value.validate(),p.value=!0;const o=await Z(r);h.setToken(o.data.token),h.setUserInfo(o.data.userInfo),i.success("登录成功"),o.data.redirectUrl?window.location.href=o.data.redirectUrl:r.redirectUrl?window.location.href=r.redirectUrl:C.push("/home")}catch(o){console.error("登录失败:",o)}finally{p.value=!1}},E=async()=>{if(!r.mobile){i.warning("请输入手机号");return}if(!/^1[3-9]\d{9}$/.test(r.mobile)){i.warning("请输入正确的手机号");return}if(!r.captcha){i.warning("请输入图片验证码");return}try{if((await P(r.captcha)).code!==0){i.error("图片验证码错误"),V();return}}catch{i.error("图片验证码校验失败"),V();return}try{await ee(r.mobile),j(),i.success("验证码已发送")}catch(o){console.error("发送验证码失败:",o)}},j=()=>{x.value=!0,f=60,y.value=`${f}秒后重新获取`,U=setInterval(()=>{f--,y.value=`${f}秒后重新获取`,f<=0&&(clearInterval(U),x.value=!1,y.value="获取验证码")},1e3)},H=()=>{C.push("/home")};return O(()=>{U&&clearInterval(U)}),(o,e)=>{const c=m("el-input"),d=m("el-form-item"),z=m("el-checkbox"),v=m("el-button"),T=m("el-form"),q=m("el-tab-pane"),A=m("el-tabs");return F(),M("div",oe,[e[15]||(e[15]=l("div",{class:"header-bar"},[l("div",{class:"logo-title"},"笔墨屋统一认证服务平台")],-1)),l("div",ae,[e[14]||(e[14]=l("div",{class:"login-left"},[l("div",{class:"welcome-text"},[l("h1",null,"欢迎使用笔墨屋统一认证服务平台"),l("p",null,"一站式登录，畅享所有系统服务")])],-1)),l("div",re,[l("div",le,[e[13]||(e[13]=l("h3",{class:"login-title"},"账号登录",-1)),t(A,{modelValue:S.value,"onUpdate:modelValue":e[7]||(e[7]=n=>S.value=n),class:"login-tabs"},{default:s(()=>[t(q,{label:"账号密码登录",name:"password"},{default:s(()=>[t(T,{ref_key:"passwordFormRef",ref:k,model:a,rules:$,class:"login-form"},{default:s(()=>[t(d,{prop:"username"},{default:s(()=>[t(c,{modelValue:a.username,"onUpdate:modelValue":e[0]||(e[0]=n=>a.username=n),placeholder:"请输入手机号/邮箱/用户名","prefix-icon":"el-icon-user"},null,8,["modelValue"])]),_:1}),t(d,{prop:"password"},{default:s(()=>[t(c,{modelValue:a.password,"onUpdate:modelValue":e[1]||(e[1]=n=>a.password=n),type:"password",placeholder:"请输入密码","prefix-icon":"el-icon-lock","show-password":""},null,8,["modelValue"])]),_:1}),b.value?(F(),M("div",se,[t(c,{modelValue:a.captcha,"onUpdate:modelValue":e[2]||(e[2]=n=>a.captcha=n),placeholder:"请输入验证码",class:"captcha-input"},null,8,["modelValue"]),l("div",{class:"captcha-img",onClick:g,title:"点击刷新验证码"},[l("img",{src:a.captchaUrl,alt:"验证码",style:{width:"100%",height:"100%","object-fit":"cover"}},null,8,ne)])])):Q("",!0),l("div",ie,[t(z,{modelValue:w.value,"onUpdate:modelValue":e[3]||(e[3]=n=>w.value=n)},{default:s(()=>e[8]||(e[8]=[_("记住密码")])),_:1,__:[8]},8,["modelValue"]),e[9]||(e[9]=l("a",{href:"#",class:"forgot-link"},"忘记密码?",-1))]),t(d,null,{default:s(()=>[t(v,{type:"primary",loading:p.value,class:"login-button",onClick:D},{default:s(()=>e[10]||(e[10]=[_("登录")])),_:1,__:[10]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1}),t(q,{label:"手机号登录",name:"mobile"},{default:s(()=>[t(T,{ref_key:"mobileFormRef",ref:I,model:r,rules:L,class:"login-form"},{default:s(()=>[t(d,{prop:"mobile"},{default:s(()=>[t(c,{modelValue:r.mobile,"onUpdate:modelValue":e[4]||(e[4]=n=>r.mobile=n),placeholder:"请输入手机号","prefix-icon":"el-icon-mobile"},null,8,["modelValue"])]),_:1}),l("div",ce,[t(c,{modelValue:r.captcha,"onUpdate:modelValue":e[5]||(e[5]=n=>r.captcha=n),placeholder:"请输入图片验证码",class:"captcha-input"},null,8,["modelValue"]),l("div",{class:"captcha-img",onClick:V,title:"点击刷新验证码"},[l("img",{src:r.captchaUrl,alt:"图片验证码",style:{width:"100%",height:"100%","object-fit":"cover"}},null,8,de)])]),t(d,{prop:"verifyCode"},{default:s(()=>[l("div",ue,[t(c,{modelValue:r.verifyCode,"onUpdate:modelValue":e[6]||(e[6]=n=>r.verifyCode=n),placeholder:"请输入验证码","prefix-icon":"el-icon-message"},null,8,["modelValue"]),t(v,{type:"primary",class:"verify-code-button",disabled:x.value,onClick:E},{default:s(()=>[_(W(y.value),1)]),_:1},8,["disabled"])])]),_:1}),t(d,null,{default:s(()=>[t(v,{type:"primary",loading:p.value,class:"login-button",onClick:N},{default:s(()=>e[11]||(e[11]=[_("登录")])),_:1,__:[11]},8,["loading"])]),_:1})]),_:1},8,["model"])]),_:1})]),_:1},8,["modelValue"]),l("div",me,[t(v,{type:"text",class:"register-link"}),t(v,{type:"text",class:"direct-link",onClick:H},{default:s(()=>e[12]||(e[12]=[_("直接进入首页")])),_:1,__:[12]})])])])])])}}},he=te(pe,[["__scopeId","data-v-94a7c7a2"]]);export{he as default};
