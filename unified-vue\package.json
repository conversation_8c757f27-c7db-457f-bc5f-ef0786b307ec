{"name": "unified-vue", "version": "0.0.1", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "axios": "^1.5.0", "element-plus": "^2.3.14", "normalize.css": "^8.0.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "sass": "^1.68.0", "vite": "^4.4.9"}}