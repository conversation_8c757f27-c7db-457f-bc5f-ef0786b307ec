package com.bimowu.unified.login.service;

import com.bimowu.unified.base.BaseResponse;
import javax.servlet.http.HttpServletResponse;

/**
 * 验证码服务接口
 */
public interface VerifyCodeService {

    /**
     * 发送短信验证码
     * @param mobile 手机号
     * @return 发送结果
     */
    BaseResponse<String> sendSmsCode(String mobile);
    
    /**
     * 验证短信验证码
     * @param mobile 手机号
     * @param code 验证码
     * @return 验证结果
     */
    boolean verifySmsCode(String mobile, String code);
    
    /**
     * 生成图形验证码
     * @param response HttpServletResponse对象，用于输出图片
     * @param sessionId 会话ID
     */
    void generateCaptcha(HttpServletResponse response, String sessionId);
    
    /**
     * 验证图形验证码
     * @param sessionId 会话ID
     * @param code 用户输入的验证码
     * @return 验证结果
     */
    boolean verifyCaptcha(String sessionId, String code);
} 