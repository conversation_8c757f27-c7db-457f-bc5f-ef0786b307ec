
DROP TABLE IF EXISTS `sys_todo`;
CREATE TABLE `sys_todo`  (
 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '待办ID',
 `user_id` bigint(20) NOT NULL COMMENT '用户ID',
 `resume_id` bigint(20) NULL DEFAULT NULL COMMENT '简历ID',
 `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标题',
 `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '内容',
 `url` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '链接URL',
 `todo_type` int(11) NULL DEFAULT NULL COMMENT '待办类型(0：创建简历；1：知识学习；2：hr面试；3：技术面试；4:正式面试)',
 `status` tinyint(4) NULL DEFAULT 0 COMMENT '状态：0-未完成，1-已完成',
 `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 PRIMARY KEY (`id`) USING BTREE,
 INDEX `idx_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 52 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '待办表' ROW_FORMAT = Compact;

-- 新闻表
CREATE TABLE IF NOT EXISTS sys_news (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '新闻ID',
  title VARCHAR(100) NOT NULL COMMENT '标题',
  content TEXT COMMENT '内容',
  author VARCHAR(50) COMMENT '作者',
  publish_time DATETIME COMMENT '发布时间',
  status TINYINT DEFAULT 0 COMMENT '状态：0-未发布，1-已发布',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT='新闻表';

-- 系统表
DROP TABLE IF EXISTS `sys_system`;
CREATE TABLE `sys_system`  (
   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '系统ID',
   `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '系统名称',
   `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '系统描述',
   `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '系统图标',
   `url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '系统URL',
   `sort` int(11) NULL DEFAULT 0 COMMENT '排序',
   `status` tinyint(4) NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
   `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `bg_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '系统卡片背景图片',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统表' ROW_FORMAT = DYNAMIC;

-- 微信授权记录表
CREATE TABLE IF NOT EXISTS sys_wechat_auth (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
  user_id BIGINT COMMENT '用户ID',
  scene_str VARCHAR(100) NOT NULL COMMENT '场景值字符串',
  qrcode_url VARCHAR(500) COMMENT '二维码URL',
  openid VARCHAR(50) COMMENT '微信OpenID',
  unionid VARCHAR(50) COMMENT '微信UnionID',
  nickname VARCHAR(50) COMMENT '微信昵称',
  subscribe TINYINT DEFAULT 0 COMMENT '是否关注：0-未关注，1-已关注',
  status TINYINT DEFAULT 0 COMMENT '状态：0-未绑定，1-已绑定',
  expire_time DATETIME COMMENT '过期时间',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY idx_user_id (user_id),
  KEY idx_scene_str (scene_str),
  KEY idx_openid (openid)
) COMMENT='微信授权记录表';

-- 如果表存在则删除
DROP TABLE IF EXISTS `sso_client`;

-- 创建SSO客户端表
CREATE TABLE `sso_client` (
  `client_id` varchar(64) NOT NULL COMMENT '客户端ID',
  `client_secret` varchar(128) NOT NULL COMMENT '客户端密钥',
  `app_name` varchar(64) NOT NULL COMMENT '应用名称',
  `app_url` varchar(255) DEFAULT NULL COMMENT '应用URL',
  `redirect_url` varchar(255) DEFAULT NULL COMMENT '回调URL',
  `logout_url` varchar(255) DEFAULT NULL COMMENT '退出登录回调URL',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `status` int(1) DEFAULT '0' COMMENT '状态：0-正常，1-禁用',
  PRIMARY KEY (`client_id`),
  UNIQUE KEY `uk_app_name` (`app_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SSO客户端应用表';

DROP TABLE IF EXISTS `resume_progress`;
CREATE TABLE `resume_progress`  (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `resume_id` bigint(20) NULL DEFAULT NULL COMMENT '简历ID',
    `current_stage` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '当前进行到的阶段（0：创建简历；1：知识学习；2：hr面试；3：技术面试；4:正式面试）',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
    `is_deleted` tinyint(4) NULL DEFAULT 0 COMMENT '删除标识（0：未删除；1：已删除）',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_user_id`(`user_id`) USING BTREE,
    INDEX `idx_current_stage`(`current_stage`) USING BTREE,
    INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '简历进度管理表' ROW_FORMAT = Compact;

DROP TABLE IF EXISTS `resume`;
CREATE TABLE `resume`  (
                           `resume_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '简历ID',
                           `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                           `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '简历标题',
                           `status` tinyint(4) NULL DEFAULT 0 COMMENT '简历状态(0-待审核,1-已通过)',
                           `is_default` tinyint(4) NULL DEFAULT 0 COMMENT '是否默认简历(0-否,1-是)',
                           `template_id` bigint(20) NULL DEFAULT NULL COMMENT '简历模板ID',
                           `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                           `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                           `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                           `is_delete` tinyint(4) NULL DEFAULT 0 COMMENT '删除标识(0-未删除,1-已删除)',
                           `audit_opinion` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核意见',
                           PRIMARY KEY (`resume_id`) USING BTREE,
                           INDEX `idx_resume_user`(`user_id`) USING BTREE,
                           INDEX `idx_resume_status`(`status`) USING BTREE,
                           INDEX `idx_resume_default`(`is_default`) USING BTREE,
                           INDEX `idx_resume_template`(`template_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '简历主表' ROW_FORMAT = DYNAMIC;




DROP TABLE IF EXISTS `resume_campus`;
CREATE TABLE `resume_campus` (
                                 `cam_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                 `resume_id` bigint NOT NULL COMMENT '简历ID',
                                 `campus_experience` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '校园经历',
                                 `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                 `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                 `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
                                 PRIMARY KEY (`cam_id`) USING BTREE,
                                 KEY `idx_campus_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='校园经历表';


DROP TABLE IF EXISTS `resume_category`;

CREATE TABLE `resume_category` (
                                   `cat_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                   `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职位类别名称',
                                   `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职位类别编码',
                                   `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '职位类别描述',
                                   `sort_order` int DEFAULT '0' COMMENT '排序号',
                                   `status` tinyint DEFAULT '1' COMMENT '状态(0-禁用,1-启用)',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                   `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
                                   PRIMARY KEY (`cat_id`) USING BTREE,
                                   UNIQUE KEY `uk_category_code` (`code`) USING BTREE,
                                   KEY `idx_category_name` (`name`) USING BTREE,
                                   KEY `idx_category_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='职位类别表';


DROP TABLE IF EXISTS `resume_category_relation`;
CREATE TABLE `resume_category_relation` (
                                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                            `user_id` bigint NOT NULL COMMENT '用户ID',
                                            `resume_id` bigint NOT NULL COMMENT '简历ID',
                                            `cat_id` bigint NOT NULL COMMENT '职位类别ID',
                                            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                            `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
                                            PRIMARY KEY (`id`),
                                            KEY `idx_resume_id` (`resume_id`),
                                            KEY `idx_cat_id` (`cat_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='简历与职位类别关联表';


DROP TABLE IF EXISTS `resume_certificate`;
CREATE TABLE `resume_certificate` (
                                      `cer_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `resume_id` bigint NOT NULL COMMENT '简历ID',
                                      `certificate_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '证书奖项',
                                      `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                      `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                      `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
                                      PRIMARY KEY (`cer_id`) USING BTREE,
                                      KEY `idx_cert_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='证书奖项表';


DROP TABLE IF EXISTS `resume_educational`;
CREATE TABLE `resume_educational` (
                                      `edu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `resume_id` bigint NOT NULL COMMENT '简历ID',
                                      `school` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '学校',
                                      `major` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '专业',
                                      `education` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '学历',
                                      `time_period` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '时间段',
                                      `main_courses` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '主修课程',
                                      `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                      `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                      `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
                                      PRIMARY KEY (`edu_id`) USING BTREE,
                                      KEY `idx_edu_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='教育经历表';


DROP TABLE IF EXISTS `resume_evaluate`;
CREATE TABLE `resume_evaluate` (
                                   `eva_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                   `resume_id` bigint NOT NULL COMMENT '简历ID',
                                   `self_evaluation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '自我评价',
                                   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                   `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                   `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
                                   PRIMARY KEY (`eva_id`) USING BTREE,
                                   KEY `idx_evaluate_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='自我评价表';

DROP TABLE IF EXISTS `resume_hr_questions`;
CREATE TABLE `resume_hr_questions` (
                                       `que_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                       `question_type` enum('HR问题') NOT NULL COMMENT '问题类型',
                                       `question` text NOT NULL COMMENT '题目内容',
                                       `answer` text COMMENT '题目答案',
                                       `create_at` varchar(50) NOT NULL COMMENT '创建人',
                                       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       PRIMARY KEY (`que_id`),
                                       KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='HR问题表';


DROP TABLE IF EXISTS `resume_information`;
CREATE TABLE `resume_information` (
                                      `infor_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `resume_id` bigint NOT NULL COMMENT '简历ID',
                                      `avatar` text COMMENT '头像',
                                      `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
                                      `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '性别',
                                      `birth_date` date DEFAULT NULL COMMENT '出生日期',
                                      `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系电话',
                                      `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系邮箱',
                                      `hometown` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '籍贯',
                                      `nationality` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '民族',
                                      `age` int DEFAULT NULL COMMENT '年龄',
                                      `job_objective` varchar(20) DEFAULT NULL COMMENT '求职意向',
                                      `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                      `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                      `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
                                      PRIMARY KEY (`infor_id`) USING BTREE,
                                      KEY `idx_info_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='基本信息表';

DROP TABLE IF EXISTS `resume_interest`;
CREATE TABLE `resume_interest` (
                                   `int_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                   `resume_id` bigint NOT NULL COMMENT '简历ID',
                                   `interest` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '兴趣爱好',
                                   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                   `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                   `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
                                   PRIMARY KEY (`int_id`) USING BTREE,
                                   KEY `idx_interest_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='兴趣爱好表';

DROP TABLE IF EXISTS `resume_practice`;
CREATE TABLE `resume_practice` (
                                   `pra_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                   `resume_id` bigint NOT NULL COMMENT '简历ID',
                                   `time_period` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '时间段',
                                   `project_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
                                   `role` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '担任角色',
                                   `project_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '项目描述',
                                   `project_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '项目地址',
                                   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                   `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                   `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
                                   PRIMARY KEY (`pra_id`) USING BTREE,
                                   KEY `idx_practice_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='练手项目表';

DROP TABLE IF EXISTS `resume_project`;
CREATE TABLE `resume_project` (
                                  `pro_id` bigint NOT NULL AUTO_INCREMENT,
                                  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
                                  `cat_id` bigint NOT NULL COMMENT '职位类别id',
                                  `content` text COMMENT '项目内容（字符串形式，用于兼容）',
                                  `create_at` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                                  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`pro_id`) USING BTREE,
                                  KEY `idx_project_name` (`name`) USING BTREE,
                                  KEY `idx_project_role` (`cat_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='项目表';


DROP TABLE IF EXISTS `resume_project_content`;
CREATE TABLE `resume_project_content` (
                                          `con_id` bigint NOT NULL AUTO_INCREMENT,
                                          `project_id` bigint NOT NULL COMMENT '项目ID',
                                          `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容文本',
                                          `content_order` int NOT NULL COMMENT '内容顺序',
                                          `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                          PRIMARY KEY (`con_id`) USING BTREE,
                                          KEY `project_id` (`project_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='项目内容表';

DROP TABLE IF EXISTS `resume_project_content_segment`;
CREATE TABLE `resume_project_content_segment` (
                                                  `seg_id` bigint NOT NULL AUTO_INCREMENT,
                                                  `content_id` bigint NOT NULL COMMENT '项目内容ID',
                                                  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '段落文本',
                                                  `is_bold` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否加粗：0-否，1-是',
                                                  `segment_order` int NOT NULL COMMENT '段落顺序',
                                                  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                  PRIMARY KEY (`seg_id`) USING BTREE,
                                                  KEY `content_id` (`content_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='项目内容段落表';



DROP TABLE IF EXISTS `resume_project_experience`;
CREATE TABLE `resume_project_experience` (
                                             `exp_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                             `resume_id` bigint NOT NULL COMMENT '简历ID',
                                             `project_id` bigint DEFAULT NULL COMMENT '项目ID',
                                             `time_period` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '时间段',
                                             `position_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职位类别',
                                             `project_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
                                             `role` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '担任角色',
                                             `project_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '项目描述',
                                             `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                             `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                             `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                             `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
                                             PRIMARY KEY (`exp_id`) USING BTREE,
                                             KEY `idx_exp_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='项目经验表';

DROP TABLE IF EXISTS `resume_project_question`;
CREATE TABLE `resume_project_question` (
                                           `que_id` bigint NOT NULL AUTO_INCREMENT,
                                           `project_id` bigint NOT NULL COMMENT '项目ID',
                                           `con_id` bigint NOT NULL,
                                           `question` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题内容',
                                           `answer` text NOT NULL COMMENT '答案',
                                           `question_order` int NOT NULL COMMENT '问题顺序',
                                           `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           PRIMARY KEY (`que_id`) USING BTREE,
                                           KEY `project_id` (`project_id`) USING BTREE,
                                           KEY `con_id` (`con_id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='项目问题表';


DROP TABLE IF EXISTS `resume_skill`;
CREATE TABLE `resume_skill` (
                                `ski_id` bigint NOT NULL AUTO_INCREMENT,
                                `name` varchar(50) NOT NULL COMMENT '技能名称',
                                `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                PRIMARY KEY (`ski_id`),
                                KEY `idx_skill_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='技能表';


DROP TABLE IF EXISTS `resume_skill_segment`;
CREATE TABLE `resume_skill_segment` (
                                        `seg_id` bigint NOT NULL AUTO_INCREMENT,
                                        `skill_id` bigint NOT NULL COMMENT '技能ID',
                                        `seg_name` varchar(50) NOT NULL COMMENT '技能点名称',
                                        `proficiency` varchar(20) NOT NULL COMMENT '熟练度（一般、良好、熟练、擅长、精通）',
                                        `text` text NOT NULL COMMENT '段落文本',
                                        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        PRIMARY KEY (`seg_id`),
                                        KEY `skill_id` (`skill_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='技能段落表';



DROP TABLE IF EXISTS `resume_talent`;
CREATE TABLE `resume_talent` (
                                 `tal_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                 `resume_id` bigint NOT NULL COMMENT '简历ID',
                                 `skill_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '技能名称',
                                 `proficiency` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '熟练度',
                                 `skill_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '技能描述',
                                 `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                 `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                 `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
                                 PRIMARY KEY (`tal_id`) USING BTREE,
                                 KEY `idx_talent_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='技能特长表';

DROP TABLE IF EXISTS `resume_work`;

CREATE TABLE `resume_work` (
                               `res_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                               `resume_id` bigint NOT NULL COMMENT '简历ID',
                               `company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公司',
                               `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职位',
                               `time_period` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '时间段',
                               `work_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '工作描述',
                               `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                               `create_at` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                               `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
                               PRIMARY KEY (`res_id`) USING BTREE,
                               KEY `idx_work_resume` (`resume_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='工作经验表';


-- 创建AI使用次数记录表
DROP TABLE IF EXISTS `resume_ai_usage`;
CREATE TABLE `resume_ai_usage` (
                                   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                   `user_id` bigint NOT NULL COMMENT '用户ID',
                                   `remaining_count` int NOT NULL DEFAULT '10' COMMENT '剩余可用次数',
                                   `total_used` int NOT NULL DEFAULT '0' COMMENT '已使用总次数',
                                   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                   `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE KEY `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='用户AI使用次数记录表';
-- 知识管理表
DROP TABLE IF EXISTS `resume_knowledge`;
CREATE TABLE `resume_knowledge` (
                                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                    `position_type` tinyint NOT NULL COMMENT '职位类别（1：开发；2：技术支持）',
                                    `knowledge_catalog` varchar(10) NOT NULL COMMENT '知识目录（1.1，1.2等）',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    `is_deleted` tinyint DEFAULT '0' COMMENT '删除标识（0：未删除；1：已删除）',
                                    PRIMARY KEY (`id`),
                                    KEY `idx_position_type` (`position_type`),
                                    KEY `idx_knowledge_catalog` (`knowledge_catalog`),
                                    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='简历知识管理表';




DROP TABLE IF EXISTS `interview`;
CREATE TABLE `interview` (
                             `id` varchar(64) NOT NULL COMMENT '面试ID',
                             `user_id` int NOT NULL COMMENT '用户id',
                             `type` varchar(20) NOT NULL DEFAULT 'mock' COMMENT '面试类型(mock:模拟面试,formal:正式面试)',
                             `candidate_name` varchar(50) DEFAULT NULL COMMENT '应聘者姓名',
                             `company` varchar(100) DEFAULT NULL COMMENT '面试公司(正式面试)',
                             `position` varchar(50) DEFAULT NULL COMMENT '面试职位',
                             `stage` varchar(20) DEFAULT NULL COMMENT '面试阶段(hr/tech)',
                             `experience` varchar(20) DEFAULT NULL COMMENT '工作经验(fresh/1-3/3-5/5+)',
                             `status` tinyint DEFAULT '0' COMMENT '面试状态(0:进行中,1:等待结果,2:已完成)',
                             `result` tinyint DEFAULT NULL COMMENT '面试结果(0:未通过,1:通过,NULL:未评定)',
                             `overall_score` int DEFAULT NULL COMMENT '总体评分(0-100)',
                             `feedback` varchar(200) DEFAULT NULL COMMENT '面试反馈',
                             `strengths` varchar(200) DEFAULT NULL COMMENT '优势表现',
                             `improvements` varchar(200) DEFAULT NULL COMMENT '改进建议',
                             `video_url` varchar(255) DEFAULT NULL COMMENT '面试视频文件URL',
                             `interview_time` datetime DEFAULT NULL COMMENT '面试时间',
                             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                             `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                             PRIMARY KEY (`id`),
                             KEY `idx_type` (`type`),
                             KEY `idx_stage_position` (`stage`,`position`),
                             KEY `idx_create_time` (`create_time`),
                             KEY `idx_interview_time` (`interview_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='面试信息表';



DROP TABLE IF EXISTS `interview_questions`;
CREATE TABLE `interview_questions` (
                                       `interview_id` varchar(64) NOT NULL COMMENT '面试ID',
                                       `questions` varchar(10000) DEFAULT NULL COMMENT '问题列表',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                       PRIMARY KEY (`interview_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='面试问题表';


DROP TABLE IF EXISTS `interview_speech_chapter`;
CREATE TABLE `interview_speech_chapter` (
                                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
                                            `interview_id` varchar(64) NOT NULL COMMENT '面试ID',
                                            `start_time` int NOT NULL COMMENT '开始时间(秒)',
                                            `end_time` int NOT NULL COMMENT '结束时间(秒)',
                                            `chapter_title` varchar(255) NOT NULL COMMENT '章节标题',
                                            `chapter_summary` varchar(1000) DEFAULT NULL COMMENT '章节总结',
                                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                            PRIMARY KEY (`id`),
                                            KEY `idx_interview_id` (`interview_id`),
                                            KEY `idx_time_range` (`start_time`,`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='面试语音转文字章节段落记录表';



DROP TABLE IF EXISTS `interview_speech_record`;
CREATE TABLE `interview_speech_record` (
                                           `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
                                           `interview_id` varchar(64) NOT NULL COMMENT '面试ID',
                                           `speaker_id` int NOT NULL DEFAULT '0' COMMENT '发言人ID',
                                           `start_time` int NOT NULL COMMENT '开始时间(秒)',
                                           `end_time` int NOT NULL COMMENT '结束时间(秒)',
                                           `content` text COMMENT '发言内容',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                           PRIMARY KEY (`id`),
                                           KEY `idx_interview_id` (`interview_id`),
                                           KEY `idx_speaker_id` (`speaker_id`),
                                           KEY `idx_time_range` (`start_time`,`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='面试语音转文字记录表';



DROP TABLE IF EXISTS `interview_transcription`;
CREATE TABLE `interview_transcription` (
                                           `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
                                           `interview_id` varchar(64) NOT NULL COMMENT '面试ID',
                                           `question_index` int NOT NULL COMMENT '问题索引',
                                           `question` text COMMENT '问题内容',
                                           `transcription` text COMMENT '转写的回答内容',
                                           `audio_url` varchar(255) DEFAULT NULL COMMENT '音频文件URL',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                           PRIMARY KEY (`id`),
                                           UNIQUE KEY `uk_interview_question` (`interview_id`,`question_index`),
                                           KEY `idx_interview_id` (`interview_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1929748171097956355 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='面试转写记录表';






-- 初始化数据

INSERT INTO `sys_system` VALUES (5, '墨仔简历系统', 'AI优化，多模板选择', 'Document', 'https://tt.bimowo.com/resume-ai/index.html', 0, 1, '2025-07-10 11:34:40', '2025-07-16 16:25:23', 'https://bmw-pic.oss-cn-beijing.aliyuncs.com/9f5a4df605a31ee40dc6410e907a08c0.jpeg');
INSERT INTO `sys_system` VALUES (6, '墨仔面试系统', '专业的面试评估与训练平台', 'Document', 'https://tt.bimowo.com/interview-ai/index.html', 1, 1, '2025-07-10 11:34:40', '2025-07-16 16:25:25', 'https://bmw-pic.oss-cn-beijing.aliyuncs.com/6f9abecbef27a4cb63a8d90b52f71780.jpeg');