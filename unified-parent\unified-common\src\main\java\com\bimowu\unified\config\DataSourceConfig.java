package com.bimowu.unified.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * 多数据源配置
 */
@Configuration
public class DataSourceConfig {

    // 主数据源配置
    @Value("${spring.datasource.primary.url}")
    private String primaryUrl;
    
    @Value("${spring.datasource.primary.username}")
    private String primaryUsername;
    
    @Value("${spring.datasource.primary.password}")
    private String primaryPassword;
    
    @Value("${spring.datasource.primary.driver-class-name}")
    private String primaryDriverClassName;
    
    // 登录数据源配置
    @Value("${spring.datasource.login.url}")
    private String loginUrl;
    
    @Value("${spring.datasource.login.username}")
    private String loginUsername;
    
    @Value("${spring.datasource.login.password}")
    private String loginPassword;
    
    @Value("${spring.datasource.login.driver-class-name}")
    private String loginDriverClassName;

    /**
     * 主数据源 - 用于除登录外的所有接口
     */
    @Bean(name = "primaryDataSource")
    @Primary
    public DataSource primaryDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setUrl(primaryUrl);
        dataSource.setUsername(primaryUsername);
        dataSource.setPassword(primaryPassword);
        dataSource.setDriverClassName(primaryDriverClassName);
        // 配置连接池属性
        dataSource.setInitialSize(5);
        dataSource.setMinIdle(5);
        dataSource.setMaxActive(20);
        dataSource.setMaxWait(60000);
        dataSource.setTimeBetweenEvictionRunsMillis(60000);
        dataSource.setMinEvictableIdleTimeMillis(300000);
        dataSource.setValidationQuery("SELECT 1");
        dataSource.setTestWhileIdle(true);
        dataSource.setTestOnBorrow(false);
        dataSource.setTestOnReturn(false);
        dataSource.setPoolPreparedStatements(true);
        dataSource.setMaxPoolPreparedStatementPerConnectionSize(20);
        return dataSource;
    }

    /**
     * 登录数据源 - 仅用于登录接口
     */
    @Bean(name = "loginDataSource")
    public DataSource loginDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setUrl(loginUrl);
        dataSource.setUsername(loginUsername);
        dataSource.setPassword(loginPassword);
        dataSource.setDriverClassName(loginDriverClassName);
        // 配置连接池属性
        dataSource.setInitialSize(5);
        dataSource.setMinIdle(5);
        dataSource.setMaxActive(20);
        dataSource.setMaxWait(60000);
        dataSource.setTimeBetweenEvictionRunsMillis(60000);
        dataSource.setMinEvictableIdleTimeMillis(300000);
        dataSource.setValidationQuery("SELECT 1");
        dataSource.setTestWhileIdle(true);
        dataSource.setTestOnBorrow(false);
        dataSource.setTestOnReturn(false);
        dataSource.setPoolPreparedStatements(true);
        dataSource.setMaxPoolPreparedStatementPerConnectionSize(20);
        return dataSource;
    }

    /**
     * 主数据源SqlSessionFactory
     */
    @Bean(name = "primarySqlSessionFactory")
    @Primary
    public SqlSessionFactory primarySqlSessionFactory(@Qualifier("primaryDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:com/bimowu/unified/common/mappers/*.xml"));
        return bean.getObject();
    }

    /**
     * 登录数据源SqlSessionFactory
     */
    @Bean(name = "loginSqlSessionFactory")
    public SqlSessionFactory loginSqlSessionFactory(@Qualifier("loginDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:com/bimowu/unified/login/mappers/*.xml"));
        return bean.getObject();
    }

    /**
     * 主数据源事务管理器
     */
    @Bean(name = "primaryTransactionManager")
    @Primary
    public DataSourceTransactionManager primaryTransactionManager(@Qualifier("primaryDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * 登录数据源事务管理器
     */
    @Bean(name = "loginTransactionManager")
    public DataSourceTransactionManager loginTransactionManager(@Qualifier("loginDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * 主数据源SqlSessionTemplate
     */
    @Bean(name = "primarySqlSessionTemplate")
    @Primary
    public SqlSessionTemplate primarySqlSessionTemplate(@Qualifier("primarySqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    /**
     * 登录数据源SqlSessionTemplate
     */
    @Bean(name = "loginSqlSessionTemplate")
    public SqlSessionTemplate loginSqlSessionTemplate(@Qualifier("loginSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
} 