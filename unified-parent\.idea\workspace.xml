<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="082d57d6-1f39-459d-8173-0937df7c8dc9" name="Changes" comment="进度折线图" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
      <option value="repository" />
    </option>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="userSettingsFile" value="D:\apache-maven-3.2.3-bin\apache-maven-3.2.3-bin\apache-maven-3.2.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2xaJeULIWtkfm92pSbcGsKCA5PV" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.unified-common [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.unified-parent [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.unified-parent [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.unified-web [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.UnifiedWebApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/unified-platform/unified-parent&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;redis&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\unified-platform\unified-parent\unified-service\src\main\java\com\bimowu\unified\common\mappers" />
      <recent name="C:\Users\<USER>\Desktop\unified-platform\unified-parent\unified-web\src\main\java\com\bimowu\unified" />
      <recent name="C:\Users\<USER>\Desktop\unified-platform\unified-parent\unified-common\src\main\java\com\bimowu\unified\utils" />
      <recent name="C:\Users\<USER>\Desktop\unified-platform\unified-parent\unified-service\src\main\resources\sql" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\unified-platform\unified-parent\unified-service\src\main\resources\sql" />
      <recent name="C:\Users\<USER>\Desktop\unified-platform\unified-parent\unified-service\src\main\java\com\bimowu\unified\login\mappers" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.bimowu.unified.utils" />
      <recent name="com.bimowu.unified.common.dao" />
      <recent name="com.bimowu.unified.common.model" />
      <recent name="com.bimowu.unified.filter" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="UnifiedWebApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="unified-web" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bimowu.UnifiedWebApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="082d57d6-1f39-459d-8173-0937df7c8dc9" name="Changes" comment="" />
      <created>1748170345256</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748170345256</updated>
      <workItem from="1748170346461" duration="3101000" />
      <workItem from="1748224606961" duration="21619000" />
      <workItem from="1748310652928" duration="18576000" />
      <workItem from="1748340403549" duration="3222000" />
      <workItem from="1748347103759" duration="341000" />
      <workItem from="1748917368808" duration="2169000" />
      <workItem from="1750402011890" duration="3109000" />
      <workItem from="1750406469408" duration="4919000" />
      <workItem from="1750417711866" duration="17768000" />
      <workItem from="1750493435268" duration="3339000" />
      <workItem from="1750643710528" duration="16651000" />
      <workItem from="1750727305477" duration="1258000" />
      <workItem from="1750728843620" duration="4726000" />
      <workItem from="1750818036237" duration="3266000" />
      <workItem from="1750827967579" duration="5836000" />
      <workItem from="1750894513896" duration="1397000" />
      <workItem from="1751281812707" duration="1206000" />
      <workItem from="1751334762987" duration="2458000" />
      <workItem from="1751421970292" duration="1173000" />
      <workItem from="1751508013072" duration="31000" />
      <workItem from="1751530494767" duration="661000" />
      <workItem from="1751594179181" duration="598000" />
      <workItem from="1751965366991" duration="463000" />
      <workItem from="1752041859876" duration="8930000" />
      <workItem from="1752214402129" duration="1064000" />
      <workItem from="1752223870791" duration="562000" />
      <workItem from="1752225000969" duration="1700000" />
      <workItem from="1752281915391" duration="53000" />
      <workItem from="1752475347698" duration="686000" />
      <workItem from="1752559786625" duration="2024000" />
      <workItem from="1752576774867" duration="9867000" />
      <workItem from="1752737961814" duration="3402000" />
      <workItem from="1752804689247" duration="2053000" />
      <workItem from="1752978740427" duration="595000" />
      <workItem from="1753152038454" duration="595000" />
      <workItem from="1755072585118" duration="761000" />
      <workItem from="1755136669845" duration="12732000" />
      <workItem from="1755236044690" duration="51000" />
      <workItem from="1755239862126" duration="1494000" />
      <workItem from="1755506209685" duration="1980000" />
      <workItem from="1755570615417" duration="371000" />
    </task>
    <task id="LOCAL-00001" summary="v0.1 统一认证平台初始化">
      <option name="closed" value="true" />
      <created>1748250876382</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1748250876382</updated>
    </task>
    <task id="LOCAL-00002" summary="v0.1 统一认证平台初始化">
      <option name="closed" value="true" />
      <created>1748253949055</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1748253949055</updated>
    </task>
    <task id="LOCAL-00003" summary="v0.2 数据源剥离">
      <option name="closed" value="true" />
      <created>1748257094651</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1748257094651</updated>
    </task>
    <task id="LOCAL-00004" summary="v0.3 sso逻辑开发">
      <option name="closed" value="true" />
      <created>1748312641020</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1748312641020</updated>
    </task>
    <task id="LOCAL-00005" summary="v0.3 sso逻辑完善">
      <option name="closed" value="true" />
      <created>1748315043096</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1748315043096</updated>
    </task>
    <task id="LOCAL-00006" summary="v0.3 sso逻辑完善">
      <option name="closed" value="true" />
      <created>1748328417400</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1748328417400</updated>
    </task>
    <task id="LOCAL-00007" summary="v0.3 sso逻辑完善">
      <option name="closed" value="true" />
      <created>1748330680850</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1748330680850</updated>
    </task>
    <task id="LOCAL-00008" summary="v0.3 sql">
      <option name="closed" value="true" />
      <created>1748332408571</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1748332408571</updated>
    </task>
    <task id="LOCAL-00009" summary="v0.3 优化">
      <option name="closed" value="true" />
      <created>1748341823585</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1748341823585</updated>
    </task>
    <task id="LOCAL-00010" summary="优化跳转逻辑，客户端上传回调地址">
      <option name="closed" value="true" />
      <created>1750407959524</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1750407959524</updated>
    </task>
    <task id="LOCAL-00011" summary="优化">
      <option name="closed" value="true" />
      <created>1750660065693</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1750660065693</updated>
    </task>
    <task id="LOCAL-00012" summary="sso">
      <option name="closed" value="true" />
      <created>1750667008333</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1750667008333</updated>
    </task>
    <task id="LOCAL-00013" summary="图形验证码">
      <option name="closed" value="true" />
      <created>1750668187793</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1750668187793</updated>
    </task>
    <task id="LOCAL-00014" summary="优化退出登录">
      <option name="closed" value="true" />
      <created>1750676202625</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1750676202625</updated>
    </task>
    <task id="LOCAL-00015" summary="配置修改">
      <option name="closed" value="true" />
      <created>1750827748992</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1750827748992</updated>
    </task>
    <task id="LOCAL-00016" summary="配置修改">
      <option name="closed" value="true" />
      <created>1750828248257</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1750828248257</updated>
    </task>
    <task id="LOCAL-00017" summary="修改端口号">
      <option name="closed" value="true" />
      <created>1750829953824</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1750829953824</updated>
    </task>
    <task id="LOCAL-00018" summary="环境配置">
      <option name="closed" value="true" />
      <created>1750894999330</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1750894999330</updated>
    </task>
    <task id="LOCAL-00019" summary="环境配置">
      <option name="closed" value="true" />
      <created>1750895225016</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1750895225016</updated>
    </task>
    <task id="LOCAL-00020" summary="bug修复">
      <option name="closed" value="true" />
      <created>1752042193142</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1752042193142</updated>
    </task>
    <task id="LOCAL-00021" summary="bug修复">
      <option name="closed" value="true" />
      <created>1752047385783</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1752047385783</updated>
    </task>
    <task id="LOCAL-00022" summary="cookie时间">
      <option name="closed" value="true" />
      <created>1752223910385</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1752223910385</updated>
    </task>
    <task id="LOCAL-00023" summary="待办类型">
      <option name="closed" value="true" />
      <created>1752559875894</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1752559875894</updated>
    </task>
    <task id="LOCAL-00024" summary="待办和进度管理">
      <option name="closed" value="true" />
      <created>1752561438577</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1752561438577</updated>
    </task>
    <task id="LOCAL-00025" summary="待办和进度管理">
      <option name="closed" value="true" />
      <created>1752561803021</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1752561803021</updated>
    </task>
    <task id="LOCAL-00026" summary="添加resumeId关联">
      <option name="closed" value="true" />
      <created>1752577638616</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1752577638616</updated>
    </task>
    <task id="LOCAL-00027" summary="验证码登录">
      <option name="closed" value="true" />
      <created>1752648152005</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1752648152005</updated>
    </task>
    <task id="LOCAL-00028" summary="验证码登录">
      <option name="closed" value="true" />
      <created>1752648448013</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1752648448013</updated>
    </task>
    <task id="LOCAL-00029" summary="验证码上限">
      <option name="closed" value="true" />
      <created>1752649954503</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1752649954503</updated>
    </task>
    <task id="LOCAL-00030" summary="验证码上限">
      <option name="closed" value="true" />
      <created>1752650495505</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1752650495505</updated>
    </task>
    <task id="LOCAL-00031" summary="验证码-日志">
      <option name="closed" value="true" />
      <created>1752650653709</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1752650653709</updated>
    </task>
    <task id="LOCAL-00032" summary="验证码-日志">
      <option name="closed" value="true" />
      <created>1752650920979</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1752650920979</updated>
    </task>
    <task id="LOCAL-00033" summary="背景图">
      <option name="closed" value="true" />
      <created>1752653416453</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1752653416453</updated>
    </task>
    <task id="LOCAL-00034" summary="重置密码">
      <option name="closed" value="true" />
      <created>1752738534209</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1752738534209</updated>
    </task>
    <task id="LOCAL-00035" summary="生产环境配置">
      <option name="closed" value="true" />
      <created>1755072747967</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1755072747967</updated>
    </task>
    <task id="LOCAL-00036" summary="面试系统、简历系统、sso，三个系统的sql整合">
      <option name="closed" value="true" />
      <created>1755139072803</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1755139072803</updated>
    </task>
    <task id="LOCAL-00037" summary="生产域名">
      <option name="closed" value="true" />
      <created>1755139858979</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1755139858979</updated>
    </task>
    <task id="LOCAL-00038" summary="升级lombok">
      <option name="closed" value="true" />
      <created>1755164055204</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1755164055204</updated>
    </task>
    <task id="LOCAL-00039" summary="生产">
      <option name="closed" value="true" />
      <created>1755226783772</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1755226783772</updated>
    </task>
    <task id="LOCAL-00040" summary="生产环境">
      <option name="closed" value="true" />
      <created>1755226889069</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1755226889069</updated>
    </task>
    <task id="LOCAL-00041" summary="新闻">
      <option name="closed" value="true" />
      <created>1755235633737</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1755235633737</updated>
    </task>
    <task id="LOCAL-00042" summary="新闻">
      <option name="closed" value="true" />
      <created>1755235881410</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1755235881410</updated>
    </task>
    <task id="LOCAL-00043" summary="生产域名">
      <option name="closed" value="true" />
      <created>1755236082545</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1755236082545</updated>
    </task>
    <task id="LOCAL-00044" summary="生产域名">
      <option name="closed" value="true" />
      <created>1755240163400</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1755240163400</updated>
    </task>
    <task id="LOCAL-00045" summary="进度折线图">
      <option name="closed" value="true" />
      <created>1755506995444</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1755506995444</updated>
    </task>
    <option name="localTasksCounter" value="46" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="72bb457d-28cc-440d-91bb-d0f425d65216" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="72bb457d-28cc-440d-91bb-d0f425d65216">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="优化" />
    <MESSAGE value="sso" />
    <MESSAGE value="图形验证码" />
    <MESSAGE value="优化退出登录" />
    <MESSAGE value="配置修改" />
    <MESSAGE value="修改端口号" />
    <MESSAGE value="环境配置" />
    <MESSAGE value="bug修复" />
    <MESSAGE value="cookie时间" />
    <MESSAGE value="待办类型" />
    <MESSAGE value="待办和进度管理" />
    <MESSAGE value="添加resumeId关联" />
    <MESSAGE value="验证码登录" />
    <MESSAGE value="验证码上限" />
    <MESSAGE value="验证码-日志" />
    <MESSAGE value="背景图" />
    <MESSAGE value="重置密码" />
    <MESSAGE value="生产环境配置" />
    <MESSAGE value="面试系统、简历系统、sso，三个系统的sql整合" />
    <MESSAGE value="升级lombok" />
    <MESSAGE value="生产" />
    <MESSAGE value="生产环境" />
    <MESSAGE value="新闻" />
    <MESSAGE value="生产域名" />
    <MESSAGE value="进度折线图" />
    <option name="LAST_COMMIT_MESSAGE" value="进度折线图" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="java-exception">
          <properties class="org.springframework.web.bind.MissingRequestHeaderException" package="org.springframework.web.bind" />
          <option name="timeStamp" value="12" />
        </breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/unified-service/src/main/java/com/bimowu/unified/login/service/impl/SsoServiceImpl.java</url>
          <line>125</line>
          <properties class="com.bimowu.unified.login.service.impl.SsoServiceImpl" method="mobileLogin">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/unified-service/src/main/java/com/bimowu/unified/login/service/impl/SsoServiceImpl.java</url>
          <line>54</line>
          <properties class="com.bimowu.unified.login.service.impl.SsoServiceImpl" method="login">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="15" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>