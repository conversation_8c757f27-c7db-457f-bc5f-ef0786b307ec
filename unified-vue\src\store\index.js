import { defineStore } from 'pinia'
import { logout } from '../api/user'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: JSON.parse(localStorage.getItem('userInfo') || '{}')
  }),
  
  actions: {
    setToken(token) {
      this.token = token
      localStorage.setItem('token', token)
    },
    
    setUserInfo(userInfo) {
      this.userInfo = userInfo
      localStorage.setItem('userInfo', JSON.stringify(userInfo))
    },
    
    async logout() {
      try {
        // 调用后端退出登录接口
        await logout()
      } catch (error) {
        console.error('退出登录失败:', error)
      } finally {
        // 无论接口是否成功，都清除本地状态
        this.token = ''
        this.userInfo = {}
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
      }
    }
  },
  
  getters: {
    isLoggedIn: (state) => !!state.token
  }
}) 