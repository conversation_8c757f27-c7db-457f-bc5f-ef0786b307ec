import{X as Oy,Y as Oo,a as Ca,f as By,j as Mr,Z as Ny,w as Ma,_ as kc,A as Fy,B as zy,$ as Hy,c as Vy,K as Oc,n as Gy,k as Wy,l as Bo,C as No,D as Gn,F as of,a0 as Uy}from"./vendor-477e13dd.js";import{_ as Yy}from"./index-9768669e.js";/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var Js=function(r,t){return Js=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])},Js(r,t)};function O(r,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Js(r,t);function e(){this.constructor=r}r.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}var $y=function(){function r(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return r}(),Xy=function(){function r(){this.browser=new $y,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow=typeof window<"u"}return r}(),Ye=new Xy;typeof wx=="object"&&typeof wx.getSystemInfoSync=="function"?(Ye.wxa=!0,Ye.touchEventsSupported=!0):typeof document>"u"&&typeof self<"u"?Ye.worker=!0:!Ye.hasGlobalWindow||"Deno"in window?(Ye.node=!0,Ye.svgSupported=!0):Zy(navigator.userAgent,Ye);function Zy(r,t){var e=t.browser,i=r.match(/Firefox\/([\d.]+)/),n=r.match(/MSIE\s([\d.]+)/)||r.match(/Trident\/.+?rv:(([\d.]+))/),a=r.match(/Edge?\/([\d.]+)/),o=/micromessenger/i.test(r);i&&(e.firefox=!0,e.version=i[1]),n&&(e.ie=!0,e.version=n[1]),a&&(e.edge=!0,e.version=a[1],e.newEdge=+a[1].split(".")[0]>18),o&&(e.weChat=!0),t.svgSupported=typeof SVGRect<"u",t.touchEventsSupported="ontouchstart"in window&&!e.ie&&!e.edge,t.pointerEventsSupported="onpointerdown"in window&&(e.edge||e.ie&&+e.version>=11),t.domSupported=typeof document<"u";var s=document.documentElement.style;t.transform3dSupported=(e.ie&&"transition"in s||e.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),t.transformSupported=t.transform3dSupported||e.ie&&+e.version>=9}const $=Ye;var Qu=12,qy="sans-serif",Or=Qu+"px "+qy,Ky=20,Qy=100,Jy="007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N";function jy(r){var t={};if(typeof JSON>"u")return t;for(var e=0;e<r.length;e++){var i=String.fromCharCode(e+32),n=(r.charCodeAt(e)-Ky)/Qy;t[i]=n}return t}var tm=jy(Jy),xi={createCanvas:function(){return typeof document<"u"&&document.createElement("canvas")},measureText:function(){var r,t;return function(e,i){if(!r){var n=xi.createCanvas();r=n&&n.getContext("2d")}if(r)return t!==i&&(t=r.font=i||Or),r.measureText(e);e=e||"",i=i||Or;var a=/((?:\d+)?\.?\d*)px/.exec(i),o=a&&+a[1]||Qu,s=0;if(i.indexOf("mono")>=0)s=o*e.length;else for(var u=0;u<e.length;u++){var l=tm[e[u]];s+=l==null?o:l*o}return{width:s}}}(),loadImage:function(r,t,e){var i=new Image;return i.onload=t,i.onerror=e,i.src=r,i}},Bc=Ti(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(r,t){return r["[object "+t+"]"]=!0,r},{}),Nc=Ti(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(r,t){return r["[object "+t+"Array]"]=!0,r},{}),On=Object.prototype.toString,ho=Array.prototype,em=ho.forEach,rm=ho.filter,Ju=ho.slice,im=ho.map,sf=(function(){}).constructor,Wn=sf?sf.prototype:null,ju="__proto__",nm=2311;function Fc(){return nm++}function tl(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];typeof console<"u"&&console.error.apply(console,r)}function K(r){if(r==null||typeof r!="object")return r;var t=r,e=On.call(r);if(e==="[object Array]"){if(!nn(r)){t=[];for(var i=0,n=r.length;i<n;i++)t[i]=K(r[i])}}else if(Nc[e]){if(!nn(r)){var a=r.constructor;if(a.from)t=a.from(r);else{t=new a(r.length);for(var i=0,n=r.length;i<n;i++)t[i]=r[i]}}}else if(!Bc[e]&&!nn(r)&&!gn(r)){t={};for(var o in r)r.hasOwnProperty(o)&&o!==ju&&(t[o]=K(r[o]))}return t}function at(r,t,e){if(!H(t)||!H(r))return e?K(t):r;for(var i in t)if(t.hasOwnProperty(i)&&i!==ju){var n=r[i],a=t[i];H(a)&&H(n)&&!B(a)&&!B(n)&&!gn(a)&&!gn(n)&&!uf(a)&&!uf(n)&&!nn(a)&&!nn(n)?at(n,a,e):(e||!(i in r))&&(r[i]=K(t[i]))}return r}function k(r,t){if(Object.assign)Object.assign(r,t);else for(var e in t)t.hasOwnProperty(e)&&e!==ju&&(r[e]=t[e]);return r}function tt(r,t,e){for(var i=lt(t),n=0,a=i.length;n<a;n++){var o=i[n];(e?t[o]!=null:r[o]==null)&&(r[o]=t[o])}return r}function nt(r,t){if(r){if(r.indexOf)return r.indexOf(t);for(var e=0,i=r.length;e<i;e++)if(r[e]===t)return e}return-1}function am(r,t){var e=r.prototype;function i(){}i.prototype=t.prototype,r.prototype=new i;for(var n in e)e.hasOwnProperty(n)&&(r.prototype[n]=e[n]);r.prototype.constructor=r,r.superClass=t}function be(r,t,e){if(r="prototype"in r?r.prototype:r,t="prototype"in t?t.prototype:t,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(t),n=0;n<i.length;n++){var a=i[n];a!=="constructor"&&(e?t[a]!=null:r[a]==null)&&(r[a]=t[a])}else tt(r,t,e)}function Nt(r){return!r||typeof r=="string"?!1:typeof r.length=="number"}function M(r,t,e){if(r&&t)if(r.forEach&&r.forEach===em)r.forEach(t,e);else if(r.length===+r.length)for(var i=0,n=r.length;i<n;i++)t.call(e,r[i],i,r);else for(var a in r)r.hasOwnProperty(a)&&t.call(e,r[a],a,r)}function G(r,t,e){if(!r)return[];if(!t)return el(r);if(r.map&&r.map===im)return r.map(t,e);for(var i=[],n=0,a=r.length;n<a;n++)i.push(t.call(e,r[n],n,r));return i}function Ti(r,t,e,i){if(r&&t){for(var n=0,a=r.length;n<a;n++)e=t.call(i,e,r[n],n,r);return e}}function Tt(r,t,e){if(!r)return[];if(!t)return el(r);if(r.filter&&r.filter===rm)return r.filter(t,e);for(var i=[],n=0,a=r.length;n<a;n++)t.call(e,r[n],n,r)&&i.push(r[n]);return i}function lt(r){if(!r)return[];if(Object.keys)return Object.keys(r);var t=[];for(var e in r)r.hasOwnProperty(e)&&t.push(e);return t}function om(r,t){for(var e=[],i=2;i<arguments.length;i++)e[i-2]=arguments[i];return function(){return r.apply(t,e.concat(Ju.call(arguments)))}}var ht=Wn&&U(Wn.bind)?Wn.call.bind(Wn.bind):om;function ee(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return function(){return r.apply(this,t.concat(Ju.call(arguments)))}}function B(r){return Array.isArray?Array.isArray(r):On.call(r)==="[object Array]"}function U(r){return typeof r=="function"}function z(r){return typeof r=="string"}function js(r){return On.call(r)==="[object String]"}function vt(r){return typeof r=="number"}function H(r){var t=typeof r;return t==="function"||!!r&&t==="object"}function uf(r){return!!Bc[On.call(r)]}function Ft(r){return!!Nc[On.call(r)]}function gn(r){return typeof r=="object"&&typeof r.nodeType=="number"&&typeof r.ownerDocument=="object"}function vo(r){return r.colorStops!=null}function sm(r){return r.image!=null}function Wa(r){return r!==r}function yn(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];for(var e=0,i=r.length;e<i;e++)if(r[e]!=null)return r[e]}function X(r,t){return r??t}function Da(r,t,e){return r??t??e}function el(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return Ju.apply(r,t)}function zc(r){if(typeof r=="number")return[r,r,r,r];var t=r.length;return t===2?[r[0],r[1],r[0],r[1]]:t===3?[r[0],r[1],r[2],r[1]]:r}function Ie(r,t){if(!r)throw new Error(t)}function me(r){return r==null?null:typeof r.trim=="function"?r.trim():r.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var Hc="__ec_primitive__";function tu(r){r[Hc]=!0}function nn(r){return r[Hc]}var um=function(){function r(){this.data={}}return r.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},r.prototype.has=function(t){return this.data.hasOwnProperty(t)},r.prototype.get=function(t){return this.data[t]},r.prototype.set=function(t,e){return this.data[t]=e,this},r.prototype.keys=function(){return lt(this.data)},r.prototype.forEach=function(t){var e=this.data;for(var i in e)e.hasOwnProperty(i)&&t(e[i],i)},r}(),Vc=typeof Map=="function";function lm(){return Vc?new Map:new um}var fm=function(){function r(t){var e=B(t);this.data=lm();var i=this;t instanceof r?t.each(n):t&&M(t,n);function n(a,o){e?i.set(a,o):i.set(o,a)}}return r.prototype.hasKey=function(t){return this.data.has(t)},r.prototype.get=function(t){return this.data.get(t)},r.prototype.set=function(t,e){return this.data.set(t,e),e},r.prototype.each=function(t,e){this.data.forEach(function(i,n){t.call(e,i,n)})},r.prototype.keys=function(){var t=this.data.keys();return Vc?Array.from(t):t},r.prototype.removeKey=function(t){this.data.delete(t)},r}();function Z(r){return new fm(r)}function hm(r,t){for(var e=new r.constructor(r.length+t.length),i=0;i<r.length;i++)e[i]=r[i];for(var n=r.length,i=0;i<t.length;i++)e[i+n]=t[i];return e}function co(r,t){var e;if(Object.create)e=Object.create(r);else{var i=function(){};i.prototype=r,e=new i}return t&&k(e,t),e}function Gc(r){var t=r.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function wi(r,t){return r.hasOwnProperty(t)}function Ot(){}var vm=180/Math.PI;function Ci(r,t){return r==null&&(r=0),t==null&&(t=0),[r,t]}function cm(r){return[r[0],r[1]]}function lf(r,t,e){return r[0]=t[0]+e[0],r[1]=t[1]+e[1],r}function dm(r,t,e){return r[0]=t[0]-e[0],r[1]=t[1]-e[1],r}function pm(r){return Math.sqrt(gm(r))}function gm(r){return r[0]*r[0]+r[1]*r[1]}function Fo(r,t,e){return r[0]=t[0]*e,r[1]=t[1]*e,r}function ym(r,t){var e=pm(t);return e===0?(r[0]=0,r[1]=0):(r[0]=t[0]/e,r[1]=t[1]/e),r}function eu(r,t){return Math.sqrt((r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1]))}var mm=eu;function _m(r,t){return(r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1])}var di=_m;function re(r,t,e){var i=t[0],n=t[1];return r[0]=e[0]*i+e[2]*n+e[4],r[1]=e[1]*i+e[3]*n+e[5],r}function li(r,t,e){return r[0]=Math.min(t[0],e[0]),r[1]=Math.min(t[1],e[1]),r}function fi(r,t,e){return r[0]=Math.max(t[0],e[0]),r[1]=Math.max(t[1],e[1]),r}var Xr=function(){function r(t,e){this.target=t,this.topTarget=e&&e.topTarget}return r}(),Sm=function(){function r(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return r.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new Xr(e,t),"dragstart",t.event))},r.prototype._drag=function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,a=i-this._x,o=n-this._y;this._x=i,this._y=n,e.drift(a,o,t),this.handler.dispatchToElement(new Xr(e,t),"drag",t.event);var s=this.handler.findHover(i,n,e).target,u=this._dropTarget;this._dropTarget=s,e!==s&&(u&&s!==u&&this.handler.dispatchToElement(new Xr(u,t),"dragleave",t.event),s&&s!==u&&this.handler.dispatchToElement(new Xr(s,t),"dragenter",t.event))}},r.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new Xr(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new Xr(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},r}();const wm=Sm;var bm=function(){function r(t){t&&(this._$eventProcessor=t)}return r.prototype.on=function(t,e,i,n){this._$handlers||(this._$handlers={});var a=this._$handlers;if(typeof e=="function"&&(n=i,i=e,e=null),!i||!t)return this;var o=this._$eventProcessor;e!=null&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),a[t]||(a[t]=[]);for(var s=0;s<a[t].length;s++)if(a[t][s].h===i)return this;var u={h:i,query:e,ctx:n||this,callAtLast:i.zrEventfulCallAtLast},l=a[t].length-1,f=a[t][l];return f&&f.callAtLast?a[t].splice(l,0,u):a[t].push(u),this},r.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},r.prototype.off=function(t,e){var i=this._$handlers;if(!i)return this;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],a=0,o=i[t].length;a<o;a++)i[t][a].h!==e&&n.push(i[t][a]);i[t]=n}i[t]&&i[t].length===0&&delete i[t]}else delete i[t];return this},r.prototype.trigger=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var o=e.length,s=n.length,u=0;u<s;u++){var l=n[u];if(!(a&&a.filter&&l.query!=null&&!a.filter(t,l.query)))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e);break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r.prototype.triggerWithContext=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var o=e.length,s=e[o-1],u=n.length,l=0;l<u;l++){var f=n[l];if(!(a&&a.filter&&f.query!=null&&!a.filter(t,f.query)))switch(o){case 0:f.h.call(s);break;case 1:f.h.call(s,e[0]);break;case 2:f.h.call(s,e[0],e[1]);break;default:f.h.apply(s,e.slice(1,o-1));break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r}();const xe=bm;var xm=Math.log(2);function ru(r,t,e,i,n,a){var o=i+"-"+n,s=r.length;if(a.hasOwnProperty(o))return a[o];if(t===1){var u=Math.round(Math.log((1<<s)-1&~n)/xm);return r[e][u]}for(var l=i|1<<e,f=e+1;i&1<<f;)f++;for(var h=0,c=0,v=0;c<s;c++){var d=1<<c;d&n||(h+=(v%2?-1:1)*r[e][c]*ru(r,t-1,f,l,n|d,a),v++)}return a[o]=h,h}function ff(r,t){var e=[[r[0],r[1],1,0,0,0,-t[0]*r[0],-t[0]*r[1]],[0,0,0,r[0],r[1],1,-t[1]*r[0],-t[1]*r[1]],[r[2],r[3],1,0,0,0,-t[2]*r[2],-t[2]*r[3]],[0,0,0,r[2],r[3],1,-t[3]*r[2],-t[3]*r[3]],[r[4],r[5],1,0,0,0,-t[4]*r[4],-t[4]*r[5]],[0,0,0,r[4],r[5],1,-t[5]*r[4],-t[5]*r[5]],[r[6],r[7],1,0,0,0,-t[6]*r[6],-t[6]*r[7]],[0,0,0,r[6],r[7],1,-t[7]*r[6],-t[7]*r[7]]],i={},n=ru(e,8,0,0,0,i);if(n!==0){for(var a=[],o=0;o<8;o++)for(var s=0;s<8;s++)a[s]==null&&(a[s]=0),a[s]+=((o+s)%2?-1:1)*ru(e,7,o===0?1:0,1<<o,1<<s,i)/n*t[o];return function(u,l,f){var h=l*a[6]+f*a[7]+1;u[0]=(l*a[0]+f*a[1]+a[2])/h,u[1]=(l*a[3]+f*a[4]+a[5])/h}}}var hf="___zrEVENTSAVED",zo=[];function Tm(r,t,e,i,n){return iu(zo,t,i,n,!0)&&iu(r,e,zo[0],zo[1])}function iu(r,t,e,i,n){if(t.getBoundingClientRect&&$.domSupported&&!Wc(t)){var a=t[hf]||(t[hf]={}),o=Cm(t,a),s=Mm(o,a,n);if(s)return s(r,e,i),!0}return!1}function Cm(r,t){var e=t.markers;if(e)return e;e=t.markers=[];for(var i=["left","right"],n=["top","bottom"],a=0;a<4;a++){var o=document.createElement("div"),s=o.style,u=a%2,l=(a>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[u]+":0",n[l]+":0",i[1-u]+":auto",n[1-l]+":auto",""].join("!important;"),r.appendChild(o),e.push(o)}return e}function Mm(r,t,e){for(var i=e?"invTrans":"trans",n=t[i],a=t.srcCoords,o=[],s=[],u=!0,l=0;l<4;l++){var f=r[l].getBoundingClientRect(),h=2*l,c=f.left,v=f.top;o.push(c,v),u=u&&a&&c===a[h]&&v===a[h+1],s.push(r[l].offsetLeft,r[l].offsetTop)}return u&&n?n:(t.srcCoords=o,t[i]=e?ff(s,o):ff(o,s))}function Wc(r){return r.nodeName.toUpperCase()==="CANVAS"}var Dm=/([&<>"'])/g,Am={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Et(r){return r==null?"":(r+"").replace(Dm,function(t,e){return Am[e]})}var Lm=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Ho=[],Pm=$.browser.firefox&&+$.browser.version.split(".")[0]<39;function nu(r,t,e,i){return e=e||{},i?vf(r,t,e):Pm&&t.layerX!=null&&t.layerX!==t.offsetX?(e.zrX=t.layerX,e.zrY=t.layerY):t.offsetX!=null?(e.zrX=t.offsetX,e.zrY=t.offsetY):vf(r,t,e),e}function vf(r,t,e){if($.domSupported&&r.getBoundingClientRect){var i=t.clientX,n=t.clientY;if(Wc(r)){var a=r.getBoundingClientRect();e.zrX=i-a.left,e.zrY=n-a.top;return}else if(iu(Ho,r,i,n)){e.zrX=Ho[0],e.zrY=Ho[1];return}}e.zrX=e.zrY=0}function rl(r){return r||window.event}function Zt(r,t,e){if(t=rl(t),t.zrX!=null)return t;var i=t.type,n=i&&i.indexOf("touch")>=0;if(n){var o=i!=="touchend"?t.targetTouches[0]:t.changedTouches[0];o&&nu(r,o,t,e)}else{nu(r,t,t,e);var a=Im(t);t.zrDelta=a?a/120:-(t.detail||0)/3}var s=t.button;return t.which==null&&s!==void 0&&Lm.test(t.type)&&(t.which=s&1?1:s&2?3:s&4?2:0),t}function Im(r){var t=r.wheelDelta;if(t)return t;var e=r.deltaX,i=r.deltaY;if(e==null||i==null)return t;var n=Math.abs(i!==0?i:e),a=i>0?-1:i<0?1:e>0?-1:1;return 3*n*a}function Em(r,t,e,i){r.addEventListener(t,e,i)}function Rm(r,t,e,i){r.removeEventListener(t,e,i)}var Uc=function(r){r.preventDefault(),r.stopPropagation(),r.cancelBubble=!0},km=function(){function r(){this._track=[]}return r.prototype.recognize=function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},r.prototype.clear=function(){return this._track.length=0,this},r.prototype._doTrack=function(t,e,i){var n=t.touches;if(n){for(var a={points:[],touches:[],target:e,event:t},o=0,s=n.length;o<s;o++){var u=n[o],l=nu(i,u,{});a.points.push([l.zrX,l.zrY]),a.touches.push(u)}this._track.push(a)}},r.prototype._recognize=function(t){for(var e in Vo)if(Vo.hasOwnProperty(e)){var i=Vo[e](this._track,t);if(i)return i}},r}();function cf(r){var t=r[1][0]-r[0][0],e=r[1][1]-r[0][1];return Math.sqrt(t*t+e*e)}function Om(r){return[(r[0][0]+r[1][0])/2,(r[0][1]+r[1][1])/2]}var Vo={pinch:function(r,t){var e=r.length;if(e){var i=(r[e-1]||{}).points,n=(r[e-2]||{}).points||i;if(n&&n.length>1&&i&&i.length>1){var a=cf(i)/cf(n);!isFinite(a)&&(a=1),t.pinchScale=a;var o=Om(i);return t.pinchX=o[0],t.pinchY=o[1],{type:"pinch",target:r[0].target,event:t}}}}};function pi(){return[1,0,0,1,0,0]}function il(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=1,r[4]=0,r[5]=0,r}function Bm(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4],r[5]=t[5],r}function gi(r,t,e){var i=t[0]*e[0]+t[2]*e[1],n=t[1]*e[0]+t[3]*e[1],a=t[0]*e[2]+t[2]*e[3],o=t[1]*e[2]+t[3]*e[3],s=t[0]*e[4]+t[2]*e[5]+t[4],u=t[1]*e[4]+t[3]*e[5]+t[5];return r[0]=i,r[1]=n,r[2]=a,r[3]=o,r[4]=s,r[5]=u,r}function au(r,t,e){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4]+e[0],r[5]=t[5]+e[1],r}function nl(r,t,e,i){i===void 0&&(i=[0,0]);var n=t[0],a=t[2],o=t[4],s=t[1],u=t[3],l=t[5],f=Math.sin(e),h=Math.cos(e);return r[0]=n*h+s*f,r[1]=-n*f+s*h,r[2]=a*h+u*f,r[3]=-a*f+h*u,r[4]=h*(o-i[0])+f*(l-i[1])+i[0],r[5]=h*(l-i[1])-f*(o-i[0])+i[1],r}function Nm(r,t,e){var i=e[0],n=e[1];return r[0]=t[0]*i,r[1]=t[1]*n,r[2]=t[2]*i,r[3]=t[3]*n,r[4]=t[4]*i,r[5]=t[5]*n,r}function al(r,t){var e=t[0],i=t[2],n=t[4],a=t[1],o=t[3],s=t[5],u=e*o-a*i;return u?(u=1/u,r[0]=o*u,r[1]=-a*u,r[2]=-i*u,r[3]=e*u,r[4]=(i*s-o*n)*u,r[5]=(a*n-e*s)*u,r):null}var Fm=function(){function r(t,e){this.x=t||0,this.y=e||0}return r.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},r.prototype.clone=function(){return new r(this.x,this.y)},r.prototype.set=function(t,e){return this.x=t,this.y=e,this},r.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},r.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},r.prototype.scale=function(t){this.x*=t,this.y*=t},r.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},r.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},r.prototype.dot=function(t){return this.x*t.x+this.y*t.y},r.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},r.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},r.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},r.prototype.distance=function(t){var e=this.x-t.x,i=this.y-t.y;return Math.sqrt(e*e+i*i)},r.prototype.distanceSquare=function(t){var e=this.x-t.x,i=this.y-t.y;return e*e+i*i},r.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},r.prototype.transform=function(t){if(t){var e=this.x,i=this.y;return this.x=t[0]*e+t[2]*i+t[4],this.y=t[1]*e+t[3]*i+t[5],this}},r.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},r.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},r.set=function(t,e,i){t.x=e,t.y=i},r.copy=function(t,e){t.x=e.x,t.y=e.y},r.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},r.lenSquare=function(t){return t.x*t.x+t.y*t.y},r.dot=function(t,e){return t.x*e.x+t.y*e.y},r.add=function(t,e,i){t.x=e.x+i.x,t.y=e.y+i.y},r.sub=function(t,e,i){t.x=e.x-i.x,t.y=e.y-i.y},r.scale=function(t,e,i){t.x=e.x*i,t.y=e.y*i},r.scaleAndAdd=function(t,e,i,n){t.x=e.x+i.x*n,t.y=e.y+i.y*n},r.lerp=function(t,e,i,n){var a=1-n;t.x=a*e.x+n*i.x,t.y=a*e.y+n*i.y},r}();const rt=Fm;var Un=Math.min,Yn=Math.max,nr=new rt,ar=new rt,or=new rt,sr=new rt,Pi=new rt,Ii=new rt,zm=function(){function r(t,e,i,n){i<0&&(t=t+i,i=-i),n<0&&(e=e+n,n=-n),this.x=t,this.y=e,this.width=i,this.height=n}return r.prototype.union=function(t){var e=Un(t.x,this.x),i=Un(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=Yn(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=Yn(t.y+t.height,this.y+this.height)-i:this.height=t.height,this.x=e,this.y=i},r.prototype.applyTransform=function(t){r.applyTransform(this,this,t)},r.prototype.calculateTransform=function(t){var e=this,i=t.width/e.width,n=t.height/e.height,a=pi();return au(a,a,[-e.x,-e.y]),Nm(a,a,[i,n]),au(a,a,[t.x,t.y]),a},r.prototype.intersect=function(t,e){if(!t)return!1;t instanceof r||(t=r.create(t));var i=this,n=i.x,a=i.x+i.width,o=i.y,s=i.y+i.height,u=t.x,l=t.x+t.width,f=t.y,h=t.y+t.height,c=!(a<u||l<n||s<f||h<o);if(e){var v=1/0,d=0,y=Math.abs(a-u),p=Math.abs(l-n),g=Math.abs(s-f),m=Math.abs(h-o),_=Math.min(y,p),S=Math.min(g,m);a<u||l<n?_>d&&(d=_,y<p?rt.set(Ii,-y,0):rt.set(Ii,p,0)):_<v&&(v=_,y<p?rt.set(Pi,y,0):rt.set(Pi,-p,0)),s<f||h<o?S>d&&(d=S,g<m?rt.set(Ii,0,-g):rt.set(Ii,0,m)):_<v&&(v=_,g<m?rt.set(Pi,0,g):rt.set(Pi,0,-m))}return e&&rt.copy(e,c?Pi:Ii),c},r.prototype.contain=function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i.height},r.prototype.clone=function(){return new r(this.x,this.y,this.width,this.height)},r.prototype.copy=function(t){r.copy(this,t)},r.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},r.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},r.prototype.isZero=function(){return this.width===0||this.height===0},r.create=function(t){return new r(t.x,t.y,t.width,t.height)},r.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},r.applyTransform=function(t,e,i){if(!i){t!==e&&r.copy(t,e);return}if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var n=i[0],a=i[3],o=i[4],s=i[5];t.x=e.x*n+o,t.y=e.y*a+s,t.width=e.width*n,t.height=e.height*a,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height);return}nr.x=or.x=e.x,nr.y=sr.y=e.y,ar.x=sr.x=e.x+e.width,ar.y=or.y=e.y+e.height,nr.transform(i),sr.transform(i),ar.transform(i),or.transform(i),t.x=Un(nr.x,ar.x,or.x,sr.x),t.y=Un(nr.y,ar.y,or.y,sr.y);var u=Yn(nr.x,ar.x,or.x,sr.x),l=Yn(nr.y,ar.y,or.y,sr.y);t.width=u-t.x,t.height=l-t.y},r}();const J=zm;var Yc="silent";function Hm(r,t,e){return{type:r,event:e,target:t.target,topTarget:t.topTarget,cancelBubble:!1,offsetX:e.zrX,offsetY:e.zrY,gestureEvent:e.gestureEvent,pinchX:e.pinchX,pinchY:e.pinchY,pinchScale:e.pinchScale,wheelDelta:e.zrDelta,zrByTouch:e.zrByTouch,which:e.which,stop:Vm}}function Vm(){Uc(this.event)}var Gm=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.handler=null,e}return t.prototype.dispose=function(){},t.prototype.setCursor=function(){},t}(xe),Ei=function(){function r(t,e){this.x=t,this.y=e}return r}(),Wm=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Go=new J(0,0,0,0),$c=function(r){O(t,r);function t(e,i,n,a,o){var s=r.call(this)||this;return s._hovered=new Ei(0,0),s.storage=e,s.painter=i,s.painterRoot=a,s._pointerSize=o,n=n||new Gm,s.proxy=null,s.setHandlerProxy(n),s._draggingMgr=new wm(s),s}return t.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(M(Wm,function(i){e.on&&e.on(i,this[i],this)},this),e.handler=this),this.proxy=e},t.prototype.mousemove=function(e){var i=e.zrX,n=e.zrY,a=Xc(this,i,n),o=this._hovered,s=o.target;s&&!s.__zr&&(o=this.findHover(o.x,o.y),s=o.target);var u=this._hovered=a?new Ei(i,n):this.findHover(i,n),l=u.target,f=this.proxy;f.setCursor&&f.setCursor(l?l.cursor:"default"),s&&l!==s&&this.dispatchToElement(o,"mouseout",e),this.dispatchToElement(u,"mousemove",e),l&&l!==s&&this.dispatchToElement(u,"mouseover",e)},t.prototype.mouseout=function(e){var i=e.zrEventControl;i!=="only_globalout"&&this.dispatchToElement(this._hovered,"mouseout",e),i!=="no_globalout"&&this.trigger("globalout",{type:"globalout",event:e})},t.prototype.resize=function(){this._hovered=new Ei(0,0)},t.prototype.dispatch=function(e,i){var n=this[e];n&&n.call(this,i)},t.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},t.prototype.setCursorStyle=function(e){var i=this.proxy;i.setCursor&&i.setCursor(e)},t.prototype.dispatchToElement=function(e,i,n){e=e||{};var a=e.target;if(!(a&&a.silent)){for(var o="on"+i,s=Hm(i,e,n);a&&(a[o]&&(s.cancelBubble=!!a[o].call(a,s)),a.trigger(i,s),a=a.__hostTarget?a.__hostTarget:a.parent,!s.cancelBubble););s.cancelBubble||(this.trigger(i,s),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(u){typeof u[o]=="function"&&u[o].call(u,s),u.trigger&&u.trigger(i,s)}))}},t.prototype.findHover=function(e,i,n){var a=this.storage.getDisplayList(),o=new Ei(e,i);if(df(a,o,e,i,n),this._pointerSize&&!o.target){for(var s=[],u=this._pointerSize,l=u/2,f=new J(e-l,i-l,u,u),h=a.length-1;h>=0;h--){var c=a[h];c!==n&&!c.ignore&&!c.ignoreCoarsePointer&&(!c.parent||!c.parent.ignoreCoarsePointer)&&(Go.copy(c.getBoundingRect()),c.transform&&Go.applyTransform(c.transform),Go.intersect(f)&&s.push(c))}if(s.length)for(var v=4,d=Math.PI/12,y=Math.PI*2,p=0;p<l;p+=v)for(var g=0;g<y;g+=d){var m=e+p*Math.cos(g),_=i+p*Math.sin(g);if(df(s,o,m,_,n),o.target)return o}}return o},t.prototype.processGesture=function(e,i){this._gestureMgr||(this._gestureMgr=new km);var n=this._gestureMgr;i==="start"&&n.clear();var a=n.recognize(e,this.findHover(e.zrX,e.zrY,null).target,this.proxy.dom);if(i==="end"&&n.clear(),a){var o=a.type;e.gestureEvent=o;var s=new Ei;s.target=a.target,this.dispatchToElement(s,o,a.event)}},t}(xe);M(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(r){$c.prototype[r]=function(t){var e=t.zrX,i=t.zrY,n=Xc(this,e,i),a,o;if((r!=="mouseup"||!n)&&(a=this.findHover(e,i),o=a.target),r==="mousedown")this._downEl=o,this._downPoint=[t.zrX,t.zrY],this._upEl=o;else if(r==="mouseup")this._upEl=o;else if(r==="click"){if(this._downEl!==this._upEl||!this._downPoint||mm(this._downPoint,[t.zrX,t.zrY])>4)return;this._downPoint=null}this.dispatchToElement(a,r,t)}});function Um(r,t,e){if(r[r.rectHover?"rectContain":"contain"](t,e)){for(var i=r,n=void 0,a=!1;i;){if(i.ignoreClip&&(a=!0),!a){var o=i.getClipPath();if(o&&!o.contain(t,e))return!1}i.silent&&(n=!0);var s=i.__hostTarget;i=s||i.parent}return n?Yc:!0}return!1}function df(r,t,e,i,n){for(var a=r.length-1;a>=0;a--){var o=r[a],s=void 0;if(o!==n&&!o.ignore&&(s=Um(o,e,i))&&(!t.topTarget&&(t.topTarget=o),s!==Yc)){t.target=o;break}}}function Xc(r,t,e){var i=r.painter;return t<0||t>i.getWidth()||e<0||e>i.getHeight()}const Ym=$c;var Zc=32,Ri=7;function $m(r){for(var t=0;r>=Zc;)t|=r&1,r>>=1;return r+t}function pf(r,t,e,i){var n=t+1;if(n===e)return 1;if(i(r[n++],r[t])<0){for(;n<e&&i(r[n],r[n-1])<0;)n++;Xm(r,t,n)}else for(;n<e&&i(r[n],r[n-1])>=0;)n++;return n-t}function Xm(r,t,e){for(e--;t<e;){var i=r[t];r[t++]=r[e],r[e--]=i}}function gf(r,t,e,i,n){for(i===t&&i++;i<e;i++){for(var a=r[i],o=t,s=i,u;o<s;)u=o+s>>>1,n(a,r[u])<0?s=u:o=u+1;var l=i-o;switch(l){case 3:r[o+3]=r[o+2];case 2:r[o+2]=r[o+1];case 1:r[o+1]=r[o];break;default:for(;l>0;)r[o+l]=r[o+l-1],l--}r[o]=a}}function Wo(r,t,e,i,n,a){var o=0,s=0,u=1;if(a(r,t[e+n])>0){for(s=i-n;u<s&&a(r,t[e+n+u])>0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s),o+=n,u+=n}else{for(s=n+1;u<s&&a(r,t[e+n-u])<=0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s);var l=o;o=n-u,u=n-l}for(o++;o<u;){var f=o+(u-o>>>1);a(r,t[e+f])>0?o=f+1:u=f}return u}function Uo(r,t,e,i,n,a){var o=0,s=0,u=1;if(a(r,t[e+n])<0){for(s=n+1;u<s&&a(r,t[e+n-u])<0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s);var l=o;o=n-u,u=n-l}else{for(s=i-n;u<s&&a(r,t[e+n+u])>=0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s),o+=n,u+=n}for(o++;o<u;){var f=o+(u-o>>>1);a(r,t[e+f])<0?u=f:o=f+1}return u}function Zm(r,t){var e=Ri,i,n,a=0,o=[];i=[],n=[];function s(v,d){i[a]=v,n[a]=d,a+=1}function u(){for(;a>1;){var v=a-2;if(v>=1&&n[v-1]<=n[v]+n[v+1]||v>=2&&n[v-2]<=n[v]+n[v-1])n[v-1]<n[v+1]&&v--;else if(n[v]>n[v+1])break;f(v)}}function l(){for(;a>1;){var v=a-2;v>0&&n[v-1]<n[v+1]&&v--,f(v)}}function f(v){var d=i[v],y=n[v],p=i[v+1],g=n[v+1];n[v]=y+g,v===a-3&&(i[v+1]=i[v+2],n[v+1]=n[v+2]),a--;var m=Uo(r[p],r,d,y,0,t);d+=m,y-=m,y!==0&&(g=Wo(r[d+y-1],r,p,g,g-1,t),g!==0&&(y<=g?h(d,y,p,g):c(d,y,p,g)))}function h(v,d,y,p){var g=0;for(g=0;g<d;g++)o[g]=r[v+g];var m=0,_=y,S=v;if(r[S++]=r[_++],--p===0){for(g=0;g<d;g++)r[S+g]=o[m+g];return}if(d===1){for(g=0;g<p;g++)r[S+g]=r[_+g];r[S+p]=o[m];return}for(var b=e,w,x,D;;){w=0,x=0,D=!1;do if(t(r[_],o[m])<0){if(r[S++]=r[_++],x++,w=0,--p===0){D=!0;break}}else if(r[S++]=o[m++],w++,x=0,--d===1){D=!0;break}while((w|x)<b);if(D)break;do{if(w=Uo(r[_],o,m,d,0,t),w!==0){for(g=0;g<w;g++)r[S+g]=o[m+g];if(S+=w,m+=w,d-=w,d<=1){D=!0;break}}if(r[S++]=r[_++],--p===0){D=!0;break}if(x=Wo(o[m],r,_,p,0,t),x!==0){for(g=0;g<x;g++)r[S+g]=r[_+g];if(S+=x,_+=x,p-=x,p===0){D=!0;break}}if(r[S++]=o[m++],--d===1){D=!0;break}b--}while(w>=Ri||x>=Ri);if(D)break;b<0&&(b=0),b+=2}if(e=b,e<1&&(e=1),d===1){for(g=0;g<p;g++)r[S+g]=r[_+g];r[S+p]=o[m]}else{if(d===0)throw new Error;for(g=0;g<d;g++)r[S+g]=o[m+g]}}function c(v,d,y,p){var g=0;for(g=0;g<p;g++)o[g]=r[y+g];var m=v+d-1,_=p-1,S=y+p-1,b=0,w=0;if(r[S--]=r[m--],--d===0){for(b=S-(p-1),g=0;g<p;g++)r[b+g]=o[g];return}if(p===1){for(S-=d,m-=d,w=S+1,b=m+1,g=d-1;g>=0;g--)r[w+g]=r[b+g];r[S]=o[_];return}for(var x=e;;){var D=0,T=0,C=!1;do if(t(o[_],r[m])<0){if(r[S--]=r[m--],D++,T=0,--d===0){C=!0;break}}else if(r[S--]=o[_--],T++,D=0,--p===1){C=!0;break}while((D|T)<x);if(C)break;do{if(D=d-Uo(o[_],r,v,d,d-1,t),D!==0){for(S-=D,m-=D,d-=D,w=S+1,b=m+1,g=D-1;g>=0;g--)r[w+g]=r[b+g];if(d===0){C=!0;break}}if(r[S--]=o[_--],--p===1){C=!0;break}if(T=p-Wo(r[m],o,0,p,p-1,t),T!==0){for(S-=T,_-=T,p-=T,w=S+1,b=_+1,g=0;g<T;g++)r[w+g]=o[b+g];if(p<=1){C=!0;break}}if(r[S--]=r[m--],--d===0){C=!0;break}x--}while(D>=Ri||T>=Ri);if(C)break;x<0&&(x=0),x+=2}if(e=x,e<1&&(e=1),p===1){for(S-=d,m-=d,w=S+1,b=m+1,g=d-1;g>=0;g--)r[w+g]=r[b+g];r[S]=o[_]}else{if(p===0)throw new Error;for(b=S-(p-1),g=0;g<p;g++)r[b+g]=o[g]}}return{mergeRuns:u,forceMergeRuns:l,pushRun:s}}function Aa(r,t,e,i){e||(e=0),i||(i=r.length);var n=i-e;if(!(n<2)){var a=0;if(n<Zc){a=pf(r,e,i,t),gf(r,e,i,e+a,t);return}var o=Zm(r,t),s=$m(n);do{if(a=pf(r,e,i,t),a<s){var u=n;u>s&&(u=s),gf(r,e,e+u,e+a,t),a=u}o.pushRun(e,a),o.mergeRuns(),n-=a,e+=a}while(n!==0);o.forceMergeRuns()}}var Gt=1,Ki=2,si=4,yf=!1;function Yo(){yf||(yf=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function mf(r,t){return r.zlevel===t.zlevel?r.z===t.z?r.z2-t.z2:r.z-t.z:r.zlevel-t.zlevel}var qm=function(){function r(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=mf}return r.prototype.traverse=function(t,e){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,e)},r.prototype.getDisplayList=function(t,e){e=e||!1;var i=this._displayList;return(t||!i.length)&&this.updateDisplayList(e),i},r.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,i=this._displayList,n=0,a=e.length;n<a;n++)this._updateAndAddDisplayable(e[n],null,t);i.length=this._displayListLen,Aa(i,mf)},r.prototype._updateAndAddDisplayable=function(t,e,i){if(!(t.ignore&&!i)){t.beforeUpdate(),t.update(),t.afterUpdate();var n=t.getClipPath();if(t.ignoreClip)e=null;else if(n){e?e=e.slice():e=[];for(var a=n,o=t;a;)a.parent=o,a.updateTransform(),e.push(a),o=a,a=a.getClipPath()}if(t.childrenRef){for(var s=t.childrenRef(),u=0;u<s.length;u++){var l=s[u];t.__dirty&&(l.__dirty|=Gt),this._updateAndAddDisplayable(l,e,i)}t.__dirty=0}else{var f=t;e&&e.length?f.__clipPaths=e:f.__clipPaths&&f.__clipPaths.length>0&&(f.__clipPaths=[]),isNaN(f.z)&&(Yo(),f.z=0),isNaN(f.z2)&&(Yo(),f.z2=0),isNaN(f.zlevel)&&(Yo(),f.zlevel=0),this._displayList[this._displayListLen++]=f}var h=t.getDecalElement&&t.getDecalElement();h&&this._updateAndAddDisplayable(h,e,i);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,e,i);var v=t.getTextContent();v&&this._updateAndAddDisplayable(v,e,i)}},r.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},r.prototype.delRoot=function(t){if(t instanceof Array){for(var e=0,i=t.length;e<i;e++)this.delRoot(t[e]);return}var n=nt(this._roots,t);n>=0&&this._roots.splice(n,1)},r.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},r.prototype.getRoots=function(){return this._roots},r.prototype.dispose=function(){this._displayList=null,this._roots=null},r}();const Km=qm;var qc;qc=$.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(r){return setTimeout(r,16)};const ou=qc;var La={linear:function(r){return r},quadraticIn:function(r){return r*r},quadraticOut:function(r){return r*(2-r)},quadraticInOut:function(r){return(r*=2)<1?.5*r*r:-.5*(--r*(r-2)-1)},cubicIn:function(r){return r*r*r},cubicOut:function(r){return--r*r*r+1},cubicInOut:function(r){return(r*=2)<1?.5*r*r*r:.5*((r-=2)*r*r+2)},quarticIn:function(r){return r*r*r*r},quarticOut:function(r){return 1- --r*r*r*r},quarticInOut:function(r){return(r*=2)<1?.5*r*r*r*r:-.5*((r-=2)*r*r*r-2)},quinticIn:function(r){return r*r*r*r*r},quinticOut:function(r){return--r*r*r*r*r+1},quinticInOut:function(r){return(r*=2)<1?.5*r*r*r*r*r:.5*((r-=2)*r*r*r*r+2)},sinusoidalIn:function(r){return 1-Math.cos(r*Math.PI/2)},sinusoidalOut:function(r){return Math.sin(r*Math.PI/2)},sinusoidalInOut:function(r){return .5*(1-Math.cos(Math.PI*r))},exponentialIn:function(r){return r===0?0:Math.pow(1024,r-1)},exponentialOut:function(r){return r===1?1:1-Math.pow(2,-10*r)},exponentialInOut:function(r){return r===0?0:r===1?1:(r*=2)<1?.5*Math.pow(1024,r-1):.5*(-Math.pow(2,-10*(r-1))+2)},circularIn:function(r){return 1-Math.sqrt(1-r*r)},circularOut:function(r){return Math.sqrt(1- --r*r)},circularInOut:function(r){return(r*=2)<1?-.5*(Math.sqrt(1-r*r)-1):.5*(Math.sqrt(1-(r-=2)*r)+1)},elasticIn:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),-(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)))},elasticOut:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),e*Math.pow(2,-10*r)*Math.sin((r-t)*(2*Math.PI)/i)+1)},elasticInOut:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),(r*=2)<1?-.5*(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)):e*Math.pow(2,-10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)*.5+1)},backIn:function(r){var t=1.70158;return r*r*((t+1)*r-t)},backOut:function(r){var t=1.70158;return--r*r*((t+1)*r+t)+1},backInOut:function(r){var t=2.5949095;return(r*=2)<1?.5*(r*r*((t+1)*r-t)):.5*((r-=2)*r*((t+1)*r+t)+2)},bounceIn:function(r){return 1-La.bounceOut(1-r)},bounceOut:function(r){return r<1/2.75?7.5625*r*r:r<2/2.75?7.5625*(r-=1.5/2.75)*r+.75:r<2.5/2.75?7.5625*(r-=2.25/2.75)*r+.9375:7.5625*(r-=2.625/2.75)*r+.984375},bounceInOut:function(r){return r<.5?La.bounceIn(r*2)*.5:La.bounceOut(r*2-1)*.5+.5}};const Kc=La;var $n=Math.pow,Ke=Math.sqrt,Ua=1e-8,Qc=1e-4,_f=Ke(3),Xn=1/3,ye=Ci(),Qt=Ci(),yi=Ci();function Ze(r){return r>-Ua&&r<Ua}function Jc(r){return r>Ua||r<-Ua}function gt(r,t,e,i,n){var a=1-n;return a*a*(a*r+3*n*t)+n*n*(n*i+3*a*e)}function Sf(r,t,e,i,n){var a=1-n;return 3*(((t-r)*a+2*(e-t)*n)*a+(i-e)*n*n)}function Ya(r,t,e,i,n,a){var o=i+3*(t-e)-r,s=3*(e-t*2+r),u=3*(t-r),l=r-n,f=s*s-3*o*u,h=s*u-9*o*l,c=u*u-3*s*l,v=0;if(Ze(f)&&Ze(h))if(Ze(s))a[0]=0;else{var d=-u/s;d>=0&&d<=1&&(a[v++]=d)}else{var y=h*h-4*f*c;if(Ze(y)){var p=h/f,d=-s/o+p,g=-p/2;d>=0&&d<=1&&(a[v++]=d),g>=0&&g<=1&&(a[v++]=g)}else if(y>0){var m=Ke(y),_=f*s+1.5*o*(-h+m),S=f*s+1.5*o*(-h-m);_<0?_=-$n(-_,Xn):_=$n(_,Xn),S<0?S=-$n(-S,Xn):S=$n(S,Xn);var d=(-s-(_+S))/(3*o);d>=0&&d<=1&&(a[v++]=d)}else{var b=(2*f*s-3*o*h)/(2*Ke(f*f*f)),w=Math.acos(b)/3,x=Ke(f),D=Math.cos(w),d=(-s-2*x*D)/(3*o),g=(-s+x*(D+_f*Math.sin(w)))/(3*o),T=(-s+x*(D-_f*Math.sin(w)))/(3*o);d>=0&&d<=1&&(a[v++]=d),g>=0&&g<=1&&(a[v++]=g),T>=0&&T<=1&&(a[v++]=T)}}return v}function jc(r,t,e,i,n){var a=6*e-12*t+6*r,o=9*t+3*i-3*r-9*e,s=3*t-3*r,u=0;if(Ze(o)){if(Jc(a)){var l=-s/a;l>=0&&l<=1&&(n[u++]=l)}}else{var f=a*a-4*o*s;if(Ze(f))n[0]=-a/(2*o);else if(f>0){var h=Ke(f),l=(-a+h)/(2*o),c=(-a-h)/(2*o);l>=0&&l<=1&&(n[u++]=l),c>=0&&c<=1&&(n[u++]=c)}}return u}function $a(r,t,e,i,n,a){var o=(t-r)*n+r,s=(e-t)*n+t,u=(i-e)*n+e,l=(s-o)*n+o,f=(u-s)*n+s,h=(f-l)*n+l;a[0]=r,a[1]=o,a[2]=l,a[3]=h,a[4]=h,a[5]=f,a[6]=u,a[7]=i}function Qm(r,t,e,i,n,a,o,s,u,l,f){var h,c=.005,v=1/0,d,y,p,g;ye[0]=u,ye[1]=l;for(var m=0;m<1;m+=.05)Qt[0]=gt(r,e,n,o,m),Qt[1]=gt(t,i,a,s,m),p=di(ye,Qt),p<v&&(h=m,v=p);v=1/0;for(var _=0;_<32&&!(c<Qc);_++)d=h-c,y=h+c,Qt[0]=gt(r,e,n,o,d),Qt[1]=gt(t,i,a,s,d),p=di(Qt,ye),d>=0&&p<v?(h=d,v=p):(yi[0]=gt(r,e,n,o,y),yi[1]=gt(t,i,a,s,y),g=di(yi,ye),y<=1&&g<v?(h=y,v=g):c*=.5);return f&&(f[0]=gt(r,e,n,o,h),f[1]=gt(t,i,a,s,h)),Ke(v)}function Jm(r,t,e,i,n,a,o,s,u){for(var l=r,f=t,h=0,c=1/u,v=1;v<=u;v++){var d=v*c,y=gt(r,e,n,o,d),p=gt(t,i,a,s,d),g=y-l,m=p-f;h+=Math.sqrt(g*g+m*m),l=y,f=p}return h}function bt(r,t,e,i){var n=1-i;return n*(n*r+2*i*t)+i*i*e}function wf(r,t,e,i){return 2*((1-i)*(t-r)+i*(e-t))}function jm(r,t,e,i,n){var a=r-2*t+e,o=2*(t-r),s=r-i,u=0;if(Ze(a)){if(Jc(o)){var l=-s/o;l>=0&&l<=1&&(n[u++]=l)}}else{var f=o*o-4*a*s;if(Ze(f)){var l=-o/(2*a);l>=0&&l<=1&&(n[u++]=l)}else if(f>0){var h=Ke(f),l=(-o+h)/(2*a),c=(-o-h)/(2*a);l>=0&&l<=1&&(n[u++]=l),c>=0&&c<=1&&(n[u++]=c)}}return u}function td(r,t,e){var i=r+e-2*t;return i===0?.5:(r-t)/i}function Xa(r,t,e,i,n){var a=(t-r)*i+r,o=(e-t)*i+t,s=(o-a)*i+a;n[0]=r,n[1]=a,n[2]=s,n[3]=s,n[4]=o,n[5]=e}function t0(r,t,e,i,n,a,o,s,u){var l,f=.005,h=1/0;ye[0]=o,ye[1]=s;for(var c=0;c<1;c+=.05){Qt[0]=bt(r,e,n,c),Qt[1]=bt(t,i,a,c);var v=di(ye,Qt);v<h&&(l=c,h=v)}h=1/0;for(var d=0;d<32&&!(f<Qc);d++){var y=l-f,p=l+f;Qt[0]=bt(r,e,n,y),Qt[1]=bt(t,i,a,y);var v=di(Qt,ye);if(y>=0&&v<h)l=y,h=v;else{yi[0]=bt(r,e,n,p),yi[1]=bt(t,i,a,p);var g=di(yi,ye);p<=1&&g<h?(l=p,h=g):f*=.5}}return u&&(u[0]=bt(r,e,n,l),u[1]=bt(t,i,a,l)),Ke(h)}function e0(r,t,e,i,n,a,o){for(var s=r,u=t,l=0,f=1/o,h=1;h<=o;h++){var c=h*f,v=bt(r,e,n,c),d=bt(t,i,a,c),y=v-s,p=d-u;l+=Math.sqrt(y*y+p*p),s=v,u=d}return l}var r0=/cubic-bezier\(([0-9,\.e ]+)\)/;function ed(r){var t=r&&r0.exec(r);if(t){var e=t[1].split(","),i=+me(e[0]),n=+me(e[1]),a=+me(e[2]),o=+me(e[3]);if(isNaN(i+n+a+o))return;var s=[];return function(u){return u<=0?0:u>=1?1:Ya(0,i,a,1,u,s)&&gt(0,n,o,1,s[0])}}}var i0=function(){function r(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||Ot,this.ondestroy=t.ondestroy||Ot,this.onrestart=t.onrestart||Ot,t.easing&&this.setEasing(t.easing)}return r.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),this._paused){this._pausedTime+=e;return}var i=this._life,n=t-this._startTime-this._pausedTime,a=n/i;a<0&&(a=0),a=Math.min(a,1);var o=this.easingFunc,s=o?o(a):a;if(this.onframe(s),a===1)if(this.loop){var u=n%i;this._startTime=t-u,this._pausedTime=0,this.onrestart()}else return!0;return!1},r.prototype.pause=function(){this._paused=!0},r.prototype.resume=function(){this._paused=!1},r.prototype.setEasing=function(t){this.easing=t,this.easingFunc=U(t)?t:Kc[t]||ed(t)},r}();const n0=i0;var rd=function(){function r(t){this.value=t}return r}(),a0=function(){function r(){this._len=0}return r.prototype.insert=function(t){var e=new rd(t);return this.insertEntry(e),e},r.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},r.prototype.remove=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},r.prototype.len=function(){return this._len},r.prototype.clear=function(){this.head=this.tail=null,this._len=0},r}(),o0=function(){function r(t){this._list=new a0,this._maxSize=10,this._map={},this._maxSize=t}return r.prototype.put=function(t,e){var i=this._list,n=this._map,a=null;if(n[t]==null){var o=i.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var u=i.head;i.remove(u),delete n[u.key],a=u.value,this._lastRemovedEntry=u}s?s.value=e:s=new rd(e),s.key=t,i.insertEntry(s),n[t]=s}return a},r.prototype.get=function(t){var e=this._map[t],i=this._list;if(e!=null)return e!==i.tail&&(i.remove(e),i.insertEntry(e)),e.value},r.prototype.clear=function(){this._list.clear(),this._map={}},r.prototype.len=function(){return this._list.len()},r}();const Bn=o0;var bf={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function Qe(r){return r=Math.round(r),r<0?0:r>255?255:r}function su(r){return r<0?0:r>1?1:r}function $o(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?Qe(parseFloat(t)/100*255):Qe(parseInt(t,10))}function an(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?su(parseFloat(t)/100):su(parseFloat(t))}function Xo(r,t,e){return e<0?e+=1:e>1&&(e-=1),e*6<1?r+(t-r)*e*6:e*2<1?t:e*3<2?r+(t-r)*(2/3-e)*6:r}function Zn(r,t,e){return r+(t-r)*e}function Xt(r,t,e,i,n){return r[0]=t,r[1]=e,r[2]=i,r[3]=n,r}function uu(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r}var id=new Bn(20),qn=null;function Zr(r,t){qn&&uu(qn,t),qn=id.put(r,qn||t.slice())}function Je(r,t){if(r){t=t||[];var e=id.get(r);if(e)return uu(t,e);r=r+"";var i=r.replace(/ /g,"").toLowerCase();if(i in bf)return uu(t,bf[i]),Zr(r,t),t;var n=i.length;if(i.charAt(0)==="#"){if(n===4||n===5){var a=parseInt(i.slice(1,4),16);if(!(a>=0&&a<=4095)){Xt(t,0,0,0,1);return}return Xt(t,(a&3840)>>4|(a&3840)>>8,a&240|(a&240)>>4,a&15|(a&15)<<4,n===5?parseInt(i.slice(4),16)/15:1),Zr(r,t),t}else if(n===7||n===9){var a=parseInt(i.slice(1,7),16);if(!(a>=0&&a<=16777215)){Xt(t,0,0,0,1);return}return Xt(t,(a&16711680)>>16,(a&65280)>>8,a&255,n===9?parseInt(i.slice(7),16)/255:1),Zr(r,t),t}return}var o=i.indexOf("("),s=i.indexOf(")");if(o!==-1&&s+1===n){var u=i.substr(0,o),l=i.substr(o+1,s-(o+1)).split(","),f=1;switch(u){case"rgba":if(l.length!==4)return l.length===3?Xt(t,+l[0],+l[1],+l[2],1):Xt(t,0,0,0,1);f=an(l.pop());case"rgb":if(l.length>=3)return Xt(t,$o(l[0]),$o(l[1]),$o(l[2]),l.length===3?f:an(l[3])),Zr(r,t),t;Xt(t,0,0,0,1);return;case"hsla":if(l.length!==4){Xt(t,0,0,0,1);return}return l[3]=an(l[3]),xf(l,t),Zr(r,t),t;case"hsl":if(l.length!==3){Xt(t,0,0,0,1);return}return xf(l,t),Zr(r,t),t;default:return}}Xt(t,0,0,0,1)}}function xf(r,t){var e=(parseFloat(r[0])%360+360)%360/360,i=an(r[1]),n=an(r[2]),a=n<=.5?n*(i+1):n+i-n*i,o=n*2-a;return t=t||[],Xt(t,Qe(Xo(o,a,e+1/3)*255),Qe(Xo(o,a,e)*255),Qe(Xo(o,a,e-1/3)*255),1),r.length===4&&(t[3]=r[3]),t}function Tf(r,t){var e=Je(r);if(e){for(var i=0;i<3;i++)t<0?e[i]=e[i]*(1-t)|0:e[i]=(255-e[i])*t+e[i]|0,e[i]>255?e[i]=255:e[i]<0&&(e[i]=0);return ol(e,e.length===4?"rgba":"rgb")}}function s0(r,t,e){if(!(!(t&&t.length)||!(r>=0&&r<=1))){var i=r*(t.length-1),n=Math.floor(i),a=Math.ceil(i),o=Je(t[n]),s=Je(t[a]),u=i-n,l=ol([Qe(Zn(o[0],s[0],u)),Qe(Zn(o[1],s[1],u)),Qe(Zn(o[2],s[2],u)),su(Zn(o[3],s[3],u))],"rgba");return e?{color:l,leftIndex:n,rightIndex:a,value:i}:l}}function ol(r,t){if(!(!r||!r.length)){var e=r[0]+","+r[1]+","+r[2];return(t==="rgba"||t==="hsva"||t==="hsla")&&(e+=","+r[3]),t+"("+e+")"}}function Za(r,t){var e=Je(r);return e?(.299*e[0]+.587*e[1]+.114*e[2])*e[3]/255+(1-e[3])*t:0}var Cf=new Bn(100);function Mf(r){if(z(r)){var t=Cf.get(r);return t||(t=Tf(r,-.1),Cf.put(r,t)),t}else if(vo(r)){var e=k({},r);return e.colorStops=G(r.colorStops,function(i){return{offset:i.offset,color:Tf(i.color,-.1)}}),e}return r}function u0(r){return r.type==="linear"}function l0(r){return r.type==="radial"}(function(){return $.hasGlobalWindow&&U(window.btoa)?function(r){return window.btoa(unescape(encodeURIComponent(r)))}:typeof Buffer<"u"?function(r){return Buffer.from(r).toString("base64")}:function(r){return null}})();var lu=Array.prototype.slice;function De(r,t,e){return(t-r)*e+r}function Zo(r,t,e,i){for(var n=t.length,a=0;a<n;a++)r[a]=De(t[a],e[a],i);return r}function f0(r,t,e,i){for(var n=t.length,a=n&&t[0].length,o=0;o<n;o++){r[o]||(r[o]=[]);for(var s=0;s<a;s++)r[o][s]=De(t[o][s],e[o][s],i)}return r}function Kn(r,t,e,i){for(var n=t.length,a=0;a<n;a++)r[a]=t[a]+e[a]*i;return r}function Df(r,t,e,i){for(var n=t.length,a=n&&t[0].length,o=0;o<n;o++){r[o]||(r[o]=[]);for(var s=0;s<a;s++)r[o][s]=t[o][s]+e[o][s]*i}return r}function h0(r,t){for(var e=r.length,i=t.length,n=e>i?t:r,a=Math.min(e,i),o=n[a-1]||{color:[0,0,0,0],offset:0},s=a;s<Math.max(e,i);s++)n.push({offset:o.offset,color:o.color.slice()})}function v0(r,t,e){var i=r,n=t;if(!(!i.push||!n.push)){var a=i.length,o=n.length;if(a!==o){var s=a>o;if(s)i.length=o;else for(var u=a;u<o;u++)i.push(e===1?n[u]:lu.call(n[u]))}for(var l=i[0]&&i[0].length,u=0;u<i.length;u++)if(e===1)isNaN(i[u])&&(i[u]=n[u]);else for(var f=0;f<l;f++)isNaN(i[u][f])&&(i[u][f]=n[u][f])}}function Pa(r){if(Nt(r)){var t=r.length;if(Nt(r[0])){for(var e=[],i=0;i<t;i++)e.push(lu.call(r[i]));return e}return lu.call(r)}return r}function Ia(r){return r[0]=Math.floor(r[0])||0,r[1]=Math.floor(r[1])||0,r[2]=Math.floor(r[2])||0,r[3]=r[3]==null?1:r[3],"rgba("+r.join(",")+")"}function c0(r){return Nt(r&&r[0])?2:1}var Qn=0,Ea=1,nd=2,Qi=3,fu=4,hu=5,Af=6;function Lf(r){return r===fu||r===hu}function Jn(r){return r===Ea||r===nd}var ki=[0,0,0,0],d0=function(){function r(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return r.prototype.isFinished=function(){return this._finished},r.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},r.prototype.needsAnimate=function(){return this.keyframes.length>=1},r.prototype.getAdditiveTrack=function(){return this._additiveTrack},r.prototype.addKeyframe=function(t,e,i){this._needsSort=!0;var n=this.keyframes,a=n.length,o=!1,s=Af,u=e;if(Nt(e)){var l=c0(e);s=l,(l===1&&!vt(e[0])||l===2&&!vt(e[0][0]))&&(o=!0)}else if(vt(e)&&!Wa(e))s=Qn;else if(z(e))if(!isNaN(+e))s=Qn;else{var f=Je(e);f&&(u=f,s=Qi)}else if(vo(e)){var h=k({},u);h.colorStops=G(e.colorStops,function(v){return{offset:v.offset,color:Je(v.color)}}),u0(e)?s=fu:l0(e)&&(s=hu),u=h}a===0?this.valType=s:(s!==this.valType||s===Af)&&(o=!0),this.discrete=this.discrete||o;var c={time:t,value:u,rawValue:e,percent:0};return i&&(c.easing=i,c.easingFunc=U(i)?i:Kc[i]||ed(i)),n.push(c),c},r.prototype.prepare=function(t,e){var i=this.keyframes;this._needsSort&&i.sort(function(y,p){return y.time-p.time});for(var n=this.valType,a=i.length,o=i[a-1],s=this.discrete,u=Jn(n),l=Lf(n),f=0;f<a;f++){var h=i[f],c=h.value,v=o.value;h.percent=h.time/t,s||(u&&f!==a-1?v0(c,v,n):l&&h0(c.colorStops,v.colorStops))}if(!s&&n!==hu&&e&&this.needsAnimate()&&e.needsAnimate()&&n===e.valType&&!e._finished){this._additiveTrack=e;for(var d=i[0].value,f=0;f<a;f++)n===Qn?i[f].additiveValue=i[f].value-d:n===Qi?i[f].additiveValue=Kn([],i[f].value,d,-1):Jn(n)&&(i[f].additiveValue=n===Ea?Kn([],i[f].value,d,-1):Df([],i[f].value,d,-1))}},r.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var i=this._additiveTrack!=null,n=i?"additiveValue":"value",a=this.valType,o=this.keyframes,s=o.length,u=this.propName,l=a===Qi,f,h=this._lastFr,c=Math.min,v,d;if(s===1)v=d=o[0];else{if(e<0)f=0;else if(e<this._lastFrP){var y=c(h+1,s-1);for(f=y;f>=0&&!(o[f].percent<=e);f--);f=c(f,s-2)}else{for(f=h;f<s&&!(o[f].percent>e);f++);f=c(f-1,s-2)}d=o[f+1],v=o[f]}if(v&&d){this._lastFr=f,this._lastFrP=e;var p=d.percent-v.percent,g=p===0?1:c((e-v.percent)/p,1);d.easingFunc&&(g=d.easingFunc(g));var m=i?this._additiveValue:l?ki:t[u];if((Jn(a)||l)&&!m&&(m=this._additiveValue=[]),this.discrete)t[u]=g<1?v.rawValue:d.rawValue;else if(Jn(a))a===Ea?Zo(m,v[n],d[n],g):f0(m,v[n],d[n],g);else if(Lf(a)){var _=v[n],S=d[n],b=a===fu;t[u]={type:b?"linear":"radial",x:De(_.x,S.x,g),y:De(_.y,S.y,g),colorStops:G(_.colorStops,function(x,D){var T=S.colorStops[D];return{offset:De(x.offset,T.offset,g),color:Ia(Zo([],x.color,T.color,g))}}),global:S.global},b?(t[u].x2=De(_.x2,S.x2,g),t[u].y2=De(_.y2,S.y2,g)):t[u].r=De(_.r,S.r,g)}else if(l)Zo(m,v[n],d[n],g),i||(t[u]=Ia(m));else{var w=De(v[n],d[n],g);i?this._additiveValue=w:t[u]=w}i&&this._addToTarget(t)}}},r.prototype._addToTarget=function(t){var e=this.valType,i=this.propName,n=this._additiveValue;e===Qn?t[i]=t[i]+n:e===Qi?(Je(t[i],ki),Kn(ki,ki,n,1),t[i]=Ia(ki)):e===Ea?Kn(t[i],t[i],n,1):e===nd&&Df(t[i],t[i],n,1)},r}(),p0=function(){function r(t,e,i,n){if(this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&n){tl("Can' use additive animation on looped animation.");return}this._additiveAnimators=n,this._allowDiscrete=i}return r.prototype.getMaxTime=function(){return this._maxTime},r.prototype.getDelay=function(){return this._delay},r.prototype.getLoop=function(){return this._loop},r.prototype.getTarget=function(){return this._target},r.prototype.changeTarget=function(t){this._target=t},r.prototype.when=function(t,e,i){return this.whenWithKeys(t,e,lt(e),i)},r.prototype.whenWithKeys=function(t,e,i,n){for(var a=this._tracks,o=0;o<i.length;o++){var s=i[o],u=a[s];if(!u){u=a[s]=new d0(s);var l=void 0,f=this._getAdditiveTrack(s);if(f){var h=f.keyframes,c=h[h.length-1];l=c&&c.value,f.valType===Qi&&l&&(l=Ia(l))}else l=this._target[s];if(l==null)continue;t>0&&u.addKeyframe(0,Pa(l),n),this._trackKeys.push(s)}u.addKeyframe(t,Pa(e[s]),n)}return this._maxTime=Math.max(this._maxTime,t),this},r.prototype.pause=function(){this._clip.pause(),this._paused=!0},r.prototype.resume=function(){this._clip.resume(),this._paused=!1},r.prototype.isPaused=function(){return!!this._paused},r.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},r.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,i=0;i<e;i++)t[i].call(this)},r.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var i=0;i<e.length;i++)e[i].call(this)},r.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,i=0;i<e.length;i++)t[e[i]].setFinished()},r.prototype._getAdditiveTrack=function(t){var e,i=this._additiveAnimators;if(i)for(var n=0;n<i.length;n++){var a=i[n].getTrack(t);a&&(e=a)}return e},r.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,i=[],n=this._maxTime||0,a=0;a<this._trackKeys.length;a++){var o=this._trackKeys[a],s=this._tracks[o],u=this._getAdditiveTrack(o),l=s.keyframes,f=l.length;if(s.prepare(n,u),s.needsAnimate())if(!this._allowDiscrete&&s.discrete){var h=l[f-1];h&&(e._target[s.propName]=h.rawValue),s.setFinished()}else i.push(s)}if(i.length||this._force){var c=new n0({life:n,loop:this._loop,delay:this._delay||0,onframe:function(v){e._started=2;var d=e._additiveAnimators;if(d){for(var y=!1,p=0;p<d.length;p++)if(d[p]._clip){y=!0;break}y||(e._additiveAnimators=null)}for(var p=0;p<i.length;p++)i[p].step(e._target,v);var g=e._onframeCbs;if(g)for(var p=0;p<g.length;p++)g[p](e._target,v)},ondestroy:function(){e._doneCallback()}});this._clip=c,this.animation&&this.animation.addClip(c),t&&c.setEasing(t)}else this._doneCallback();return this}},r.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},r.prototype.delay=function(t){return this._delay=t,this},r.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},r.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},r.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},r.prototype.getClip=function(){return this._clip},r.prototype.getTrack=function(t){return this._tracks[t]},r.prototype.getTracks=function(){var t=this;return G(this._trackKeys,function(e){return t._tracks[e]})},r.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var i=this._tracks,n=this._trackKeys,a=0;a<t.length;a++){var o=i[t[a]];o&&!o.isFinished()&&(e?o.step(this._target,1):this._started===1&&o.step(this._target,0),o.setFinished())}for(var s=!0,a=0;a<n.length;a++)if(!i[n[a]].isFinished()){s=!1;break}return s&&this._abortedCallback(),s},r.prototype.saveTo=function(t,e,i){if(t){e=e||this._trackKeys;for(var n=0;n<e.length;n++){var a=e[n],o=this._tracks[a];if(!(!o||o.isFinished())){var s=o.keyframes,u=s[i?0:s.length-1];u&&(t[a]=Pa(u.rawValue))}}}},r.prototype.__changeFinalValue=function(t,e){e=e||lt(t);for(var i=0;i<e.length;i++){var n=e[i],a=this._tracks[n];if(a){var o=a.keyframes;if(o.length>1){var s=o.pop();a.addKeyframe(s.time,t[n]),a.prepare(this._maxTime,a.getAdditiveTrack())}}}},r}();const sl=p0;function hi(){return new Date().getTime()}var g0=function(r){O(t,r);function t(e){var i=r.call(this)||this;return i._running=!1,i._time=0,i._pausedTime=0,i._pauseStart=0,i._paused=!1,e=e||{},i.stage=e.stage||{},i}return t.prototype.addClip=function(e){e.animation&&this.removeClip(e),this._head?(this._tail.next=e,e.prev=this._tail,e.next=null,this._tail=e):this._head=this._tail=e,e.animation=this},t.prototype.addAnimator=function(e){e.animation=this;var i=e.getClip();i&&this.addClip(i)},t.prototype.removeClip=function(e){if(e.animation){var i=e.prev,n=e.next;i?i.next=n:this._head=n,n?n.prev=i:this._tail=i,e.next=e.prev=e.animation=null}},t.prototype.removeAnimator=function(e){var i=e.getClip();i&&this.removeClip(i),e.animation=null},t.prototype.update=function(e){for(var i=hi()-this._pausedTime,n=i-this._time,a=this._head;a;){var o=a.next,s=a.step(i,n);s&&(a.ondestroy(),this.removeClip(a)),a=o}this._time=i,e||(this.trigger("frame",n),this.stage.update&&this.stage.update())},t.prototype._startLoop=function(){var e=this;this._running=!0;function i(){e._running&&(ou(i),!e._paused&&e.update())}ou(i)},t.prototype.start=function(){this._running||(this._time=hi(),this._pausedTime=0,this._startLoop())},t.prototype.stop=function(){this._running=!1},t.prototype.pause=function(){this._paused||(this._pauseStart=hi(),this._paused=!0)},t.prototype.resume=function(){this._paused&&(this._pausedTime+=hi()-this._pauseStart,this._paused=!1)},t.prototype.clear=function(){for(var e=this._head;e;){var i=e.next;e.prev=e.next=e.animation=null,e=i}this._head=this._tail=null},t.prototype.isFinished=function(){return this._head==null},t.prototype.animate=function(e,i){i=i||{},this.start();var n=new sl(e,i.loop);return this.addAnimator(n),n},t}(xe);const y0=g0;var m0=300,qo=$.domSupported,Ko=function(){var r=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],t=["touchstart","touchend","touchmove"],e={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=G(r,function(n){var a=n.replace("mouse","pointer");return e.hasOwnProperty(a)?a:n});return{mouse:r,touch:t,pointer:i}}(),Pf={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},If=!1;function vu(r){var t=r.pointerType;return t==="pen"||t==="touch"}function _0(r){r.touching=!0,r.touchTimer!=null&&(clearTimeout(r.touchTimer),r.touchTimer=null),r.touchTimer=setTimeout(function(){r.touching=!1,r.touchTimer=null},700)}function Qo(r){r&&(r.zrByTouch=!0)}function S0(r,t){return Zt(r.dom,new w0(r,t),!0)}function ad(r,t){for(var e=t,i=!1;e&&e.nodeType!==9&&!(i=e.domBelongToZr||e!==t&&e===r.painterRoot);)e=e.parentNode;return i}var w0=function(){function r(t,e){this.stopPropagation=Ot,this.stopImmediatePropagation=Ot,this.preventDefault=Ot,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}return r}(),le={mousedown:function(r){r=Zt(this.dom,r),this.__mayPointerCapture=[r.zrX,r.zrY],this.trigger("mousedown",r)},mousemove:function(r){r=Zt(this.dom,r);var t=this.__mayPointerCapture;t&&(r.zrX!==t[0]||r.zrY!==t[1])&&this.__togglePointerCapture(!0),this.trigger("mousemove",r)},mouseup:function(r){r=Zt(this.dom,r),this.__togglePointerCapture(!1),this.trigger("mouseup",r)},mouseout:function(r){r=Zt(this.dom,r);var t=r.toElement||r.relatedTarget;ad(this,t)||(this.__pointerCapturing&&(r.zrEventControl="no_globalout"),this.trigger("mouseout",r))},wheel:function(r){If=!0,r=Zt(this.dom,r),this.trigger("mousewheel",r)},mousewheel:function(r){If||(r=Zt(this.dom,r),this.trigger("mousewheel",r))},touchstart:function(r){r=Zt(this.dom,r),Qo(r),this.__lastTouchMoment=new Date,this.handler.processGesture(r,"start"),le.mousemove.call(this,r),le.mousedown.call(this,r)},touchmove:function(r){r=Zt(this.dom,r),Qo(r),this.handler.processGesture(r,"change"),le.mousemove.call(this,r)},touchend:function(r){r=Zt(this.dom,r),Qo(r),this.handler.processGesture(r,"end"),le.mouseup.call(this,r),+new Date-+this.__lastTouchMoment<m0&&le.click.call(this,r)},pointerdown:function(r){le.mousedown.call(this,r)},pointermove:function(r){vu(r)||le.mousemove.call(this,r)},pointerup:function(r){le.mouseup.call(this,r)},pointerout:function(r){vu(r)||le.mouseout.call(this,r)}};M(["click","dblclick","contextmenu"],function(r){le[r]=function(t){t=Zt(this.dom,t),this.trigger(r,t)}});var cu={pointermove:function(r){vu(r)||cu.mousemove.call(this,r)},pointerup:function(r){cu.mouseup.call(this,r)},mousemove:function(r){this.trigger("mousemove",r)},mouseup:function(r){var t=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",r),t&&(r.zrEventControl="only_globalout",this.trigger("mouseout",r))}};function b0(r,t){var e=t.domHandlers;$.pointerEventsSupported?M(Ko.pointer,function(i){Ra(t,i,function(n){e[i].call(r,n)})}):($.touchEventsSupported&&M(Ko.touch,function(i){Ra(t,i,function(n){e[i].call(r,n),_0(t)})}),M(Ko.mouse,function(i){Ra(t,i,function(n){n=rl(n),t.touching||e[i].call(r,n)})}))}function x0(r,t){$.pointerEventsSupported?M(Pf.pointer,e):$.touchEventsSupported||M(Pf.mouse,e);function e(i){function n(a){a=rl(a),ad(r,a.target)||(a=S0(r,a),t.domHandlers[i].call(r,a))}Ra(t,i,n,{capture:!0})}}function Ra(r,t,e,i){r.mounted[t]=e,r.listenerOpts[t]=i,Em(r.domTarget,t,e,i)}function Jo(r){var t=r.mounted;for(var e in t)t.hasOwnProperty(e)&&Rm(r.domTarget,e,t[e],r.listenerOpts[e]);r.mounted={}}var Ef=function(){function r(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e}return r}(),T0=function(r){O(t,r);function t(e,i){var n=r.call(this)||this;return n.__pointerCapturing=!1,n.dom=e,n.painterRoot=i,n._localHandlerScope=new Ef(e,le),qo&&(n._globalHandlerScope=new Ef(document,cu)),b0(n,n._localHandlerScope),n}return t.prototype.dispose=function(){Jo(this._localHandlerScope),qo&&Jo(this._globalHandlerScope)},t.prototype.setCursor=function(e){this.dom.style&&(this.dom.style.cursor=e||"default")},t.prototype.__togglePointerCapture=function(e){if(this.__mayPointerCapture=null,qo&&+this.__pointerCapturing^+e){this.__pointerCapturing=e;var i=this._globalHandlerScope;e?x0(this,i):Jo(i)}},t}(xe);const C0=T0;var od=1;$.hasGlobalWindow&&(od=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var qa=od,du=.4,pu="#333",gu="#ccc",M0="#eee",Rf=il,kf=5e-5;function ur(r){return r>kf||r<-kf}var lr=[],qr=[],jo=pi(),ts=Math.abs,D0=function(){function r(){}return r.prototype.getLocalTransform=function(t){return r.getLocalTransform(this,t)},r.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},r.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},r.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},r.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},r.prototype.needLocalTransform=function(){return ur(this.rotation)||ur(this.x)||ur(this.y)||ur(this.scaleX-1)||ur(this.scaleY-1)||ur(this.skewX)||ur(this.skewY)},r.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),i=this.transform;if(!(e||t)){i&&(Rf(i),this.invTransform=null);return}i=i||pi(),e?this.getLocalTransform(i):Rf(i),t&&(e?gi(i,t,i):Bm(i,t)),this.transform=i,this._resolveGlobalScaleRatio(i)},r.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(e!=null&&e!==1){this.getGlobalScale(lr);var i=lr[0]<0?-1:1,n=lr[1]<0?-1:1,a=((lr[0]-i)*e+i)/lr[0]||0,o=((lr[1]-n)*e+n)/lr[1]||0;t[0]*=a,t[1]*=a,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||pi(),al(this.invTransform,t)},r.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},r.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],n=Math.atan2(t[1],t[0]),a=Math.PI/2+n-Math.atan2(t[3],t[2]);i=Math.sqrt(i)*Math.cos(a),e=Math.sqrt(e),this.skewX=a,this.skewY=0,this.rotation=-n,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=i,this.originX=0,this.originY=0}},r.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||pi(),gi(qr,t.invTransform,e),e=qr);var i=this.originX,n=this.originY;(i||n)&&(jo[4]=i,jo[5]=n,gi(qr,e,jo),qr[4]-=i,qr[5]-=n,e=qr),this.setLocalTransform(e)}},r.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},r.prototype.transformCoordToLocal=function(t,e){var i=[t,e],n=this.invTransform;return n&&re(i,i,n),i},r.prototype.transformCoordToGlobal=function(t,e){var i=[t,e],n=this.transform;return n&&re(i,i,n),i},r.prototype.getLineScale=function(){var t=this.transform;return t&&ts(t[0]-1)>1e-10&&ts(t[3]-1)>1e-10?Math.sqrt(ts(t[0]*t[3]-t[2]*t[1])):1},r.prototype.copyTransform=function(t){A0(this,t)},r.getLocalTransform=function(t,e){e=e||[];var i=t.originX||0,n=t.originY||0,a=t.scaleX,o=t.scaleY,s=t.anchorX,u=t.anchorY,l=t.rotation||0,f=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,v=t.skewY?Math.tan(-t.skewY):0;if(i||n||s||u){var d=i+s,y=n+u;e[4]=-d*a-c*y*o,e[5]=-y*o-v*d*a}else e[4]=e[5]=0;return e[0]=a,e[3]=o,e[1]=v*a,e[2]=c*o,l&&nl(e,e,l),e[4]+=i+f,e[5]+=n+h,e},r.initDefaultProps=function(){var t=r.prototype;t.scaleX=t.scaleY=t.globalScaleRatio=1,t.x=t.y=t.originX=t.originY=t.skewX=t.skewY=t.rotation=t.anchorX=t.anchorY=0}(),r}(),mn=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function A0(r,t){for(var e=0;e<mn.length;e++){var i=mn[e];r[i]=t[i]}}const ul=D0;var Of={};function Wt(r,t){t=t||Or;var e=Of[t];e||(e=Of[t]=new Bn(500));var i=e.get(r);return i==null&&(i=xi.measureText(r,t).width,e.put(r,i)),i}function Bf(r,t,e,i){var n=Wt(r,t),a=fl(t),o=Ji(0,n,e),s=ui(0,a,i),u=new J(o,s,n,a);return u}function ll(r,t,e,i){var n=((r||"")+"").split(`
`),a=n.length;if(a===1)return Bf(n[0],t,e,i);for(var o=new J(0,0,0,0),s=0;s<n.length;s++){var u=Bf(n[s],t,e,i);s===0?o.copy(u):o.union(u)}return o}function Ji(r,t,e){return e==="right"?r-=t:e==="center"&&(r-=t/2),r}function ui(r,t,e){return e==="middle"?r-=t/2:e==="bottom"&&(r-=t),r}function fl(r){return Wt("国",r)}function Br(r,t){return typeof r=="string"?r.lastIndexOf("%")>=0?parseFloat(r)/100*t:parseFloat(r):r}function sd(r,t,e){var i=t.position||"inside",n=t.distance!=null?t.distance:5,a=e.height,o=e.width,s=a/2,u=e.x,l=e.y,f="left",h="top";if(i instanceof Array)u+=Br(i[0],e.width),l+=Br(i[1],e.height),f=null,h=null;else switch(i){case"left":u-=n,l+=s,f="right",h="middle";break;case"right":u+=n+o,l+=s,h="middle";break;case"top":u+=o/2,l-=n,f="center",h="bottom";break;case"bottom":u+=o/2,l+=a+n,f="center";break;case"inside":u+=o/2,l+=s,f="center",h="middle";break;case"insideLeft":u+=n,l+=s,h="middle";break;case"insideRight":u+=o-n,l+=s,f="right",h="middle";break;case"insideTop":u+=o/2,l+=n,f="center";break;case"insideBottom":u+=o/2,l+=a-n,f="center",h="bottom";break;case"insideTopLeft":u+=n,l+=n;break;case"insideTopRight":u+=o-n,l+=n,f="right";break;case"insideBottomLeft":u+=n,l+=a-n,h="bottom";break;case"insideBottomRight":u+=o-n,l+=a-n,f="right",h="bottom";break}return r=r||{},r.x=u,r.y=l,r.align=f,r.verticalAlign=h,r}var es="__zr_normal__",rs=mn.concat(["ignore"]),L0=Ti(mn,function(r,t){return r[t]=!0,r},{ignore:!1}),Kr={},P0=new J(0,0,0,0),hl=function(){function r(t){this.id=Fc(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return r.prototype._init=function(t){this.attr(t)},r.prototype.drift=function(t,e,i){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0;break}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=e,this.decomposeTransform(),this.markRedraw()},r.prototype.beforeUpdate=function(){},r.prototype.afterUpdate=function(){},r.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},r.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var i=this.textConfig,n=i.local,a=e.innerTransformable,o=void 0,s=void 0,u=!1;a.parent=n?this:null;var l=!1;if(a.copyTransform(e),i.position!=null){var f=P0;i.layoutRect?f.copy(i.layoutRect):f.copy(this.getBoundingRect()),n||f.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Kr,i,f):sd(Kr,i,f),a.x=Kr.x,a.y=Kr.y,o=Kr.align,s=Kr.verticalAlign;var h=i.origin;if(h&&i.rotation!=null){var c=void 0,v=void 0;h==="center"?(c=f.width*.5,v=f.height*.5):(c=Br(h[0],f.width),v=Br(h[1],f.height)),l=!0,a.originX=-a.x+c+(n?0:f.x),a.originY=-a.y+v+(n?0:f.y)}}i.rotation!=null&&(a.rotation=i.rotation);var d=i.offset;d&&(a.x+=d[0],a.y+=d[1],l||(a.originX=-d[0],a.originY=-d[1]));var y=i.inside==null?typeof i.position=="string"&&i.position.indexOf("inside")>=0:i.inside,p=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),g=void 0,m=void 0,_=void 0;y&&this.canBeInsideText()?(g=i.insideFill,m=i.insideStroke,(g==null||g==="auto")&&(g=this.getInsideTextFill()),(m==null||m==="auto")&&(m=this.getInsideTextStroke(g),_=!0)):(g=i.outsideFill,m=i.outsideStroke,(g==null||g==="auto")&&(g=this.getOutsideFill()),(m==null||m==="auto")&&(m=this.getOutsideStroke(g),_=!0)),g=g||"#000",(g!==p.fill||m!==p.stroke||_!==p.autoStroke||o!==p.align||s!==p.verticalAlign)&&(u=!0,p.fill=g,p.stroke=m,p.autoStroke=_,p.align=o,p.verticalAlign=s,e.setDefaultTextStyle(p)),e.__dirty|=Gt,u&&e.dirtyStyle(!0)}},r.prototype.canBeInsideText=function(){return!0},r.prototype.getInsideTextFill=function(){return"#fff"},r.prototype.getInsideTextStroke=function(t){return"#000"},r.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?gu:pu},r.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),i=typeof e=="string"&&Je(e);i||(i=[255,255,255,1]);for(var n=i[3],a=this.__zr.isDarkMode(),o=0;o<3;o++)i[o]=i[o]*n+(a?0:255)*(1-n);return i[3]=1,ol(i,"rgba")},r.prototype.traverse=function(t,e){},r.prototype.attrKV=function(t,e){t==="textConfig"?this.setTextConfig(e):t==="textContent"?this.setTextContent(e):t==="clipPath"?this.setClipPath(e):t==="extra"?(this.extra=this.extra||{},k(this.extra,e)):this[t]=e},r.prototype.hide=function(){this.ignore=!0,this.markRedraw()},r.prototype.show=function(){this.ignore=!1,this.markRedraw()},r.prototype.attr=function(t,e){if(typeof t=="string")this.attrKV(t,e);else if(H(t))for(var i=t,n=lt(i),a=0;a<n.length;a++){var o=n[a];this.attrKV(o,t[o])}return this.markRedraw(),this},r.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,i=0;i<this.animators.length;i++){var n=this.animators[i],a=n.__fromStateTransition;if(!(n.getLoop()||a&&a!==es)){var o=n.targetName,s=o?e[o]:e;n.saveTo(s)}}},r.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,rs)},r.prototype._savePrimaryToNormal=function(t,e,i){for(var n=0;n<i.length;n++){var a=i[n];t[a]!=null&&!(a in e)&&(e[a]=this[a])}},r.prototype.hasState=function(){return this.currentStates.length>0},r.prototype.getState=function(t){return this.states[t]},r.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},r.prototype.clearStates=function(t){this.useState(es,!1,t)},r.prototype.useState=function(t,e,i,n){var a=t===es,o=this.hasState();if(!(!o&&a)){var s=this.currentStates,u=this.stateTransition;if(!(nt(s,t)>=0&&(e||s.length===1))){var l;if(this.stateProxy&&!a&&(l=this.stateProxy(t)),l||(l=this.states&&this.states[t]),!l&&!a){tl("State "+t+" not exists.");return}a||this.saveCurrentToNormalState(l);var f=!!(l&&l.hoverLayer||n);f&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,l,this._normalState,e,!i&&!this.__inHover&&u&&u.duration>0,u);var h=this._textContent,c=this._textGuide;return h&&h.useState(t,e,i,f),c&&c.useState(t,e,i,f),a?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!f&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~Gt),l}}},r.prototype.useStates=function(t,e,i){if(!t.length)this.clearStates();else{var n=[],a=this.currentStates,o=t.length,s=o===a.length;if(s){for(var u=0;u<o;u++)if(t[u]!==a[u]){s=!1;break}}if(s)return;for(var u=0;u<o;u++){var l=t[u],f=void 0;this.stateProxy&&(f=this.stateProxy(l,t)),f||(f=this.states[l]),f&&n.push(f)}var h=n[o-1],c=!!(h&&h.hoverLayer||i);c&&this._toggleHoverLayerFlag(!0);var v=this._mergeStates(n),d=this.stateTransition;this.saveCurrentToNormalState(v),this._applyStateObj(t.join(","),v,this._normalState,!1,!e&&!this.__inHover&&d&&d.duration>0,d);var y=this._textContent,p=this._textGuide;y&&y.useStates(t,e,c),p&&p.useStates(t,e,c),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!c&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~Gt)}},r.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},r.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},r.prototype.removeState=function(t){var e=nt(this.currentStates,t);if(e>=0){var i=this.currentStates.slice();i.splice(e,1),this.useStates(i)}},r.prototype.replaceState=function(t,e,i){var n=this.currentStates.slice(),a=nt(n,t),o=nt(n,e)>=0;a>=0?o?n.splice(a,1):n[a]=e:i&&!o&&n.push(e),this.useStates(n)},r.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},r.prototype._mergeStates=function(t){for(var e={},i,n=0;n<t.length;n++){var a=t[n];k(e,a),a.textConfig&&(i=i||{},k(i,a.textConfig))}return i&&(e.textConfig=i),e},r.prototype._applyStateObj=function(t,e,i,n,a,o){var s=!(e&&n);e&&e.textConfig?(this.textConfig=k({},n?this.textConfig:i.textConfig),k(this.textConfig,e.textConfig)):s&&i.textConfig&&(this.textConfig=i.textConfig);for(var u={},l=!1,f=0;f<rs.length;f++){var h=rs[f],c=a&&L0[h];e&&e[h]!=null?c?(l=!0,u[h]=e[h]):this[h]=e[h]:s&&i[h]!=null&&(c?(l=!0,u[h]=i[h]):this[h]=i[h])}if(!a)for(var f=0;f<this.animators.length;f++){var v=this.animators[f],d=v.targetName;v.getLoop()||v.__changeFinalValue(d?(e||i)[d]:e||i)}l&&this._transitionState(t,u,o)},r.prototype._attachComponent=function(t){if(!(t.__zr&&!t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},r.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},r.prototype.getClipPath=function(){return this._clipPath},r.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},r.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},r.prototype.getTextContent=function(){return this._textContent},r.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new ul,this._attachComponent(t),this._textContent=t,this.markRedraw())},r.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),k(this.textConfig,t),this.markRedraw()},r.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},r.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},r.prototype.getTextGuideLine=function(){return this._textGuide},r.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},r.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},r.prototype.markRedraw=function(){this.__dirty|=Gt;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},r.prototype.dirty=function(){this.markRedraw()},r.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,i=this._textGuide;e&&(e.__inHover=t),i&&(i.__inHover=t)},r.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.addAnimator(e[i]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.removeAnimator(e[i]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},r.prototype.animate=function(t,e,i){var n=t?this[t]:this,a=new sl(n,e,i);return t&&(a.targetName=t),this.addAnimator(a,t),a},r.prototype.addAnimator=function(t,e){var i=this.__zr,n=this;t.during(function(){n.updateDuringAnimation(e)}).done(function(){var a=n.animators,o=nt(a,t);o>=0&&a.splice(o,1)}),this.animators.push(t),i&&i.animation.addAnimator(t),i&&i.wakeUp()},r.prototype.updateDuringAnimation=function(t){this.markRedraw()},r.prototype.stopAnimation=function(t,e){for(var i=this.animators,n=i.length,a=[],o=0;o<n;o++){var s=i[o];!t||t===s.scope?s.stop(e):a.push(s)}return this.animators=a,this},r.prototype.animateTo=function(t,e,i){is(this,t,e,i)},r.prototype.animateFrom=function(t,e,i){is(this,t,e,i,!0)},r.prototype._transitionState=function(t,e,i,n){for(var a=is(this,e,i,n),o=0;o<a.length;o++)a[o].__fromStateTransition=t},r.prototype.getBoundingRect=function(){return null},r.prototype.getPaintRect=function(){return null},r.initDefaultProps=function(){var t=r.prototype;t.type="element",t.name="",t.ignore=t.silent=t.isGroup=t.draggable=t.dragging=t.ignoreClip=t.__inHover=!1,t.__dirty=Gt;function e(i,n,a,o){Object.defineProperty(t,i,{get:function(){if(!this[n]){var u=this[n]=[];s(this,u)}return this[n]},set:function(u){this[a]=u[0],this[o]=u[1],this[n]=u,s(this,u)}});function s(u,l){Object.defineProperty(l,0,{get:function(){return u[a]},set:function(f){u[a]=f}}),Object.defineProperty(l,1,{get:function(){return u[o]},set:function(f){u[o]=f}})}}Object.defineProperty&&(e("position","_legacyPos","x","y"),e("scale","_legacyScale","scaleX","scaleY"),e("origin","_legacyOrigin","originX","originY"))}(),r}();be(hl,xe);be(hl,ul);function is(r,t,e,i,n){e=e||{};var a=[];ud(r,"",r,t,e,i,a,n);var o=a.length,s=!1,u=e.done,l=e.aborted,f=function(){s=!0,o--,o<=0&&(s?u&&u():l&&l())},h=function(){o--,o<=0&&(s?u&&u():l&&l())};o||u&&u(),a.length>0&&e.during&&a[0].during(function(d,y){e.during(y)});for(var c=0;c<a.length;c++){var v=a[c];f&&v.done(f),h&&v.aborted(h),e.force&&v.duration(e.duration),v.start(e.easing)}return a}function ns(r,t,e){for(var i=0;i<e;i++)r[i]=t[i]}function I0(r){return Nt(r[0])}function E0(r,t,e){if(Nt(t[e]))if(Nt(r[e])||(r[e]=[]),Ft(t[e])){var i=t[e].length;r[e].length!==i&&(r[e]=new t[e].constructor(i),ns(r[e],t[e],i))}else{var n=t[e],a=r[e],o=n.length;if(I0(n))for(var s=n[0].length,u=0;u<o;u++)a[u]?ns(a[u],n[u],s):a[u]=Array.prototype.slice.call(n[u]);else ns(a,n,o);a.length=n.length}else r[e]=t[e]}function R0(r,t){return r===t||Nt(r)&&Nt(t)&&k0(r,t)}function k0(r,t){var e=r.length;if(e!==t.length)return!1;for(var i=0;i<e;i++)if(r[i]!==t[i])return!1;return!0}function ud(r,t,e,i,n,a,o,s){for(var u=lt(i),l=n.duration,f=n.delay,h=n.additive,c=n.setToFinal,v=!H(a),d=r.animators,y=[],p=0;p<u.length;p++){var g=u[p],m=i[g];if(m!=null&&e[g]!=null&&(v||a[g]))if(H(m)&&!Nt(m)&&!vo(m)){if(t){s||(e[g]=m,r.updateDuringAnimation(t));continue}ud(r,g,e[g],m,n,a&&a[g],o,s)}else y.push(g);else s||(e[g]=m,r.updateDuringAnimation(t),y.push(g))}var _=y.length;if(!h&&_)for(var S=0;S<d.length;S++){var b=d[S];if(b.targetName===t){var w=b.stopTracks(y);if(w){var x=nt(d,b);d.splice(x,1)}}}if(n.force||(y=Tt(y,function(A){return!R0(i[A],e[A])}),_=y.length),_>0||n.force&&!o.length){var D=void 0,T=void 0,C=void 0;if(s){T={},c&&(D={});for(var S=0;S<_;S++){var g=y[S];T[g]=e[g],c?D[g]=i[g]:e[g]=i[g]}}else if(c){C={};for(var S=0;S<_;S++){var g=y[S];C[g]=Pa(e[g]),E0(e,i,g)}}var b=new sl(e,!1,!1,h?Tt(d,function(L){return L.targetName===t}):null);b.targetName=t,n.scope&&(b.scope=n.scope),c&&D&&b.whenWithKeys(0,D,y),C&&b.whenWithKeys(0,C,y),b.whenWithKeys(l??500,s?T:i,y).delay(f||0),r.addAnimator(b,t),o.push(b)}}const ld=hl;var fd=function(r){O(t,r);function t(e){var i=r.call(this)||this;return i.isGroup=!0,i._children=[],i.attr(e),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.children=function(){return this._children.slice()},t.prototype.childAt=function(e){return this._children[e]},t.prototype.childOfName=function(e){for(var i=this._children,n=0;n<i.length;n++)if(i[n].name===e)return i[n]},t.prototype.childCount=function(){return this._children.length},t.prototype.add=function(e){return e&&e!==this&&e.parent!==this&&(this._children.push(e),this._doAdd(e)),this},t.prototype.addBefore=function(e,i){if(e&&e!==this&&e.parent!==this&&i&&i.parent===this){var n=this._children,a=n.indexOf(i);a>=0&&(n.splice(a,0,e),this._doAdd(e))}return this},t.prototype.replace=function(e,i){var n=nt(this._children,e);return n>=0&&this.replaceAt(i,n),this},t.prototype.replaceAt=function(e,i){var n=this._children,a=n[i];if(e&&e!==this&&e.parent!==this&&e!==a){n[i]=e,a.parent=null;var o=this.__zr;o&&a.removeSelfFromZr(o),this._doAdd(e)}return this},t.prototype._doAdd=function(e){e.parent&&e.parent.remove(e),e.parent=this;var i=this.__zr;i&&i!==e.__zr&&e.addSelfToZr(i),i&&i.refresh()},t.prototype.remove=function(e){var i=this.__zr,n=this._children,a=nt(n,e);return a<0?this:(n.splice(a,1),e.parent=null,i&&e.removeSelfFromZr(i),i&&i.refresh(),this)},t.prototype.removeAll=function(){for(var e=this._children,i=this.__zr,n=0;n<e.length;n++){var a=e[n];i&&a.removeSelfFromZr(i),a.parent=null}return e.length=0,this},t.prototype.eachChild=function(e,i){for(var n=this._children,a=0;a<n.length;a++){var o=n[a];e.call(i,o,a)}return this},t.prototype.traverse=function(e,i){for(var n=0;n<this._children.length;n++){var a=this._children[n],o=e.call(i,a);a.isGroup&&!o&&a.traverse(e,i)}return this},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.addSelfToZr(e)}},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.removeSelfFromZr(e)}},t.prototype.getBoundingRect=function(e){for(var i=new J(0,0,0,0),n=e||this._children,a=[],o=null,s=0;s<n.length;s++){var u=n[s];if(!(u.ignore||u.invisible)){var l=u.getBoundingRect(),f=u.getLocalTransform(a);f?(J.applyTransform(i,l,f),o=o||i.clone(),o.union(i)):(o=o||l.clone(),o.union(l))}}return o||i},t}(ld);fd.prototype.type="group";const Ut=fd;/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var ka={},hd={};function O0(r){delete hd[r]}function B0(r){if(!r)return!1;if(typeof r=="string")return Za(r,1)<du;if(r.colorStops){for(var t=r.colorStops,e=0,i=t.length,n=0;n<i;n++)e+=Za(t[n].color,1);return e/=i,e<du}return!1}var N0=function(){function r(t,e,i){var n=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,i=i||{},this.dom=e,this.id=t;var a=new Km,o=i.renderer||"canvas";ka[o]||(o=lt(ka)[0]),i.useDirtyRect=i.useDirtyRect==null?!1:i.useDirtyRect;var s=new ka[o](e,a,i,t),u=i.ssr||s.ssrOnly;this.storage=a,this.painter=s;var l=!$.node&&!$.worker&&!u?new C0(s.getViewportRoot(),s.root):null,f=i.useCoarsePointer,h=f==null||f==="auto"?$.touchEventsSupported:!!f,c=44,v;h&&(v=X(i.pointerSize,c)),this.handler=new Ym(a,s,l,s.root,v),this.animation=new y0({stage:{update:u?null:function(){return n._flush(!0)}}}),u||this.animation.start()}return r.prototype.add=function(t){this._disposed||!t||(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},r.prototype.remove=function(t){this._disposed||!t||(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},r.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},r.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=B0(t))},r.prototype.getBackgroundColor=function(){return this._backgroundColor},r.prototype.setDarkMode=function(t){this._darkMode=t},r.prototype.isDarkMode=function(){return this._darkMode},r.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},r.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},r.prototype.flush=function(){this._disposed||this._flush(!1)},r.prototype._flush=function(t){var e,i=hi();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var n=hi();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:n-i})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},r.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},r.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},r.prototype.refreshHover=function(){this._needsRefreshHover=!0},r.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.getType()==="canvas"&&this.painter.refreshHover())},r.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},r.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},r.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},r.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},r.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},r.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},r.prototype.on=function(t,e,i){return this._disposed||this.handler.on(t,e,i),this},r.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},r.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},r.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Ut&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},r.prototype.dispose=function(){this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,O0(this.id))},r}();function Nf(r,t){var e=new N0(Fc(),r,t);return hd[e.id]=e,e}function F0(r,t){ka[r]=t}var Ff=1e-4,vd=20;function z0(r){return r.replace(/^\s+|\s+$/g,"")}function zf(r,t,e,i){var n=t[0],a=t[1],o=e[0],s=e[1],u=a-n,l=s-o;if(u===0)return l===0?o:(o+s)/2;if(i)if(u>0){if(r<=n)return o;if(r>=a)return s}else{if(r>=n)return o;if(r<=a)return s}else{if(r===n)return o;if(r===a)return s}return(r-n)/u*l+o}function Dt(r,t){switch(r){case"center":case"middle":r="50%";break;case"left":case"top":r="0%";break;case"right":case"bottom":r="100%";break}return z(r)?z0(r).match(/%$/)?parseFloat(r)/100*t:parseFloat(r):r==null?NaN:+r}function pt(r,t,e){return t==null&&(t=10),t=Math.min(Math.max(0,t),vd),r=(+r).toFixed(t),e?r:+r}function Ae(r){if(r=+r,isNaN(r))return 0;if(r>1e-14){for(var t=1,e=0;e<15;e++,t*=10)if(Math.round(r*t)/t===r)return e}return H0(r)}function H0(r){var t=r.toString().toLowerCase(),e=t.indexOf("e"),i=e>0?+t.slice(e+1):0,n=e>0?e:t.length,a=t.indexOf("."),o=a<0?0:n-1-a;return Math.max(0,o-i)}function V0(r,t){var e=Math.log,i=Math.LN10,n=Math.floor(e(r[1]-r[0])/i),a=Math.round(e(Math.abs(t[1]-t[0]))/i),o=Math.min(Math.max(-n+a,0),20);return isFinite(o)?o:20}function G0(r,t){var e=Math.max(Ae(r),Ae(t)),i=r+t;return e>vd?i:pt(i,e)}function cd(r){var t=Math.PI*2;return(r%t+t)%t}function Ka(r){return r>-Ff&&r<Ff}var W0=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Ee(r){if(r instanceof Date)return r;if(z(r)){var t=W0.exec(r);if(!t)return new Date(NaN);if(t[8]){var e=+t[4]||0;return t[8].toUpperCase()!=="Z"&&(e-=+t[8].slice(0,3)),new Date(Date.UTC(+t[1],+(t[2]||1)-1,+t[3]||1,e,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0))}else return new Date(+t[1],+(t[2]||1)-1,+t[3]||1,+t[4]||0,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0)}else if(r==null)return new Date(NaN);return new Date(Math.round(r))}function U0(r){return Math.pow(10,vl(r))}function vl(r){if(r===0)return 0;var t=Math.floor(Math.log(r)/Math.LN10);return r/Math.pow(10,t)>=10&&t++,t}function dd(r,t){var e=vl(r),i=Math.pow(10,e),n=r/i,a;return t?n<1.5?a=1:n<2.5?a=2:n<4?a=3:n<7?a=5:a=10:n<1?a=1:n<2?a=2:n<3?a=3:n<5?a=5:a=10,r=a*i,e>=-20?+r.toFixed(e<0?-e:0):r}function Qa(r){var t=parseFloat(r);return t==r&&(t!==0||!z(r)||r.indexOf("x")<=0)?t:NaN}function Y0(r){return!isNaN(Qa(r))}function pd(){return Math.round(Math.random()*9)}function gd(r,t){return t===0?r:gd(t,r%t)}function Hf(r,t){return r==null?t:t==null?r:r*t/gd(r,t)}function Rt(r){throw new Error(r)}function Vf(r,t,e){return(t-r)*e+r}var yd="series\0",$0="\0_ec_\0";function Lt(r){return r instanceof Array?r:r==null?[]:[r]}function Gf(r,t,e){if(r){r[t]=r[t]||{},r.emphasis=r.emphasis||{},r.emphasis[t]=r.emphasis[t]||{};for(var i=0,n=e.length;i<n;i++){var a=e[i];!r.emphasis[t].hasOwnProperty(a)&&r[t].hasOwnProperty(a)&&(r.emphasis[t][a]=r[t][a])}}}var Wf=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function Nn(r){return H(r)&&!B(r)&&!(r instanceof Date)?r.value:r}function X0(r){return H(r)&&!(r instanceof Array)}function Z0(r,t,e){var i=e==="normalMerge",n=e==="replaceMerge",a=e==="replaceAll";r=r||[],t=(t||[]).slice();var o=Z();M(t,function(u,l){if(!H(u)){t[l]=null;return}});var s=q0(r,o,e);return(i||n)&&K0(s,r,o,t),i&&Q0(s,t),i||n?J0(s,t,n):a&&j0(s,t),t_(s),s}function q0(r,t,e){var i=[];if(e==="replaceAll")return i;for(var n=0;n<r.length;n++){var a=r[n];a&&a.id!=null&&t.set(a.id,n),i.push({existing:e==="replaceMerge"||_n(a)?null:a,newOption:null,keyInfo:null,brandNew:null})}return i}function K0(r,t,e,i){M(i,function(n,a){if(!(!n||n.id==null)){var o=on(n.id),s=e.get(o);if(s!=null){var u=r[s];Ie(!u.newOption,'Duplicated option on id "'+o+'".'),u.newOption=n,u.existing=t[s],i[a]=null}}})}function Q0(r,t){M(t,function(e,i){if(!(!e||e.name==null))for(var n=0;n<r.length;n++){var a=r[n].existing;if(!r[n].newOption&&a&&(a.id==null||e.id==null)&&!_n(e)&&!_n(a)&&md("name",a,e)){r[n].newOption=e,t[i]=null;return}}})}function J0(r,t,e){M(t,function(i){if(i){for(var n,a=0;(n=r[a])&&(n.newOption||_n(n.existing)||n.existing&&i.id!=null&&!md("id",i,n.existing));)a++;n?(n.newOption=i,n.brandNew=e):r.push({newOption:i,brandNew:e,existing:null,keyInfo:null}),a++}})}function j0(r,t){M(t,function(e){r.push({newOption:e,brandNew:!0,existing:null,keyInfo:null})})}function t_(r){var t=Z();M(r,function(e){var i=e.existing;i&&t.set(i.id,e)}),M(r,function(e){var i=e.newOption;Ie(!i||i.id==null||!t.get(i.id)||t.get(i.id)===e,"id duplicates: "+(i&&i.id)),i&&i.id!=null&&t.set(i.id,e),!e.keyInfo&&(e.keyInfo={})}),M(r,function(e,i){var n=e.existing,a=e.newOption,o=e.keyInfo;if(H(a)){if(o.name=a.name!=null?on(a.name):n?n.name:yd+i,n)o.id=on(n.id);else if(a.id!=null)o.id=on(a.id);else{var s=0;do o.id="\0"+o.name+"\0"+s++;while(t.get(o.id))}t.set(o.id,e)}})}function md(r,t,e){var i=Se(t[r],null),n=Se(e[r],null);return i!=null&&n!=null&&i===n}function on(r){return Se(r,"")}function Se(r,t){return r==null?t:z(r)?r:vt(r)||js(r)?r+"":t}function _d(r){var t=r.name;return!!(t&&t.indexOf(yd))}function _n(r){return r&&r.id!=null&&on(r.id).indexOf($0)===0}function e_(r,t,e){M(r,function(i){var n=i.newOption;H(n)&&(i.keyInfo.mainType=t,i.keyInfo.subType=r_(t,n,i.existing,e))})}function r_(r,t,e,i){var n=t.type?t.type:e?e.subType:i.determineSubType(r,t);return n}function Nr(r,t){if(t.dataIndexInside!=null)return t.dataIndexInside;if(t.dataIndex!=null)return B(t.dataIndex)?G(t.dataIndex,function(e){return r.indexOfRawIndex(e)}):r.indexOfRawIndex(t.dataIndex);if(t.name!=null)return B(t.name)?G(t.name,function(e){return r.indexOfName(e)}):r.indexOfName(t.name)}function yt(){var r="__ec_inner_"+i_++;return function(t){return t[r]||(t[r]={})}}var i_=pd();function as(r,t,e){var i=cl(t,e),n=i.mainTypeSpecified,a=i.queryOptionMap,o=i.others,s=o,u=e?e.defaultMainType:null;return!n&&u&&a.set(u,{}),a.each(function(l,f){var h=Fn(r,f,l,{useDefault:u===f,enableAll:e&&e.enableAll!=null?e.enableAll:!0,enableNone:e&&e.enableNone!=null?e.enableNone:!0});s[f+"Models"]=h.models,s[f+"Model"]=h.models[0]}),s}function cl(r,t){var e;if(z(r)){var i={};i[r+"Index"]=0,e=i}else e=r;var n=Z(),a={},o=!1;return M(e,function(s,u){if(u==="dataIndex"||u==="dataIndexInside"){a[u]=s;return}var l=u.match(/^(\w+)(Index|Id|Name)$/)||[],f=l[1],h=(l[2]||"").toLowerCase();if(!(!f||!h||t&&t.includeMainTypes&&nt(t.includeMainTypes,f)<0)){o=o||!!f;var c=n.get(f)||n.set(f,{});c[h]=s}}),{mainTypeSpecified:o,queryOptionMap:n,others:a}}var he={useDefault:!0,enableAll:!1,enableNone:!1};function Fn(r,t,e,i){i=i||he;var n=e.index,a=e.id,o=e.name,s={models:null,specified:n!=null||a!=null||o!=null};if(!s.specified){var u=void 0;return s.models=i.useDefault&&(u=r.getComponent(t))?[u]:[],s}return n==="none"||n===!1?(Ie(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),s.models=[],s):(n==="all"&&(Ie(i.enableAll,'`"all"` is not a valid value on index option.'),n=a=o=null),s.models=r.queryComponents({mainType:t,index:n,id:a,name:o}),s)}function Sd(r,t,e){r.setAttribute?r.setAttribute(t,e):r[t]=e}function n_(r,t){return r.getAttribute?r.getAttribute(t):r[t]}function a_(r){return r==="auto"?$.domSupported?"html":"richText":r||"html"}function o_(r,t,e,i,n){var a=t==null||t==="auto";if(i==null)return i;if(vt(i)){var o=Vf(e||0,i,n);return pt(o,a?Math.max(Ae(e||0),Ae(i)):t)}else{if(z(i))return n<1?e:i;for(var s=[],u=e,l=i,f=Math.max(u?u.length:0,l.length),h=0;h<f;++h){var c=r.getDimensionInfo(h);if(c&&c.type==="ordinal")s[h]=(n<1&&u?u:l)[h];else{var v=u&&u[h]?u[h]:0,d=l[h],o=Vf(v,d,n);s[h]=pt(o,a?Math.max(Ae(v),Ae(d)):t)}}return s}}var s_=".",fr="___EC__COMPONENT__CONTAINER___",wd="___EC__EXTENDED_CLASS___";function _e(r){var t={main:"",sub:""};if(r){var e=r.split(s_);t.main=e[0]||"",t.sub=e[1]||""}return t}function u_(r){Ie(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(r),'componentType "'+r+'" illegal')}function l_(r){return!!(r&&r[wd])}function dl(r,t){r.$constructor=r,r.extend=function(e){var i=this,n;return f_(i)?n=function(a){O(o,a);function o(){return a.apply(this,arguments)||this}return o}(i):(n=function(){(e.$constructor||i).apply(this,arguments)},am(n,this)),k(n.prototype,e),n[wd]=!0,n.extend=this.extend,n.superCall=c_,n.superApply=d_,n.superClass=i,n}}function f_(r){return U(r)&&/^class\s/.test(Function.prototype.toString.call(r))}function bd(r,t){r.extend=t.extend}var h_=Math.round(Math.random()*10);function v_(r){var t=["__\0is_clz",h_++].join("_");r.prototype[t]=!0,r.isInstance=function(e){return!!(e&&e[t])}}function c_(r,t){for(var e=[],i=2;i<arguments.length;i++)e[i-2]=arguments[i];return this.superClass.prototype[t].apply(r,e)}function d_(r,t,e){return this.superClass.prototype[t].apply(r,e)}function po(r){var t={};r.registerClass=function(i){var n=i.type||i.prototype.type;if(n){u_(n),i.prototype.type=n;var a=_e(n);if(!a.sub)t[a.main]=i;else if(a.sub!==fr){var o=e(a);o[a.sub]=i}}return i},r.getClass=function(i,n,a){var o=t[i];if(o&&o[fr]&&(o=n?o[n]:null),a&&!o)throw new Error(n?"Component "+i+"."+(n||"")+" is used but not imported.":i+".type should be specified.");return o},r.getClassesByMainType=function(i){var n=_e(i),a=[],o=t[n.main];return o&&o[fr]?M(o,function(s,u){u!==fr&&a.push(s)}):a.push(o),a},r.hasClass=function(i){var n=_e(i);return!!t[n.main]},r.getAllClassMainTypes=function(){var i=[];return M(t,function(n,a){i.push(a)}),i},r.hasSubTypes=function(i){var n=_e(i),a=t[n.main];return a&&a[fr]};function e(i){var n=t[i.main];return(!n||!n[fr])&&(n=t[i.main]={},n[fr]=!0),n}}function Sn(r,t){for(var e=0;e<r.length;e++)r[e][1]||(r[e][1]=r[e][0]);return t=t||!1,function(i,n,a){for(var o={},s=0;s<r.length;s++){var u=r[s][1];if(!(n&&nt(n,u)>=0||a&&nt(a,u)<0)){var l=i.getShallow(u,t);l!=null&&(o[r[s][0]]=l)}}return o}}var p_=[["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]],g_=Sn(p_),y_=function(){function r(){}return r.prototype.getAreaStyle=function(t,e){return g_(this,t,e)},r}(),yu=new Bn(50);function m_(r){if(typeof r=="string"){var t=yu.get(r);return t&&t.image}else return r}function xd(r,t,e,i,n){if(r)if(typeof r=="string"){if(t&&t.__zrImageSrc===r||!e)return t;var a=yu.get(r),o={hostEl:e,cb:i,cbPayload:n};return a?(t=a.image,!go(t)&&a.pending.push(o)):(t=xi.loadImage(r,Uf,Uf),t.__zrImageSrc=r,yu.put(r,t.__cachedImgObj={image:t,pending:[o]})),t}else return r;else return t}function Uf(){var r=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var t=0;t<r.pending.length;t++){var e=r.pending[t],i=e.cb;i&&i(this,e.cbPayload),e.hostEl.dirty()}r.pending.length=0}function go(r){return r&&r.width&&r.height}var os=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function __(r,t,e,i,n,a){if(!e){r.text="",r.isTruncated=!1;return}var o=(t+"").split(`
`);a=Td(e,i,n,a);for(var s=!1,u={},l=0,f=o.length;l<f;l++)Cd(u,o[l],a),o[l]=u.textLine,s=s||u.isTruncated;r.text=o.join(`
`),r.isTruncated=s}function Td(r,t,e,i){i=i||{};var n=k({},i);n.font=t,e=X(e,"..."),n.maxIterations=X(i.maxIterations,2);var a=n.minChar=X(i.minChar,0);n.cnCharWidth=Wt("国",t);var o=n.ascCharWidth=Wt("a",t);n.placeholder=X(i.placeholder,"");for(var s=r=Math.max(0,r-1),u=0;u<a&&s>=o;u++)s-=o;var l=Wt(e,t);return l>s&&(e="",l=0),s=r-l,n.ellipsis=e,n.ellipsisWidth=l,n.contentWidth=s,n.containerWidth=r,n}function Cd(r,t,e){var i=e.containerWidth,n=e.font,a=e.contentWidth;if(!i){r.textLine="",r.isTruncated=!1;return}var o=Wt(t,n);if(o<=i){r.textLine=t,r.isTruncated=!1;return}for(var s=0;;s++){if(o<=a||s>=e.maxIterations){t+=e.ellipsis;break}var u=s===0?S_(t,a,e.ascCharWidth,e.cnCharWidth):o>0?Math.floor(t.length*a/o):0;t=t.substr(0,u),o=Wt(t,n)}t===""&&(t=e.placeholder),r.textLine=t,r.isTruncated=!0}function S_(r,t,e,i){for(var n=0,a=0,o=r.length;a<o&&n<t;a++){var s=r.charCodeAt(a);n+=0<=s&&s<=127?e:i}return a}function w_(r,t){r!=null&&(r+="");var e=t.overflow,i=t.padding,n=t.font,a=e==="truncate",o=fl(n),s=X(t.lineHeight,o),u=!!t.backgroundColor,l=t.lineOverflow==="truncate",f=!1,h=t.width,c;h!=null&&(e==="break"||e==="breakAll")?c=r?Md(r,t.font,h,e==="breakAll",0).lines:[]:c=r?r.split(`
`):[];var v=c.length*s,d=X(t.height,v);if(v>d&&l){var y=Math.floor(d/s);f=f||c.length>y,c=c.slice(0,y)}if(r&&a&&h!=null)for(var p=Td(h,n,t.ellipsis,{minChar:t.truncateMinChar,placeholder:t.placeholder}),g={},m=0;m<c.length;m++)Cd(g,c[m],p),c[m]=g.textLine,f=f||g.isTruncated;for(var _=d,S=0,m=0;m<c.length;m++)S=Math.max(Wt(c[m],n),S);h==null&&(h=S);var b=S;return i&&(_+=i[0]+i[2],b+=i[1]+i[3],h+=i[1]+i[3]),u&&(b=h),{lines:c,height:d,outerWidth:b,outerHeight:_,lineHeight:s,calculatedLineHeight:o,contentWidth:S,contentHeight:v,width:h,isTruncated:f}}var b_=function(){function r(){}return r}(),Yf=function(){function r(t){this.tokens=[],t&&(this.tokens=t)}return r}(),x_=function(){function r(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1}return r}();function T_(r,t){var e=new x_;if(r!=null&&(r+=""),!r)return e;for(var i=t.width,n=t.height,a=t.overflow,o=(a==="break"||a==="breakAll")&&i!=null?{width:i,accumWidth:0,breakAll:a==="breakAll"}:null,s=os.lastIndex=0,u;(u=os.exec(r))!=null;){var l=u.index;l>s&&ss(e,r.substring(s,l),t,o),ss(e,u[2],t,o,u[1]),s=os.lastIndex}s<r.length&&ss(e,r.substring(s,r.length),t,o);var f=[],h=0,c=0,v=t.padding,d=a==="truncate",y=t.lineOverflow==="truncate",p={};function g(Y,et,j){Y.width=et,Y.lineHeight=j,h+=j,c=Math.max(c,et)}t:for(var m=0;m<e.lines.length;m++){for(var _=e.lines[m],S=0,b=0,w=0;w<_.tokens.length;w++){var x=_.tokens[w],D=x.styleName&&t.rich[x.styleName]||{},T=x.textPadding=D.padding,C=T?T[1]+T[3]:0,A=x.font=D.font||t.font;x.contentHeight=fl(A);var L=X(D.height,x.contentHeight);if(x.innerHeight=L,T&&(L+=T[0]+T[2]),x.height=L,x.lineHeight=Da(D.lineHeight,t.lineHeight,L),x.align=D&&D.align||t.align,x.verticalAlign=D&&D.verticalAlign||"middle",y&&n!=null&&h+x.lineHeight>n){var I=e.lines.length;w>0?(_.tokens=_.tokens.slice(0,w),g(_,b,S),e.lines=e.lines.slice(0,m+1)):e.lines=e.lines.slice(0,m),e.isTruncated=e.isTruncated||e.lines.length<I;break t}var P=D.width,E=P==null||P==="auto";if(typeof P=="string"&&P.charAt(P.length-1)==="%")x.percentWidth=P,f.push(x),x.contentWidth=Wt(x.text,A);else{if(E){var R=D.backgroundColor,V=R&&R.image;V&&(V=m_(V),go(V)&&(x.width=Math.max(x.width,V.width*L/V.height)))}var N=d&&i!=null?i-b:null;N!=null&&N<x.width?!E||N<C?(x.text="",x.width=x.contentWidth=0):(__(p,x.text,N-C,A,t.ellipsis,{minChar:t.truncateMinChar}),x.text=p.text,e.isTruncated=e.isTruncated||p.isTruncated,x.width=x.contentWidth=Wt(x.text,A)):x.contentWidth=Wt(x.text,A)}x.width+=C,b+=x.width,D&&(S=Math.max(S,x.lineHeight))}g(_,b,S)}e.outerWidth=e.width=X(i,c),e.outerHeight=e.height=X(n,h),e.contentHeight=h,e.contentWidth=c,v&&(e.outerWidth+=v[1]+v[3],e.outerHeight+=v[0]+v[2]);for(var m=0;m<f.length;m++){var x=f[m],F=x.percentWidth;x.width=parseInt(F,10)/100*e.width}return e}function ss(r,t,e,i,n){var a=t==="",o=n&&e.rich[n]||{},s=r.lines,u=o.font||e.font,l=!1,f,h;if(i){var c=o.padding,v=c?c[1]+c[3]:0;if(o.width!=null&&o.width!=="auto"){var d=Br(o.width,i.width)+v;s.length>0&&d+i.accumWidth>i.width&&(f=t.split(`
`),l=!0),i.accumWidth=d}else{var y=Md(t,u,i.width,i.breakAll,i.accumWidth);i.accumWidth=y.accumWidth+v,h=y.linesWidths,f=y.lines}}else f=t.split(`
`);for(var p=0;p<f.length;p++){var g=f[p],m=new b_;if(m.styleName=n,m.text=g,m.isLineHolder=!g&&!a,typeof o.width=="number"?m.width=o.width:m.width=h?h[p]:Wt(g,u),!p&&!l){var _=(s[s.length-1]||(s[0]=new Yf)).tokens,S=_.length;S===1&&_[0].isLineHolder?_[0]=m:(g||!S||a)&&_.push(m)}else s.push(new Yf([m]))}}function C_(r){var t=r.charCodeAt(0);return t>=32&&t<=591||t>=880&&t<=4351||t>=4608&&t<=5119||t>=7680&&t<=8303}var M_=Ti(",&?/;] ".split(""),function(r,t){return r[t]=!0,r},{});function D_(r){return C_(r)?!!M_[r]:!0}function Md(r,t,e,i,n){for(var a=[],o=[],s="",u="",l=0,f=0,h=0;h<r.length;h++){var c=r.charAt(h);if(c===`
`){u&&(s+=u,f+=l),a.push(s),o.push(f),s="",u="",l=0,f=0;continue}var v=Wt(c,t),d=i?!1:!D_(c);if(a.length?f+v>e:n+f+v>e){f?(s||u)&&(d?(s||(s=u,u="",l=0,f=l),a.push(s),o.push(f-l),u+=c,l+=v,s="",f=l):(u&&(s+=u,u="",l=0),a.push(s),o.push(f),s=c,f=v)):d?(a.push(u),o.push(l),u=c,l=v):(a.push(c),o.push(v));continue}f+=v,d?(u+=c,l+=v):(u&&(s+=u,u="",l=0),s+=c)}return!a.length&&!s&&(s=r,u="",l=0),u&&(s+=u),s&&(a.push(s),o.push(f)),a.length===1&&(f+=n),{accumWidth:f,lines:a,linesWidths:o}}var mu="__zr_style_"+Math.round(Math.random()*10),Ir={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},yo={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};Ir[mu]=!0;var $f=["z","z2","invisible"],A_=["invisible"],L_=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype._init=function(e){for(var i=lt(e),n=0;n<i.length;n++){var a=i[n];a==="style"?this.useStyle(e[a]):r.prototype.attrKV.call(this,a,e[a])}this.style||this.useStyle({})},t.prototype.beforeBrush=function(){},t.prototype.afterBrush=function(){},t.prototype.innerBeforeBrush=function(){},t.prototype.innerAfterBrush=function(){},t.prototype.shouldBePainted=function(e,i,n,a){var o=this.transform;if(this.ignore||this.invisible||this.style.opacity===0||this.culling&&P_(this,e,i)||o&&!o[0]&&!o[3])return!1;if(n&&this.__clipPaths){for(var s=0;s<this.__clipPaths.length;++s)if(this.__clipPaths[s].isZeroArea())return!1}if(a&&this.parent)for(var u=this.parent;u;){if(u.ignore)return!1;u=u.parent}return!0},t.prototype.contain=function(e,i){return this.rectContain(e,i)},t.prototype.traverse=function(e,i){e.call(i,this)},t.prototype.rectContain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect();return a.contain(n[0],n[1])},t.prototype.getPaintRect=function(){var e=this._paintRect;if(!this._paintRect||this.__dirty){var i=this.transform,n=this.getBoundingRect(),a=this.style,o=a.shadowBlur||0,s=a.shadowOffsetX||0,u=a.shadowOffsetY||0;e=this._paintRect||(this._paintRect=new J(0,0,0,0)),i?J.applyTransform(e,n,i):e.copy(n),(o||s||u)&&(e.width+=o*2+Math.abs(s),e.height+=o*2+Math.abs(u),e.x=Math.min(e.x,e.x+s-o),e.y=Math.min(e.y,e.y+u-o));var l=this.dirtyRectTolerance;e.isZero()||(e.x=Math.floor(e.x-l),e.y=Math.floor(e.y-l),e.width=Math.ceil(e.width+1+l*2),e.height=Math.ceil(e.height+1+l*2))}return e},t.prototype.setPrevPaintRect=function(e){e?(this._prevPaintRect=this._prevPaintRect||new J(0,0,0,0),this._prevPaintRect.copy(e)):this._prevPaintRect=null},t.prototype.getPrevPaintRect=function(){return this._prevPaintRect},t.prototype.animateStyle=function(e){return this.animate("style",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():this.markRedraw()},t.prototype.attrKV=function(e,i){e!=="style"?r.prototype.attrKV.call(this,e,i):this.style?this.setStyle(i):this.useStyle(i)},t.prototype.setStyle=function(e,i){return typeof e=="string"?this.style[e]=i:k(this.style,e),this.dirtyStyle(),this},t.prototype.dirtyStyle=function(e){e||this.markRedraw(),this.__dirty|=Ki,this._rect&&(this._rect=null)},t.prototype.dirty=function(){this.dirtyStyle()},t.prototype.styleChanged=function(){return!!(this.__dirty&Ki)},t.prototype.styleUpdated=function(){this.__dirty&=~Ki},t.prototype.createStyle=function(e){return co(Ir,e)},t.prototype.useStyle=function(e){e[mu]||(e=this.createStyle(e)),this.__inHover?this.__hoverStyle=e:this.style=e,this.dirtyStyle()},t.prototype.isStyleObject=function(e){return e[mu]},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var i=this._normalState;e.style&&!i.style&&(i.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,i,$f)},t.prototype._applyStateObj=function(e,i,n,a,o,s){r.prototype._applyStateObj.call(this,e,i,n,a,o,s);var u=!(i&&a),l;if(i&&i.style?o?a?l=i.style:(l=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(l,i.style)):(l=this._mergeStyle(this.createStyle(),a?this.style:n.style),this._mergeStyle(l,i.style)):u&&(l=n.style),l)if(o){var f=this.style;if(this.style=this.createStyle(u?{}:f),u)for(var h=lt(f),c=0;c<h.length;c++){var v=h[c];v in l&&(l[v]=l[v],this.style[v]=f[v])}for(var d=lt(l),c=0;c<d.length;c++){var v=d[c];this.style[v]=this.style[v]}this._transitionState(e,{style:l},s,this.getAnimationStyleProps())}else this.useStyle(l);for(var y=this.__inHover?A_:$f,c=0;c<y.length;c++){var v=y[c];i&&i[v]!=null?this[v]=i[v]:u&&n[v]!=null&&(this[v]=n[v])}},t.prototype._mergeStates=function(e){for(var i=r.prototype._mergeStates.call(this,e),n,a=0;a<e.length;a++){var o=e[a];o.style&&(n=n||{},this._mergeStyle(n,o.style))}return n&&(i.style=n),i},t.prototype._mergeStyle=function(e,i){return k(e,i),e},t.prototype.getAnimationStyleProps=function(){return yo},t.initDefaultProps=function(){var e=t.prototype;e.type="displayable",e.invisible=!1,e.z=0,e.z2=0,e.zlevel=0,e.culling=!1,e.cursor="pointer",e.rectHover=!1,e.incremental=!1,e._rect=null,e.dirtyRectTolerance=0,e.__dirty=Gt|Ki}(),t}(ld),us=new J(0,0,0,0),ls=new J(0,0,0,0);function P_(r,t,e){return us.copy(r.getBoundingRect()),r.transform&&us.applyTransform(r.transform),ls.width=t,ls.height=e,!us.intersect(ls)}const zn=L_;var Jt=Math.min,jt=Math.max,fs=Math.sin,hs=Math.cos,hr=Math.PI*2,jn=Ci(),ta=Ci(),ea=Ci();function Xf(r,t,e,i,n,a){n[0]=Jt(r,e),n[1]=Jt(t,i),a[0]=jt(r,e),a[1]=jt(t,i)}var Zf=[],qf=[];function I_(r,t,e,i,n,a,o,s,u,l){var f=jc,h=gt,c=f(r,e,n,o,Zf);u[0]=1/0,u[1]=1/0,l[0]=-1/0,l[1]=-1/0;for(var v=0;v<c;v++){var d=h(r,e,n,o,Zf[v]);u[0]=Jt(d,u[0]),l[0]=jt(d,l[0])}c=f(t,i,a,s,qf);for(var v=0;v<c;v++){var y=h(t,i,a,s,qf[v]);u[1]=Jt(y,u[1]),l[1]=jt(y,l[1])}u[0]=Jt(r,u[0]),l[0]=jt(r,l[0]),u[0]=Jt(o,u[0]),l[0]=jt(o,l[0]),u[1]=Jt(t,u[1]),l[1]=jt(t,l[1]),u[1]=Jt(s,u[1]),l[1]=jt(s,l[1])}function E_(r,t,e,i,n,a,o,s){var u=td,l=bt,f=jt(Jt(u(r,e,n),1),0),h=jt(Jt(u(t,i,a),1),0),c=l(r,e,n,f),v=l(t,i,a,h);o[0]=Jt(r,n,c),o[1]=Jt(t,a,v),s[0]=jt(r,n,c),s[1]=jt(t,a,v)}function R_(r,t,e,i,n,a,o,s,u){var l=li,f=fi,h=Math.abs(n-a);if(h%hr<1e-4&&h>1e-4){s[0]=r-e,s[1]=t-i,u[0]=r+e,u[1]=t+i;return}if(jn[0]=hs(n)*e+r,jn[1]=fs(n)*i+t,ta[0]=hs(a)*e+r,ta[1]=fs(a)*i+t,l(s,jn,ta),f(u,jn,ta),n=n%hr,n<0&&(n=n+hr),a=a%hr,a<0&&(a=a+hr),n>a&&!o?a+=hr:n<a&&o&&(n+=hr),o){var c=a;a=n,n=c}for(var v=0;v<a;v+=Math.PI/2)v>n&&(ea[0]=hs(v)*e+r,ea[1]=fs(v)*i+t,l(s,ea,s),f(u,ea,u))}var Q={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},vr=[],cr=[],ce=[],Be=[],de=[],pe=[],vs=Math.min,cs=Math.max,dr=Math.cos,pr=Math.sin,Ce=Math.abs,_u=Math.PI,$e=_u*2,ds=typeof Float32Array<"u",Oi=[];function ps(r){var t=Math.round(r/_u*1e8)/1e8;return t%2*_u}function k_(r,t){var e=ps(r[0]);e<0&&(e+=$e);var i=e-r[0],n=r[1];n+=i,!t&&n-e>=$e?n=e+$e:t&&e-n>=$e?n=e-$e:!t&&e>n?n=e+($e-ps(e-n)):t&&e<n&&(n=e-($e-ps(n-e))),r[0]=e,r[1]=n}var O_=function(){function r(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return r.prototype.increaseVersion=function(){this._version++},r.prototype.getVersion=function(){return this._version},r.prototype.setScale=function(t,e,i){i=i||0,i>0&&(this._ux=Ce(i/qa/t)||0,this._uy=Ce(i/qa/e)||0)},r.prototype.setDPR=function(t){this.dpr=t},r.prototype.setContext=function(t){this._ctx=t},r.prototype.getContext=function(){return this._ctx},r.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},r.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},r.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(Q.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},r.prototype.lineTo=function(t,e){var i=Ce(t-this._xi),n=Ce(e-this._yi),a=i>this._ux||n>this._uy;if(this.addData(Q.L,t,e),this._ctx&&a&&this._ctx.lineTo(t,e),a)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=i*i+n*n;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},r.prototype.bezierCurveTo=function(t,e,i,n,a,o){return this._drawPendingPt(),this.addData(Q.C,t,e,i,n,a,o),this._ctx&&this._ctx.bezierCurveTo(t,e,i,n,a,o),this._xi=a,this._yi=o,this},r.prototype.quadraticCurveTo=function(t,e,i,n){return this._drawPendingPt(),this.addData(Q.Q,t,e,i,n),this._ctx&&this._ctx.quadraticCurveTo(t,e,i,n),this._xi=i,this._yi=n,this},r.prototype.arc=function(t,e,i,n,a,o){this._drawPendingPt(),Oi[0]=n,Oi[1]=a,k_(Oi,o),n=Oi[0],a=Oi[1];var s=a-n;return this.addData(Q.A,t,e,i,i,n,s,0,o?0:1),this._ctx&&this._ctx.arc(t,e,i,n,a,o),this._xi=dr(a)*i+t,this._yi=pr(a)*i+e,this},r.prototype.arcTo=function(t,e,i,n,a){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,i,n,a),this},r.prototype.rect=function(t,e,i,n){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,i,n),this.addData(Q.R,t,e,i,n),this},r.prototype.closePath=function(){this._drawPendingPt(),this.addData(Q.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&t.closePath(),this._xi=e,this._yi=i,this},r.prototype.fill=function(t){t&&t.fill(),this.toStatic()},r.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},r.prototype.len=function(){return this._len},r.prototype.setData=function(t){var e=t.length;!(this.data&&this.data.length===e)&&ds&&(this.data=new Float32Array(e));for(var i=0;i<e;i++)this.data[i]=t[i];this._len=e},r.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,i=0,n=this._len,a=0;a<e;a++)i+=t[a].len();ds&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var a=0;a<e;a++)for(var o=t[a].data,s=0;s<o.length;s++)this.data[n++]=o[s];this._len=n},r.prototype.addData=function(t,e,i,n,a,o,s,u,l){if(this._saveData){var f=this.data;this._len+arguments.length>f.length&&(this._expandData(),f=this.data);for(var h=0;h<arguments.length;h++)f[this._len++]=arguments[h]}},r.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},r.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},r.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,ds&&this._len>11&&(this.data=new Float32Array(t)))}},r.prototype.getBoundingRect=function(){ce[0]=ce[1]=de[0]=de[1]=Number.MAX_VALUE,Be[0]=Be[1]=pe[0]=pe[1]=-Number.MAX_VALUE;var t=this.data,e=0,i=0,n=0,a=0,o;for(o=0;o<this._len;){var s=t[o++],u=o===1;switch(u&&(e=t[o],i=t[o+1],n=e,a=i),s){case Q.M:e=n=t[o++],i=a=t[o++],de[0]=n,de[1]=a,pe[0]=n,pe[1]=a;break;case Q.L:Xf(e,i,t[o],t[o+1],de,pe),e=t[o++],i=t[o++];break;case Q.C:I_(e,i,t[o++],t[o++],t[o++],t[o++],t[o],t[o+1],de,pe),e=t[o++],i=t[o++];break;case Q.Q:E_(e,i,t[o++],t[o++],t[o],t[o+1],de,pe),e=t[o++],i=t[o++];break;case Q.A:var l=t[o++],f=t[o++],h=t[o++],c=t[o++],v=t[o++],d=t[o++]+v;o+=1;var y=!t[o++];u&&(n=dr(v)*h+l,a=pr(v)*c+f),R_(l,f,h,c,v,d,y,de,pe),e=dr(d)*h+l,i=pr(d)*c+f;break;case Q.R:n=e=t[o++],a=i=t[o++];var p=t[o++],g=t[o++];Xf(n,a,n+p,a+g,de,pe);break;case Q.Z:e=n,i=a;break}li(ce,ce,de),fi(Be,Be,pe)}return o===0&&(ce[0]=ce[1]=Be[0]=Be[1]=0),new J(ce[0],ce[1],Be[0]-ce[0],Be[1]-ce[1])},r.prototype._calculateLength=function(){var t=this.data,e=this._len,i=this._ux,n=this._uy,a=0,o=0,s=0,u=0;this._pathSegLen||(this._pathSegLen=[]);for(var l=this._pathSegLen,f=0,h=0,c=0;c<e;){var v=t[c++],d=c===1;d&&(a=t[c],o=t[c+1],s=a,u=o);var y=-1;switch(v){case Q.M:a=s=t[c++],o=u=t[c++];break;case Q.L:{var p=t[c++],g=t[c++],m=p-a,_=g-o;(Ce(m)>i||Ce(_)>n||c===e-1)&&(y=Math.sqrt(m*m+_*_),a=p,o=g);break}case Q.C:{var S=t[c++],b=t[c++],p=t[c++],g=t[c++],w=t[c++],x=t[c++];y=Jm(a,o,S,b,p,g,w,x,10),a=w,o=x;break}case Q.Q:{var S=t[c++],b=t[c++],p=t[c++],g=t[c++];y=e0(a,o,S,b,p,g,10),a=p,o=g;break}case Q.A:var D=t[c++],T=t[c++],C=t[c++],A=t[c++],L=t[c++],I=t[c++],P=I+L;c+=1,d&&(s=dr(L)*C+D,u=pr(L)*A+T),y=cs(C,A)*vs($e,Math.abs(I)),a=dr(P)*C+D,o=pr(P)*A+T;break;case Q.R:{s=a=t[c++],u=o=t[c++];var E=t[c++],R=t[c++];y=E*2+R*2;break}case Q.Z:{var m=s-a,_=u-o;y=Math.sqrt(m*m+_*_),a=s,o=u;break}}y>=0&&(l[h++]=y,f+=y)}return this._pathLen=f,f},r.prototype.rebuildPath=function(t,e){var i=this.data,n=this._ux,a=this._uy,o=this._len,s,u,l,f,h,c,v=e<1,d,y,p=0,g=0,m,_=0,S,b;if(!(v&&(this._pathSegLen||this._calculateLength(),d=this._pathSegLen,y=this._pathLen,m=e*y,!m)))t:for(var w=0;w<o;){var x=i[w++],D=w===1;switch(D&&(l=i[w],f=i[w+1],s=l,u=f),x!==Q.L&&_>0&&(t.lineTo(S,b),_=0),x){case Q.M:s=l=i[w++],u=f=i[w++],t.moveTo(l,f);break;case Q.L:{h=i[w++],c=i[w++];var T=Ce(h-l),C=Ce(c-f);if(T>n||C>a){if(v){var A=d[g++];if(p+A>m){var L=(m-p)/A;t.lineTo(l*(1-L)+h*L,f*(1-L)+c*L);break t}p+=A}t.lineTo(h,c),l=h,f=c,_=0}else{var I=T*T+C*C;I>_&&(S=h,b=c,_=I)}break}case Q.C:{var P=i[w++],E=i[w++],R=i[w++],V=i[w++],N=i[w++],F=i[w++];if(v){var A=d[g++];if(p+A>m){var L=(m-p)/A;$a(l,P,R,N,L,vr),$a(f,E,V,F,L,cr),t.bezierCurveTo(vr[1],cr[1],vr[2],cr[2],vr[3],cr[3]);break t}p+=A}t.bezierCurveTo(P,E,R,V,N,F),l=N,f=F;break}case Q.Q:{var P=i[w++],E=i[w++],R=i[w++],V=i[w++];if(v){var A=d[g++];if(p+A>m){var L=(m-p)/A;Xa(l,P,R,L,vr),Xa(f,E,V,L,cr),t.quadraticCurveTo(vr[1],cr[1],vr[2],cr[2]);break t}p+=A}t.quadraticCurveTo(P,E,R,V),l=R,f=V;break}case Q.A:var Y=i[w++],et=i[w++],j=i[w++],st=i[w++],ut=i[w++],ct=i[w++],ae=i[w++],rr=!i[w++],$r=j>st?j:st,Ht=Ce(j-st)>.001,mt=ut+ct,W=!1;if(v){var A=d[g++];p+A>m&&(mt=ut+ct*(m-p)/A,W=!0),p+=A}if(Ht&&t.ellipse?t.ellipse(Y,et,j,st,ae,ut,mt,rr):t.arc(Y,et,$r,ut,mt,rr),W)break t;D&&(s=dr(ut)*j+Y,u=pr(ut)*st+et),l=dr(mt)*j+Y,f=pr(mt)*st+et;break;case Q.R:s=l=i[w],u=f=i[w+1],h=i[w++],c=i[w++];var q=i[w++],ir=i[w++];if(v){var A=d[g++];if(p+A>m){var Ct=m-p;t.moveTo(h,c),t.lineTo(h+vs(Ct,q),c),Ct-=q,Ct>0&&t.lineTo(h+q,c+vs(Ct,ir)),Ct-=ir,Ct>0&&t.lineTo(h+cs(q-Ct,0),c+ir),Ct-=q,Ct>0&&t.lineTo(h,c+cs(ir-Ct,0));break t}p+=A}t.rect(h,c,q,ir);break;case Q.Z:if(v){var A=d[g++];if(p+A>m){var L=(m-p)/A;t.lineTo(l*(1-L)+s*L,f*(1-L)+u*L);break t}p+=A}t.closePath(),l=s,f=u}}},r.prototype.clone=function(){var t=new r,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},r.CMD=Q,r.initDefaultProps=function(){var t=r.prototype;t._saveData=!0,t._ux=0,t._uy=0,t._pendingPtDist=0,t._version=0}(),r}();const Fr=O_;function Qr(r,t,e,i,n,a,o){if(n===0)return!1;var s=n,u=0,l=r;if(o>t+s&&o>i+s||o<t-s&&o<i-s||a>r+s&&a>e+s||a<r-s&&a<e-s)return!1;if(r!==e)u=(t-i)/(r-e),l=(r*i-e*t)/(r-e);else return Math.abs(a-r)<=s/2;var f=u*a-o+l,h=f*f/(u*u+1);return h<=s/2*s/2}function B_(r,t,e,i,n,a,o,s,u,l,f){if(u===0)return!1;var h=u;if(f>t+h&&f>i+h&&f>a+h&&f>s+h||f<t-h&&f<i-h&&f<a-h&&f<s-h||l>r+h&&l>e+h&&l>n+h&&l>o+h||l<r-h&&l<e-h&&l<n-h&&l<o-h)return!1;var c=Qm(r,t,e,i,n,a,o,s,l,f,null);return c<=h/2}function N_(r,t,e,i,n,a,o,s,u){if(o===0)return!1;var l=o;if(u>t+l&&u>i+l&&u>a+l||u<t-l&&u<i-l&&u<a-l||s>r+l&&s>e+l&&s>n+l||s<r-l&&s<e-l&&s<n-l)return!1;var f=t0(r,t,e,i,n,a,s,u,null);return f<=l/2}var Kf=Math.PI*2;function ra(r){return r%=Kf,r<0&&(r+=Kf),r}var Bi=Math.PI*2;function F_(r,t,e,i,n,a,o,s,u){if(o===0)return!1;var l=o;s-=r,u-=t;var f=Math.sqrt(s*s+u*u);if(f-l>e||f+l<e)return!1;if(Math.abs(i-n)%Bi<1e-4)return!0;if(a){var h=i;i=ra(n),n=ra(h)}else i=ra(i),n=ra(n);i>n&&(n+=Bi);var c=Math.atan2(u,s);return c<0&&(c+=Bi),c>=i&&c<=n||c+Bi>=i&&c+Bi<=n}function gr(r,t,e,i,n,a){if(a>t&&a>i||a<t&&a<i||i===t)return 0;var o=(a-t)/(i-t),s=i<t?1:-1;(o===1||o===0)&&(s=i<t?.5:-.5);var u=o*(e-r)+r;return u===n?1/0:u>n?s:0}var Ne=Fr.CMD,yr=Math.PI*2,z_=1e-4;function H_(r,t){return Math.abs(r-t)<z_}var Mt=[-1,-1,-1],Kt=[-1,-1];function V_(){var r=Kt[0];Kt[0]=Kt[1],Kt[1]=r}function G_(r,t,e,i,n,a,o,s,u,l){if(l>t&&l>i&&l>a&&l>s||l<t&&l<i&&l<a&&l<s)return 0;var f=Ya(t,i,a,s,l,Mt);if(f===0)return 0;for(var h=0,c=-1,v=void 0,d=void 0,y=0;y<f;y++){var p=Mt[y],g=p===0||p===1?.5:1,m=gt(r,e,n,o,p);m<u||(c<0&&(c=jc(t,i,a,s,Kt),Kt[1]<Kt[0]&&c>1&&V_(),v=gt(t,i,a,s,Kt[0]),c>1&&(d=gt(t,i,a,s,Kt[1]))),c===2?p<Kt[0]?h+=v<t?g:-g:p<Kt[1]?h+=d<v?g:-g:h+=s<d?g:-g:p<Kt[0]?h+=v<t?g:-g:h+=s<v?g:-g)}return h}function W_(r,t,e,i,n,a,o,s){if(s>t&&s>i&&s>a||s<t&&s<i&&s<a)return 0;var u=jm(t,i,a,s,Mt);if(u===0)return 0;var l=td(t,i,a);if(l>=0&&l<=1){for(var f=0,h=bt(t,i,a,l),c=0;c<u;c++){var v=Mt[c]===0||Mt[c]===1?.5:1,d=bt(r,e,n,Mt[c]);d<o||(Mt[c]<l?f+=h<t?v:-v:f+=a<h?v:-v)}return f}else{var v=Mt[0]===0||Mt[0]===1?.5:1,d=bt(r,e,n,Mt[0]);return d<o?0:a<t?v:-v}}function U_(r,t,e,i,n,a,o,s){if(s-=t,s>e||s<-e)return 0;var u=Math.sqrt(e*e-s*s);Mt[0]=-u,Mt[1]=u;var l=Math.abs(i-n);if(l<1e-4)return 0;if(l>=yr-1e-4){i=0,n=yr;var f=a?1:-1;return o>=Mt[0]+r&&o<=Mt[1]+r?f:0}if(i>n){var h=i;i=n,n=h}i<0&&(i+=yr,n+=yr);for(var c=0,v=0;v<2;v++){var d=Mt[v];if(d+r>o){var y=Math.atan2(s,d),f=a?1:-1;y<0&&(y=yr+y),(y>=i&&y<=n||y+yr>=i&&y+yr<=n)&&(y>Math.PI/2&&y<Math.PI*1.5&&(f=-f),c+=f)}}return c}function Dd(r,t,e,i,n){for(var a=r.data,o=r.len(),s=0,u=0,l=0,f=0,h=0,c,v,d=0;d<o;){var y=a[d++],p=d===1;switch(y===Ne.M&&d>1&&(e||(s+=gr(u,l,f,h,i,n))),p&&(u=a[d],l=a[d+1],f=u,h=l),y){case Ne.M:f=a[d++],h=a[d++],u=f,l=h;break;case Ne.L:if(e){if(Qr(u,l,a[d],a[d+1],t,i,n))return!0}else s+=gr(u,l,a[d],a[d+1],i,n)||0;u=a[d++],l=a[d++];break;case Ne.C:if(e){if(B_(u,l,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],t,i,n))return!0}else s+=G_(u,l,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],i,n)||0;u=a[d++],l=a[d++];break;case Ne.Q:if(e){if(N_(u,l,a[d++],a[d++],a[d],a[d+1],t,i,n))return!0}else s+=W_(u,l,a[d++],a[d++],a[d],a[d+1],i,n)||0;u=a[d++],l=a[d++];break;case Ne.A:var g=a[d++],m=a[d++],_=a[d++],S=a[d++],b=a[d++],w=a[d++];d+=1;var x=!!(1-a[d++]);c=Math.cos(b)*_+g,v=Math.sin(b)*S+m,p?(f=c,h=v):s+=gr(u,l,c,v,i,n);var D=(i-g)*S/_+g;if(e){if(F_(g,m,S,b,b+w,x,t,D,n))return!0}else s+=U_(g,m,S,b,b+w,x,D,n);u=Math.cos(b+w)*_+g,l=Math.sin(b+w)*S+m;break;case Ne.R:f=u=a[d++],h=l=a[d++];var T=a[d++],C=a[d++];if(c=f+T,v=h+C,e){if(Qr(f,h,c,h,t,i,n)||Qr(c,h,c,v,t,i,n)||Qr(c,v,f,v,t,i,n)||Qr(f,v,f,h,t,i,n))return!0}else s+=gr(c,h,c,v,i,n),s+=gr(f,v,f,h,i,n);break;case Ne.Z:if(e){if(Qr(u,l,f,h,t,i,n))return!0}else s+=gr(u,l,f,h,i,n);u=f,l=h;break}}return!e&&!H_(l,h)&&(s+=gr(u,l,f,h,i,n)||0),s!==0}function Y_(r,t,e){return Dd(r,0,!1,t,e)}function $_(r,t,e,i){return Dd(r,t,!0,e,i)}var Ad=tt({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},Ir),X_={style:tt({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},yo.style)},gs=mn.concat(["invisible","culling","z","z2","zlevel","parent"]),Z_=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.update=function(){var e=this;r.prototype.update.call(this);var i=this.style;if(i.decal){var n=this._decalEl=this._decalEl||new t;n.buildPath===t.prototype.buildPath&&(n.buildPath=function(u){e.buildPath(u,e.shape)}),n.silent=!0;var a=n.style;for(var o in i)a[o]!==i[o]&&(a[o]=i[o]);a.fill=i.fill?i.decal:null,a.decal=null,a.shadowColor=null,i.strokeFirst&&(a.stroke=null);for(var s=0;s<gs.length;++s)n[gs[s]]=this[gs[s]];n.__dirty|=Gt}else this._decalEl&&(this._decalEl=null)},t.prototype.getDecalElement=function(){return this._decalEl},t.prototype._init=function(e){var i=lt(e);this.shape=this.getDefaultShape();var n=this.getDefaultStyle();n&&this.useStyle(n);for(var a=0;a<i.length;a++){var o=i[a],s=e[o];o==="style"?this.style?k(this.style,s):this.useStyle(s):o==="shape"?k(this.shape,s):r.prototype.attrKV.call(this,o,s)}this.style||this.useStyle({})},t.prototype.getDefaultStyle=function(){return null},t.prototype.getDefaultShape=function(){return{}},t.prototype.canBeInsideText=function(){return this.hasFill()},t.prototype.getInsideTextFill=function(){var e=this.style.fill;if(e!=="none"){if(z(e)){var i=Za(e,0);return i>.5?pu:i>.2?M0:gu}else if(e)return gu}return pu},t.prototype.getInsideTextStroke=function(e){var i=this.style.fill;if(z(i)){var n=this.__zr,a=!!(n&&n.isDarkMode()),o=Za(e,0)<du;if(a===o)return i}},t.prototype.buildPath=function(e,i,n){},t.prototype.pathUpdated=function(){this.__dirty&=~si},t.prototype.getUpdatedPathProxy=function(e){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,e),this.path},t.prototype.createPathProxy=function(){this.path=new Fr(!1)},t.prototype.hasStroke=function(){var e=this.style,i=e.stroke;return!(i==null||i==="none"||!(e.lineWidth>0))},t.prototype.hasFill=function(){var e=this.style,i=e.fill;return i!=null&&i!=="none"},t.prototype.getBoundingRect=function(){var e=this._rect,i=this.style,n=!e;if(n){var a=!1;this.path||(a=!0,this.createPathProxy());var o=this.path;(a||this.__dirty&si)&&(o.beginPath(),this.buildPath(o,this.shape,!1),this.pathUpdated()),e=o.getBoundingRect()}if(this._rect=e,this.hasStroke()&&this.path&&this.path.len()>0){var s=this._rectStroke||(this._rectStroke=e.clone());if(this.__dirty||n){s.copy(e);var u=i.strokeNoScale?this.getLineScale():1,l=i.lineWidth;if(!this.hasFill()){var f=this.strokeContainThreshold;l=Math.max(l,f??4)}u>1e-10&&(s.width+=l/u,s.height+=l/u,s.x-=l/u/2,s.y-=l/u/2)}return s}return e},t.prototype.contain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect(),o=this.style;if(e=n[0],i=n[1],a.contain(e,i)){var s=this.path;if(this.hasStroke()){var u=o.lineWidth,l=o.strokeNoScale?this.getLineScale():1;if(l>1e-10&&(this.hasFill()||(u=Math.max(u,this.strokeContainThreshold)),$_(s,u/l,e,i)))return!0}if(this.hasFill())return Y_(s,e,i)}return!1},t.prototype.dirtyShape=function(){this.__dirty|=si,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},t.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},t.prototype.animateShape=function(e){return this.animate("shape",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():e==="shape"?this.dirtyShape():this.markRedraw()},t.prototype.attrKV=function(e,i){e==="shape"?this.setShape(i):r.prototype.attrKV.call(this,e,i)},t.prototype.setShape=function(e,i){var n=this.shape;return n||(n=this.shape={}),typeof e=="string"?n[e]=i:k(n,e),this.dirtyShape(),this},t.prototype.shapeChanged=function(){return!!(this.__dirty&si)},t.prototype.createStyle=function(e){return co(Ad,e)},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var i=this._normalState;e.shape&&!i.shape&&(i.shape=k({},this.shape))},t.prototype._applyStateObj=function(e,i,n,a,o,s){r.prototype._applyStateObj.call(this,e,i,n,a,o,s);var u=!(i&&a),l;if(i&&i.shape?o?a?l=i.shape:(l=k({},n.shape),k(l,i.shape)):(l=k({},a?this.shape:n.shape),k(l,i.shape)):u&&(l=n.shape),l)if(o){this.shape=k({},this.shape);for(var f={},h=lt(l),c=0;c<h.length;c++){var v=h[c];typeof l[v]=="object"?this.shape[v]=l[v]:f[v]=l[v]}this._transitionState(e,{shape:f},s)}else this.shape=l,this.dirtyShape()},t.prototype._mergeStates=function(e){for(var i=r.prototype._mergeStates.call(this,e),n,a=0;a<e.length;a++){var o=e[a];o.shape&&(n=n||{},this._mergeStyle(n,o.shape))}return n&&(i.shape=n),i},t.prototype.getAnimationStyleProps=function(){return X_},t.prototype.isZeroArea=function(){return!1},t.extend=function(e){var i=function(a){O(o,a);function o(s){var u=a.call(this,s)||this;return e.init&&e.init.call(u,s),u}return o.prototype.getDefaultStyle=function(){return K(e.style)},o.prototype.getDefaultShape=function(){return K(e.shape)},o}(t);for(var n in e)typeof e[n]=="function"&&(i.prototype[n]=e[n]);return i},t.initDefaultProps=function(){var e=t.prototype;e.type="path",e.strokeContainThreshold=5,e.segmentIgnoreThreshold=0,e.subPixelOptimize=!1,e.autoBatch=!1,e.__dirty=Gt|Ki|si}(),t}(zn);const ot=Z_;var q_=tt({strokeFirst:!0,font:Or,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},Ad),Ld=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.hasStroke=function(){var e=this.style,i=e.stroke;return i!=null&&i!=="none"&&e.lineWidth>0},t.prototype.hasFill=function(){var e=this.style,i=e.fill;return i!=null&&i!=="none"},t.prototype.createStyle=function(e){return co(q_,e)},t.prototype.setBoundingRect=function(e){this._rect=e},t.prototype.getBoundingRect=function(){var e=this.style;if(!this._rect){var i=e.text;i!=null?i+="":i="";var n=ll(i,e.font,e.textAlign,e.textBaseline);if(n.x+=e.x||0,n.y+=e.y||0,this.hasStroke()){var a=e.lineWidth;n.x-=a/2,n.y-=a/2,n.width+=a,n.height+=a}this._rect=n}return this._rect},t.initDefaultProps=function(){var e=t.prototype;e.dirtyRectTolerance=10}(),t}(zn);Ld.prototype.type="tspan";const Su=Ld;var K_=tt({x:0,y:0},Ir),Q_={style:tt({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},yo.style)};function J_(r){return!!(r&&typeof r!="string"&&r.width&&r.height)}var Pd=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.createStyle=function(e){return co(K_,e)},t.prototype._getSize=function(e){var i=this.style,n=i[e];if(n!=null)return n;var a=J_(i.image)?i.image:this.__image;if(!a)return 0;var o=e==="width"?"height":"width",s=i[o];return s==null?a[e]:a[e]/a[o]*s},t.prototype.getWidth=function(){return this._getSize("width")},t.prototype.getHeight=function(){return this._getSize("height")},t.prototype.getAnimationStyleProps=function(){return Q_},t.prototype.getBoundingRect=function(){var e=this.style;return this._rect||(this._rect=new J(e.x||0,e.y||0,this.getWidth(),this.getHeight())),this._rect},t}(zn);Pd.prototype.type="image";const Wr=Pd;function j_(r,t){var e=t.x,i=t.y,n=t.width,a=t.height,o=t.r,s,u,l,f;n<0&&(e=e+n,n=-n),a<0&&(i=i+a,a=-a),typeof o=="number"?s=u=l=f=o:o instanceof Array?o.length===1?s=u=l=f=o[0]:o.length===2?(s=l=o[0],u=f=o[1]):o.length===3?(s=o[0],u=f=o[1],l=o[2]):(s=o[0],u=o[1],l=o[2],f=o[3]):s=u=l=f=0;var h;s+u>n&&(h=s+u,s*=n/h,u*=n/h),l+f>n&&(h=l+f,l*=n/h,f*=n/h),u+l>a&&(h=u+l,u*=a/h,l*=a/h),s+f>a&&(h=s+f,s*=a/h,f*=a/h),r.moveTo(e+s,i),r.lineTo(e+n-u,i),u!==0&&r.arc(e+n-u,i+u,u,-Math.PI/2,0),r.lineTo(e+n,i+a-l),l!==0&&r.arc(e+n-l,i+a-l,l,0,Math.PI/2),r.lineTo(e+f,i+a),f!==0&&r.arc(e+f,i+a-f,f,Math.PI/2,Math.PI),r.lineTo(e,i+s),s!==0&&r.arc(e+s,i+s,s,Math.PI,Math.PI*1.5)}var vi=Math.round;function Id(r,t,e){if(t){var i=t.x1,n=t.x2,a=t.y1,o=t.y2;r.x1=i,r.x2=n,r.y1=a,r.y2=o;var s=e&&e.lineWidth;return s&&(vi(i*2)===vi(n*2)&&(r.x1=r.x2=Ar(i,s,!0)),vi(a*2)===vi(o*2)&&(r.y1=r.y2=Ar(a,s,!0))),r}}function Ed(r,t,e){if(t){var i=t.x,n=t.y,a=t.width,o=t.height;r.x=i,r.y=n,r.width=a,r.height=o;var s=e&&e.lineWidth;return s&&(r.x=Ar(i,s,!0),r.y=Ar(n,s,!0),r.width=Math.max(Ar(i+a,s,!1)-r.x,a===0?0:1),r.height=Math.max(Ar(n+o,s,!1)-r.y,o===0?0:1)),r}}function Ar(r,t,e){if(!t)return r;var i=vi(r*2);return(i+vi(t))%2===0?i/2:(i+(e?1:-1))/2}var t1=function(){function r(){this.x=0,this.y=0,this.width=0,this.height=0}return r}(),e1={},Rd=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new t1},t.prototype.buildPath=function(e,i){var n,a,o,s;if(this.subPixelOptimize){var u=Ed(e1,i,this.style);n=u.x,a=u.y,o=u.width,s=u.height,u.r=i.r,i=u}else n=i.x,a=i.y,o=i.width,s=i.height;i.r?j_(e,i):e.rect(n,a,o,s)},t.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},t}(ot);Rd.prototype.type="rect";const Bt=Rd;var Qf={fill:"#000"},Jf=2,r1={style:tt({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},yo.style)},kd=function(r){O(t,r);function t(e){var i=r.call(this)||this;return i.type="text",i._children=[],i._defaultStyle=Qf,i.attr(e),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.update=function(){r.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var i=this._children[e];i.zlevel=this.zlevel,i.z=this.z,i.z2=this.z2,i.culling=this.culling,i.cursor=this.cursor,i.invisible=this.invisible}},t.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):r.prototype.updateTransform.call(this)},t.prototype.getLocalTransform=function(e){var i=this.innerTransformable;return i?i.getLocalTransform(e):r.prototype.getLocalTransform.call(this,e)},t.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),r.prototype.getComputedTransform.call(this)},t.prototype._updateSubTexts=function(){this._childCursor=0,s1(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var i=0;i<this._children.length;i++)this._children[i].__zr=e},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var i=0;i<this._children.length;i++)this._children[i].__zr=null},t.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var e=new J(0,0,0,0),i=this._children,n=[],a=null,o=0;o<i.length;o++){var s=i[o],u=s.getBoundingRect(),l=s.getLocalTransform(n);l?(e.copy(u),e.applyTransform(l),a=a||e.clone(),a.union(e)):(a=a||u.clone(),a.union(u))}this._rect=a||e}return this._rect},t.prototype.setDefaultTextStyle=function(e){this._defaultStyle=e||Qf},t.prototype.setTextContent=function(e){},t.prototype._mergeStyle=function(e,i){if(!i)return e;var n=i.rich,a=e.rich||n&&{};return k(e,i),n&&a?(this._mergeRich(a,n),e.rich=a):a&&(e.rich=a),e},t.prototype._mergeRich=function(e,i){for(var n=lt(i),a=0;a<n.length;a++){var o=n[a];e[o]=e[o]||{},k(e[o],i[o])}},t.prototype.getAnimationStyleProps=function(){return r1},t.prototype._getOrCreateChild=function(e){var i=this._children[this._childCursor];return(!i||!(i instanceof e))&&(i=new e),this._children[this._childCursor++]=i,i.__zr=this.__zr,i.parent=this,i},t.prototype._updatePlainTexts=function(){var e=this.style,i=e.font||Or,n=e.padding,a=ah(e),o=w_(a,e),s=ys(e),u=!!e.backgroundColor,l=o.outerHeight,f=o.outerWidth,h=o.contentWidth,c=o.lines,v=o.lineHeight,d=this._defaultStyle;this.isTruncated=!!o.isTruncated;var y=e.x||0,p=e.y||0,g=e.align||d.align||"left",m=e.verticalAlign||d.verticalAlign||"top",_=y,S=ui(p,o.contentHeight,m);if(s||n){var b=Ji(y,f,g),w=ui(p,l,m);s&&this._renderBackground(e,e,b,w,f,l)}S+=v/2,n&&(_=nh(y,g,n),m==="top"?S+=n[0]:m==="bottom"&&(S-=n[2]));for(var x=0,D=!1,T=ih("fill"in e?e.fill:(D=!0,d.fill)),C=rh("stroke"in e?e.stroke:!u&&(!d.autoStroke||D)?(x=Jf,d.stroke):null),A=e.textShadowBlur>0,L=e.width!=null&&(e.overflow==="truncate"||e.overflow==="break"||e.overflow==="breakAll"),I=o.calculatedLineHeight,P=0;P<c.length;P++){var E=this._getOrCreateChild(Su),R=E.createStyle();E.useStyle(R),R.text=c[P],R.x=_,R.y=S,g&&(R.textAlign=g),R.textBaseline="middle",R.opacity=e.opacity,R.strokeFirst=!0,A&&(R.shadowBlur=e.textShadowBlur||0,R.shadowColor=e.textShadowColor||"transparent",R.shadowOffsetX=e.textShadowOffsetX||0,R.shadowOffsetY=e.textShadowOffsetY||0),R.stroke=C,R.fill=T,C&&(R.lineWidth=e.lineWidth||x,R.lineDash=e.lineDash,R.lineDashOffset=e.lineDashOffset||0),R.font=i,th(R,e),S+=v,L&&E.setBoundingRect(new J(Ji(R.x,h,R.textAlign),ui(R.y,I,R.textBaseline),h,I))}},t.prototype._updateRichTexts=function(){var e=this.style,i=ah(e),n=T_(i,e),a=n.width,o=n.outerWidth,s=n.outerHeight,u=e.padding,l=e.x||0,f=e.y||0,h=this._defaultStyle,c=e.align||h.align,v=e.verticalAlign||h.verticalAlign;this.isTruncated=!!n.isTruncated;var d=Ji(l,o,c),y=ui(f,s,v),p=d,g=y;u&&(p+=u[3],g+=u[0]);var m=p+a;ys(e)&&this._renderBackground(e,e,d,y,o,s);for(var _=!!e.backgroundColor,S=0;S<n.lines.length;S++){for(var b=n.lines[S],w=b.tokens,x=w.length,D=b.lineHeight,T=b.width,C=0,A=p,L=m,I=x-1,P=void 0;C<x&&(P=w[C],!P.align||P.align==="left");)this._placeToken(P,e,D,g,A,"left",_),T-=P.width,A+=P.width,C++;for(;I>=0&&(P=w[I],P.align==="right");)this._placeToken(P,e,D,g,L,"right",_),T-=P.width,L-=P.width,I--;for(A+=(a-(A-p)-(m-L)-T)/2;C<=I;)P=w[C],this._placeToken(P,e,D,g,A+P.width/2,"center",_),A+=P.width,C++;g+=D}},t.prototype._placeToken=function(e,i,n,a,o,s,u){var l=i.rich[e.styleName]||{};l.text=e.text;var f=e.verticalAlign,h=a+n/2;f==="top"?h=a+e.height/2:f==="bottom"&&(h=a+n-e.height/2);var c=!e.isLineHolder&&ys(l);c&&this._renderBackground(l,i,s==="right"?o-e.width:s==="center"?o-e.width/2:o,h-e.height/2,e.width,e.height);var v=!!l.backgroundColor,d=e.textPadding;d&&(o=nh(o,s,d),h-=e.height/2-d[0]-e.innerHeight/2);var y=this._getOrCreateChild(Su),p=y.createStyle();y.useStyle(p);var g=this._defaultStyle,m=!1,_=0,S=ih("fill"in l?l.fill:"fill"in i?i.fill:(m=!0,g.fill)),b=rh("stroke"in l?l.stroke:"stroke"in i?i.stroke:!v&&!u&&(!g.autoStroke||m)?(_=Jf,g.stroke):null),w=l.textShadowBlur>0||i.textShadowBlur>0;p.text=e.text,p.x=o,p.y=h,w&&(p.shadowBlur=l.textShadowBlur||i.textShadowBlur||0,p.shadowColor=l.textShadowColor||i.textShadowColor||"transparent",p.shadowOffsetX=l.textShadowOffsetX||i.textShadowOffsetX||0,p.shadowOffsetY=l.textShadowOffsetY||i.textShadowOffsetY||0),p.textAlign=s,p.textBaseline="middle",p.font=e.font||Or,p.opacity=Da(l.opacity,i.opacity,1),th(p,l),b&&(p.lineWidth=Da(l.lineWidth,i.lineWidth,_),p.lineDash=X(l.lineDash,i.lineDash),p.lineDashOffset=i.lineDashOffset||0,p.stroke=b),S&&(p.fill=S);var x=e.contentWidth,D=e.contentHeight;y.setBoundingRect(new J(Ji(p.x,x,p.textAlign),ui(p.y,D,p.textBaseline),x,D))},t.prototype._renderBackground=function(e,i,n,a,o,s){var u=e.backgroundColor,l=e.borderWidth,f=e.borderColor,h=u&&u.image,c=u&&!h,v=e.borderRadius,d=this,y,p;if(c||e.lineHeight||l&&f){y=this._getOrCreateChild(Bt),y.useStyle(y.createStyle()),y.style.fill=null;var g=y.shape;g.x=n,g.y=a,g.width=o,g.height=s,g.r=v,y.dirtyShape()}if(c){var m=y.style;m.fill=u||null,m.fillOpacity=X(e.fillOpacity,1)}else if(h){p=this._getOrCreateChild(Wr),p.onload=function(){d.dirtyStyle()};var _=p.style;_.image=u.image,_.x=n,_.y=a,_.width=o,_.height=s}if(l&&f){var m=y.style;m.lineWidth=l,m.stroke=f,m.strokeOpacity=X(e.strokeOpacity,1),m.lineDash=e.borderDash,m.lineDashOffset=e.borderDashOffset||0,y.strokeContainThreshold=0,y.hasFill()&&y.hasStroke()&&(m.strokeFirst=!0,m.lineWidth*=2)}var S=(y||p).style;S.shadowBlur=e.shadowBlur||0,S.shadowColor=e.shadowColor||"transparent",S.shadowOffsetX=e.shadowOffsetX||0,S.shadowOffsetY=e.shadowOffsetY||0,S.opacity=Da(e.opacity,i.opacity,1)},t.makeFont=function(e){var i="";return o1(e)&&(i=[e.fontStyle,e.fontWeight,a1(e.fontSize),e.fontFamily||"sans-serif"].join(" ")),i&&me(i)||e.textFont||e.font},t}(zn),i1={left:!0,right:1,center:1},n1={top:1,bottom:1,middle:1},jf=["fontStyle","fontWeight","fontSize","fontFamily"];function a1(r){return typeof r=="string"&&(r.indexOf("px")!==-1||r.indexOf("rem")!==-1||r.indexOf("em")!==-1)?r:isNaN(+r)?Qu+"px":r+"px"}function th(r,t){for(var e=0;e<jf.length;e++){var i=jf[e],n=t[i];n!=null&&(r[i]=n)}}function o1(r){return r.fontSize!=null||r.fontFamily||r.fontWeight}function s1(r){return eh(r),M(r.rich,eh),r}function eh(r){if(r){r.font=kd.makeFont(r);var t=r.align;t==="middle"&&(t="center"),r.align=t==null||i1[t]?t:"left";var e=r.verticalAlign;e==="center"&&(e="middle"),r.verticalAlign=e==null||n1[e]?e:"top";var i=r.padding;i&&(r.padding=zc(r.padding))}}function rh(r,t){return r==null||t<=0||r==="transparent"||r==="none"?null:r.image||r.colorStops?"#000":r}function ih(r){return r==null||r==="none"?null:r.image||r.colorStops?"#000":r}function nh(r,t,e){return t==="right"?r-e[1]:t==="center"?r+e[3]/2-e[1]/2:r+e[3]}function ah(r){var t=r.text;return t!=null&&(t+=""),t}function ys(r){return!!(r.backgroundColor||r.lineHeight||r.borderWidth&&r.borderColor)}const ie=kd;var it=yt(),u1=function(r,t,e,i){if(i){var n=it(i);n.dataIndex=e,n.dataType=t,n.seriesIndex=r,n.ssrType="chart",i.type==="group"&&i.traverse(function(a){var o=it(a);o.seriesIndex=r,o.dataIndex=e,o.dataType=t,o.ssrType="chart"})}},oh=1,sh={},Od=yt(),pl=yt(),gl=0,mo=1,_o=2,we=["emphasis","blur","select"],uh=["normal","emphasis","blur","select"],l1=10,f1=9,Er="highlight",Oa="downplay",sn="select",Ba="unselect",un="toggleSelect";function Jr(r){return r!=null&&r!=="none"}function So(r,t,e){r.onHoverStateChange&&(r.hoverState||0)!==e&&r.onHoverStateChange(t),r.hoverState=e}function Bd(r){So(r,"emphasis",_o)}function Nd(r){r.hoverState===_o&&So(r,"normal",gl)}function yl(r){So(r,"blur",mo)}function Fd(r){r.hoverState===mo&&So(r,"normal",gl)}function h1(r){r.selected=!0}function v1(r){r.selected=!1}function lh(r,t,e){t(r,e)}function Re(r,t,e){lh(r,t,e),r.isGroup&&r.traverse(function(i){lh(i,t,e)})}function fh(r,t){switch(t){case"emphasis":r.hoverState=_o;break;case"normal":r.hoverState=gl;break;case"blur":r.hoverState=mo;break;case"select":r.selected=!0}}function c1(r,t,e,i){for(var n=r.style,a={},o=0;o<t.length;o++){var s=t[o],u=n[s];a[s]=u??(i&&i[s])}for(var o=0;o<r.animators.length;o++){var l=r.animators[o];l.__fromStateTransition&&l.__fromStateTransition.indexOf(e)<0&&l.targetName==="style"&&l.saveTo(a,t)}return a}function d1(r,t,e,i){var n=e&&nt(e,"select")>=0,a=!1;if(r instanceof ot){var o=Od(r),s=n&&o.selectFill||o.normalFill,u=n&&o.selectStroke||o.normalStroke;if(Jr(s)||Jr(u)){i=i||{};var l=i.style||{};l.fill==="inherit"?(a=!0,i=k({},i),l=k({},l),l.fill=s):!Jr(l.fill)&&Jr(s)?(a=!0,i=k({},i),l=k({},l),l.fill=Mf(s)):!Jr(l.stroke)&&Jr(u)&&(a||(i=k({},i),l=k({},l)),l.stroke=Mf(u)),i.style=l}}if(i&&i.z2==null){a||(i=k({},i));var f=r.z2EmphasisLift;i.z2=r.z2+(f??l1)}return i}function p1(r,t,e){if(e&&e.z2==null){e=k({},e);var i=r.z2SelectLift;e.z2=r.z2+(i??f1)}return e}function g1(r,t,e){var i=nt(r.currentStates,t)>=0,n=r.style.opacity,a=i?null:c1(r,["opacity"],t,{opacity:1});e=e||{};var o=e.style||{};return o.opacity==null&&(e=k({},e),o=k({opacity:i?n:a.opacity*.1},o),e.style=o),e}function ms(r,t){var e=this.states[r];if(this.style){if(r==="emphasis")return d1(this,r,t,e);if(r==="blur")return g1(this,r,e);if(r==="select")return p1(this,r,e)}return e}function y1(r){r.stateProxy=ms;var t=r.getTextContent(),e=r.getTextGuideLine();t&&(t.stateProxy=ms),e&&(e.stateProxy=ms)}function hh(r,t){!Gd(r,t)&&!r.__highByOuter&&Re(r,Bd)}function vh(r,t){!Gd(r,t)&&!r.__highByOuter&&Re(r,Nd)}function Ja(r,t){r.__highByOuter|=1<<(t||0),Re(r,Bd)}function ja(r,t){!(r.__highByOuter&=~(1<<(t||0)))&&Re(r,Nd)}function m1(r){Re(r,yl)}function zd(r){Re(r,Fd)}function Hd(r){Re(r,h1)}function Vd(r){Re(r,v1)}function Gd(r,t){return r.__highDownSilentOnTouch&&t.zrByTouch}function Wd(r){var t=r.getModel(),e=[],i=[];t.eachComponent(function(n,a){var o=pl(a),s=n==="series",u=s?r.getViewOfSeriesModel(a):r.getViewOfComponentModel(a);!s&&i.push(u),o.isBlured&&(u.group.traverse(function(l){Fd(l)}),s&&e.push(a)),o.isBlured=!1}),M(i,function(n){n&&n.toggleBlurSeries&&n.toggleBlurSeries(e,!1,t)})}function wu(r,t,e,i){var n=i.getModel();e=e||"coordinateSystem";function a(l,f){for(var h=0;h<f.length;h++){var c=l.getItemGraphicEl(f[h]);c&&zd(c)}}if(r!=null&&!(!t||t==="none")){var o=n.getSeriesByIndex(r),s=o.coordinateSystem;s&&s.master&&(s=s.master);var u=[];n.eachSeries(function(l){var f=o===l,h=l.coordinateSystem;h&&h.master&&(h=h.master);var c=h&&s?h===s:f;if(!(e==="series"&&!f||e==="coordinateSystem"&&!c||t==="series"&&f)){var v=i.getViewOfSeriesModel(l);if(v.group.traverse(function(p){p.__highByOuter&&f&&t==="self"||yl(p)}),Nt(t))a(l.getData(),t);else if(H(t))for(var d=lt(t),y=0;y<d.length;y++)a(l.getData(d[y]),t[d[y]]);u.push(l),pl(l).isBlured=!0}}),n.eachComponent(function(l,f){if(l!=="series"){var h=i.getViewOfComponentModel(f);h&&h.toggleBlurSeries&&h.toggleBlurSeries(u,!0,n)}})}}function bu(r,t,e){if(!(r==null||t==null)){var i=e.getModel().getComponent(r,t);if(i){pl(i).isBlured=!0;var n=e.getViewOfComponentModel(i);!n||!n.focusBlurEnabled||n.group.traverse(function(a){yl(a)})}}}function _1(r,t,e){var i=r.seriesIndex,n=r.getData(t.dataType);if(n){var a=Nr(n,t);a=(B(a)?a[0]:a)||0;var o=n.getItemGraphicEl(a);if(!o)for(var s=n.count(),u=0;!o&&u<s;)o=n.getItemGraphicEl(u++);if(o){var l=it(o);wu(i,l.focus,l.blurScope,e)}else{var f=r.get(["emphasis","focus"]),h=r.get(["emphasis","blurScope"]);f!=null&&wu(i,f,h,e)}}}function ml(r,t,e,i){var n={focusSelf:!1,dispatchers:null};if(r==null||r==="series"||t==null||e==null)return n;var a=i.getModel().getComponent(r,t);if(!a)return n;var o=i.getViewOfComponentModel(a);if(!o||!o.findHighDownDispatchers)return n;for(var s=o.findHighDownDispatchers(e),u,l=0;l<s.length;l++)if(it(s[l]).focus==="self"){u=!0;break}return{focusSelf:u,dispatchers:s}}function S1(r,t,e){var i=it(r),n=ml(i.componentMainType,i.componentIndex,i.componentHighDownName,e),a=n.dispatchers,o=n.focusSelf;a?(o&&bu(i.componentMainType,i.componentIndex,e),M(a,function(s){return hh(s,t)})):(wu(i.seriesIndex,i.focus,i.blurScope,e),i.focus==="self"&&bu(i.componentMainType,i.componentIndex,e),hh(r,t))}function w1(r,t,e){Wd(e);var i=it(r),n=ml(i.componentMainType,i.componentIndex,i.componentHighDownName,e).dispatchers;n?M(n,function(a){return vh(a,t)}):vh(r,t)}function b1(r,t,e){if(Cu(t)){var i=t.dataType,n=r.getData(i),a=Nr(n,t);B(a)||(a=[a]),r[t.type===un?"toggleSelect":t.type===sn?"select":"unselect"](a,i)}}function ch(r){var t=r.getAllData();M(t,function(e){var i=e.data,n=e.type;i.eachItemGraphicEl(function(a,o){r.isSelected(o,n)?Hd(a):Vd(a)})})}function x1(r){var t=[];return r.eachSeries(function(e){var i=e.getAllData();M(i,function(n){n.data;var a=n.type,o=e.getSelectedDataIndices();if(o.length>0){var s={dataIndex:o,seriesIndex:e.seriesIndex};a!=null&&(s.dataType=a),t.push(s)}})}),t}function T1(r,t,e){Ud(r,!0),Re(r,y1),M1(r,t,e)}function C1(r){Ud(r,!1)}function xu(r,t,e,i){i?C1(r):T1(r,t,e)}function M1(r,t,e){var i=it(r);t!=null?(i.focus=t,i.blurScope=e):i.focus&&(i.focus=null)}var dh=["emphasis","blur","select"],D1={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function ph(r,t,e,i){e=e||"itemStyle";for(var n=0;n<dh.length;n++){var a=dh[n],o=t.getModel([a,e]),s=r.ensureState(a);s.style=i?i(o):o[D1[e]]()}}function Ud(r,t){var e=t===!1,i=r;r.highDownSilentOnTouch&&(i.__highDownSilentOnTouch=r.highDownSilentOnTouch),(!e||i.__highDownDispatcher)&&(i.__highByOuter=i.__highByOuter||0,i.__highDownDispatcher=!e)}function Tu(r){return!!(r&&r.__highDownDispatcher)}function A1(r){var t=sh[r];return t==null&&oh<=32&&(t=sh[r]=oh++),t}function Cu(r){var t=r.type;return t===sn||t===Ba||t===un}function gh(r){var t=r.type;return t===Er||t===Oa}function L1(r){var t=Od(r);t.normalFill=r.style.fill,t.normalStroke=r.style.stroke;var e=r.states.select||{};t.selectFill=e.style&&e.style.fill||null,t.selectStroke=e.style&&e.style.stroke||null}var jr=Fr.CMD,P1=[[],[],[]],yh=Math.sqrt,I1=Math.atan2;function E1(r,t){if(t){var e=r.data,i=r.len(),n,a,o,s,u,l,f=jr.M,h=jr.C,c=jr.L,v=jr.R,d=jr.A,y=jr.Q;for(o=0,s=0;o<i;){switch(n=e[o++],s=o,a=0,n){case f:a=1;break;case c:a=1;break;case h:a=3;break;case y:a=2;break;case d:var p=t[4],g=t[5],m=yh(t[0]*t[0]+t[1]*t[1]),_=yh(t[2]*t[2]+t[3]*t[3]),S=I1(-t[1]/_,t[0]/m);e[o]*=m,e[o++]+=p,e[o]*=_,e[o++]+=g,e[o++]*=m,e[o++]*=_,e[o++]+=S,e[o++]+=S,o+=2,s=o;break;case v:l[0]=e[o++],l[1]=e[o++],re(l,l,t),e[s++]=l[0],e[s++]=l[1],l[0]+=e[o++],l[1]+=e[o++],re(l,l,t),e[s++]=l[0],e[s++]=l[1]}for(u=0;u<a;u++){var b=P1[u];b[0]=e[o++],b[1]=e[o++],re(b,b,t),e[s++]=b[0],e[s++]=b[1]}}r.increaseVersion()}}var _s=Math.sqrt,ia=Math.sin,na=Math.cos,Ni=Math.PI;function mh(r){return Math.sqrt(r[0]*r[0]+r[1]*r[1])}function Mu(r,t){return(r[0]*t[0]+r[1]*t[1])/(mh(r)*mh(t))}function _h(r,t){return(r[0]*t[1]<r[1]*t[0]?-1:1)*Math.acos(Mu(r,t))}function Sh(r,t,e,i,n,a,o,s,u,l,f){var h=u*(Ni/180),c=na(h)*(r-e)/2+ia(h)*(t-i)/2,v=-1*ia(h)*(r-e)/2+na(h)*(t-i)/2,d=c*c/(o*o)+v*v/(s*s);d>1&&(o*=_s(d),s*=_s(d));var y=(n===a?-1:1)*_s((o*o*(s*s)-o*o*(v*v)-s*s*(c*c))/(o*o*(v*v)+s*s*(c*c)))||0,p=y*o*v/s,g=y*-s*c/o,m=(r+e)/2+na(h)*p-ia(h)*g,_=(t+i)/2+ia(h)*p+na(h)*g,S=_h([1,0],[(c-p)/o,(v-g)/s]),b=[(c-p)/o,(v-g)/s],w=[(-1*c-p)/o,(-1*v-g)/s],x=_h(b,w);if(Mu(b,w)<=-1&&(x=Ni),Mu(b,w)>=1&&(x=0),x<0){var D=Math.round(x/Ni*1e6)/1e6;x=Ni*2+D%2*Ni}f.addData(l,m,_,o,s,S,x,h,a)}var R1=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig,k1=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function O1(r){var t=new Fr;if(!r)return t;var e=0,i=0,n=e,a=i,o,s=Fr.CMD,u=r.match(R1);if(!u)return t;for(var l=0;l<u.length;l++){for(var f=u[l],h=f.charAt(0),c=void 0,v=f.match(k1)||[],d=v.length,y=0;y<d;y++)v[y]=parseFloat(v[y]);for(var p=0;p<d;){var g=void 0,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0,x=void 0,D=e,T=i,C=void 0,A=void 0;switch(h){case"l":e+=v[p++],i+=v[p++],c=s.L,t.addData(c,e,i);break;case"L":e=v[p++],i=v[p++],c=s.L,t.addData(c,e,i);break;case"m":e+=v[p++],i+=v[p++],c=s.M,t.addData(c,e,i),n=e,a=i,h="l";break;case"M":e=v[p++],i=v[p++],c=s.M,t.addData(c,e,i),n=e,a=i,h="L";break;case"h":e+=v[p++],c=s.L,t.addData(c,e,i);break;case"H":e=v[p++],c=s.L,t.addData(c,e,i);break;case"v":i+=v[p++],c=s.L,t.addData(c,e,i);break;case"V":i=v[p++],c=s.L,t.addData(c,e,i);break;case"C":c=s.C,t.addData(c,v[p++],v[p++],v[p++],v[p++],v[p++],v[p++]),e=v[p-2],i=v[p-1];break;case"c":c=s.C,t.addData(c,v[p++]+e,v[p++]+i,v[p++]+e,v[p++]+i,v[p++]+e,v[p++]+i),e+=v[p-2],i+=v[p-1];break;case"S":g=e,m=i,C=t.len(),A=t.data,o===s.C&&(g+=e-A[C-4],m+=i-A[C-3]),c=s.C,D=v[p++],T=v[p++],e=v[p++],i=v[p++],t.addData(c,g,m,D,T,e,i);break;case"s":g=e,m=i,C=t.len(),A=t.data,o===s.C&&(g+=e-A[C-4],m+=i-A[C-3]),c=s.C,D=e+v[p++],T=i+v[p++],e+=v[p++],i+=v[p++],t.addData(c,g,m,D,T,e,i);break;case"Q":D=v[p++],T=v[p++],e=v[p++],i=v[p++],c=s.Q,t.addData(c,D,T,e,i);break;case"q":D=v[p++]+e,T=v[p++]+i,e+=v[p++],i+=v[p++],c=s.Q,t.addData(c,D,T,e,i);break;case"T":g=e,m=i,C=t.len(),A=t.data,o===s.Q&&(g+=e-A[C-4],m+=i-A[C-3]),e=v[p++],i=v[p++],c=s.Q,t.addData(c,g,m,e,i);break;case"t":g=e,m=i,C=t.len(),A=t.data,o===s.Q&&(g+=e-A[C-4],m+=i-A[C-3]),e+=v[p++],i+=v[p++],c=s.Q,t.addData(c,g,m,e,i);break;case"A":_=v[p++],S=v[p++],b=v[p++],w=v[p++],x=v[p++],D=e,T=i,e=v[p++],i=v[p++],c=s.A,Sh(D,T,e,i,w,x,_,S,b,c,t);break;case"a":_=v[p++],S=v[p++],b=v[p++],w=v[p++],x=v[p++],D=e,T=i,e+=v[p++],i+=v[p++],c=s.A,Sh(D,T,e,i,w,x,_,S,b,c,t);break}}(h==="z"||h==="Z")&&(c=s.Z,t.addData(c),e=n,i=a),o=c}return t.toStatic(),t}var Yd=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.applyTransform=function(e){},t}(ot);function $d(r){return r.setData!=null}function Xd(r,t){var e=O1(r),i=k({},t);return i.buildPath=function(n){if($d(n)){n.setData(e.data);var a=n.getContext();a&&n.rebuildPath(a,1)}else{var a=n;e.rebuildPath(a,1)}},i.applyTransform=function(n){E1(e,n),this.dirtyShape()},i}function B1(r,t){return new Yd(Xd(r,t))}function N1(r,t){var e=Xd(r,t),i=function(n){O(a,n);function a(o){var s=n.call(this,o)||this;return s.applyTransform=e.applyTransform,s.buildPath=e.buildPath,s}return a}(Yd);return i}function F1(r,t){for(var e=[],i=r.length,n=0;n<i;n++){var a=r[n];e.push(a.getUpdatedPathProxy(!0))}var o=new ot(t);return o.createPathProxy(),o.buildPath=function(s){if($d(s)){s.appendPath(e);var u=s.getContext();u&&s.rebuildPath(u,1)}},o}var z1=function(){function r(){this.cx=0,this.cy=0,this.r=0}return r}(),Zd=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new z1},t.prototype.buildPath=function(e,i){e.moveTo(i.cx+i.r,i.cy),e.arc(i.cx,i.cy,i.r,0,Math.PI*2)},t}(ot);Zd.prototype.type="circle";const _l=Zd;var H1=function(){function r(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return r}(),qd=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new H1},t.prototype.buildPath=function(e,i){var n=.5522848,a=i.cx,o=i.cy,s=i.rx,u=i.ry,l=s*n,f=u*n;e.moveTo(a-s,o),e.bezierCurveTo(a-s,o-f,a-l,o-u,a,o-u),e.bezierCurveTo(a+l,o-u,a+s,o-f,a+s,o),e.bezierCurveTo(a+s,o+f,a+l,o+u,a,o+u),e.bezierCurveTo(a-l,o+u,a-s,o+f,a-s,o),e.closePath()},t}(ot);qd.prototype.type="ellipse";const Kd=qd;var Qd=Math.PI,Ss=Qd*2,mr=Math.sin,ti=Math.cos,V1=Math.acos,_t=Math.atan2,wh=Math.abs,ln=Math.sqrt,ji=Math.max,ge=Math.min,ue=1e-4;function G1(r,t,e,i,n,a,o,s){var u=e-r,l=i-t,f=o-n,h=s-a,c=h*u-f*l;if(!(c*c<ue))return c=(f*(t-a)-h*(r-n))/c,[r+c*u,t+c*l]}function aa(r,t,e,i,n,a,o){var s=r-e,u=t-i,l=(o?a:-a)/ln(s*s+u*u),f=l*u,h=-l*s,c=r+f,v=t+h,d=e+f,y=i+h,p=(c+d)/2,g=(v+y)/2,m=d-c,_=y-v,S=m*m+_*_,b=n-a,w=c*y-d*v,x=(_<0?-1:1)*ln(ji(0,b*b*S-w*w)),D=(w*_-m*x)/S,T=(-w*m-_*x)/S,C=(w*_+m*x)/S,A=(-w*m+_*x)/S,L=D-p,I=T-g,P=C-p,E=A-g;return L*L+I*I>P*P+E*E&&(D=C,T=A),{cx:D,cy:T,x0:-f,y0:-h,x1:D*(n/b-1),y1:T*(n/b-1)}}function W1(r){var t;if(B(r)){var e=r.length;if(!e)return r;e===1?t=[r[0],r[0],0,0]:e===2?t=[r[0],r[0],r[1],r[1]]:e===3?t=r.concat(r[2]):t=r}else t=[r,r,r,r];return t}function U1(r,t){var e,i=ji(t.r,0),n=ji(t.r0||0,0),a=i>0,o=n>0;if(!(!a&&!o)){if(a||(i=n,n=0),n>i){var s=i;i=n,n=s}var u=t.startAngle,l=t.endAngle;if(!(isNaN(u)||isNaN(l))){var f=t.cx,h=t.cy,c=!!t.clockwise,v=wh(l-u),d=v>Ss&&v%Ss;if(d>ue&&(v=d),!(i>ue))r.moveTo(f,h);else if(v>Ss-ue)r.moveTo(f+i*ti(u),h+i*mr(u)),r.arc(f,h,i,u,l,!c),n>ue&&(r.moveTo(f+n*ti(l),h+n*mr(l)),r.arc(f,h,n,l,u,c));else{var y=void 0,p=void 0,g=void 0,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0,x=void 0,D=void 0,T=void 0,C=void 0,A=void 0,L=void 0,I=void 0,P=void 0,E=i*ti(u),R=i*mr(u),V=n*ti(l),N=n*mr(l),F=v>ue;if(F){var Y=t.cornerRadius;Y&&(e=W1(Y),y=e[0],p=e[1],g=e[2],m=e[3]);var et=wh(i-n)/2;if(_=ge(et,g),S=ge(et,m),b=ge(et,y),w=ge(et,p),T=x=ji(_,S),C=D=ji(b,w),(x>ue||D>ue)&&(A=i*ti(l),L=i*mr(l),I=n*ti(u),P=n*mr(u),v<Qd)){var j=G1(E,R,I,P,A,L,V,N);if(j){var st=E-j[0],ut=R-j[1],ct=A-j[0],ae=L-j[1],rr=1/mr(V1((st*ct+ut*ae)/(ln(st*st+ut*ut)*ln(ct*ct+ae*ae)))/2),$r=ln(j[0]*j[0]+j[1]*j[1]);T=ge(x,(i-$r)/(rr+1)),C=ge(D,(n-$r)/(rr-1))}}}if(!F)r.moveTo(f+E,h+R);else if(T>ue){var Ht=ge(g,T),mt=ge(m,T),W=aa(I,P,E,R,i,Ht,c),q=aa(A,L,V,N,i,mt,c);r.moveTo(f+W.cx+W.x0,h+W.cy+W.y0),T<x&&Ht===mt?r.arc(f+W.cx,h+W.cy,T,_t(W.y0,W.x0),_t(q.y0,q.x0),!c):(Ht>0&&r.arc(f+W.cx,h+W.cy,Ht,_t(W.y0,W.x0),_t(W.y1,W.x1),!c),r.arc(f,h,i,_t(W.cy+W.y1,W.cx+W.x1),_t(q.cy+q.y1,q.cx+q.x1),!c),mt>0&&r.arc(f+q.cx,h+q.cy,mt,_t(q.y1,q.x1),_t(q.y0,q.x0),!c))}else r.moveTo(f+E,h+R),r.arc(f,h,i,u,l,!c);if(!(n>ue)||!F)r.lineTo(f+V,h+N);else if(C>ue){var Ht=ge(y,C),mt=ge(p,C),W=aa(V,N,A,L,n,-mt,c),q=aa(E,R,I,P,n,-Ht,c);r.lineTo(f+W.cx+W.x0,h+W.cy+W.y0),C<D&&Ht===mt?r.arc(f+W.cx,h+W.cy,C,_t(W.y0,W.x0),_t(q.y0,q.x0),!c):(mt>0&&r.arc(f+W.cx,h+W.cy,mt,_t(W.y0,W.x0),_t(W.y1,W.x1),!c),r.arc(f,h,n,_t(W.cy+W.y1,W.cx+W.x1),_t(q.cy+q.y1,q.cx+q.x1),c),Ht>0&&r.arc(f+q.cx,h+q.cy,Ht,_t(q.y1,q.x1),_t(q.y0,q.x0),!c))}else r.lineTo(f+V,h+N),r.arc(f,h,n,l,u,c)}r.closePath()}}}var Y1=function(){function r(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0,this.cornerRadius=0}return r}(),Jd=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new Y1},t.prototype.buildPath=function(e,i){U1(e,i)},t.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},t}(ot);Jd.prototype.type="sector";const Sl=Jd;var $1=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return r}(),jd=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new $1},t.prototype.buildPath=function(e,i){var n=i.cx,a=i.cy,o=Math.PI*2;e.moveTo(n+i.r,a),e.arc(n,a,i.r,0,o,!1),e.moveTo(n+i.r0,a),e.arc(n,a,i.r0,0,o,!0)},t}(ot);jd.prototype.type="ring";const tp=jd;function X1(r,t,e,i){var n=[],a=[],o=[],s=[],u,l,f,h;if(i){f=[1/0,1/0],h=[-1/0,-1/0];for(var c=0,v=r.length;c<v;c++)li(f,f,r[c]),fi(h,h,r[c]);li(f,f,i[0]),fi(h,h,i[1])}for(var c=0,v=r.length;c<v;c++){var d=r[c];if(e)u=r[c?c-1:v-1],l=r[(c+1)%v];else if(c===0||c===v-1){n.push(cm(r[c]));continue}else u=r[c-1],l=r[c+1];dm(a,l,u),Fo(a,a,t);var y=eu(d,u),p=eu(d,l),g=y+p;g!==0&&(y/=g,p/=g),Fo(o,a,-y),Fo(s,a,p);var m=lf([],d,o),_=lf([],d,s);i&&(fi(m,m,f),li(m,m,h),fi(_,_,f),li(_,_,h)),n.push(m),n.push(_)}return e&&n.push(n.shift()),n}function ep(r,t,e){var i=t.smooth,n=t.points;if(n&&n.length>=2){if(i){var a=X1(n,i,e,t.smoothConstraint);r.moveTo(n[0][0],n[0][1]);for(var o=n.length,s=0;s<(e?o:o-1);s++){var u=a[s*2],l=a[s*2+1],f=n[(s+1)%o];r.bezierCurveTo(u[0],u[1],l[0],l[1],f[0],f[1])}}else{r.moveTo(n[0][0],n[0][1]);for(var s=1,h=n.length;s<h;s++)r.lineTo(n[s][0],n[s][1])}e&&r.closePath()}}var Z1=function(){function r(){this.points=null,this.smooth=0,this.smoothConstraint=null}return r}(),rp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new Z1},t.prototype.buildPath=function(e,i){ep(e,i,!0)},t}(ot);rp.prototype.type="polygon";const ip=rp;var q1=function(){function r(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return r}(),np=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new q1},t.prototype.buildPath=function(e,i){ep(e,i,!1)},t}(ot);np.prototype.type="polyline";const ap=np;var K1={},Q1=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return r}(),op=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Q1},t.prototype.buildPath=function(e,i){var n,a,o,s;if(this.subPixelOptimize){var u=Id(K1,i,this.style);n=u.x1,a=u.y1,o=u.x2,s=u.y2}else n=i.x1,a=i.y1,o=i.x2,s=i.y2;var l=i.percent;l!==0&&(e.moveTo(n,a),l<1&&(o=n*(1-l)+o*l,s=a*(1-l)+s*l),e.lineTo(o,s))},t.prototype.pointAt=function(e){var i=this.shape;return[i.x1*(1-e)+i.x2*e,i.y1*(1-e)+i.y2*e]},t}(ot);op.prototype.type="line";const zr=op;var Pt=[],J1=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return r}();function bh(r,t,e){var i=r.cpx2,n=r.cpy2;return i!=null||n!=null?[(e?Sf:gt)(r.x1,r.cpx1,r.cpx2,r.x2,t),(e?Sf:gt)(r.y1,r.cpy1,r.cpy2,r.y2,t)]:[(e?wf:bt)(r.x1,r.cpx1,r.x2,t),(e?wf:bt)(r.y1,r.cpy1,r.y2,t)]}var sp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new J1},t.prototype.buildPath=function(e,i){var n=i.x1,a=i.y1,o=i.x2,s=i.y2,u=i.cpx1,l=i.cpy1,f=i.cpx2,h=i.cpy2,c=i.percent;c!==0&&(e.moveTo(n,a),f==null||h==null?(c<1&&(Xa(n,u,o,c,Pt),u=Pt[1],o=Pt[2],Xa(a,l,s,c,Pt),l=Pt[1],s=Pt[2]),e.quadraticCurveTo(u,l,o,s)):(c<1&&($a(n,u,f,o,c,Pt),u=Pt[1],f=Pt[2],o=Pt[3],$a(a,l,h,s,c,Pt),l=Pt[1],h=Pt[2],s=Pt[3]),e.bezierCurveTo(u,l,f,h,o,s)))},t.prototype.pointAt=function(e){return bh(this.shape,e,!1)},t.prototype.tangentAt=function(e){var i=bh(this.shape,e,!0);return ym(i,i)},t}(ot);sp.prototype.type="bezier-curve";const up=sp;var j1=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return r}(),lp=function(r){O(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new j1},t.prototype.buildPath=function(e,i){var n=i.cx,a=i.cy,o=Math.max(i.r,0),s=i.startAngle,u=i.endAngle,l=i.clockwise,f=Math.cos(s),h=Math.sin(s);e.moveTo(f*o+n,h*o+a),e.arc(n,a,o,s,u,!l)},t}(ot);lp.prototype.type="arc";const wl=lp;var tS=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="compound",e}return t.prototype._updatePathDirty=function(){for(var e=this.shape.paths,i=this.shapeChanged(),n=0;n<e.length;n++)i=i||e[n].shapeChanged();i&&this.dirtyShape()},t.prototype.beforeBrush=function(){this._updatePathDirty();for(var e=this.shape.paths||[],i=this.getGlobalScale(),n=0;n<e.length;n++)e[n].path||e[n].createPathProxy(),e[n].path.setScale(i[0],i[1],e[n].segmentIgnoreThreshold)},t.prototype.buildPath=function(e,i){for(var n=i.paths||[],a=0;a<n.length;a++)n[a].buildPath(e,n[a].shape,!0)},t.prototype.afterBrush=function(){for(var e=this.shape.paths||[],i=0;i<e.length;i++)e[i].pathUpdated()},t.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),ot.prototype.getBoundingRect.call(this)},t}(ot);const eS=tS;var rS=function(){function r(t){this.colorStops=t||[]}return r.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},r}();const fp=rS;var iS=function(r){O(t,r);function t(e,i,n,a,o,s){var u=r.call(this,o)||this;return u.x=e??0,u.y=i??0,u.x2=n??1,u.y2=a??0,u.type="linear",u.global=s||!1,u}return t}(fp);const hp=iS;var nS=function(r){O(t,r);function t(e,i,n,a,o){var s=r.call(this,a)||this;return s.x=e??.5,s.y=i??.5,s.r=n??.5,s.type="radial",s.global=o||!1,s}return t}(fp);const aS=nS;var _r=[0,0],Sr=[0,0],oa=new rt,sa=new rt,oS=function(){function r(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var i=0;i<4;i++)this._corners[i]=new rt;for(var i=0;i<2;i++)this._axes[i]=new rt;t&&this.fromBoundingRect(t,e)}return r.prototype.fromBoundingRect=function(t,e){var i=this._corners,n=this._axes,a=t.x,o=t.y,s=a+t.width,u=o+t.height;if(i[0].set(a,o),i[1].set(s,o),i[2].set(s,u),i[3].set(a,u),e)for(var l=0;l<4;l++)i[l].transform(e);rt.sub(n[0],i[1],i[0]),rt.sub(n[1],i[3],i[0]),n[0].normalize(),n[1].normalize();for(var l=0;l<2;l++)this._origin[l]=n[l].dot(i[0])},r.prototype.intersect=function(t,e){var i=!0,n=!e;return oa.set(1/0,1/0),sa.set(0,0),!this._intersectCheckOneSide(this,t,oa,sa,n,1)&&(i=!1,n)||!this._intersectCheckOneSide(t,this,oa,sa,n,-1)&&(i=!1,n)||n||rt.copy(e,i?oa:sa),i},r.prototype._intersectCheckOneSide=function(t,e,i,n,a,o){for(var s=!0,u=0;u<2;u++){var l=this._axes[u];if(this._getProjMinMaxOnAxis(u,t._corners,_r),this._getProjMinMaxOnAxis(u,e._corners,Sr),_r[1]<Sr[0]||_r[0]>Sr[1]){if(s=!1,a)return s;var f=Math.abs(Sr[0]-_r[1]),h=Math.abs(_r[0]-Sr[1]);Math.min(f,h)>n.len()&&(f<h?rt.scale(n,l,-f*o):rt.scale(n,l,h*o))}else if(i){var f=Math.abs(Sr[0]-_r[1]),h=Math.abs(_r[0]-Sr[1]);Math.min(f,h)<i.len()&&(f<h?rt.scale(i,l,f*o):rt.scale(i,l,-h*o))}}return s},r.prototype._getProjMinMaxOnAxis=function(t,e,i){for(var n=this._axes[t],a=this._origin,o=e[0].dot(n)+a[t],s=o,u=o,l=1;l<e.length;l++){var f=e[l].dot(n)+a[t];s=Math.min(f,s),u=Math.max(f,u)}i[0]=s,i[1]=u},r}();const to=oS;var sS=[],uS=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return t.prototype.traverse=function(e,i){e.call(i,this)},t.prototype.useStyle=function(){this.style={}},t.prototype.getCursor=function(){return this._cursor},t.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},t.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},t.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},t.prototype.addDisplayable=function(e,i){i?this._temporaryDisplayables.push(e):this._displayables.push(e),this.markRedraw()},t.prototype.addDisplayables=function(e,i){i=i||!1;for(var n=0;n<e.length;n++)this.addDisplayable(e[n],i)},t.prototype.getDisplayables=function(){return this._displayables},t.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},t.prototype.eachPendingDisplayable=function(e){for(var i=this._cursor;i<this._displayables.length;i++)e&&e(this._displayables[i]);for(var i=0;i<this._temporaryDisplayables.length;i++)e&&e(this._temporaryDisplayables[i])},t.prototype.update=function(){this.updateTransform();for(var e=this._cursor;e<this._displayables.length;e++){var i=this._displayables[e];i.parent=this,i.update(),i.parent=null}for(var e=0;e<this._temporaryDisplayables.length;e++){var i=this._temporaryDisplayables[e];i.parent=this,i.update(),i.parent=null}},t.prototype.getBoundingRect=function(){if(!this._rect){for(var e=new J(1/0,1/0,-1/0,-1/0),i=0;i<this._displayables.length;i++){var n=this._displayables[i],a=n.getBoundingRect().clone();n.needLocalTransform()&&a.applyTransform(n.getLocalTransform(sS)),e.union(a)}this._rect=e}return this._rect},t.prototype.contain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect();if(a.contain(n[0],n[1]))for(var o=0;o<this._displayables.length;o++){var s=this._displayables[o];if(s.contain(e,i))return!0}return!1},t}(zn);const lS=uS;var fS=yt();function hS(r,t,e,i,n){var a;if(t&&t.ecModel){var o=t.ecModel.getUpdatePayload();a=o&&o.animation}var s=t&&t.isAnimationEnabled(),u=r==="update";if(s){var l=void 0,f=void 0,h=void 0;i?(l=X(i.duration,200),f=X(i.easing,"cubicOut"),h=0):(l=t.getShallow(u?"animationDurationUpdate":"animationDuration"),f=t.getShallow(u?"animationEasingUpdate":"animationEasing"),h=t.getShallow(u?"animationDelayUpdate":"animationDelay")),a&&(a.duration!=null&&(l=a.duration),a.easing!=null&&(f=a.easing),a.delay!=null&&(h=a.delay)),U(h)&&(h=h(e,n)),U(l)&&(l=l(e));var c={duration:l||0,delay:h,easing:f};return c}else return null}function bl(r,t,e,i,n,a,o){var s=!1,u;U(n)?(o=a,a=n,n=null):H(n)&&(a=n.cb,o=n.during,s=n.isFrom,u=n.removeOpt,n=n.dataIndex);var l=r==="leave";l||t.stopAnimation("leave");var f=hS(r,i,n,l?u||{}:null,i&&i.getAnimationDelayParams?i.getAnimationDelayParams(t,n):null);if(f&&f.duration>0){var h=f.duration,c=f.delay,v=f.easing,d={duration:h,delay:c||0,easing:v,done:a,force:!!a||!!o,setToFinal:!l,scope:r,during:o};s?t.animateFrom(e,d):t.animateTo(e,d)}else t.stopAnimation(),!s&&t.attr(e),o&&o(1),a&&a()}function Hr(r,t,e,i,n,a){bl("update",r,t,e,i,n,a)}function Hn(r,t,e,i,n,a){bl("enter",r,t,e,i,n,a)}function fn(r){if(!r.__zr)return!0;for(var t=0;t<r.animators.length;t++){var e=r.animators[t];if(e.scope==="leave")return!0}return!1}function eo(r,t,e,i,n,a){fn(r)||bl("leave",r,t,e,i,n,a)}function xh(r,t,e,i){r.removeTextContent(),r.removeTextGuideLine(),eo(r,{style:{opacity:0}},t,e,i)}function vS(r,t,e){function i(){r.parent&&r.parent.remove(r)}r.isGroup?r.traverse(function(n){n.isGroup||xh(n,t,e,i)}):xh(r,t,e,i)}function cS(r){fS(r).oldStyle=r.style}var ro=Math.max,io=Math.min,Du={};function dS(r){return ot.extend(r)}var pS=N1;function gS(r,t){return pS(r,t)}function ve(r,t){Du[r]=t}function yS(r){if(Du.hasOwnProperty(r))return Du[r]}function xl(r,t,e,i){var n=B1(r,t);return e&&(i==="center"&&(e=cp(e,n.getBoundingRect())),dp(n,e)),n}function vp(r,t,e){var i=new Wr({style:{image:r,x:t.x,y:t.y,width:t.width,height:t.height},onload:function(n){if(e==="center"){var a={width:n.width,height:n.height};i.setStyle(cp(t,a))}}});return i}function cp(r,t){var e=t.width/t.height,i=r.height*e,n;i<=r.width?n=r.height:(i=r.width,n=i/e);var a=r.x+r.width/2,o=r.y+r.height/2;return{x:a-i/2,y:o-n/2,width:i,height:n}}var mS=F1;function dp(r,t){if(r.applyTransform){var e=r.getBoundingRect(),i=e.calculateTransform(t);r.applyTransform(i)}}function wn(r,t){return Id(r,r,{lineWidth:t}),r}function _S(r){return Ed(r.shape,r.shape,r.style),r}var SS=Ar;function wS(r,t){for(var e=il([]);r&&r!==t;)gi(e,r.getLocalTransform(),e),r=r.parent;return e}function Tl(r,t,e){return t&&!Nt(t)&&(t=ul.getLocalTransform(t)),e&&(t=al([],t)),re([],r,t)}function bS(r,t,e){var i=t[4]===0||t[5]===0||t[0]===0?1:Math.abs(2*t[4]/t[0]),n=t[4]===0||t[5]===0||t[2]===0?1:Math.abs(2*t[4]/t[2]),a=[r==="left"?-i:r==="right"?i:0,r==="top"?-n:r==="bottom"?n:0];return a=Tl(a,t,e),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"}function Th(r){return!r.isGroup}function xS(r){return r.shape!=null}function pp(r,t,e){if(!r||!t)return;function i(o){var s={};return o.traverse(function(u){Th(u)&&u.anid&&(s[u.anid]=u)}),s}function n(o){var s={x:o.x,y:o.y,rotation:o.rotation};return xS(o)&&(s.shape=k({},o.shape)),s}var a=i(r);t.traverse(function(o){if(Th(o)&&o.anid){var s=a[o.anid];if(s){var u=n(o);o.attr(n(s)),Hr(o,u,e,it(o).dataIndex)}}})}function TS(r,t){return G(r,function(e){var i=e[0];i=ro(i,t.x),i=io(i,t.x+t.width);var n=e[1];return n=ro(n,t.y),n=io(n,t.y+t.height),[i,n]})}function CS(r,t){var e=ro(r.x,t.x),i=io(r.x+r.width,t.x+t.width),n=ro(r.y,t.y),a=io(r.y+r.height,t.y+t.height);if(i>=e&&a>=n)return{x:e,y:n,width:i-e,height:a-n}}function gp(r,t,e){var i=k({rectHover:!0},t),n=i.style={strokeNoScale:!0};if(e=e||{x:-1,y:-1,width:2,height:2},r)return r.indexOf("image://")===0?(n.image=r.slice(8),tt(n,e),new Wr(i)):xl(r.replace("path://",""),i,e,"center")}function MS(r,t,e,i,n){for(var a=0,o=n[n.length-1];a<n.length;a++){var s=n[a];if(yp(r,t,e,i,s[0],s[1],o[0],o[1]))return!0;o=s}}function yp(r,t,e,i,n,a,o,s){var u=e-r,l=i-t,f=o-n,h=s-a,c=ws(f,h,u,l);if(DS(c))return!1;var v=r-n,d=t-a,y=ws(v,d,u,l)/c;if(y<0||y>1)return!1;var p=ws(v,d,f,h)/c;return!(p<0||p>1)}function ws(r,t,e,i){return r*i-e*t}function DS(r){return r<=1e-6&&r>=-1e-6}function Cl(r){var t=r.itemTooltipOption,e=r.componentModel,i=r.itemName,n=z(t)?{formatter:t}:t,a=e.mainType,o=e.componentIndex,s={componentType:a,name:i,$vars:["name"]};s[a+"Index"]=o;var u=r.formatterParamsExtra;u&&M(lt(u),function(f){wi(s,f)||(s[f]=u[f],s.$vars.push(f))});var l=it(r.el);l.componentMainType=a,l.componentIndex=o,l.tooltipConfig={name:i,option:tt({content:i,encodeHTMLContent:!0,formatterParams:s},n)}}function Ch(r,t){var e;r.isGroup&&(e=t(r)),e||r.traverse(t)}function Ml(r,t){if(r)if(B(r))for(var e=0;e<r.length;e++)Ch(r[e],t);else Ch(r,t)}ve("circle",_l);ve("ellipse",Kd);ve("sector",Sl);ve("ring",tp);ve("polygon",ip);ve("polyline",ap);ve("rect",Bt);ve("line",zr);ve("bezierCurve",up);ve("arc",wl);const AS=Object.freeze(Object.defineProperty({__proto__:null,Arc:wl,BezierCurve:up,BoundingRect:J,Circle:_l,CompoundPath:eS,Ellipse:Kd,Group:Ut,Image:Wr,IncrementalDisplayable:lS,Line:zr,LinearGradient:hp,OrientedBoundingRect:to,Path:ot,Point:rt,Polygon:ip,Polyline:ap,RadialGradient:aS,Rect:Bt,Ring:tp,Sector:Sl,Text:ie,applyTransform:Tl,clipPointsByRect:TS,clipRectByRect:CS,createIcon:gp,extendPath:gS,extendShape:dS,getShapeClass:yS,getTransform:wS,groupTransition:pp,initProps:Hn,isElementRemoved:fn,lineLineIntersect:yp,linePolygonIntersect:MS,makeImage:vp,makePath:xl,mergePath:mS,registerShape:ve,removeElement:eo,removeElementWithFadeOut:vS,resizePath:dp,setTooltipConfig:Cl,subPixelOptimize:SS,subPixelOptimizeLine:wn,subPixelOptimizeRect:_S,transformDirection:bS,traverseElements:Ml,updateProps:Hr},Symbol.toStringTag,{value:"Module"}));var wo={};function LS(r,t){for(var e=0;e<we.length;e++){var i=we[e],n=t[i],a=r.ensureState(i);a.style=a.style||{},a.style.text=n}var o=r.currentStates.slice();r.clearStates(!0),r.setStyle({text:t.normal}),r.useStates(o,!0)}function Mh(r,t,e){var i=r.labelFetcher,n=r.labelDataIndex,a=r.labelDimIndex,o=t.normal,s;i&&(s=i.getFormattedLabel(n,"normal",null,a,o&&o.get("formatter"),e!=null?{interpolatedValue:e}:null)),s==null&&(s=U(r.defaultText)?r.defaultText(n,r,e):r.defaultText);for(var u={normal:s},l=0;l<we.length;l++){var f=we[l],h=t[f];u[f]=X(i?i.getFormattedLabel(n,f,null,a,h&&h.get("formatter")):null,s)}return u}function mp(r,t,e,i){e=e||wo;for(var n=r instanceof ie,a=!1,o=0;o<uh.length;o++){var s=t[uh[o]];if(s&&s.getShallow("show")){a=!0;break}}var u=n?r:r.getTextContent();if(a){n||(u||(u=new ie,r.setTextContent(u)),r.stateProxy&&(u.stateProxy=r.stateProxy));var l=Mh(e,t),f=t.normal,h=!!f.getShallow("show"),c=Vr(f,i&&i.normal,e,!1,!n);c.text=l.normal,n||r.setTextConfig(Dh(f,e,!1));for(var o=0;o<we.length;o++){var v=we[o],s=t[v];if(s){var d=u.ensureState(v),y=!!X(s.getShallow("show"),h);if(y!==h&&(d.ignore=!y),d.style=Vr(s,i&&i[v],e,!0,!n),d.style.text=l[v],!n){var p=r.ensureState(v);p.textConfig=Dh(s,e,!0)}}}u.silent=!!f.getShallow("silent"),u.style.x!=null&&(c.x=u.style.x),u.style.y!=null&&(c.y=u.style.y),u.ignore=!h,u.useStyle(c),u.dirty(),e.enableTextSetter&&(_p(u).setLabelText=function(g){var m=Mh(e,t,g);LS(u,m)})}else u&&(u.ignore=!0);r.dirty()}function Dl(r,t){t=t||"label";for(var e={normal:r.getModel(t)},i=0;i<we.length;i++){var n=we[i];e[n]=r.getModel([n,t])}return e}function Vr(r,t,e,i,n){var a={};return PS(a,r,e,i,n),t&&k(a,t),a}function Dh(r,t,e){t=t||{};var i={},n,a=r.getShallow("rotate"),o=X(r.getShallow("distance"),e?null:5),s=r.getShallow("offset");return n=r.getShallow("position")||(e?null:"inside"),n==="outside"&&(n=t.defaultOutsidePosition||"top"),n!=null&&(i.position=n),s!=null&&(i.offset=s),a!=null&&(a*=Math.PI/180,i.rotation=a),o!=null&&(i.distance=o),i.outsideFill=r.get("color")==="inherit"?t.inheritColor||null:"auto",i}function PS(r,t,e,i,n){e=e||wo;var a=t.ecModel,o=a&&a.option.textStyle,s=IS(t),u;if(s){u={};for(var l in s)if(s.hasOwnProperty(l)){var f=t.getModel(["rich",l]);Ih(u[l]={},f,o,e,i,n,!1,!0)}}u&&(r.rich=u);var h=t.get("overflow");h&&(r.overflow=h);var c=t.get("minMargin");c!=null&&(r.margin=c),Ih(r,t,o,e,i,n,!0,!1)}function IS(r){for(var t;r&&r!==r.ecModel;){var e=(r.option||wo).rich;if(e){t=t||{};for(var i=lt(e),n=0;n<i.length;n++){var a=i[n];t[a]=1}}r=r.parentModel}return t}var Ah=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],Lh=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],Ph=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function Ih(r,t,e,i,n,a,o,s){e=!n&&e||wo;var u=i&&i.inheritColor,l=t.getShallow("color"),f=t.getShallow("textBorderColor"),h=X(t.getShallow("opacity"),e.opacity);(l==="inherit"||l==="auto")&&(u?l=u:l=null),(f==="inherit"||f==="auto")&&(u?f=u:f=null),a||(l=l||e.color,f=f||e.textBorderColor),l!=null&&(r.fill=l),f!=null&&(r.stroke=f);var c=X(t.getShallow("textBorderWidth"),e.textBorderWidth);c!=null&&(r.lineWidth=c);var v=X(t.getShallow("textBorderType"),e.textBorderType);v!=null&&(r.lineDash=v);var d=X(t.getShallow("textBorderDashOffset"),e.textBorderDashOffset);d!=null&&(r.lineDashOffset=d),!n&&h==null&&!s&&(h=i&&i.defaultOpacity),h!=null&&(r.opacity=h),!n&&!a&&r.fill==null&&i.inheritColor&&(r.fill=i.inheritColor);for(var y=0;y<Ah.length;y++){var p=Ah[y],g=X(t.getShallow(p),e[p]);g!=null&&(r[p]=g)}for(var y=0;y<Lh.length;y++){var p=Lh[y],g=t.getShallow(p);g!=null&&(r[p]=g)}if(r.verticalAlign==null){var m=t.getShallow("baseline");m!=null&&(r.verticalAlign=m)}if(!o||!i.disableBox){for(var y=0;y<Ph.length;y++){var p=Ph[y],g=t.getShallow(p);g!=null&&(r[p]=g)}var _=t.getShallow("borderType");_!=null&&(r.borderDash=_),(r.backgroundColor==="auto"||r.backgroundColor==="inherit")&&u&&(r.backgroundColor=u),(r.borderColor==="auto"||r.borderColor==="inherit")&&u&&(r.borderColor=u)}}function ES(r,t){var e=t&&t.getModel("textStyle");return me([r.fontStyle||e&&e.getShallow("fontStyle")||"",r.fontWeight||e&&e.getShallow("fontWeight")||"",(r.fontSize||e&&e.getShallow("fontSize")||12)+"px",r.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "))}var _p=yt(),RS=["textStyle","color"],bs=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],xs=new ie,kS=function(){function r(){}return r.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(RS):null)},r.prototype.getFont=function(){return ES({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},r.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},i=0;i<bs.length;i++)e[bs[i]]=this.getShallow(bs[i]);return xs.useStyle(e),xs.update(),xs.getBoundingRect()},r}();const OS=kS;var Sp=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],BS=Sn(Sp),NS=function(){function r(){}return r.prototype.getLineStyle=function(t){return BS(this,t)},r}(),wp=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],FS=Sn(wp),zS=function(){function r(){}return r.prototype.getItemStyle=function(t,e){return FS(this,t,e)},r}(),Ur=function(){function r(t,e,i){this.parentModel=e,this.ecModel=i,this.option=t}return r.prototype.init=function(t,e,i){},r.prototype.mergeOption=function(t,e){at(this.option,t,!0)},r.prototype.get=function(t,e){return t==null?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},r.prototype.getShallow=function(t,e){var i=this.option,n=i==null?i:i[t];if(n==null&&!e){var a=this.parentModel;a&&(n=a.getShallow(t))}return n},r.prototype.getModel=function(t,e){var i=t!=null,n=i?this.parsePath(t):null,a=i?this._doGet(n):this.option;return e=e||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(n)),new r(a,e,this.ecModel)},r.prototype.isEmpty=function(){return this.option==null},r.prototype.restoreData=function(){},r.prototype.clone=function(){var t=this.constructor;return new t(K(this.option))},r.prototype.parsePath=function(t){return typeof t=="string"?t.split("."):t},r.prototype.resolveParentPath=function(t){return t},r.prototype.isAnimationEnabled=function(){if(!$.node&&this.option){if(this.option.animation!=null)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},r.prototype._doGet=function(t,e){var i=this.option;if(!t)return i;for(var n=0;n<t.length&&!(t[n]&&(i=i&&typeof i=="object"?i[t[n]]:null,i==null));n++);return i==null&&e&&(i=e._doGet(this.resolveParentPath(t),e.parentModel)),i},r}();dl(Ur);v_(Ur);be(Ur,NS);be(Ur,zS);be(Ur,y_);be(Ur,OS);const At=Ur;var HS=Math.round(Math.random()*10);function bo(r){return[r||"",HS++].join("_")}function VS(r){var t={};r.registerSubTypeDefaulter=function(e,i){var n=_e(e);t[n.main]=i},r.determineSubType=function(e,i){var n=i.type;if(!n){var a=_e(e).main;r.hasSubTypes(e)&&t[a]&&(n=t[a](i))}return n}}function GS(r,t){r.topologicalTravel=function(a,o,s,u){if(!a.length)return;var l=e(o),f=l.graph,h=l.noEntryList,c={};for(M(a,function(m){c[m]=!0});h.length;){var v=h.pop(),d=f[v],y=!!c[v];y&&(s.call(u,v,d.originalDeps.slice()),delete c[v]),M(d.successor,y?g:p)}M(c,function(){var m="";throw new Error(m)});function p(m){f[m].entryCount--,f[m].entryCount===0&&h.push(m)}function g(m){c[m]=!0,p(m)}};function e(a){var o={},s=[];return M(a,function(u){var l=i(o,u),f=l.originalDeps=t(u),h=n(f,a);l.entryCount=h.length,l.entryCount===0&&s.push(u),M(h,function(c){nt(l.predecessor,c)<0&&l.predecessor.push(c);var v=i(o,c);nt(v.successor,c)<0&&v.successor.push(u)})}),{graph:o,noEntryList:s}}function i(a,o){return a[o]||(a[o]={predecessor:[],successor:[]}),a[o]}function n(a,o){var s=[];return M(a,function(u){nt(o,u)>=0&&s.push(u)}),s}}const WS={time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}},US={time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}};var no="ZH",Al="EN",mi=Al,Na={},Ll={},bp=$.domSupported?function(){var r=(document.documentElement.lang||navigator.language||navigator.browserLanguage||mi).toUpperCase();return r.indexOf(no)>-1?no:mi}():mi;function xp(r,t){r=r.toUpperCase(),Ll[r]=new At(t),Na[r]=t}function YS(r){if(z(r)){var t=Na[r.toUpperCase()]||{};return r===no||r===Al?K(t):at(K(t),K(Na[mi]),!1)}else return at(K(r),K(Na[mi]),!1)}function $S(r){return Ll[r]}function XS(){return Ll[mi]}xp(Al,WS);xp(no,US);var Pl=1e3,Il=Pl*60,hn=Il*60,te=hn*24,Eh=te*365,tn={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},ua="{yyyy}-{MM}-{dd}",Rh={year:"{yyyy}",month:"{yyyy}-{MM}",day:ua,hour:ua+" "+tn.hour,minute:ua+" "+tn.minute,second:ua+" "+tn.second,millisecond:tn.none},Ts=["year","month","day","hour","minute","second","millisecond"],Tp=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function Fe(r,t){return r+="","0000".substr(0,t-r.length)+r}function _i(r){switch(r){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return r}}function ZS(r){return r===_i(r)}function qS(r){switch(r){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}function xo(r,t,e,i){var n=Ee(r),a=n[El(e)](),o=n[Si(e)]()+1,s=Math.floor((o-1)/3)+1,u=n[To(e)](),l=n["get"+(e?"UTC":"")+"Day"](),f=n[bn(e)](),h=(f-1)%12+1,c=n[Co(e)](),v=n[Mo(e)](),d=n[Do(e)](),y=f>=12?"pm":"am",p=y.toUpperCase(),g=i instanceof At?i:$S(i||bp)||XS(),m=g.getModel("time"),_=m.get("month"),S=m.get("monthAbbr"),b=m.get("dayOfWeek"),w=m.get("dayOfWeekAbbr");return(t||"").replace(/{a}/g,y+"").replace(/{A}/g,p+"").replace(/{yyyy}/g,a+"").replace(/{yy}/g,Fe(a%100+"",2)).replace(/{Q}/g,s+"").replace(/{MMMM}/g,_[o-1]).replace(/{MMM}/g,S[o-1]).replace(/{MM}/g,Fe(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,Fe(u,2)).replace(/{d}/g,u+"").replace(/{eeee}/g,b[l]).replace(/{ee}/g,w[l]).replace(/{e}/g,l+"").replace(/{HH}/g,Fe(f,2)).replace(/{H}/g,f+"").replace(/{hh}/g,Fe(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,Fe(c,2)).replace(/{m}/g,c+"").replace(/{ss}/g,Fe(v,2)).replace(/{s}/g,v+"").replace(/{SSS}/g,Fe(d,3)).replace(/{S}/g,d+"")}function KS(r,t,e,i,n){var a=null;if(z(e))a=e;else if(U(e))a=e(r.value,t,{level:r.level});else{var o=k({},tn);if(r.level>0)for(var s=0;s<Ts.length;++s)o[Ts[s]]="{primary|"+o[Ts[s]]+"}";var u=e?e.inherit===!1?e:tt(e,o):o,l=Cp(r.value,n);if(u[l])a=u[l];else if(u.inherit){for(var f=Tp.indexOf(l),s=f-1;s>=0;--s)if(u[l]){a=u[l];break}a=a||o.none}if(B(a)){var h=r.level==null?0:r.level>=0?r.level:a.length+r.level;h=Math.min(h,a.length-1),a=a[h]}}return xo(new Date(r.value),a,n,i)}function Cp(r,t){var e=Ee(r),i=e[Si(t)]()+1,n=e[To(t)](),a=e[bn(t)](),o=e[Co(t)](),s=e[Mo(t)](),u=e[Do(t)](),l=u===0,f=l&&s===0,h=f&&o===0,c=h&&a===0,v=c&&n===1,d=v&&i===1;return d?"year":v?"month":c?"day":h?"hour":f?"minute":l?"second":"millisecond"}function kh(r,t,e){var i=vt(r)?Ee(r):r;switch(t=t||Cp(r,e),t){case"year":return i[El(e)]();case"half-year":return i[Si(e)]()>=6?1:0;case"quarter":return Math.floor((i[Si(e)]()+1)/4);case"month":return i[Si(e)]();case"day":return i[To(e)]();case"half-day":return i[bn(e)]()/24;case"hour":return i[bn(e)]();case"minute":return i[Co(e)]();case"second":return i[Mo(e)]();case"millisecond":return i[Do(e)]()}}function El(r){return r?"getUTCFullYear":"getFullYear"}function Si(r){return r?"getUTCMonth":"getMonth"}function To(r){return r?"getUTCDate":"getDate"}function bn(r){return r?"getUTCHours":"getHours"}function Co(r){return r?"getUTCMinutes":"getMinutes"}function Mo(r){return r?"getUTCSeconds":"getSeconds"}function Do(r){return r?"getUTCMilliseconds":"getMilliseconds"}function QS(r){return r?"setUTCFullYear":"setFullYear"}function Mp(r){return r?"setUTCMonth":"setMonth"}function Dp(r){return r?"setUTCDate":"setDate"}function Ap(r){return r?"setUTCHours":"setHours"}function Lp(r){return r?"setUTCMinutes":"setMinutes"}function Pp(r){return r?"setUTCSeconds":"setSeconds"}function Ip(r){return r?"setUTCMilliseconds":"setMilliseconds"}function Ep(r){if(!Y0(r))return z(r)?r:"-";var t=(r+"").split(".");return t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:"")}function Rp(r,t){return r=(r||"").toLowerCase().replace(/-(.)/g,function(e,i){return i.toUpperCase()}),t&&r&&(r=r.charAt(0).toUpperCase()+r.slice(1)),r}var Rl=zc;function Au(r,t,e){var i="{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}";function n(f){return f&&me(f)?f:"-"}function a(f){return!!(f!=null&&!isNaN(f)&&isFinite(f))}var o=t==="time",s=r instanceof Date;if(o||s){var u=o?Ee(r):r;if(isNaN(+u)){if(s)return"-"}else return xo(u,i,e)}if(t==="ordinal")return js(r)?n(r):vt(r)&&a(r)?r+"":"-";var l=Qa(r);return a(l)?Ep(l):js(r)?n(r):typeof r=="boolean"?r+"":"-"}var Oh=["a","b","c","d","e","f","g"],Cs=function(r,t){return"{"+r+(t??"")+"}"};function kp(r,t,e){B(t)||(t=[t]);var i=t.length;if(!i)return"";for(var n=t[0].$vars||[],a=0;a<n.length;a++){var o=Oh[a];r=r.replace(Cs(o),Cs(o,0))}for(var s=0;s<i;s++)for(var u=0;u<n.length;u++){var l=t[s][n[u]];r=r.replace(Cs(Oh[u],s),e?Et(l):l)}return r}function JS(r,t){var e=z(r)?{color:r,extraCssText:t}:r||{},i=e.color,n=e.type;t=e.extraCssText;var a=e.renderMode||"html";if(!i)return"";if(a==="html")return n==="subItem"?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Et(i)+";"+(t||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+Et(i)+";"+(t||"")+'"></span>';var o=e.markerId||"markerX";return{renderMode:a,content:"{"+o+"|}  ",style:n==="subItem"?{width:4,height:4,borderRadius:2,backgroundColor:i}:{width:10,height:10,borderRadius:5,backgroundColor:i}}}function Gr(r,t){return t=t||"transparent",z(r)?r:H(r)&&r.colorStops&&(r.colorStops[0]||{}).color||t}function Bh(r,t){if(t==="_blank"||t==="blank"){var e=window.open();e.opener=null,e.location.href=r}else window.open(r,t)}var Fa=M,jS=["left","right","top","bottom","width","height"],la=[["width","left","right"],["height","top","bottom"]];function Op(r,t,e,i,n){var a=0,o=0;i==null&&(i=1/0),n==null&&(n=1/0);var s=0;t.eachChild(function(u,l){var f=u.getBoundingRect(),h=t.childAt(l+1),c=h&&h.getBoundingRect(),v,d;if(r==="horizontal"){var y=f.width+(c?-c.x+f.x:0);v=a+y,v>i||u.newline?(a=0,v=y,o+=s+e,s=f.height):s=Math.max(s,f.height)}else{var p=f.height+(c?-c.y+f.y:0);d=o+p,d>n||u.newline?(a+=s+e,o=0,d=p,s=f.width):s=Math.max(s,f.width)}u.newline||(u.x=a,u.y=o,u.markRedraw(),r==="horizontal"?a=v+e:o=d+e)})}ee(Op,"vertical");ee(Op,"horizontal");function kl(r,t,e){e=Rl(e||0);var i=t.width,n=t.height,a=Dt(r.left,i),o=Dt(r.top,n),s=Dt(r.right,i),u=Dt(r.bottom,n),l=Dt(r.width,i),f=Dt(r.height,n),h=e[2]+e[0],c=e[1]+e[3],v=r.aspect;switch(isNaN(l)&&(l=i-s-c-a),isNaN(f)&&(f=n-u-h-o),v!=null&&(isNaN(l)&&isNaN(f)&&(v>i/n?l=i*.8:f=n*.8),isNaN(l)&&(l=v*f),isNaN(f)&&(f=l/v)),isNaN(a)&&(a=i-s-l-c),isNaN(o)&&(o=n-u-f-h),r.left||r.right){case"center":a=i/2-l/2-e[3];break;case"right":a=i-l-c;break}switch(r.top||r.bottom){case"middle":case"center":o=n/2-f/2-e[0];break;case"bottom":o=n-f-h;break}a=a||0,o=o||0,isNaN(l)&&(l=i-c-a-(s||0)),isNaN(f)&&(f=n-h-o-(u||0));var d=new J(a+e[3],o+e[0],l,f);return d.margin=e,d}function xn(r){var t=r.layoutMode||r.constructor.layoutMode;return H(t)?t:t?{type:t}:null}function Tn(r,t,e){var i=e&&e.ignoreSize;!B(i)&&(i=[i,i]);var n=o(la[0],0),a=o(la[1],1);l(la[0],r,n),l(la[1],r,a);function o(f,h){var c={},v=0,d={},y=0,p=2;if(Fa(f,function(_){d[_]=r[_]}),Fa(f,function(_){s(t,_)&&(c[_]=d[_]=t[_]),u(c,_)&&v++,u(d,_)&&y++}),i[h])return u(t,f[1])?d[f[2]]=null:u(t,f[2])&&(d[f[1]]=null),d;if(y===p||!v)return d;if(v>=p)return c;for(var g=0;g<f.length;g++){var m=f[g];if(!s(c,m)&&s(r,m)){c[m]=r[m];break}}return c}function s(f,h){return f.hasOwnProperty(h)}function u(f,h){return f[h]!=null&&f[h]!=="auto"}function l(f,h,c){Fa(f,function(v){h[v]=c[v]})}}function Ol(r){return tw({},r)}function tw(r,t){return t&&r&&Fa(jS,function(e){t.hasOwnProperty(e)&&(r[e]=t[e])}),r}var ew=yt(),Mi=function(r){O(t,r);function t(e,i,n){var a=r.call(this,e,i,n)||this;return a.uid=bo("ec_cpt_model"),a}return t.prototype.init=function(e,i,n){this.mergeDefaultAndTheme(e,n)},t.prototype.mergeDefaultAndTheme=function(e,i){var n=xn(this),a=n?Ol(e):{},o=i.getTheme();at(e,o.get(this.mainType)),at(e,this.getDefaultOption()),n&&Tn(e,a,n)},t.prototype.mergeOption=function(e,i){at(this.option,e,!0);var n=xn(this);n&&Tn(this.option,e,n)},t.prototype.optionUpdated=function(e,i){},t.prototype.getDefaultOption=function(){var e=this.constructor;if(!l_(e))return e.defaultOption;var i=ew(this);if(!i.defaultOption){for(var n=[],a=e;a;){var o=a.prototype.defaultOption;o&&n.push(o),a=a.superClass}for(var s={},u=n.length-1;u>=0;u--)s=at(s,n[u],!0);i.defaultOption=s}return i.defaultOption},t.prototype.getReferringComponents=function(e,i){var n=e+"Index",a=e+"Id";return Fn(this.ecModel,e,{index:this.get(n,!0),id:this.get(a,!0)},i)},t.prototype.getBoxLayoutParams=function(){var e=this;return{left:e.get("left"),top:e.get("top"),right:e.get("right"),bottom:e.get("bottom"),width:e.get("width"),height:e.get("height")}},t.prototype.getZLevelKey=function(){return""},t.prototype.setZLevel=function(e){this.option.zlevel=e},t.protoInitialize=function(){var e=t.prototype;e.type="component",e.id="",e.name="",e.mainType="",e.subType="",e.componentIndex=0}(),t}(At);bd(Mi,At);po(Mi);VS(Mi);GS(Mi,rw);function rw(r){var t=[];return M(Mi.getClassesByMainType(r),function(e){t=t.concat(e.dependencies||e.prototype.dependencies||[])}),t=G(t,function(e){return _e(e).main}),r!=="dataset"&&nt(t,"dataset")<=0&&t.unshift("dataset"),t}const ft=Mi;var Bp="";typeof navigator<"u"&&(Bp=navigator.platform||"");var ei="rgba(0, 0, 0, 0.2)";const iw={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:ei,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:ei,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:ei,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:ei,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:ei,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:ei,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:Bp.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1};var Np=Z(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),ne="original",zt="arrayRows",Te="objectRows",ke="keyedColumns",je="typedArray",Fp="unknown",Pe="column",Di="row",Vt={Must:1,Might:2,Not:3},zp=yt();function nw(r){zp(r).datasetMap=Z()}function aw(r,t,e){var i={},n=Hp(t);if(!n||!r)return i;var a=[],o=[],s=t.ecModel,u=zp(s).datasetMap,l=n.uid+"_"+e.seriesLayoutBy,f,h;r=r.slice(),M(r,function(y,p){var g=H(y)?y:r[p]={name:y};g.type==="ordinal"&&f==null&&(f=p,h=d(g)),i[g.name]=[]});var c=u.get(l)||u.set(l,{categoryWayDim:h,valueWayDim:0});M(r,function(y,p){var g=y.name,m=d(y);if(f==null){var _=c.valueWayDim;v(i[g],_,m),v(o,_,m),c.valueWayDim+=m}else if(f===p)v(i[g],0,m),v(a,0,m);else{var _=c.categoryWayDim;v(i[g],_,m),v(o,_,m),c.categoryWayDim+=m}});function v(y,p,g){for(var m=0;m<g;m++)y.push(p+m)}function d(y){var p=y.dimsDef;return p?p.length:1}return a.length&&(i.itemName=a),o.length&&(i.seriesName=o),i}function Hp(r){var t=r.get("data",!0);if(!t)return Fn(r.ecModel,"dataset",{index:r.get("datasetIndex",!0),id:r.get("datasetId",!0)},he).models[0]}function ow(r){return!r.get("transform",!0)&&!r.get("fromTransformResult",!0)?[]:Fn(r.ecModel,"dataset",{index:r.get("fromDatasetIndex",!0),id:r.get("fromDatasetId",!0)},he).models}function Vp(r,t){return sw(r.data,r.sourceFormat,r.seriesLayoutBy,r.dimensionsDefine,r.startIndex,t)}function sw(r,t,e,i,n,a){var o,s=5;if(Ft(r))return Vt.Not;var u,l;if(i){var f=i[a];H(f)?(u=f.name,l=f.type):z(f)&&(u=f)}if(l!=null)return l==="ordinal"?Vt.Must:Vt.Not;if(t===zt){var h=r;if(e===Di){for(var c=h[a],v=0;v<(c||[]).length&&v<s;v++)if((o=S(c[n+v]))!=null)return o}else for(var v=0;v<h.length&&v<s;v++){var d=h[n+v];if(d&&(o=S(d[a]))!=null)return o}}else if(t===Te){var y=r;if(!u)return Vt.Not;for(var v=0;v<y.length&&v<s;v++){var p=y[v];if(p&&(o=S(p[u]))!=null)return o}}else if(t===ke){var g=r;if(!u)return Vt.Not;var c=g[u];if(!c||Ft(c))return Vt.Not;for(var v=0;v<c.length&&v<s;v++)if((o=S(c[v]))!=null)return o}else if(t===ne)for(var m=r,v=0;v<m.length&&v<s;v++){var p=m[v],_=Nn(p);if(!B(_))return Vt.Not;if((o=S(_[a]))!=null)return o}function S(b){var w=z(b);if(b!=null&&Number.isFinite(Number(b))&&b!=="")return w?Vt.Might:Vt.Not;if(w&&b!=="-")return Vt.Must}return Vt.Not}var uw=Z();function lw(r,t,e){var i=uw.get(t);if(!i)return e;var n=i(r);return n?e.concat(n):e}var Nh=yt();yt();var Bl=function(){function r(){}return r.prototype.getColorFromPalette=function(t,e,i){var n=Lt(this.get("color",!0)),a=this.get("colorLayer",!0);return hw(this,Nh,n,a,t,e,i)},r.prototype.clearColorPalette=function(){vw(this,Nh)},r}();function fw(r,t){for(var e=r.length,i=0;i<e;i++)if(r[i].length>t)return r[i];return r[e-1]}function hw(r,t,e,i,n,a,o){a=a||r;var s=t(a),u=s.paletteIdx||0,l=s.paletteNameMap=s.paletteNameMap||{};if(l.hasOwnProperty(n))return l[n];var f=o==null||!i?e:fw(i,o);if(f=f||e,!(!f||!f.length)){var h=f[u];return n&&(l[n]=h),s.paletteIdx=(u+1)%f.length,h}}function vw(r,t){t(r).paletteIdx=0,t(r).paletteNameMap={}}var fa,Fi,Fh,zh="\0_ec_inner",cw=1,Gp=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.init=function(e,i,n,a,o,s){a=a||{},this.option=null,this._theme=new At(a),this._locale=new At(o),this._optionManager=s},t.prototype.setOption=function(e,i,n){var a=Gh(i);this._optionManager.setOption(e,n,a),this._resetOption(null,a)},t.prototype.resetOption=function(e,i){return this._resetOption(e,Gh(i))},t.prototype._resetOption=function(e,i){var n=!1,a=this._optionManager;if(!e||e==="recreate"){var o=a.mountOption(e==="recreate");!this.option||e==="recreate"?Fh(this,o):(this.restoreData(),this._mergeOption(o,i)),n=!0}if((e==="timeline"||e==="media")&&this.restoreData(),!e||e==="recreate"||e==="timeline"){var s=a.getTimelineOption(this);s&&(n=!0,this._mergeOption(s,i))}if(!e||e==="recreate"||e==="media"){var u=a.getMediaOption(this);u.length&&M(u,function(l){n=!0,this._mergeOption(l,i)},this)}return n},t.prototype.mergeOption=function(e){this._mergeOption(e,null)},t.prototype._mergeOption=function(e,i){var n=this.option,a=this._componentsMap,o=this._componentsCount,s=[],u=Z(),l=i&&i.replaceMergeMainTypeMap;nw(this),M(e,function(h,c){h!=null&&(ft.hasClass(c)?c&&(s.push(c),u.set(c,!0)):n[c]=n[c]==null?K(h):at(n[c],h,!0))}),l&&l.each(function(h,c){ft.hasClass(c)&&!u.get(c)&&(s.push(c),u.set(c,!0))}),ft.topologicalTravel(s,ft.getAllClassMainTypes(),f,this);function f(h){var c=lw(this,h,Lt(e[h])),v=a.get(h),d=v?l&&l.get(h)?"replaceMerge":"normalMerge":"replaceAll",y=Z0(v,c,d);e_(y,h,ft),n[h]=null,a.set(h,null),o.set(h,0);var p=[],g=[],m=0,_;M(y,function(S,b){var w=S.existing,x=S.newOption;if(!x)w&&(w.mergeOption({},this),w.optionUpdated({},!1));else{var D=h==="series",T=ft.getClass(h,S.keyInfo.subType,!D);if(!T)return;if(h==="tooltip"){if(_)return;_=!0}if(w&&w.constructor===T)w.name=S.keyInfo.name,w.mergeOption(x,this),w.optionUpdated(x,!1);else{var C=k({componentIndex:b},S.keyInfo);w=new T(x,this,this,C),k(w,C),S.brandNew&&(w.__requireNewView=!0),w.init(x,this,this),w.optionUpdated(null,!0)}}w?(p.push(w.option),g.push(w),m++):(p.push(void 0),g.push(void 0))},this),n[h]=p,a.set(h,g),o.set(h,m),h==="series"&&fa(this)}this._seriesIndices||fa(this)},t.prototype.getOption=function(){var e=K(this.option);return M(e,function(i,n){if(ft.hasClass(n)){for(var a=Lt(i),o=a.length,s=!1,u=o-1;u>=0;u--)a[u]&&!_n(a[u])?s=!0:(a[u]=null,!s&&o--);a.length=o,e[n]=a}}),delete e[zh],e},t.prototype.getTheme=function(){return this._theme},t.prototype.getLocaleModel=function(){return this._locale},t.prototype.setUpdatePayload=function(e){this._payload=e},t.prototype.getUpdatePayload=function(){return this._payload},t.prototype.getComponent=function(e,i){var n=this._componentsMap.get(e);if(n){var a=n[i||0];if(a)return a;if(i==null){for(var o=0;o<n.length;o++)if(n[o])return n[o]}}},t.prototype.queryComponents=function(e){var i=e.mainType;if(!i)return[];var n=e.index,a=e.id,o=e.name,s=this._componentsMap.get(i);if(!s||!s.length)return[];var u;return n!=null?(u=[],M(Lt(n),function(l){s[l]&&u.push(s[l])})):a!=null?u=Hh("id",a,s):o!=null?u=Hh("name",o,s):u=Tt(s,function(l){return!!l}),Vh(u,e)},t.prototype.findComponents=function(e){var i=e.query,n=e.mainType,a=s(i),o=a?this.queryComponents(a):Tt(this._componentsMap.get(n),function(l){return!!l});return u(Vh(o,e));function s(l){var f=n+"Index",h=n+"Id",c=n+"Name";return l&&(l[f]!=null||l[h]!=null||l[c]!=null)?{mainType:n,index:l[f],id:l[h],name:l[c]}:null}function u(l){return e.filter?Tt(l,e.filter):l}},t.prototype.eachComponent=function(e,i,n){var a=this._componentsMap;if(U(e)){var o=i,s=e;a.each(function(h,c){for(var v=0;h&&v<h.length;v++){var d=h[v];d&&s.call(o,c,d,d.componentIndex)}})}else for(var u=z(e)?a.get(e):H(e)?this.findComponents(e):null,l=0;u&&l<u.length;l++){var f=u[l];f&&i.call(n,f,f.componentIndex)}},t.prototype.getSeriesByName=function(e){var i=Se(e,null);return Tt(this._componentsMap.get("series"),function(n){return!!n&&i!=null&&n.name===i})},t.prototype.getSeriesByIndex=function(e){return this._componentsMap.get("series")[e]},t.prototype.getSeriesByType=function(e){return Tt(this._componentsMap.get("series"),function(i){return!!i&&i.subType===e})},t.prototype.getSeries=function(){return Tt(this._componentsMap.get("series"),function(e){return!!e})},t.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},t.prototype.eachSeries=function(e,i){Fi(this),M(this._seriesIndices,function(n){var a=this._componentsMap.get("series")[n];e.call(i,a,n)},this)},t.prototype.eachRawSeries=function(e,i){M(this._componentsMap.get("series"),function(n){n&&e.call(i,n,n.componentIndex)})},t.prototype.eachSeriesByType=function(e,i,n){Fi(this),M(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];o.subType===e&&i.call(n,o,a)},this)},t.prototype.eachRawSeriesByType=function(e,i,n){return M(this.getSeriesByType(e),i,n)},t.prototype.isSeriesFiltered=function(e){return Fi(this),this._seriesIndicesMap.get(e.componentIndex)==null},t.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},t.prototype.filterSeries=function(e,i){Fi(this);var n=[];M(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];e.call(i,o,a)&&n.push(a)},this),this._seriesIndices=n,this._seriesIndicesMap=Z(n)},t.prototype.restoreData=function(e){fa(this);var i=this._componentsMap,n=[];i.each(function(a,o){ft.hasClass(o)&&n.push(o)}),ft.topologicalTravel(n,ft.getAllClassMainTypes(),function(a){M(i.get(a),function(o){o&&(a!=="series"||!dw(o,e))&&o.restoreData()})})},t.internalField=function(){fa=function(e){var i=e._seriesIndices=[];M(e._componentsMap.get("series"),function(n){n&&i.push(n.componentIndex)}),e._seriesIndicesMap=Z(i)},Fi=function(e){},Fh=function(e,i){e.option={},e.option[zh]=cw,e._componentsMap=Z({series:[]}),e._componentsCount=Z();var n=i.aria;H(n)&&n.enabled==null&&(n.enabled=!0),pw(i,e._theme.option),at(i,iw,!1),e._mergeOption(i,null)}}(),t}(At);function dw(r,t){if(t){var e=t.seriesIndex,i=t.seriesId,n=t.seriesName;return e!=null&&r.componentIndex!==e||i!=null&&r.id!==i||n!=null&&r.name!==n}}function pw(r,t){var e=r.color&&!r.colorLayer;M(t,function(i,n){n==="colorLayer"&&e||ft.hasClass(n)||(typeof i=="object"?r[n]=r[n]?at(r[n],i,!1):K(i):r[n]==null&&(r[n]=i))})}function Hh(r,t,e){if(B(t)){var i=Z();return M(t,function(a){if(a!=null){var o=Se(a,null);o!=null&&i.set(a,!0)}}),Tt(e,function(a){return a&&i.get(a[r])})}else{var n=Se(t,null);return Tt(e,function(a){return a&&n!=null&&a[r]===n})}}function Vh(r,t){return t.hasOwnProperty("subType")?Tt(r,function(e){return e&&e.subType===t.subType}):r}function Gh(r){var t=Z();return r&&M(Lt(r.replaceMerge),function(e){t.set(e,!0)}),{replaceMergeMainTypeMap:t}}be(Gp,Bl);const Wp=Gp;var gw=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],yw=function(){function r(t){M(gw,function(e){this[e]=ht(t[e],t)},this)}return r}();const Up=yw;var Ms={},mw=function(){function r(){this._coordinateSystems=[]}return r.prototype.create=function(t,e){var i=[];M(Ms,function(n,a){var o=n.create(t,e);i=i.concat(o||[])}),this._coordinateSystems=i},r.prototype.update=function(t,e){M(this._coordinateSystems,function(i){i.update&&i.update(t,e)})},r.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},r.register=function(t,e){Ms[t]=e},r.get=function(t){return Ms[t]},r}();const Nl=mw;var _w=/^(min|max)?(.+)$/,Sw=function(){function r(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return r.prototype.setOption=function(t,e,i){t&&(M(Lt(t.series),function(o){o&&o.data&&Ft(o.data)&&tu(o.data)}),M(Lt(t.dataset),function(o){o&&o.source&&Ft(o.source)&&tu(o.source)})),t=K(t);var n=this._optionBackup,a=ww(t,e,!n);this._newBaseOption=a.baseOption,n?(a.timelineOptions.length&&(n.timelineOptions=a.timelineOptions),a.mediaList.length&&(n.mediaList=a.mediaList),a.mediaDefault&&(n.mediaDefault=a.mediaDefault)):this._optionBackup=a},r.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],K(t?e.baseOption:this._newBaseOption)},r.prototype.getTimelineOption=function(t){var e,i=this._timelineOptions;if(i.length){var n=t.getComponent("timeline");n&&(e=K(i[n.getCurrentIndex()]))}return e},r.prototype.getMediaOption=function(t){var e=this._api.getWidth(),i=this._api.getHeight(),n=this._mediaList,a=this._mediaDefault,o=[],s=[];if(!n.length&&!a)return s;for(var u=0,l=n.length;u<l;u++)bw(n[u].query,e,i)&&o.push(u);return!o.length&&a&&(o=[-1]),o.length&&!Tw(o,this._currentMediaIndices)&&(s=G(o,function(f){return K(f===-1?a.option:n[f].option)})),this._currentMediaIndices=o,s},r}();function ww(r,t,e){var i=[],n,a,o=r.baseOption,s=r.timeline,u=r.options,l=r.media,f=!!r.media,h=!!(u||s||o&&o.timeline);o?(a=o,a.timeline||(a.timeline=s)):((h||f)&&(r.options=r.media=null),a=r),f&&B(l)&&M(l,function(v){v&&v.option&&(v.query?i.push(v):n||(n=v))}),c(a),M(u,function(v){return c(v)}),M(i,function(v){return c(v.option)});function c(v){M(t,function(d){d(v,e)})}return{baseOption:a,timelineOptions:u||[],mediaDefault:n,mediaList:i}}function bw(r,t,e){var i={width:t,height:e,aspectratio:t/e},n=!0;return M(r,function(a,o){var s=o.match(_w);if(!(!s||!s[1]||!s[2])){var u=s[1],l=s[2].toLowerCase();xw(i[l],a,u)||(n=!1)}}),n}function xw(r,t,e){return e==="min"?r>=t:e==="max"?r<=t:r===t}function Tw(r,t){return r.join(",")===t.join(",")}const Cw=Sw;var oe=M,Cn=H,Wh=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function Ds(r){var t=r&&r.itemStyle;if(t)for(var e=0,i=Wh.length;e<i;e++){var n=Wh[e],a=t.normal,o=t.emphasis;a&&a[n]&&(r[n]=r[n]||{},r[n].normal?at(r[n].normal,a[n]):r[n].normal=a[n],a[n]=null),o&&o[n]&&(r[n]=r[n]||{},r[n].emphasis?at(r[n].emphasis,o[n]):r[n].emphasis=o[n],o[n]=null)}}function xt(r,t,e){if(r&&r[t]&&(r[t].normal||r[t].emphasis)){var i=r[t].normal,n=r[t].emphasis;i&&(e?(r[t].normal=r[t].emphasis=null,tt(r[t],i)):r[t]=i),n&&(r.emphasis=r.emphasis||{},r.emphasis[t]=n,n.focus&&(r.emphasis.focus=n.focus),n.blurScope&&(r.emphasis.blurScope=n.blurScope))}}function en(r){xt(r,"itemStyle"),xt(r,"lineStyle"),xt(r,"areaStyle"),xt(r,"label"),xt(r,"labelLine"),xt(r,"upperLabel"),xt(r,"edgeLabel")}function dt(r,t){var e=Cn(r)&&r[t],i=Cn(e)&&e.textStyle;if(i)for(var n=0,a=Wf.length;n<a;n++){var o=Wf[n];i.hasOwnProperty(o)&&(e[o]=i[o])}}function qt(r){r&&(en(r),dt(r,"label"),r.emphasis&&dt(r.emphasis,"label"))}function Mw(r){if(Cn(r)){Ds(r),en(r),dt(r,"label"),dt(r,"upperLabel"),dt(r,"edgeLabel"),r.emphasis&&(dt(r.emphasis,"label"),dt(r.emphasis,"upperLabel"),dt(r.emphasis,"edgeLabel"));var t=r.markPoint;t&&(Ds(t),qt(t));var e=r.markLine;e&&(Ds(e),qt(e));var i=r.markArea;i&&qt(i);var n=r.data;if(r.type==="graph"){n=n||r.nodes;var a=r.links||r.edges;if(a&&!Ft(a))for(var o=0;o<a.length;o++)qt(a[o]);M(r.categories,function(l){en(l)})}if(n&&!Ft(n))for(var o=0;o<n.length;o++)qt(n[o]);if(t=r.markPoint,t&&t.data)for(var s=t.data,o=0;o<s.length;o++)qt(s[o]);if(e=r.markLine,e&&e.data)for(var u=e.data,o=0;o<u.length;o++)B(u[o])?(qt(u[o][0]),qt(u[o][1])):qt(u[o]);r.type==="gauge"?(dt(r,"axisLabel"),dt(r,"title"),dt(r,"detail")):r.type==="treemap"?(xt(r.breadcrumb,"itemStyle"),M(r.levels,function(l){en(l)})):r.type==="tree"&&en(r.leaves)}}function Me(r){return B(r)?r:r?[r]:[]}function Uh(r){return(B(r)?r[0]:r)||{}}function Dw(r,t){oe(Me(r.series),function(i){Cn(i)&&Mw(i)});var e=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&e.push("valueAxis","categoryAxis","logAxis","timeAxis"),oe(e,function(i){oe(Me(r[i]),function(n){n&&(dt(n,"axisLabel"),dt(n.axisPointer,"label"))})}),oe(Me(r.parallel),function(i){var n=i&&i.parallelAxisDefault;dt(n,"axisLabel"),dt(n&&n.axisPointer,"label")}),oe(Me(r.calendar),function(i){xt(i,"itemStyle"),dt(i,"dayLabel"),dt(i,"monthLabel"),dt(i,"yearLabel")}),oe(Me(r.radar),function(i){dt(i,"name"),i.name&&i.axisName==null&&(i.axisName=i.name,delete i.name),i.nameGap!=null&&i.axisNameGap==null&&(i.axisNameGap=i.nameGap,delete i.nameGap)}),oe(Me(r.geo),function(i){Cn(i)&&(qt(i),oe(Me(i.regions),function(n){qt(n)}))}),oe(Me(r.timeline),function(i){qt(i),xt(i,"label"),xt(i,"itemStyle"),xt(i,"controlStyle",!0);var n=i.data;B(n)&&M(n,function(a){H(a)&&(xt(a,"label"),xt(a,"itemStyle"))})}),oe(Me(r.toolbox),function(i){xt(i,"iconStyle"),oe(i.feature,function(n){xt(n,"iconStyle")})}),dt(Uh(r.axisPointer),"label"),dt(Uh(r.tooltip).axisPointer,"label")}function Aw(r,t){for(var e=t.split(","),i=r,n=0;n<e.length&&(i=i&&i[e[n]],i!=null);n++);return i}function Lw(r,t,e,i){for(var n=t.split(","),a=r,o,s=0;s<n.length-1;s++)o=n[s],a[o]==null&&(a[o]={}),a=a[o];(i||a[n[s]]==null)&&(a[n[s]]=e)}function Yh(r){r&&M(Pw,function(t){t[0]in r&&!(t[1]in r)&&(r[t[1]]=r[t[0]])})}var Pw=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],Iw=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],As=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function zi(r){var t=r&&r.itemStyle;if(t)for(var e=0;e<As.length;e++){var i=As[e][1],n=As[e][0];t[i]!=null&&(t[n]=t[i])}}function $h(r){r&&r.alignTo==="edge"&&r.margin!=null&&r.edgeDistance==null&&(r.edgeDistance=r.margin)}function Xh(r){r&&r.downplay&&!r.blur&&(r.blur=r.downplay)}function Ew(r){r&&r.focusNodeAdjacency!=null&&(r.emphasis=r.emphasis||{},r.emphasis.focus==null&&(r.emphasis.focus="adjacency"))}function Yp(r,t){if(r)for(var e=0;e<r.length;e++)t(r[e]),r[e]&&Yp(r[e].children,t)}function $p(r,t){Dw(r,t),r.series=Lt(r.series),M(r.series,function(e){if(H(e)){var i=e.type;if(i==="line")e.clipOverflow!=null&&(e.clip=e.clipOverflow);else if(i==="pie"||i==="gauge"){e.clockWise!=null&&(e.clockwise=e.clockWise),$h(e.label);var n=e.data;if(n&&!Ft(n))for(var a=0;a<n.length;a++)$h(n[a]);e.hoverOffset!=null&&(e.emphasis=e.emphasis||{},(e.emphasis.scaleSize=null)&&(e.emphasis.scaleSize=e.hoverOffset))}else if(i==="gauge"){var o=Aw(e,"pointer.color");o!=null&&Lw(e,"itemStyle.color",o)}else if(i==="bar"){zi(e),zi(e.backgroundStyle),zi(e.emphasis);var n=e.data;if(n&&!Ft(n))for(var a=0;a<n.length;a++)typeof n[a]=="object"&&(zi(n[a]),zi(n[a]&&n[a].emphasis))}else if(i==="sunburst"){var s=e.highlightPolicy;s&&(e.emphasis=e.emphasis||{},e.emphasis.focus||(e.emphasis.focus=s)),Xh(e),Yp(e.data,Xh)}else i==="graph"||i==="sankey"?Ew(e):i==="map"&&(e.mapType&&!e.map&&(e.map=e.mapType),e.mapLocation&&tt(e,e.mapLocation));e.hoverAnimation!=null&&(e.emphasis=e.emphasis||{},e.emphasis&&e.emphasis.scale==null&&(e.emphasis.scale=e.hoverAnimation)),Yh(e)}}),r.dataRange&&(r.visualMap=r.dataRange),M(Iw,function(e){var i=r[e];i&&(B(i)||(i=[i]),M(i,function(n){Yh(n)}))})}function Rw(r){var t=Z();r.eachSeries(function(e){var i=e.get("stack");if(i){var n=t.get(i)||t.set(i,[]),a=e.getData(),o={stackResultDimension:a.getCalculationInfo("stackResultDimension"),stackedOverDimension:a.getCalculationInfo("stackedOverDimension"),stackedDimension:a.getCalculationInfo("stackedDimension"),stackedByDimension:a.getCalculationInfo("stackedByDimension"),isStackedByIndex:a.getCalculationInfo("isStackedByIndex"),data:a,seriesModel:e};if(!o.stackedDimension||!(o.isStackedByIndex||o.stackedByDimension))return;n.length&&a.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(o)}}),t.each(kw)}function kw(r){M(r,function(t,e){var i=[],n=[NaN,NaN],a=[t.stackResultDimension,t.stackedOverDimension],o=t.data,s=t.isStackedByIndex,u=t.seriesModel.get("stackStrategy")||"samesign";o.modify(a,function(l,f,h){var c=o.get(t.stackedDimension,h);if(isNaN(c))return n;var v,d;s?d=o.getRawIndex(h):v=o.get(t.stackedByDimension,h);for(var y=NaN,p=e-1;p>=0;p--){var g=r[p];if(s||(d=g.data.rawIndexOf(g.stackedByDimension,v)),d>=0){var m=g.data.getByRawIndex(g.stackResultDimension,d);if(u==="all"||u==="positive"&&m>0||u==="negative"&&m<0||u==="samesign"&&c>=0&&m>0||u==="samesign"&&c<=0&&m<0){c=G0(c,m),y=m;break}}}return i[0]=c,i[1]=y,i})})}var Ao=function(){function r(t){this.data=t.data||(t.sourceFormat===ke?{}:[]),this.sourceFormat=t.sourceFormat||Fp,this.seriesLayoutBy=t.seriesLayoutBy||Pe,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var i=0;i<e.length;i++){var n=e[i];n.type==null&&Vp(this,i)===Vt.Must&&(n.type="ordinal")}}return r}();function Fl(r){return r instanceof Ao}function Lu(r,t,e){e=e||Xp(r);var i=t.seriesLayoutBy,n=Bw(r,e,i,t.sourceHeader,t.dimensions),a=new Ao({data:r,sourceFormat:e,seriesLayoutBy:i,dimensionsDefine:n.dimensionsDefine,startIndex:n.startIndex,dimensionsDetectedCount:n.dimensionsDetectedCount,metaRawOption:K(t)});return a}function zl(r){return new Ao({data:r,sourceFormat:Ft(r)?je:ne})}function Ow(r){return new Ao({data:r.data,sourceFormat:r.sourceFormat,seriesLayoutBy:r.seriesLayoutBy,dimensionsDefine:K(r.dimensionsDefine),startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount})}function Xp(r){var t=Fp;if(Ft(r))t=je;else if(B(r)){r.length===0&&(t=zt);for(var e=0,i=r.length;e<i;e++){var n=r[e];if(n!=null){if(B(n)||Ft(n)){t=zt;break}else if(H(n)){t=Te;break}}}}else if(H(r)){for(var a in r)if(wi(r,a)&&Nt(r[a])){t=ke;break}}return t}function Bw(r,t,e,i,n){var a,o;if(!r)return{dimensionsDefine:Zh(n),startIndex:o,dimensionsDetectedCount:a};if(t===zt){var s=r;i==="auto"||i==null?qh(function(l){l!=null&&l!=="-"&&(z(l)?o==null&&(o=1):o=0)},e,s,10):o=vt(i)?i:i?1:0,!n&&o===1&&(n=[],qh(function(l,f){n[f]=l!=null?l+"":""},e,s,1/0)),a=n?n.length:e===Di?s.length:s[0]?s[0].length:null}else if(t===Te)n||(n=Nw(r));else if(t===ke)n||(n=[],M(r,function(l,f){n.push(f)}));else if(t===ne){var u=Nn(r[0]);a=B(u)&&u.length||1}return{startIndex:o,dimensionsDefine:Zh(n),dimensionsDetectedCount:a}}function Nw(r){for(var t=0,e;t<r.length&&!(e=r[t++]););if(e)return lt(e)}function Zh(r){if(r){var t=Z();return G(r,function(e,i){e=H(e)?e:{name:e};var n={name:e.name,displayName:e.displayName,type:e.type};if(n.name==null)return n;n.name+="",n.displayName==null&&(n.displayName=n.name);var a=t.get(n.name);return a?n.name+="-"+a.count++:t.set(n.name,{count:1}),n})}}function qh(r,t,e,i){if(t===Di)for(var n=0;n<e.length&&n<i;n++)r(e[n]?e[n][0]:null,n);else for(var a=e[0]||[],n=0;n<a.length&&n<i;n++)r(a[n],n)}function Zp(r){var t=r.sourceFormat;return t===Te||t===ke}var wr,br,xr,Kh,Qh,qp=function(){function r(t,e){var i=Fl(t)?t:zl(t);this._source=i;var n=this._data=i.data;i.sourceFormat===je&&(this._offset=0,this._dimSize=e,this._data=n),Qh(this,n,i)}return r.prototype.getSource=function(){return this._source},r.prototype.count=function(){return 0},r.prototype.getItem=function(t,e){},r.prototype.appendData=function(t){},r.prototype.clean=function(){},r.protoInitialize=function(){var t=r.prototype;t.pure=!1,t.persistent=!0}(),r.internalField=function(){var t;Qh=function(o,s,u){var l=u.sourceFormat,f=u.seriesLayoutBy,h=u.startIndex,c=u.dimensionsDefine,v=Kh[Hl(l,f)];if(k(o,v),l===je)o.getItem=e,o.count=n,o.fillStorage=i;else{var d=Kp(l,f);o.getItem=ht(d,null,s,h,c);var y=Qp(l,f);o.count=ht(y,null,s,h,c)}};var e=function(o,s){o=o-this._offset,s=s||[];for(var u=this._data,l=this._dimSize,f=l*o,h=0;h<l;h++)s[h]=u[f+h];return s},i=function(o,s,u,l){for(var f=this._data,h=this._dimSize,c=0;c<h;c++){for(var v=l[c],d=v[0]==null?1/0:v[0],y=v[1]==null?-1/0:v[1],p=s-o,g=u[c],m=0;m<p;m++){var _=f[m*h+c];g[o+m]=_,_<d&&(d=_),_>y&&(y=_)}v[0]=d,v[1]=y}},n=function(){return this._data?this._data.length/this._dimSize:0};Kh=(t={},t[zt+"_"+Pe]={pure:!0,appendData:a},t[zt+"_"+Di]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[Te]={pure:!0,appendData:a},t[ke]={pure:!0,appendData:function(o){var s=this._data;M(o,function(u,l){for(var f=s[l]||(s[l]=[]),h=0;h<(u||[]).length;h++)f.push(u[h])})}},t[ne]={appendData:a},t[je]={persistent:!1,pure:!0,appendData:function(o){this._data=o},clean:function(){this._offset+=this.count(),this._data=null}},t);function a(o){for(var s=0;s<o.length;s++)this._data.push(o[s])}}(),r}(),Jh=function(r,t,e,i){return r[i]},Fw=(wr={},wr[zt+"_"+Pe]=function(r,t,e,i){return r[i+t]},wr[zt+"_"+Di]=function(r,t,e,i,n){i+=t;for(var a=n||[],o=r,s=0;s<o.length;s++){var u=o[s];a[s]=u?u[i]:null}return a},wr[Te]=Jh,wr[ke]=function(r,t,e,i,n){for(var a=n||[],o=0;o<e.length;o++){var s=e[o].name,u=r[s];a[o]=u?u[i]:null}return a},wr[ne]=Jh,wr);function Kp(r,t){var e=Fw[Hl(r,t)];return e}var jh=function(r,t,e){return r.length},zw=(br={},br[zt+"_"+Pe]=function(r,t,e){return Math.max(0,r.length-t)},br[zt+"_"+Di]=function(r,t,e){var i=r[0];return i?Math.max(0,i.length-t):0},br[Te]=jh,br[ke]=function(r,t,e){var i=e[0].name,n=r[i];return n?n.length:0},br[ne]=jh,br);function Qp(r,t){var e=zw[Hl(r,t)];return e}var Ls=function(r,t,e){return r[t]},Hw=(xr={},xr[zt]=Ls,xr[Te]=function(r,t,e){return r[e]},xr[ke]=Ls,xr[ne]=function(r,t,e){var i=Nn(r);return i instanceof Array?i[t]:i},xr[je]=Ls,xr);function Jp(r){var t=Hw[r];return t}function Hl(r,t){return r===zt?r+"_"+t:r}function bi(r,t,e){if(r){var i=r.getRawDataItem(t);if(i!=null){var n=r.getStore(),a=n.getSource().sourceFormat;if(e!=null){var o=r.getDimensionIndex(e),s=n.getDimensionProperty(o);return Jp(a)(i,o,s)}else{var u=i;return a===ne&&(u=Nn(i)),u}}}}var Vw=/\{@(.+?)\}/g,Gw=function(){function r(){}return r.prototype.getDataParams=function(t,e){var i=this.getData(e),n=this.getRawValue(t,e),a=i.getRawIndex(t),o=i.getName(t),s=i.getRawDataItem(t),u=i.getItemVisual(t,"style"),l=u&&u[i.getItemVisual(t,"drawType")||"fill"],f=u&&u.stroke,h=this.mainType,c=h==="series",v=i.userOutput&&i.userOutput.get();return{componentType:h,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:c?this.subType:null,seriesIndex:this.seriesIndex,seriesId:c?this.id:null,seriesName:c?this.name:null,name:o,dataIndex:a,data:s,dataType:e,value:n,color:l,borderColor:f,dimensionNames:v?v.fullDimensions:null,encode:v?v.encode:null,$vars:["seriesName","name","value"]}},r.prototype.getFormattedLabel=function(t,e,i,n,a,o){e=e||"normal";var s=this.getData(i),u=this.getDataParams(t,i);if(o&&(u.value=o.interpolatedValue),n!=null&&B(u.value)&&(u.value=u.value[n]),!a){var l=s.getItemModel(t);a=l.get(e==="normal"?["label","formatter"]:[e,"label","formatter"])}if(U(a))return u.status=e,u.dimensionIndex=n,a(u);if(z(a)){var f=kp(a,u);return f.replace(Vw,function(h,c){var v=c.length,d=c;d.charAt(0)==="["&&d.charAt(v-1)==="]"&&(d=+d.slice(1,v-1));var y=bi(s,t,d);if(o&&B(o.interpolatedValue)){var p=s.getDimensionIndex(d);p>=0&&(y=o.interpolatedValue[p])}return y!=null?y+"":""})}},r.prototype.getRawValue=function(t,e){return bi(this.getData(e),t)},r.prototype.formatTooltip=function(t,e,i){},r}();function tv(r){var t,e;return H(r)?r.type&&(e=r):t=r,{text:t,frag:e}}function vn(r){return new Ww(r)}var Ww=function(){function r(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return r.prototype.perform=function(t){var e=this._upstream,i=t&&t.skip;if(this._dirty&&e){var n=this.context;n.data=n.outputData=e.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var a;this._plan&&!i&&(a=this._plan(this.context));var o=f(this._modBy),s=this._modDataCount||0,u=f(t&&t.modBy),l=t&&t.modDataCount||0;(o!==u||s!==l)&&(a="reset");function f(m){return!(m>=1)&&(m=1),m}var h;(this._dirty||a==="reset")&&(this._dirty=!1,h=this._doReset(i)),this._modBy=u,this._modDataCount=l;var c=t&&t.step;if(e?this._dueEnd=e._outputDueEnd:this._dueEnd=this._count?this._count(this.context):1/0,this._progress){var v=this._dueIndex,d=Math.min(c!=null?this._dueIndex+c:1/0,this._dueEnd);if(!i&&(h||v<d)){var y=this._progress;if(B(y))for(var p=0;p<y.length;p++)this._doProgress(y[p],v,d,u,l);else this._doProgress(y,v,d,u,l)}this._dueIndex=d;var g=this._settedOutputEnd!=null?this._settedOutputEnd:d;this._outputDueEnd=g}else this._dueIndex=this._outputDueEnd=this._settedOutputEnd!=null?this._settedOutputEnd:this._dueEnd;return this.unfinished()},r.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},r.prototype._doProgress=function(t,e,i,n,a){ev.reset(e,i,n,a),this._callingProgress=t,this._callingProgress({start:e,end:i,count:i-e,next:ev.next},this.context)},r.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null;var e,i;!t&&this._reset&&(e=this._reset(this.context),e&&e.progress&&(i=e.forceFirstProgress,e=e.progress),B(e)&&!e.length&&(e=null)),this._progress=e,this._modBy=this._modDataCount=null;var n=this._downstream;return n&&n.dirty(),i},r.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},r.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},r.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},r.prototype.getUpstream=function(){return this._upstream},r.prototype.getDownstream=function(){return this._downstream},r.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},r}(),ev=function(){var r,t,e,i,n,a={reset:function(u,l,f,h){t=u,r=l,e=f,i=h,n=Math.ceil(i/e),a.next=e>1&&i>0?s:o}};return a;function o(){return t<r?t++:null}function s(){var u=t%n*e+Math.ceil(t/n),l=t>=r?null:u<i?u:t;return t++,l}}();function za(r,t){var e=t&&t.type;return e==="ordinal"?r:(e==="time"&&!vt(r)&&r!=null&&r!=="-"&&(r=+Ee(r)),r==null||r===""?NaN:Number(r))}Z({number:function(r){return parseFloat(r)},time:function(r){return+Ee(r)},trim:function(r){return z(r)?me(r):r}});var Uw=function(){function r(t,e){var i=t==="desc";this._resultLT=i?1:-1,e==null&&(e=i?"min":"max"),this._incomparable=e==="min"?-1/0:1/0}return r.prototype.evaluate=function(t,e){var i=vt(t)?t:Qa(t),n=vt(e)?e:Qa(e),a=isNaN(i),o=isNaN(n);if(a&&(i=this._incomparable),o&&(n=this._incomparable),a&&o){var s=z(t),u=z(e);s&&(i=u?t:0),u&&(n=s?e:0)}return i<n?this._resultLT:i>n?-this._resultLT:0},r}(),Yw=function(){function r(){}return r.prototype.getRawData=function(){throw new Error("not supported")},r.prototype.getRawDataItem=function(t){throw new Error("not supported")},r.prototype.cloneRawData=function(){},r.prototype.getDimensionInfo=function(t){},r.prototype.cloneAllDimensionInfo=function(){},r.prototype.count=function(){},r.prototype.retrieveValue=function(t,e){},r.prototype.retrieveValueFromItem=function(t,e){},r.prototype.convertValue=function(t,e){return za(t,e)},r}();function $w(r,t){var e=new Yw,i=r.data,n=e.sourceFormat=r.sourceFormat,a=r.startIndex,o="";r.seriesLayoutBy!==Pe&&Rt(o);var s=[],u={},l=r.dimensionsDefine;if(l)M(l,function(y,p){var g=y.name,m={index:p,name:g,displayName:y.displayName};if(s.push(m),g!=null){var _="";wi(u,g)&&Rt(_),u[g]=m}});else for(var f=0;f<r.dimensionsDetectedCount;f++)s.push({index:f});var h=Kp(n,Pe);t.__isBuiltIn&&(e.getRawDataItem=function(y){return h(i,a,s,y)},e.getRawData=ht(Xw,null,r)),e.cloneRawData=ht(Zw,null,r);var c=Qp(n,Pe);e.count=ht(c,null,i,a,s);var v=Jp(n);e.retrieveValue=function(y,p){var g=h(i,a,s,y);return d(g,p)};var d=e.retrieveValueFromItem=function(y,p){if(y!=null){var g=s[p];if(g)return v(y,p,g.name)}};return e.getDimensionInfo=ht(qw,null,s,u),e.cloneAllDimensionInfo=ht(Kw,null,s),e}function Xw(r){var t=r.sourceFormat;if(!Vl(t)){var e="";Rt(e)}return r.data}function Zw(r){var t=r.sourceFormat,e=r.data;if(!Vl(t)){var i="";Rt(i)}if(t===zt){for(var n=[],a=0,o=e.length;a<o;a++)n.push(e[a].slice());return n}else if(t===Te){for(var n=[],a=0,o=e.length;a<o;a++)n.push(k({},e[a]));return n}}function qw(r,t,e){if(e!=null){if(vt(e)||!isNaN(e)&&!wi(t,e))return r[e];if(wi(t,e))return t[e]}}function Kw(r){return K(r)}var jp=Z();function Qw(r){r=K(r);var t=r.type,e="";t||Rt(e);var i=t.split(":");i.length!==2&&Rt(e);var n=!1;i[0]==="echarts"&&(t=i[1],n=!0),r.__isBuiltIn=n,jp.set(t,r)}function Jw(r,t,e){var i=Lt(r),n=i.length,a="";n||Rt(a);for(var o=0,s=n;o<s;o++){var u=i[o];t=jw(u,t),o!==s-1&&(t.length=Math.max(t.length,1))}return t}function jw(r,t,e,i){var n="";t.length||Rt(n),H(r)||Rt(n);var a=r.type,o=jp.get(a);o||Rt(n);var s=G(t,function(l){return $w(l,o)}),u=Lt(o.transform({upstream:s[0],upstreamList:s,config:K(r.config)}));return G(u,function(l,f){var h="";H(l)||Rt(h),l.data||Rt(h);var c=Xp(l.data);Vl(c)||Rt(h);var v,d=t[0];if(d&&f===0&&!l.dimensions){var y=d.startIndex;y&&(l.data=d.data.slice(0,y).concat(l.data)),v={seriesLayoutBy:Pe,sourceHeader:y,dimensions:d.metaRawOption.dimensions}}else v={seriesLayoutBy:Pe,sourceHeader:0,dimensions:l.dimensions};return Lu(l.data,v,null)})}function Vl(r){return r===zt||r===Te}var Lo="undefined",tb=typeof Uint32Array===Lo?Array:Uint32Array,eb=typeof Uint16Array===Lo?Array:Uint16Array,tg=typeof Int32Array===Lo?Array:Int32Array,rv=typeof Float64Array===Lo?Array:Float64Array,eg={float:rv,int:tg,ordinal:Array,number:Array,time:rv},Ps;function ri(r){return r>65535?tb:eb}function ii(){return[1/0,-1/0]}function rb(r){var t=r.constructor;return t===Array?r.slice():new t(r)}function iv(r,t,e,i,n){var a=eg[e||"float"];if(n){var o=r[t],s=o&&o.length;if(s!==i){for(var u=new a(i),l=0;l<s;l++)u[l]=o[l];r[t]=u}}else r[t]=new a(i)}var Pu=function(){function r(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=Z()}return r.prototype.initData=function(t,e,i){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var n=t.getSource(),a=this.defaultDimValueGetter=Ps[n.sourceFormat];this._dimValueGetter=i||a,this._rawExtent=[],Zp(n),this._dimensions=G(e,function(o){return{type:o.type,property:o.property}}),this._initDataFromProvider(0,t.count())},r.prototype.getProvider=function(){return this._provider},r.prototype.getSource=function(){return this._provider.getSource()},r.prototype.ensureCalculationDimension=function(t,e){var i=this._calcDimNameToIdx,n=this._dimensions,a=i.get(t);if(a!=null){if(n[a].type===e)return a}else a=n.length;return n[a]={type:e},i.set(t,a),this._chunks[a]=new eg[e||"float"](this._rawCount),this._rawExtent[a]=ii(),a},r.prototype.collectOrdinalMeta=function(t,e){var i=this._chunks[t],n=this._dimensions[t],a=this._rawExtent,o=n.ordinalOffset||0,s=i.length;o===0&&(a[t]=ii());for(var u=a[t],l=o;l<s;l++){var f=i[l]=e.parseAndCollect(i[l]);isNaN(f)||(u[0]=Math.min(f,u[0]),u[1]=Math.max(f,u[1]))}n.ordinalMeta=e,n.ordinalOffset=s,n.type="ordinal"},r.prototype.getOrdinalMeta=function(t){var e=this._dimensions[t],i=e.ordinalMeta;return i},r.prototype.getDimensionProperty=function(t){var e=this._dimensions[t];return e&&e.property},r.prototype.appendData=function(t){var e=this._provider,i=this.count();e.appendData(t);var n=e.count();return e.persistent||(n+=i),i<n&&this._initDataFromProvider(i,n,!0),[i,n]},r.prototype.appendValues=function(t,e){for(var i=this._chunks,n=this._dimensions,a=n.length,o=this._rawExtent,s=this.count(),u=s+Math.max(t.length,e||0),l=0;l<a;l++){var f=n[l];iv(i,l,f.type,u,!0)}for(var h=[],c=s;c<u;c++)for(var v=c-s,d=0;d<a;d++){var f=n[d],y=Ps.arrayRows.call(this,t[v]||h,f.property,v,d);i[d][c]=y;var p=o[d];y<p[0]&&(p[0]=y),y>p[1]&&(p[1]=y)}return this._rawCount=this._count=u,{start:s,end:u}},r.prototype._initDataFromProvider=function(t,e,i){for(var n=this._provider,a=this._chunks,o=this._dimensions,s=o.length,u=this._rawExtent,l=G(o,function(m){return m.property}),f=0;f<s;f++){var h=o[f];u[f]||(u[f]=ii()),iv(a,f,h.type,e,i)}if(n.fillStorage)n.fillStorage(t,e,a,u);else for(var c=[],v=t;v<e;v++){c=n.getItem(v,c);for(var d=0;d<s;d++){var y=a[d],p=this._dimValueGetter(c,l[d],v,d);y[v]=p;var g=u[d];p<g[0]&&(g[0]=p),p>g[1]&&(g[1]=p)}}!n.persistent&&n.clean&&n.clean(),this._rawCount=this._count=e,this._extent=[]},r.prototype.count=function(){return this._count},r.prototype.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var i=this._chunks[t];return i?i[this.getRawIndex(e)]:NaN},r.prototype.getValues=function(t,e){var i=[],n=[];if(e==null){e=t,t=[];for(var a=0;a<this._dimensions.length;a++)n.push(a)}else n=t;for(var a=0,o=n.length;a<o;a++)i.push(this.get(n[a],e));return i},r.prototype.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var i=this._chunks[t];return i?i[e]:NaN},r.prototype.getSum=function(t){var e=this._chunks[t],i=0;if(e)for(var n=0,a=this.count();n<a;n++){var o=this.get(t,n);isNaN(o)||(i+=o)}return i},r.prototype.getMedian=function(t){var e=[];this.each([t],function(a){isNaN(a)||e.push(a)});var i=e.sort(function(a,o){return a-o}),n=this.count();return n===0?0:n%2===1?i[(n-1)/2]:(i[n/2]+i[n/2-1])/2},r.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,i=e[t];if(i!=null&&i<this._count&&i===t)return t;for(var n=0,a=this._count-1;n<=a;){var o=(n+a)/2|0;if(e[o]<t)n=o+1;else if(e[o]>t)a=o-1;else return o}return-1},r.prototype.indicesOfNearest=function(t,e,i){var n=this._chunks,a=n[t],o=[];if(!a)return o;i==null&&(i=1/0);for(var s=1/0,u=-1,l=0,f=0,h=this.count();f<h;f++){var c=this.getRawIndex(f),v=e-a[c],d=Math.abs(v);d<=i&&((d<s||d===s&&v>=0&&u<0)&&(s=d,u=v,l=0),v===u&&(o[l++]=f))}return o.length=l,o},r.prototype.getIndices=function(){var t,e=this._indices;if(e){var i=e.constructor,n=this._count;if(i===Array){t=new i(n);for(var a=0;a<n;a++)t[a]=e[a]}else t=new i(e.buffer,0,n)}else{var i=ri(this._rawCount);t=new i(this.count());for(var a=0;a<t.length;a++)t[a]=a}return t},r.prototype.filter=function(t,e){if(!this._count)return this;for(var i=this.clone(),n=i.count(),a=ri(i._rawCount),o=new a(n),s=[],u=t.length,l=0,f=t[0],h=i._chunks,c=0;c<n;c++){var v=void 0,d=i.getRawIndex(c);if(u===0)v=e(c);else if(u===1){var y=h[f][d];v=e(y,c)}else{for(var p=0;p<u;p++)s[p]=h[t[p]][d];s[p]=c,v=e.apply(null,s)}v&&(o[l++]=d)}return l<n&&(i._indices=o),i._count=l,i._extent=[],i._updateGetRawIdx(),i},r.prototype.selectRange=function(t){var e=this.clone(),i=e._count;if(!i)return this;var n=lt(t),a=n.length;if(!a)return this;var o=e.count(),s=ri(e._rawCount),u=new s(o),l=0,f=n[0],h=t[f][0],c=t[f][1],v=e._chunks,d=!1;if(!e._indices){var y=0;if(a===1){for(var p=v[n[0]],g=0;g<i;g++){var m=p[g];(m>=h&&m<=c||isNaN(m))&&(u[l++]=y),y++}d=!0}else if(a===2){for(var p=v[n[0]],_=v[n[1]],S=t[n[1]][0],b=t[n[1]][1],g=0;g<i;g++){var m=p[g],w=_[g];(m>=h&&m<=c||isNaN(m))&&(w>=S&&w<=b||isNaN(w))&&(u[l++]=y),y++}d=!0}}if(!d)if(a===1)for(var g=0;g<o;g++){var x=e.getRawIndex(g),m=v[n[0]][x];(m>=h&&m<=c||isNaN(m))&&(u[l++]=x)}else for(var g=0;g<o;g++){for(var D=!0,x=e.getRawIndex(g),T=0;T<a;T++){var C=n[T],m=v[C][x];(m<t[C][0]||m>t[C][1])&&(D=!1)}D&&(u[l++]=e.getRawIndex(g))}return l<o&&(e._indices=u),e._count=l,e._extent=[],e._updateGetRawIdx(),e},r.prototype.map=function(t,e){var i=this.clone(t);return this._updateDims(i,t,e),i},r.prototype.modify=function(t,e){this._updateDims(this,t,e)},r.prototype._updateDims=function(t,e,i){for(var n=t._chunks,a=[],o=e.length,s=t.count(),u=[],l=t._rawExtent,f=0;f<e.length;f++)l[e[f]]=ii();for(var h=0;h<s;h++){for(var c=t.getRawIndex(h),v=0;v<o;v++)u[v]=n[e[v]][c];u[o]=h;var d=i&&i.apply(null,u);if(d!=null){typeof d!="object"&&(a[0]=d,d=a);for(var f=0;f<d.length;f++){var y=e[f],p=d[f],g=l[y],m=n[y];m&&(m[c]=p),p<g[0]&&(g[0]=p),p>g[1]&&(g[1]=p)}}}},r.prototype.lttbDownSample=function(t,e){var i=this.clone([t],!0),n=i._chunks,a=n[t],o=this.count(),s=0,u=Math.floor(1/e),l=this.getRawIndex(0),f,h,c,v=new(ri(this._rawCount))(Math.min((Math.ceil(o/u)+2)*2,o));v[s++]=l;for(var d=1;d<o-1;d+=u){for(var y=Math.min(d+u,o-1),p=Math.min(d+u*2,o),g=(p+y)/2,m=0,_=y;_<p;_++){var S=this.getRawIndex(_),b=a[S];isNaN(b)||(m+=b)}m/=p-y;var w=d,x=Math.min(d+u,o),D=d-1,T=a[l];f=-1,c=w;for(var C=-1,A=0,_=w;_<x;_++){var S=this.getRawIndex(_),b=a[S];if(isNaN(b)){A++,C<0&&(C=S);continue}h=Math.abs((D-g)*(b-T)-(D-_)*(m-T)),h>f&&(f=h,c=S)}A>0&&A<x-w&&(v[s++]=Math.min(C,c),c=Math.max(C,c)),v[s++]=c,l=c}return v[s++]=this.getRawIndex(o-1),i._count=s,i._indices=v,i.getRawIndex=this._getRawIdx,i},r.prototype.minmaxDownSample=function(t,e){for(var i=this.clone([t],!0),n=i._chunks,a=Math.floor(1/e),o=n[t],s=this.count(),u=new(ri(this._rawCount))(Math.ceil(s/a)*2),l=0,f=0;f<s;f+=a){var h=f,c=o[this.getRawIndex(h)],v=f,d=o[this.getRawIndex(v)],y=a;f+a>s&&(y=s-f);for(var p=0;p<y;p++){var g=this.getRawIndex(f+p),m=o[g];m<c&&(c=m,h=f+p),m>d&&(d=m,v=f+p)}var _=this.getRawIndex(h),S=this.getRawIndex(v);h<v?(u[l++]=_,u[l++]=S):(u[l++]=S,u[l++]=_)}return i._count=l,i._indices=u,i._updateGetRawIdx(),i},r.prototype.downSample=function(t,e,i,n){for(var a=this.clone([t],!0),o=a._chunks,s=[],u=Math.floor(1/e),l=o[t],f=this.count(),h=a._rawExtent[t]=ii(),c=new(ri(this._rawCount))(Math.ceil(f/u)),v=0,d=0;d<f;d+=u){u>f-d&&(u=f-d,s.length=u);for(var y=0;y<u;y++){var p=this.getRawIndex(d+y);s[y]=l[p]}var g=i(s),m=this.getRawIndex(Math.min(d+n(s,g)||0,f-1));l[m]=g,g<h[0]&&(h[0]=g),g>h[1]&&(h[1]=g),c[v++]=m}return a._count=v,a._indices=c,a._updateGetRawIdx(),a},r.prototype.each=function(t,e){if(this._count)for(var i=t.length,n=this._chunks,a=0,o=this.count();a<o;a++){var s=this.getRawIndex(a);switch(i){case 0:e(a);break;case 1:e(n[t[0]][s],a);break;case 2:e(n[t[0]][s],n[t[1]][s],a);break;default:for(var u=0,l=[];u<i;u++)l[u]=n[t[u]][s];l[u]=a,e.apply(null,l)}}},r.prototype.getDataExtent=function(t){var e=this._chunks[t],i=ii();if(!e)return i;var n=this.count(),a=!this._indices,o;if(a)return this._rawExtent[t].slice();if(o=this._extent[t],o)return o.slice();o=i;for(var s=o[0],u=o[1],l=0;l<n;l++){var f=this.getRawIndex(l),h=e[f];h<s&&(s=h),h>u&&(u=h)}return o=[s,u],this._extent[t]=o,o},r.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var i=[],n=this._chunks,a=0;a<n.length;a++)i.push(n[a][e]);return i},r.prototype.clone=function(t,e){var i=new r,n=this._chunks,a=t&&Ti(t,function(s,u){return s[u]=!0,s},{});if(a)for(var o=0;o<n.length;o++)i._chunks[o]=a[o]?rb(n[o]):n[o];else i._chunks=n;return this._copyCommonProps(i),e||(i._indices=this._cloneIndices()),i._updateGetRawIdx(),i},r.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=K(this._extent),t._rawExtent=K(this._rawExtent)},r.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array){var i=this._indices.length;e=new t(i);for(var n=0;n<i;n++)e[n]=this._indices[n]}else e=new t(this._indices);return e}return null},r.prototype._getRawIdxIdentity=function(t){return t},r.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},r.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},r.internalField=function(){function t(e,i,n,a){return za(e[a],this._dimensions[a])}Ps={arrayRows:t,objectRows:function(e,i,n,a){return za(e[i],this._dimensions[a])},keyedColumns:t,original:function(e,i,n,a){var o=e&&(e.value==null?e:e.value);return za(o instanceof Array?o[a]:o,this._dimensions[a])},typedArray:function(e,i,n,a){return e[a]}}}(),r}(),ib=function(){function r(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return r.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},r.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},r.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},r.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},r.prototype._createSource=function(){this._setLocalSource([],[]);var t=this._sourceHost,e=this._getUpstreamSourceManagers(),i=!!e.length,n,a;if(ha(t)){var o=t,s=void 0,u=void 0,l=void 0;if(i){var f=e[0];f.prepareSource(),l=f.getSource(),s=l.data,u=l.sourceFormat,a=[f._getVersionSign()]}else s=o.get("data",!0),u=Ft(s)?je:ne,a=[];var h=this._getSourceMetaRawOption()||{},c=l&&l.metaRawOption||{},v=X(h.seriesLayoutBy,c.seriesLayoutBy)||null,d=X(h.sourceHeader,c.sourceHeader),y=X(h.dimensions,c.dimensions),p=v!==c.seriesLayoutBy||!!d!=!!c.sourceHeader||y;n=p?[Lu(s,{seriesLayoutBy:v,sourceHeader:d,dimensions:y},u)]:[]}else{var g=t;if(i){var m=this._applyTransform(e);n=m.sourceList,a=m.upstreamSignList}else{var _=g.get("source",!0);n=[Lu(_,this._getSourceMetaRawOption(),null)],a=[]}}this._setLocalSource(n,a)},r.prototype._applyTransform=function(t){var e=this._sourceHost,i=e.get("transform",!0),n=e.get("fromTransformResult",!0);if(n!=null){var a="";t.length!==1&&nv(a)}var o,s=[],u=[];return M(t,function(l){l.prepareSource();var f=l.getSource(n||0),h="";n!=null&&!f&&nv(h),s.push(f),u.push(l._getVersionSign())}),i?o=Jw(i,s,{datasetIndex:e.componentIndex}):n!=null&&(o=[Ow(s[0])]),{sourceList:o,upstreamSignList:u}},r.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var i=t[e];if(i._isDirty()||this._upstreamSignList[e]!==i._getVersionSign())return!0}},r.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var i=this._getUpstreamSourceManagers();return i[0]&&i[0].getSource(t)}return e},r.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},r.prototype._innerGetDataStore=function(t,e,i){var n=0,a=this._storeList,o=a[n];o||(o=a[n]={});var s=o[i];if(!s){var u=this._getUpstreamSourceManagers()[0];ha(this._sourceHost)&&u?s=u._innerGetDataStore(t,e,i):(s=new Pu,s.initData(new qp(e,t.length),t)),o[i]=s}return s},r.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(ha(t)){var e=Hp(t);return e?[e.getSourceManager()]:[]}else return G(ow(t),function(i){return i.getSourceManager()})},r.prototype._getSourceMetaRawOption=function(){var t=this._sourceHost,e,i,n;if(ha(t))e=t.get("seriesLayoutBy",!0),i=t.get("sourceHeader",!0),n=t.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var a=t;e=a.get("seriesLayoutBy",!0),i=a.get("sourceHeader",!0),n=a.get("dimensions",!0)}return{seriesLayoutBy:e,sourceHeader:i,dimensions:n}},r}();function ha(r){return r.mainType==="series"}function nv(r){throw new Error(r)}var nb="line-height:1";function rg(r){var t=r.lineHeight;return t==null?nb:"line-height:"+Et(t+"")+"px"}function ig(r,t){var e=r.color||"#6e7079",i=r.fontSize||12,n=r.fontWeight||"400",a=r.color||"#464646",o=r.fontSize||14,s=r.fontWeight||"900";return t==="html"?{nameStyle:"font-size:"+Et(i+"")+"px;color:"+Et(e)+";font-weight:"+Et(n+""),valueStyle:"font-size:"+Et(o+"")+"px;color:"+Et(a)+";font-weight:"+Et(s+"")}:{nameStyle:{fontSize:i,fill:e,fontWeight:n},valueStyle:{fontSize:o,fill:a,fontWeight:s}}}var ab=[0,10,20,30],ob=["",`
`,`

`,`


`];function Mn(r,t){return t.type=r,t}function Iu(r){return r.type==="section"}function ng(r){return Iu(r)?sb:ub}function ag(r){if(Iu(r)){var t=0,e=r.blocks.length,i=e>1||e>0&&!r.noHeader;return M(r.blocks,function(n){var a=ag(n);a>=t&&(t=a+ +(i&&(!a||Iu(n)&&!n.noHeader)))}),t}return 0}function sb(r,t,e,i){var n=t.noHeader,a=lb(ag(t)),o=[],s=t.blocks||[];Ie(!s||B(s)),s=s||[];var u=r.orderMode;if(t.sortBlocks&&u){s=s.slice();var l={valueAsc:"asc",valueDesc:"desc"};if(wi(l,u)){var f=new Uw(l[u],null);s.sort(function(y,p){return f.evaluate(y.sortParam,p.sortParam)})}else u==="seriesDesc"&&s.reverse()}M(s,function(y,p){var g=t.valueFormatter,m=ng(y)(g?k(k({},r),{valueFormatter:g}):r,y,p>0?a.html:0,i);m!=null&&o.push(m)});var h=r.renderMode==="richText"?o.join(a.richText):Eu(i,o.join(""),n?e:a.html);if(n)return h;var c=Au(t.header,"ordinal",r.useUTC),v=ig(i,r.renderMode).nameStyle,d=rg(i);return r.renderMode==="richText"?og(r,c,v)+a.richText+h:Eu(i,'<div style="'+v+";"+d+';">'+Et(c)+"</div>"+h,e)}function ub(r,t,e,i){var n=r.renderMode,a=t.noName,o=t.noValue,s=!t.markerType,u=t.name,l=r.useUTC,f=t.valueFormatter||r.valueFormatter||function(S){return S=B(S)?S:[S],G(S,function(b,w){return Au(b,B(v)?v[w]:v,l)})};if(!(a&&o)){var h=s?"":r.markupStyleCreator.makeTooltipMarker(t.markerType,t.markerColor||"#333",n),c=a?"":Au(u,"ordinal",l),v=t.valueType,d=o?[]:f(t.value,t.dataIndex),y=!s||!a,p=!s&&a,g=ig(i,n),m=g.nameStyle,_=g.valueStyle;return n==="richText"?(s?"":h)+(a?"":og(r,c,m))+(o?"":vb(r,d,y,p,_)):Eu(i,(s?"":h)+(a?"":fb(c,!s,m))+(o?"":hb(d,y,p,_)),e)}}function av(r,t,e,i,n,a){if(r){var o=ng(r),s={useUTC:n,renderMode:e,orderMode:i,markupStyleCreator:t,valueFormatter:r.valueFormatter};return o(s,r,0,a)}}function lb(r){return{html:ab[r],richText:ob[r]}}function Eu(r,t,e){var i='<div style="clear:both"></div>',n="margin: "+e+"px 0 0",a=rg(r);return'<div style="'+n+";"+a+';">'+t+i+"</div>"}function fb(r,t,e){var i=t?"margin-left:2px":"";return'<span style="'+e+";"+i+'">'+Et(r)+"</span>"}function hb(r,t,e,i){var n=e?"10px":"20px",a=t?"float:right;margin-left:"+n:"";return r=B(r)?r:[r],'<span style="'+a+";"+i+'">'+G(r,function(o){return Et(o)}).join("&nbsp;&nbsp;")+"</span>"}function og(r,t,e){return r.markupStyleCreator.wrapRichTextStyle(t,e)}function vb(r,t,e,i,n){var a=[n],o=i?10:20;return e&&a.push({padding:[0,0,0,o],align:"right"}),r.markupStyleCreator.wrapRichTextStyle(B(t)?t.join("  "):t,a)}function cb(r,t){var e=r.getData().getItemVisual(t,"style"),i=e[r.visualDrawType];return Gr(i)}function sg(r,t){var e=r.get("padding");return e??(t==="richText"?[8,10]:10)}var Is=function(){function r(){this.richTextStyles={},this._nextStyleNameId=pd()}return r.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},r.prototype.makeTooltipMarker=function(t,e,i){var n=i==="richText"?this._generateStyleName():null,a=JS({color:e,type:t,renderMode:i,markerId:n});return z(a)?a:(this.richTextStyles[n]=a.style,a.content)},r.prototype.wrapRichTextStyle=function(t,e){var i={};B(e)?M(e,function(a){return k(i,a)}):k(i,e);var n=this._generateStyleName();return this.richTextStyles[n]=i,"{"+n+"|"+t+"}"},r}();function db(r){var t=r.series,e=r.dataIndex,i=r.multipleSeries,n=t.getData(),a=n.mapDimensionsAll("defaultedTooltip"),o=a.length,s=t.getRawValue(e),u=B(s),l=cb(t,e),f,h,c,v;if(o>1||u&&!o){var d=pb(s,t,e,a,l);f=d.inlineValues,h=d.inlineValueTypes,c=d.blocks,v=d.inlineValues[0]}else if(o){var y=n.getDimensionInfo(a[0]);v=f=bi(n,e,a[0]),h=y.type}else v=f=u?s[0]:s;var p=_d(t),g=p&&t.name||"",m=n.getName(e),_=i?g:m;return Mn("section",{header:g,noHeader:i||!p,sortParam:v,blocks:[Mn("nameValue",{markerType:"item",markerColor:l,name:_,noName:!me(_),value:f,valueType:h,dataIndex:e})].concat(c||[])})}function pb(r,t,e,i,n){var a=t.getData(),o=Ti(r,function(h,c,v){var d=a.getDimensionInfo(v);return h=h||d&&d.tooltip!==!1&&d.displayName!=null},!1),s=[],u=[],l=[];i.length?M(i,function(h){f(bi(a,e,h),h)}):M(r,f);function f(h,c){var v=a.getDimensionInfo(c);!v||v.otherDims.tooltip===!1||(o?l.push(Mn("nameValue",{markerType:"subItem",markerColor:n,name:v.displayName,value:h,valueType:v.type})):(s.push(h),u.push(v.type)))}return{inlineValues:s,inlineValueTypes:u,blocks:l}}var ze=yt();function va(r,t){return r.getName(t)||r.getId(t)}var gb="__universalTransitionEnabled",Po=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e._selectedDataIndicesMap={},e}return t.prototype.init=function(e,i,n){this.seriesIndex=this.componentIndex,this.dataTask=vn({count:mb,reset:_b}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(e,n);var a=ze(this).sourceManager=new ib(this);a.prepareSource();var o=this.getInitialData(e,n);sv(o,this),this.dataTask.context.data=o,ze(this).dataBeforeProcessed=o,ov(this),this._initSelectedMapFromData(o)},t.prototype.mergeDefaultAndTheme=function(e,i){var n=xn(this),a=n?Ol(e):{},o=this.subType;ft.hasClass(o)&&(o+="Series"),at(e,i.getTheme().get(this.subType)),at(e,this.getDefaultOption()),Gf(e,"label",["show"]),this.fillDataTextStyle(e.data),n&&Tn(e,a,n)},t.prototype.mergeOption=function(e,i){e=at(this.option,e,!0),this.fillDataTextStyle(e.data);var n=xn(this);n&&Tn(this.option,e,n);var a=ze(this).sourceManager;a.dirty(),a.prepareSource();var o=this.getInitialData(e,i);sv(o,this),this.dataTask.dirty(),this.dataTask.context.data=o,ze(this).dataBeforeProcessed=o,ov(this),this._initSelectedMapFromData(o)},t.prototype.fillDataTextStyle=function(e){if(e&&!Ft(e))for(var i=["show"],n=0;n<e.length;n++)e[n]&&e[n].label&&Gf(e[n],"label",i)},t.prototype.getInitialData=function(e,i){},t.prototype.appendData=function(e){var i=this.getRawData();i.appendData(e.data)},t.prototype.getData=function(e){var i=Ru(this);if(i){var n=i.context.data;return e==null||!n.getLinkedData?n:n.getLinkedData(e)}else return ze(this).data},t.prototype.getAllData=function(){var e=this.getData();return e&&e.getLinkedDataAll?e.getLinkedDataAll():[{data:e}]},t.prototype.setData=function(e){var i=Ru(this);if(i){var n=i.context;n.outputData=e,i!==this.dataTask&&(n.data=e)}ze(this).data=e},t.prototype.getEncode=function(){var e=this.get("encode",!0);if(e)return Z(e)},t.prototype.getSourceManager=function(){return ze(this).sourceManager},t.prototype.getSource=function(){return this.getSourceManager().getSource()},t.prototype.getRawData=function(){return ze(this).dataBeforeProcessed},t.prototype.getColorBy=function(){var e=this.get("colorBy");return e||"series"},t.prototype.isColorBySeries=function(){return this.getColorBy()==="series"},t.prototype.getBaseAxis=function(){var e=this.coordinateSystem;return e&&e.getBaseAxis&&e.getBaseAxis()},t.prototype.formatTooltip=function(e,i,n){return db({series:this,dataIndex:e,multipleSeries:i})},t.prototype.isAnimationEnabled=function(){var e=this.ecModel;if($.node&&!(e&&e.ssr))return!1;var i=this.getShallow("animation");return i&&this.getData().count()>this.getShallow("animationThreshold")&&(i=!1),!!i},t.prototype.restoreData=function(){this.dataTask.dirty()},t.prototype.getColorFromPalette=function(e,i,n){var a=this.ecModel,o=Bl.prototype.getColorFromPalette.call(this,e,i,n);return o||(o=a.getColorFromPalette(e,i,n)),o},t.prototype.coordDimToDataDim=function(e){return this.getRawData().mapDimensionsAll(e)},t.prototype.getProgressive=function(){return this.get("progressive")},t.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},t.prototype.select=function(e,i){this._innerSelect(this.getData(i),e)},t.prototype.unselect=function(e,i){var n=this.option.selectedMap;if(n){var a=this.option.selectedMode,o=this.getData(i);if(a==="series"||n==="all"){this.option.selectedMap={},this._selectedDataIndicesMap={};return}for(var s=0;s<e.length;s++){var u=e[s],l=va(o,u);n[l]=!1,this._selectedDataIndicesMap[l]=-1}}},t.prototype.toggleSelect=function(e,i){for(var n=[],a=0;a<e.length;a++)n[0]=e[a],this.isSelected(e[a],i)?this.unselect(n,i):this.select(n,i)},t.prototype.getSelectedDataIndices=function(){if(this.option.selectedMap==="all")return[].slice.call(this.getData().getIndices());for(var e=this._selectedDataIndicesMap,i=lt(e),n=[],a=0;a<i.length;a++){var o=e[i[a]];o>=0&&n.push(o)}return n},t.prototype.isSelected=function(e,i){var n=this.option.selectedMap;if(!n)return!1;var a=this.getData(i);return(n==="all"||n[va(a,e)])&&!a.getItemModel(e).get(["select","disabled"])},t.prototype.isUniversalTransitionEnabled=function(){if(this[gb])return!0;var e=this.option.universalTransition;return e?e===!0?!0:e&&e.enabled:!1},t.prototype._innerSelect=function(e,i){var n,a,o=this.option,s=o.selectedMode,u=i.length;if(!(!s||!u)){if(s==="series")o.selectedMap="all";else if(s==="multiple"){H(o.selectedMap)||(o.selectedMap={});for(var l=o.selectedMap,f=0;f<u;f++){var h=i[f],c=va(e,h);l[c]=!0,this._selectedDataIndicesMap[c]=e.getRawIndex(h)}}else if(s==="single"||s===!0){var v=i[u-1],c=va(e,v);o.selectedMap=(n={},n[c]=!0,n),this._selectedDataIndicesMap=(a={},a[c]=e.getRawIndex(v),a)}}},t.prototype._initSelectedMapFromData=function(e){if(!this.option.selectedMap){var i=[];e.hasItemOption&&e.each(function(n){var a=e.getRawDataItem(n);a&&a.selected&&i.push(n)}),i.length>0&&this._innerSelect(e,i)}},t.registerClass=function(e){return ft.registerClass(e)},t.protoInitialize=function(){var e=t.prototype;e.type="series.__base__",e.seriesIndex=0,e.ignoreStyleOnData=!1,e.hasSymbolVisual=!1,e.defaultSymbol="circle",e.visualStyleAccessPath="itemStyle",e.visualDrawType="fill"}(),t}(ft);be(Po,Gw);be(Po,Bl);bd(Po,ft);function ov(r){var t=r.name;_d(r)||(r.name=yb(r)||t)}function yb(r){var t=r.getRawData(),e=t.mapDimensionsAll("seriesName"),i=[];return M(e,function(n){var a=t.getDimensionInfo(n);a.displayName&&i.push(a.displayName)}),i.join(" ")}function mb(r){return r.model.getRawData().count()}function _b(r){var t=r.model;return t.setData(t.getRawData().cloneShallow()),Sb}function Sb(r,t){t.outputData&&r.end>t.outputData.count()&&t.model.getRawData().cloneShallow(t.outputData)}function sv(r,t){M(hm(r.CHANGABLE_METHODS,r.DOWNSAMPLE_METHODS),function(e){r.wrapMethod(e,ee(wb,t))})}function wb(r,t){var e=Ru(r);return e&&e.setOutputEnd((t||this).count()),t}function Ru(r){var t=(r.ecModel||{}).scheduler,e=t&&t.getPipeline(r.uid);if(e){var i=e.currentTask;if(i){var n=i.agentStubMap;n&&(i=n.get(r.uid))}return i}}const Dn=Po;var Gl=function(){function r(){this.group=new Ut,this.uid=bo("viewComponent")}return r.prototype.init=function(t,e){},r.prototype.render=function(t,e,i,n){},r.prototype.dispose=function(t,e){},r.prototype.updateView=function(t,e,i,n){},r.prototype.updateLayout=function(t,e,i,n){},r.prototype.updateVisual=function(t,e,i,n){},r.prototype.toggleBlurSeries=function(t,e,i){},r.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)},r}();dl(Gl);po(Gl);const er=Gl;function ug(){var r=yt();return function(t){var e=r(t),i=t.pipelineContext,n=!!e.large,a=!!e.progressiveRender,o=e.large=!!(i&&i.large),s=e.progressiveRender=!!(i&&i.progressiveRender);return(n!==o||a!==s)&&"reset"}}var lg=yt(),bb=ug(),Wl=function(){function r(){this.group=new Ut,this.uid=bo("viewChart"),this.renderTask=vn({plan:xb,reset:Tb}),this.renderTask.context={view:this}}return r.prototype.init=function(t,e){},r.prototype.render=function(t,e,i,n){},r.prototype.highlight=function(t,e,i,n){var a=t.getData(n&&n.dataType);a&&lv(a,n,"emphasis")},r.prototype.downplay=function(t,e,i,n){var a=t.getData(n&&n.dataType);a&&lv(a,n,"normal")},r.prototype.remove=function(t,e){this.group.removeAll()},r.prototype.dispose=function(t,e){},r.prototype.updateView=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.updateLayout=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.updateVisual=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.eachRendered=function(t){Ml(this.group,t)},r.markUpdateMethod=function(t,e){lg(t).updateMethod=e},r.protoInitialize=function(){var t=r.prototype;t.type="chart"}(),r}();function uv(r,t,e){r&&Tu(r)&&(t==="emphasis"?Ja:ja)(r,e)}function lv(r,t,e){var i=Nr(r,t),n=t&&t.highlightKey!=null?A1(t.highlightKey):null;i!=null?M(Lt(i),function(a){uv(r.getItemGraphicEl(a),e,n)}):r.eachItemGraphicEl(function(a){uv(a,e,n)})}dl(Wl);po(Wl);function xb(r){return bb(r.model)}function Tb(r){var t=r.model,e=r.ecModel,i=r.api,n=r.payload,a=t.pipelineContext.progressiveRender,o=r.view,s=n&&lg(n).updateMethod,u=a?"incrementalPrepareRender":s&&o[s]?s:"render";return u!=="render"&&o[u](t,e,i,n),Cb[u]}var Cb={incrementalPrepareRender:{progress:function(r,t){t.view.incrementalRender(r,t.model,t.ecModel,t.api,t.payload)}},render:{forceFirstProgress:!0,progress:function(r,t){t.view.render(t.model,t.ecModel,t.api,t.payload)}}};const tr=Wl;var ao="\0__throttleOriginMethod",fv="\0__throttleRate",hv="\0__throttleType";function Ul(r,t,e){var i,n=0,a=0,o=null,s,u,l,f;t=t||0;function h(){a=new Date().getTime(),o=null,r.apply(u,l||[])}var c=function(){for(var v=[],d=0;d<arguments.length;d++)v[d]=arguments[d];i=new Date().getTime(),u=this,l=v;var y=f||t,p=f||e;f=null,s=i-(p?n:a)-y,clearTimeout(o),p?o=setTimeout(h,y):s>=0?h():o=setTimeout(h,-s),n=i};return c.clear=function(){o&&(clearTimeout(o),o=null)},c.debounceNextCall=function(v){f=v},c}function fg(r,t,e,i){var n=r[t];if(n){var a=n[ao]||n,o=n[hv],s=n[fv];if(s!==e||o!==i){if(e==null||!i)return r[t]=a;n=r[t]=Ul(a,e,i==="debounce"),n[ao]=a,n[hv]=i,n[fv]=e}return n}}function ku(r,t){var e=r[t];e&&e[ao]&&(e.clear&&e.clear(),r[t]=e[ao])}var vv=yt(),cv={itemStyle:Sn(wp,!0),lineStyle:Sn(Sp,!0)},Mb={lineStyle:"stroke",itemStyle:"fill"};function hg(r,t){var e=r.visualStyleMapper||cv[t];return e||(console.warn("Unknown style type '"+t+"'."),cv.itemStyle)}function vg(r,t){var e=r.visualDrawType||Mb[t];return e||(console.warn("Unknown style type '"+t+"'."),"fill")}var Db={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData(),i=r.visualStyleAccessPath||"itemStyle",n=r.getModel(i),a=hg(r,i),o=a(n),s=n.getShallow("decal");s&&(e.setVisual("decal",s),s.dirty=!0);var u=vg(r,i),l=o[u],f=U(l)?l:null,h=o.fill==="auto"||o.stroke==="auto";if(!o[u]||f||h){var c=r.getColorFromPalette(r.name,null,t.getSeriesCount());o[u]||(o[u]=c,e.setVisual("colorFromPalette",!0)),o.fill=o.fill==="auto"||U(o.fill)?c:o.fill,o.stroke=o.stroke==="auto"||U(o.stroke)?c:o.stroke}if(e.setVisual("style",o),e.setVisual("drawType",u),!t.isSeriesFiltered(r)&&f)return e.setVisual("colorFromPalette",!1),{dataEach:function(v,d){var y=r.getDataParams(d),p=k({},o);p[u]=f(y),v.setItemVisual(d,"style",p)}}}},Hi=new At,Ab={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){if(!(r.ignoreStyleOnData||t.isSeriesFiltered(r))){var e=r.getData(),i=r.visualStyleAccessPath||"itemStyle",n=hg(r,i),a=e.getVisual("drawType");return{dataEach:e.hasItemOption?function(o,s){var u=o.getRawDataItem(s);if(u&&u[i]){Hi.option=u[i];var l=n(Hi),f=o.ensureUniqueItemVisual(s,"style");k(f,l),Hi.option.decal&&(o.setItemVisual(s,"decal",Hi.option.decal),Hi.option.decal.dirty=!0),a in l&&o.setItemVisual(s,"colorFromPalette",!1)}}:null}}}},Lb={performRawSeries:!0,overallReset:function(r){var t=Z();r.eachSeries(function(e){var i=e.getColorBy();if(!e.isColorBySeries()){var n=e.type+"-"+i,a=t.get(n);a||(a={},t.set(n,a)),vv(e).scope=a}}),r.eachSeries(function(e){if(!(e.isColorBySeries()||r.isSeriesFiltered(e))){var i=e.getRawData(),n={},a=e.getData(),o=vv(e).scope,s=e.visualStyleAccessPath||"itemStyle",u=vg(e,s);a.each(function(l){var f=a.getRawIndex(l);n[f]=l}),i.each(function(l){var f=n[l],h=a.getItemVisual(f,"colorFromPalette");if(h){var c=a.ensureUniqueItemVisual(f,"style"),v=i.getName(l)||l+"",d=i.count();c[u]=e.getColorFromPalette(v,o,d)}})}})}},ca=Math.PI;function Pb(r,t){t=t||{},tt(t,{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var e=new Ut,i=new Bt({style:{fill:t.maskColor},zlevel:t.zlevel,z:1e4});e.add(i);var n=new ie({style:{text:t.text,fill:t.textColor,fontSize:t.fontSize,fontWeight:t.fontWeight,fontStyle:t.fontStyle,fontFamily:t.fontFamily},zlevel:t.zlevel,z:10001}),a=new Bt({style:{fill:"none"},textContent:n,textConfig:{position:"right",distance:10},zlevel:t.zlevel,z:10001});e.add(a);var o;return t.showSpinner&&(o=new wl({shape:{startAngle:-ca/2,endAngle:-ca/2+.1,r:t.spinnerRadius},style:{stroke:t.color,lineCap:"round",lineWidth:t.lineWidth},zlevel:t.zlevel,z:10001}),o.animateShape(!0).when(1e3,{endAngle:ca*3/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:ca*3/2}).delay(300).start("circularInOut"),e.add(o)),e.resize=function(){var s=n.getBoundingRect().width,u=t.showSpinner?t.spinnerRadius:0,l=(r.getWidth()-u*2-(t.showSpinner&&s?10:0)-s)/2-(t.showSpinner&&s?0:5+s/2)+(t.showSpinner?0:s/2)+(s?0:u),f=r.getHeight()/2;t.showSpinner&&o.setShape({cx:l,cy:f}),a.setShape({x:l-u,y:f-u,width:u*2,height:u*2}),i.setShape({x:0,y:0,width:r.getWidth(),height:r.getHeight()})},e.resize(),e}var Ib=function(){function r(t,e,i,n){this._stageTaskMap=Z(),this.ecInstance=t,this.api=e,i=this._dataProcessorHandlers=i.slice(),n=this._visualHandlers=n.slice(),this._allHandlers=i.concat(n)}return r.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(i){var n=i.overallTask;n&&n.dirty()})},r.prototype.getPerformArgs=function(t,e){if(t.__pipeline){var i=this._pipelineMap.get(t.__pipeline.id),n=i.context,a=!e&&i.progressiveEnabled&&(!n||n.progressiveRender)&&t.__idxInPipeline>i.blockIndex,o=a?i.step:null,s=n&&n.modDataCount,u=s!=null?Math.ceil(s/o):null;return{step:o,modBy:u,modDataCount:s}}},r.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},r.prototype.updateStreamModes=function(t,e){var i=this._pipelineMap.get(t.uid),n=t.getData(),a=n.count(),o=i.progressiveEnabled&&e.incrementalPrepareRender&&a>=i.threshold,s=t.get("large")&&a>=t.get("largeThreshold"),u=t.get("progressiveChunkMode")==="mod"?a:null;t.pipelineContext=i.context={progressiveRender:o,modDataCount:u,large:s}},r.prototype.restorePipelines=function(t){var e=this,i=e._pipelineMap=Z();t.eachSeries(function(n){var a=n.getProgressive(),o=n.uid;i.set(o,{id:o,head:null,tail:null,threshold:n.getProgressiveThreshold(),progressiveEnabled:a&&!(n.preventIncremental&&n.preventIncremental()),blockIndex:-1,step:Math.round(a||700),count:0}),e._pipe(n,n.dataTask)})},r.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),i=this.api;M(this._allHandlers,function(n){var a=t.get(n.uid)||t.set(n.uid,{}),o="";Ie(!(n.reset&&n.overallReset),o),n.reset&&this._createSeriesStageTask(n,a,e,i),n.overallReset&&this._createOverallStageTask(n,a,e,i)},this)},r.prototype.prepareView=function(t,e,i,n){var a=t.renderTask,o=a.context;o.model=e,o.ecModel=i,o.api=n,a.__block=!t.incrementalPrepareRender,this._pipe(e,a)},r.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},r.prototype.performVisualTasks=function(t,e,i){this._performStageTasks(this._visualHandlers,t,e,i)},r.prototype._performStageTasks=function(t,e,i,n){n=n||{};var a=!1,o=this;M(t,function(u,l){if(!(n.visualType&&n.visualType!==u.visualType)){var f=o._stageTaskMap.get(u.uid),h=f.seriesTaskMap,c=f.overallTask;if(c){var v,d=c.agentStubMap;d.each(function(p){s(n,p)&&(p.dirty(),v=!0)}),v&&c.dirty(),o.updatePayload(c,i);var y=o.getPerformArgs(c,n.block);d.each(function(p){p.perform(y)}),c.perform(y)&&(a=!0)}else h&&h.each(function(p,g){s(n,p)&&p.dirty();var m=o.getPerformArgs(p,n.block);m.skip=!u.performRawSeries&&e.isSeriesFiltered(p.context.model),o.updatePayload(p,i),p.perform(m)&&(a=!0)})}});function s(u,l){return u.setDirty&&(!u.dirtyMap||u.dirtyMap.get(l.__pipeline.id))}this.unfinished=a||this.unfinished},r.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(i){e=i.dataTask.perform()||e}),this.unfinished=e||this.unfinished},r.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})},r.prototype.updatePayload=function(t,e){e!=="remain"&&(t.context.payload=e)},r.prototype._createSeriesStageTask=function(t,e,i,n){var a=this,o=e.seriesTaskMap,s=e.seriesTaskMap=Z(),u=t.seriesType,l=t.getTargetSeries;t.createOnAllSeries?i.eachRawSeries(f):u?i.eachRawSeriesByType(u,f):l&&l(i,n).each(f);function f(h){var c=h.uid,v=s.set(c,o&&o.get(c)||vn({plan:Bb,reset:Nb,count:zb}));v.context={model:h,ecModel:i,api:n,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:a},a._pipe(h,v)}},r.prototype._createOverallStageTask=function(t,e,i,n){var a=this,o=e.overallTask=e.overallTask||vn({reset:Eb});o.context={ecModel:i,api:n,overallReset:t.overallReset,scheduler:a};var s=o.agentStubMap,u=o.agentStubMap=Z(),l=t.seriesType,f=t.getTargetSeries,h=!0,c=!1,v="";Ie(!t.createOnAllSeries,v),l?i.eachRawSeriesByType(l,d):f?f(i,n).each(d):(h=!1,M(i.getSeries(),d));function d(y){var p=y.uid,g=u.set(p,s&&s.get(p)||(c=!0,vn({reset:Rb,onDirty:Ob})));g.context={model:y,overallProgress:h},g.agent=o,g.__block=h,a._pipe(y,g)}c&&o.dirty()},r.prototype._pipe=function(t,e){var i=t.uid,n=this._pipelineMap.get(i);!n.head&&(n.head=e),n.tail&&n.tail.pipe(e),n.tail=e,e.__idxInPipeline=n.count++,e.__pipeline=n},r.wrapStageHandler=function(t,e){return U(t)&&(t={overallReset:t,seriesType:Hb(t)}),t.uid=bo("stageHandler"),e&&(t.visualType=e),t},r}();function Eb(r){r.overallReset(r.ecModel,r.api,r.payload)}function Rb(r){return r.overallProgress&&kb}function kb(){this.agent.dirty(),this.getDownstream().dirty()}function Ob(){this.agent&&this.agent.dirty()}function Bb(r){return r.plan?r.plan(r.model,r.ecModel,r.api,r.payload):null}function Nb(r){r.useClearVisual&&r.data.clearAllVisual();var t=r.resetDefines=Lt(r.reset(r.model,r.ecModel,r.api,r.payload));return t.length>1?G(t,function(e,i){return cg(i)}):Fb}var Fb=cg(0);function cg(r){return function(t,e){var i=e.data,n=e.resetDefines[r];if(n&&n.dataEach)for(var a=t.start;a<t.end;a++)n.dataEach(i,a);else n&&n.progress&&n.progress(t,i)}}function zb(r){return r.data.count()}function Hb(r){oo=null;try{r(An,dg)}catch{}return oo}var An={},dg={},oo;pg(An,Wp);pg(dg,Up);An.eachSeriesByType=An.eachRawSeriesByType=function(r){oo=r};An.eachComponent=function(r){r.mainType==="series"&&r.subType&&(oo=r.subType)};function pg(r,t){for(var e in t.prototype)r[e]=Ot}const gg=Ib;var dv=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"];const Vb={color:dv,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],dv]};var wt="#B9B8CE",pv="#100C2A",da=function(){return{axisLine:{lineStyle:{color:wt}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},gv=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],yg={darkMode:!0,color:gv,backgroundColor:pv,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:wt},pageTextStyle:{color:wt}},textStyle:{color:wt},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:wt}},dataZoom:{borderColor:"#71708A",textStyle:{color:wt},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:wt}},timeline:{lineStyle:{color:wt},label:{color:wt},controlStyle:{color:wt,borderColor:wt}},calendar:{itemStyle:{color:pv},dayLabel:{color:wt},monthLabel:{color:wt},yearLabel:{color:wt}},timeAxis:da(),logAxis:da(),valueAxis:da(),categoryAxis:da(),line:{symbol:"circle"},graph:{color:gv},gauge:{title:{color:wt},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:wt},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};yg.categoryAxis.splitLine.show=!1;const Gb=yg;var Wb=function(){function r(){}return r.prototype.normalizeQuery=function(t){var e={},i={},n={};if(z(t)){var a=_e(t);e.mainType=a.main||null,e.subType=a.sub||null}else{var o=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1};M(t,function(u,l){for(var f=!1,h=0;h<o.length;h++){var c=o[h],v=l.lastIndexOf(c);if(v>0&&v===l.length-c.length){var d=l.slice(0,v);d!=="data"&&(e.mainType=d,e[c.toLowerCase()]=u,f=!0)}}s.hasOwnProperty(l)&&(i[l]=u,f=!0),f||(n[l]=u)})}return{cptQuery:e,dataQuery:i,otherQuery:n}},r.prototype.filter=function(t,e){var i=this.eventInfo;if(!i)return!0;var n=i.targetEl,a=i.packedEvent,o=i.model,s=i.view;if(!o||!s)return!0;var u=e.cptQuery,l=e.dataQuery;return f(u,o,"mainType")&&f(u,o,"subType")&&f(u,o,"index","componentIndex")&&f(u,o,"name")&&f(u,o,"id")&&f(l,a,"name")&&f(l,a,"dataIndex")&&f(l,a,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,n,a));function f(h,c,v,d){return h[v]==null||c[d||v]===h[v]}},r.prototype.afterTrigger=function(){this.eventInfo=null},r}(),Ou=["symbol","symbolSize","symbolRotate","symbolOffset"],yv=Ou.concat(["symbolKeepAspect"]),Ub={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData();if(r.legendIcon&&e.setVisual("legendIcon",r.legendIcon),!r.hasSymbolVisual)return;for(var i={},n={},a=!1,o=0;o<Ou.length;o++){var s=Ou[o],u=r.get(s);U(u)?(a=!0,n[s]=u):i[s]=u}if(i.symbol=i.symbol||r.defaultSymbol,e.setVisual(k({legendIcon:r.legendIcon||i.symbol,symbolKeepAspect:r.get("symbolKeepAspect")},i)),t.isSeriesFiltered(r))return;var l=lt(n);function f(h,c){for(var v=r.getRawValue(c),d=r.getDataParams(c),y=0;y<l.length;y++){var p=l[y];h.setItemVisual(c,p,n[p](v,d))}}return{dataEach:a?f:null}}},Yb={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){if(!r.hasSymbolVisual||t.isSeriesFiltered(r))return;var e=r.getData();function i(n,a){for(var o=n.getItemModel(a),s=0;s<yv.length;s++){var u=yv[s],l=o.getShallow(u,!0);l!=null&&n.setItemVisual(a,u,l)}}return{dataEach:e.hasItemOption?i:null}}};function $b(r,t,e){switch(e){case"color":var i=r.getItemVisual(t,"style");return i[r.getVisual("drawType")];case"opacity":return r.getItemVisual(t,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getItemVisual(t,e)}}function Xb(r,t){switch(t){case"color":var e=r.getVisual("style");return e[r.getVisual("drawType")];case"opacity":return r.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getVisual(t)}}function ni(r,t,e,i,n){var a=r+t;e.isSilent(a)||i.eachComponent({mainType:"series",subType:"pie"},function(o){for(var s=o.seriesIndex,u=o.option.selectedMap,l=n.selected,f=0;f<l.length;f++)if(l[f].seriesIndex===s){var h=o.getData(),c=Nr(h,n.fromActionPayload);e.trigger(a,{type:a,seriesId:o.id,name:B(c)?h.getName(c[0]):h.getName(c),selected:z(u)?u:k({},u)})}})}function Zb(r,t,e){r.on("selectchanged",function(i){var n=e.getModel();i.isFromClick?(ni("map","selectchanged",t,n,i),ni("pie","selectchanged",t,n,i)):i.fromAction==="select"?(ni("map","selected",t,n,i),ni("pie","selected",t,n,i)):i.fromAction==="unselect"&&(ni("map","unselected",t,n,i),ni("pie","unselected",t,n,i))})}function rn(r,t,e){for(var i;r&&!(t(r)&&(i=r,e));)r=r.__hostTarget||r.parent;return i}var qb=Math.round(Math.random()*9),Kb=typeof Object.defineProperty=="function",Qb=function(){function r(){this._id="__ec_inner_"+qb++}return r.prototype.get=function(t){return this._guard(t)[this._id]},r.prototype.set=function(t,e){var i=this._guard(t);return Kb?Object.defineProperty(i,this._id,{value:e,enumerable:!1,configurable:!0}):i[this._id]=e,this},r.prototype.delete=function(t){return this.has(t)?(delete this._guard(t)[this._id],!0):!1},r.prototype.has=function(t){return!!this._guard(t)[this._id]},r.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},r}();const Jb=Qb;var jb=ot.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,t){var e=t.cx,i=t.cy,n=t.width/2,a=t.height/2;r.moveTo(e,i-a),r.lineTo(e+n,i+a),r.lineTo(e-n,i+a),r.closePath()}}),tx=ot.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,t){var e=t.cx,i=t.cy,n=t.width/2,a=t.height/2;r.moveTo(e,i-a),r.lineTo(e+n,i),r.lineTo(e,i+a),r.lineTo(e-n,i),r.closePath()}}),ex=ot.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,t){var e=t.x,i=t.y,n=t.width/5*3,a=Math.max(n,t.height),o=n/2,s=o*o/(a-o),u=i-a+o+s,l=Math.asin(s/o),f=Math.cos(l)*o,h=Math.sin(l),c=Math.cos(l),v=o*.6,d=o*.7;r.moveTo(e-f,u+s),r.arc(e,u,o,Math.PI-l,Math.PI*2+l),r.bezierCurveTo(e+f-h*v,u+s+c*v,e,i-d,e,i),r.bezierCurveTo(e,i-d,e-f+h*v,u+s+c*v,e-f,u+s),r.closePath()}}),rx=ot.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,t){var e=t.height,i=t.width,n=t.x,a=t.y,o=i/3*2;r.moveTo(n,a),r.lineTo(n+o,a+e),r.lineTo(n,a+e/4*3),r.lineTo(n-o,a+e),r.lineTo(n,a),r.closePath()}}),ix={line:zr,rect:Bt,roundRect:Bt,square:Bt,circle:_l,diamond:tx,pin:ex,arrow:rx,triangle:jb},nx={line:function(r,t,e,i,n){n.x1=r,n.y1=t+i/2,n.x2=r+e,n.y2=t+i/2},rect:function(r,t,e,i,n){n.x=r,n.y=t,n.width=e,n.height=i},roundRect:function(r,t,e,i,n){n.x=r,n.y=t,n.width=e,n.height=i,n.r=Math.min(e,i)/4},square:function(r,t,e,i,n){var a=Math.min(e,i);n.x=r,n.y=t,n.width=a,n.height=a},circle:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.r=Math.min(e,i)/2},diamond:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.width=e,n.height=i},pin:function(r,t,e,i,n){n.x=r+e/2,n.y=t+i/2,n.width=e,n.height=i},arrow:function(r,t,e,i,n){n.x=r+e/2,n.y=t+i/2,n.width=e,n.height=i},triangle:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.width=e,n.height=i}},Bu={};M(ix,function(r,t){Bu[t]=new r});var ax=ot.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(r,t,e){var i=sd(r,t,e),n=this.shape;return n&&n.symbolType==="pin"&&t.position==="inside"&&(i.y=e.y+e.height*.4),i},buildPath:function(r,t,e){var i=t.symbolType;if(i!=="none"){var n=Bu[i];n||(i="rect",n=Bu[i]),nx[i](t.x,t.y,t.width,t.height,n.shape),n.buildPath(r,n.shape,e)}}});function ox(r,t){if(this.type!=="image"){var e=this.style;this.__isEmptyBrush?(e.stroke=r,e.fill=t||"#fff",e.lineWidth=2):this.shape.symbolType==="line"?e.stroke=r:e.fill=r,this.markRedraw()}}function Ln(r,t,e,i,n,a,o){var s=r.indexOf("empty")===0;s&&(r=r.substr(5,1).toLowerCase()+r.substr(6));var u;return r.indexOf("image://")===0?u=vp(r.slice(8),new J(t,e,i,n),o?"center":"cover"):r.indexOf("path://")===0?u=xl(r.slice(7),{},new J(t,e,i,n),o?"center":"cover"):u=new ax({shape:{symbolType:r,x:t,y:e,width:i,height:n}}),u.__isEmptyBrush=s,u.setColor=ox,a&&u.setColor(a),u}function sx(r){return B(r)||(r=[+r,+r]),[r[0]||0,r[1]||0]}function mg(r,t){if(r!=null)return B(r)||(r=[r,r]),[Dt(r[0],t[0])||0,Dt(X(r[1],r[0]),t[1])||0]}function Lr(r){return isFinite(r)}function ux(r,t,e){var i=t.x==null?0:t.x,n=t.x2==null?1:t.x2,a=t.y==null?0:t.y,o=t.y2==null?0:t.y2;t.global||(i=i*e.width+e.x,n=n*e.width+e.x,a=a*e.height+e.y,o=o*e.height+e.y),i=Lr(i)?i:0,n=Lr(n)?n:1,a=Lr(a)?a:0,o=Lr(o)?o:0;var s=r.createLinearGradient(i,a,n,o);return s}function lx(r,t,e){var i=e.width,n=e.height,a=Math.min(i,n),o=t.x==null?.5:t.x,s=t.y==null?.5:t.y,u=t.r==null?.5:t.r;t.global||(o=o*i+e.x,s=s*n+e.y,u=u*a),o=Lr(o)?o:.5,s=Lr(s)?s:.5,u=u>=0&&Lr(u)?u:.5;var l=r.createRadialGradient(o,s,0,o,s,u);return l}function Nu(r,t,e){for(var i=t.type==="radial"?lx(r,t,e):ux(r,t,e),n=t.colorStops,a=0;a<n.length;a++)i.addColorStop(n[a].offset,n[a].color);return i}function fx(r,t){if(r===t||!r&&!t)return!1;if(!r||!t||r.length!==t.length)return!0;for(var e=0;e<r.length;e++)if(r[e]!==t[e])return!0;return!1}function pa(r){return parseInt(r,10)}function ga(r,t,e){var i=["width","height"][t],n=["clientWidth","clientHeight"][t],a=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(e[i]!=null&&e[i]!=="auto")return parseFloat(e[i]);var s=document.defaultView.getComputedStyle(r);return(r[n]||pa(s[i])||pa(r.style[i]))-(pa(s[a])||0)-(pa(s[o])||0)|0}function hx(r,t){return!r||r==="solid"||!(t>0)?null:r==="dashed"?[4*t,2*t]:r==="dotted"?[t]:vt(r)?[r]:B(r)?r:null}function _g(r){var t=r.style,e=t.lineDash&&t.lineWidth>0&&hx(t.lineDash,t.lineWidth),i=t.lineDashOffset;if(e){var n=t.strokeNoScale&&r.getLineScale?r.getLineScale():1;n&&n!==1&&(e=G(e,function(a){return a/n}),i/=n)}return[e,i]}var vx=new Fr(!0);function so(r){var t=r.stroke;return!(t==null||t==="none"||!(r.lineWidth>0))}function mv(r){return typeof r=="string"&&r!=="none"}function uo(r){var t=r.fill;return t!=null&&t!=="none"}function _v(r,t){if(t.fillOpacity!=null&&t.fillOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.fillOpacity*t.opacity,r.fill(),r.globalAlpha=e}else r.fill()}function Sv(r,t){if(t.strokeOpacity!=null&&t.strokeOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.strokeOpacity*t.opacity,r.stroke(),r.globalAlpha=e}else r.stroke()}function Fu(r,t,e){var i=xd(t.image,t.__image,e);if(go(i)){var n=r.createPattern(i,t.repeat||"repeat");if(typeof DOMMatrix=="function"&&n&&n.setTransform){var a=new DOMMatrix;a.translateSelf(t.x||0,t.y||0),a.rotateSelf(0,0,(t.rotation||0)*vm),a.scaleSelf(t.scaleX||1,t.scaleY||1),n.setTransform(a)}return n}}function cx(r,t,e,i){var n,a=so(e),o=uo(e),s=e.strokePercent,u=s<1,l=!t.path;(!t.silent||u)&&l&&t.createPathProxy();var f=t.path||vx,h=t.__dirty;if(!i){var c=e.fill,v=e.stroke,d=o&&!!c.colorStops,y=a&&!!v.colorStops,p=o&&!!c.image,g=a&&!!v.image,m=void 0,_=void 0,S=void 0,b=void 0,w=void 0;(d||y)&&(w=t.getBoundingRect()),d&&(m=h?Nu(r,c,w):t.__canvasFillGradient,t.__canvasFillGradient=m),y&&(_=h?Nu(r,v,w):t.__canvasStrokeGradient,t.__canvasStrokeGradient=_),p&&(S=h||!t.__canvasFillPattern?Fu(r,c,t):t.__canvasFillPattern,t.__canvasFillPattern=S),g&&(b=h||!t.__canvasStrokePattern?Fu(r,v,t):t.__canvasStrokePattern,t.__canvasStrokePattern=S),d?r.fillStyle=m:p&&(S?r.fillStyle=S:o=!1),y?r.strokeStyle=_:g&&(b?r.strokeStyle=b:a=!1)}var x=t.getGlobalScale();f.setScale(x[0],x[1],t.segmentIgnoreThreshold);var D,T;r.setLineDash&&e.lineDash&&(n=_g(t),D=n[0],T=n[1]);var C=!0;(l||h&si)&&(f.setDPR(r.dpr),u?f.setContext(null):(f.setContext(r),C=!1),f.reset(),t.buildPath(f,t.shape,i),f.toStatic(),t.pathUpdated()),C&&f.rebuildPath(r,u?s:1),D&&(r.setLineDash(D),r.lineDashOffset=T),i||(e.strokeFirst?(a&&Sv(r,e),o&&_v(r,e)):(o&&_v(r,e),a&&Sv(r,e))),D&&r.setLineDash([])}function dx(r,t,e){var i=t.__image=xd(e.image,t.__image,t,t.onload);if(!(!i||!go(i))){var n=e.x||0,a=e.y||0,o=t.getWidth(),s=t.getHeight(),u=i.width/i.height;if(o==null&&s!=null?o=s*u:s==null&&o!=null?s=o/u:o==null&&s==null&&(o=i.width,s=i.height),e.sWidth&&e.sHeight){var l=e.sx||0,f=e.sy||0;r.drawImage(i,l,f,e.sWidth,e.sHeight,n,a,o,s)}else if(e.sx&&e.sy){var l=e.sx,f=e.sy,h=o-l,c=s-f;r.drawImage(i,l,f,h,c,n,a,o,s)}else r.drawImage(i,n,a,o,s)}}function px(r,t,e){var i,n=e.text;if(n!=null&&(n+=""),n){r.font=e.font||Or,r.textAlign=e.textAlign,r.textBaseline=e.textBaseline;var a=void 0,o=void 0;r.setLineDash&&e.lineDash&&(i=_g(t),a=i[0],o=i[1]),a&&(r.setLineDash(a),r.lineDashOffset=o),e.strokeFirst?(so(e)&&r.strokeText(n,e.x,e.y),uo(e)&&r.fillText(n,e.x,e.y)):(uo(e)&&r.fillText(n,e.x,e.y),so(e)&&r.strokeText(n,e.x,e.y)),a&&r.setLineDash([])}}var wv=["shadowBlur","shadowOffsetX","shadowOffsetY"],bv=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Sg(r,t,e,i,n){var a=!1;if(!i&&(e=e||{},t===e))return!1;if(i||t.opacity!==e.opacity){kt(r,n),a=!0;var o=Math.max(Math.min(t.opacity,1),0);r.globalAlpha=isNaN(o)?Ir.opacity:o}(i||t.blend!==e.blend)&&(a||(kt(r,n),a=!0),r.globalCompositeOperation=t.blend||Ir.blend);for(var s=0;s<wv.length;s++){var u=wv[s];(i||t[u]!==e[u])&&(a||(kt(r,n),a=!0),r[u]=r.dpr*(t[u]||0))}return(i||t.shadowColor!==e.shadowColor)&&(a||(kt(r,n),a=!0),r.shadowColor=t.shadowColor||Ir.shadowColor),a}function xv(r,t,e,i,n){var a=Pn(t,n.inHover),o=i?null:e&&Pn(e,n.inHover)||{};if(a===o)return!1;var s=Sg(r,a,o,i,n);if((i||a.fill!==o.fill)&&(s||(kt(r,n),s=!0),mv(a.fill)&&(r.fillStyle=a.fill)),(i||a.stroke!==o.stroke)&&(s||(kt(r,n),s=!0),mv(a.stroke)&&(r.strokeStyle=a.stroke)),(i||a.opacity!==o.opacity)&&(s||(kt(r,n),s=!0),r.globalAlpha=a.opacity==null?1:a.opacity),t.hasStroke()){var u=a.lineWidth,l=u/(a.strokeNoScale&&t.getLineScale?t.getLineScale():1);r.lineWidth!==l&&(s||(kt(r,n),s=!0),r.lineWidth=l)}for(var f=0;f<bv.length;f++){var h=bv[f],c=h[0];(i||a[c]!==o[c])&&(s||(kt(r,n),s=!0),r[c]=a[c]||h[1])}return s}function gx(r,t,e,i,n){return Sg(r,Pn(t,n.inHover),e&&Pn(e,n.inHover),i,n)}function wg(r,t){var e=t.transform,i=r.dpr||1;e?r.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):r.setTransform(i,0,0,i,0,0)}function yx(r,t,e){for(var i=!1,n=0;n<r.length;n++){var a=r[n];i=i||a.isZeroArea(),wg(t,a),t.beginPath(),a.buildPath(t,a.shape),t.clip()}e.allClipped=i}function mx(r,t){return r&&t?r[0]!==t[0]||r[1]!==t[1]||r[2]!==t[2]||r[3]!==t[3]||r[4]!==t[4]||r[5]!==t[5]:!(!r&&!t)}var Tv=1,Cv=2,Mv=3,Dv=4;function _x(r){var t=uo(r),e=so(r);return!(r.lineDash||!(+t^+e)||t&&typeof r.fill!="string"||e&&typeof r.stroke!="string"||r.strokePercent<1||r.strokeOpacity<1||r.fillOpacity<1)}function kt(r,t){t.batchFill&&r.fill(),t.batchStroke&&r.stroke(),t.batchFill="",t.batchStroke=""}function Pn(r,t){return t&&r.__hoverStyle||r.style}function bg(r,t){Pr(r,t,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function Pr(r,t,e,i){var n=t.transform;if(!t.shouldBePainted(e.viewWidth,e.viewHeight,!1,!1)){t.__dirty&=~Gt,t.__isRendered=!1;return}var a=t.__clipPaths,o=e.prevElClipPaths,s=!1,u=!1;if((!o||fx(a,o))&&(o&&o.length&&(kt(r,e),r.restore(),u=s=!0,e.prevElClipPaths=null,e.allClipped=!1,e.prevEl=null),a&&a.length&&(kt(r,e),r.save(),yx(a,r,e),s=!0),e.prevElClipPaths=a),e.allClipped){t.__isRendered=!1;return}t.beforeBrush&&t.beforeBrush(),t.innerBeforeBrush();var l=e.prevEl;l||(u=s=!0);var f=t instanceof ot&&t.autoBatch&&_x(t.style);s||mx(n,l.transform)?(kt(r,e),wg(r,t)):f||kt(r,e);var h=Pn(t,e.inHover);t instanceof ot?(e.lastDrawType!==Tv&&(u=!0,e.lastDrawType=Tv),xv(r,t,l,u,e),(!f||!e.batchFill&&!e.batchStroke)&&r.beginPath(),cx(r,t,h,f),f&&(e.batchFill=h.fill||"",e.batchStroke=h.stroke||"")):t instanceof Su?(e.lastDrawType!==Mv&&(u=!0,e.lastDrawType=Mv),xv(r,t,l,u,e),px(r,t,h)):t instanceof Wr?(e.lastDrawType!==Cv&&(u=!0,e.lastDrawType=Cv),gx(r,t,l,u,e),dx(r,t,h)):t.getTemporalDisplayables&&(e.lastDrawType!==Dv&&(u=!0,e.lastDrawType=Dv),Sx(r,t,e)),f&&i&&kt(r,e),t.innerAfterBrush(),t.afterBrush&&t.afterBrush(),e.prevEl=t,t.__dirty=0,t.__isRendered=!0}function Sx(r,t,e){var i=t.getDisplayables(),n=t.getTemporalDisplayables();r.save();var a={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:e.viewWidth,viewHeight:e.viewHeight,inHover:e.inHover},o,s;for(o=t.getCursor(),s=i.length;o<s;o++){var u=i[o];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),Pr(r,u,a,o===s-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),a.prevEl=u}for(var l=0,f=n.length;l<f;l++){var u=n[l];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),Pr(r,u,a,l===f-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),a.prevEl=u}t.clearTemporalDisplayables(),t.notClear=!0,r.restore()}var Es=new Jb,Av=new Bn(100),Lv=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function Pv(r,t){if(r==="none")return null;var e=t.getDevicePixelRatio(),i=t.getZr(),n=i.painter.type==="svg";r.dirty&&Es.delete(r);var a=Es.get(r);if(a)return a;var o=tt(r,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});o.backgroundColor==="none"&&(o.backgroundColor=null);var s={repeat:"repeat"};return u(s),s.rotation=o.rotation,s.scaleX=s.scaleY=n?1:1/e,Es.set(r,s),r.dirty=!1,s;function u(l){for(var f=[e],h=!0,c=0;c<Lv.length;++c){var v=o[Lv[c]];if(v!=null&&!B(v)&&!z(v)&&!vt(v)&&typeof v!="boolean"){h=!1;break}f.push(v)}var d;if(h){d=f.join(",")+(n?"-svg":"");var y=Av.get(d);y&&(n?l.svgElement=y:l.image=y)}var p=Tg(o.dashArrayX),g=bx(o.dashArrayY),m=xg(o.symbol),_=xx(p),S=Cg(g),b=!n&&xi.createCanvas(),w=n&&{tag:"g",attrs:{},key:"dcl",children:[]},x=T(),D;b&&(b.width=x.width*e,b.height=x.height*e,D=b.getContext("2d")),C(),h&&Av.put(d,b||w),l.image=b,l.svgElement=w,l.svgWidth=x.width,l.svgHeight=x.height;function T(){for(var A=1,L=0,I=_.length;L<I;++L)A=Hf(A,_[L]);for(var P=1,L=0,I=m.length;L<I;++L)P=Hf(P,m[L].length);A*=P;var E=S*_.length*m.length;return{width:Math.max(1,Math.min(A,o.maxTileWidth)),height:Math.max(1,Math.min(E,o.maxTileHeight))}}function C(){D&&(D.clearRect(0,0,b.width,b.height),o.backgroundColor&&(D.fillStyle=o.backgroundColor,D.fillRect(0,0,b.width,b.height)));for(var A=0,L=0;L<g.length;++L)A+=g[L];if(A<=0)return;for(var I=-S,P=0,E=0,R=0;I<x.height;){if(P%2===0){for(var V=E/2%m.length,N=0,F=0,Y=0;N<x.width*2;){for(var et=0,L=0;L<p[R].length;++L)et+=p[R][L];if(et<=0)break;if(F%2===0){var j=(1-o.symbolSize)*.5,st=N+p[R][F]*j,ut=I+g[P]*j,ct=p[R][F]*o.symbolSize,ae=g[P]*o.symbolSize,rr=Y/2%m[V].length;$r(st,ut,ct,ae,m[V][rr])}N+=p[R][F],++Y,++F,F===p[R].length&&(F=0)}++R,R===p.length&&(R=0)}I+=g[P],++E,++P,P===g.length&&(P=0)}function $r(Ht,mt,W,q,ir){var Ct=n?1:e,nf=Ln(ir,Ht*Ct,mt*Ct,W*Ct,q*Ct,o.color,o.symbolKeepAspect);if(n){var af=i.painter.renderOneToVNode(nf);af&&w.children.push(af)}else bg(D,nf)}}}}function xg(r){if(!r||r.length===0)return[["rect"]];if(z(r))return[[r]];for(var t=!0,e=0;e<r.length;++e)if(!z(r[e])){t=!1;break}if(t)return xg([r]);for(var i=[],e=0;e<r.length;++e)z(r[e])?i.push([r[e]]):i.push(r[e]);return i}function Tg(r){if(!r||r.length===0)return[[0,0]];if(vt(r)){var t=Math.ceil(r);return[[t,t]]}for(var e=!0,i=0;i<r.length;++i)if(!vt(r[i])){e=!1;break}if(e)return Tg([r]);for(var n=[],i=0;i<r.length;++i)if(vt(r[i])){var t=Math.ceil(r[i]);n.push([t,t])}else{var t=G(r[i],function(s){return Math.ceil(s)});t.length%2===1?n.push(t.concat(t)):n.push(t)}return n}function bx(r){if(!r||typeof r=="object"&&r.length===0)return[0,0];if(vt(r)){var t=Math.ceil(r);return[t,t]}var e=G(r,function(i){return Math.ceil(i)});return r.length%2?e.concat(e):e}function xx(r){return G(r,function(t){return Cg(t)})}function Cg(r){for(var t=0,e=0;e<r.length;++e)t+=r[e];return r.length%2===1?t*2:t}function Tx(r,t){r.eachRawSeries(function(e){if(!r.isSeriesFiltered(e)){var i=e.getData();i.hasItemVisual()&&i.each(function(o){var s=i.getItemVisual(o,"decal");if(s){var u=i.ensureUniqueItemVisual(o,"style");u.decal=Pv(s,t)}});var n=i.getVisual("decal");if(n){var a=i.getVisual("style");a.decal=Pv(n,t)}}})}var Cx=new xe;const fe=Cx;var Mg={};function Mx(r,t){Mg[r]=t}function Dx(r){return Mg[r]}var Ax=1,Lx=800,Px=900,Ix=1e3,Ex=2e3,Rx=5e3,Dg=1e3,kx=1100,Yl=2e3,Ag=3e3,Ox=4e3,Io=4500,Bx=4600,Nx=5e3,Fx=6e3,Lg=7e3,zx={PROCESSOR:{FILTER:Ix,SERIES_FILTER:Lx,STATISTIC:Rx},VISUAL:{LAYOUT:Dg,PROGRESSIVE_LAYOUT:kx,GLOBAL:Yl,CHART:Ag,POST_CHART_LAYOUT:Bx,COMPONENT:Ox,BRUSH:Nx,CHART_ITEM:Io,ARIA:Fx,DECAL:Lg}},St="__flagInMainProcess",It="__pendingUpdate",Rs="__needsUpdateStatus",Iv=/^[a-zA-Z0-9_]+$/,ks="__connectUpdateStatus",Ev=0,Hx=1,Vx=2;function Pg(r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(this.isDisposed()){this.id;return}return Eg(this,r,t)}}function Ig(r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return Eg(this,r,t)}}function Eg(r,t,e){return e[0]=e[0]&&e[0].toLowerCase(),xe.prototype[t].apply(r,e)}var Rg=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t}(xe),kg=Rg.prototype;kg.on=Ig("on");kg.off=Ig("off");var ai,Os,ya,He,Bs,Ns,Fs,Vi,Gi,Rv,kv,zs,Ov,ma,Bv,Og,Yt,Nv,Bg=function(r){O(t,r);function t(e,i,n){var a=r.call(this,new Wb)||this;a._chartsViews=[],a._chartsMap={},a._componentsViews=[],a._componentsMap={},a._pendingActions=[],n=n||{},z(i)&&(i=Ng[i]),a._dom=e;var o="canvas",s="auto",u=!1;n.ssr;var l=a._zr=Nf(e,{renderer:n.renderer||o,devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height,ssr:n.ssr,useDirtyRect:X(n.useDirtyRect,u),useCoarsePointer:X(n.useCoarsePointer,s),pointerSize:n.pointerSize});a._ssr=n.ssr,a._throttledZrFlush=Ul(ht(l.flush,l),17),i=K(i),i&&$p(i,!0),a._theme=i,a._locale=YS(n.locale||bp),a._coordSysMgr=new Nl;var f=a._api=Bv(a);function h(c,v){return c.__prio-v.__prio}return Aa(fo,h),Aa(zu,h),a._scheduler=new gg(a,f,zu,fo),a._messageCenter=new Rg,a._initEvents(),a.resize=ht(a.resize,a),l.animation.on("frame",a._onframe,a),Rv(l,a),kv(l,a),tu(a),a}return t.prototype._onframe=function(){if(!this._disposed){Nv(this);var e=this._scheduler;if(this[It]){var i=this[It].silent;this[St]=!0;try{ai(this),He.update.call(this,null,this[It].updateParams)}catch(u){throw this[St]=!1,this[It]=null,u}this._zr.flush(),this[St]=!1,this[It]=null,Vi.call(this,i),Gi.call(this,i)}else if(e.unfinished){var n=Ax,a=this._model,o=this._api;e.unfinished=!1;do{var s=+new Date;e.performSeriesTasks(a),e.performDataProcessorTasks(a),Ns(this,a),e.performVisualTasks(a),ma(this,this._model,o,"remain",{}),n-=+new Date-s}while(n>0&&e.unfinished);e.unfinished||this._zr.flush()}}},t.prototype.getDom=function(){return this._dom},t.prototype.getId=function(){return this.id},t.prototype.getZr=function(){return this._zr},t.prototype.isSSR=function(){return this._ssr},t.prototype.setOption=function(e,i,n){if(!this[St]){if(this._disposed){this.id;return}var a,o,s;if(H(i)&&(n=i.lazyUpdate,a=i.silent,o=i.replaceMerge,s=i.transition,i=i.notMerge),this[St]=!0,!this._model||i){var u=new Cw(this._api),l=this._theme,f=this._model=new Wp;f.scheduler=this._scheduler,f.ssr=this._ssr,f.init(null,null,null,l,this._locale,u)}this._model.setOption(e,{replaceMerge:o},Hu);var h={seriesTransition:s,optionChanged:!0};if(n)this[It]={silent:a,updateParams:h},this[St]=!1,this.getZr().wakeUp();else{try{ai(this),He.update.call(this,null,h)}catch(c){throw this[It]=null,this[St]=!1,c}this._ssr||this._zr.flush(),this[It]=null,this[St]=!1,Vi.call(this,a),Gi.call(this,a)}}},t.prototype.setTheme=function(){},t.prototype.getModel=function(){return this._model},t.prototype.getOption=function(){return this._model&&this._model.getOption()},t.prototype.getWidth=function(){return this._zr.getWidth()},t.prototype.getHeight=function(){return this._zr.getHeight()},t.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||$.hasGlobalWindow&&window.devicePixelRatio||1},t.prototype.getRenderedCanvas=function(e){return this.renderToCanvas(e)},t.prototype.renderToCanvas=function(e){e=e||{};var i=this._zr.painter;return i.getRenderedCanvas({backgroundColor:e.backgroundColor||this._model.get("backgroundColor"),pixelRatio:e.pixelRatio||this.getDevicePixelRatio()})},t.prototype.renderToSVGString=function(e){e=e||{};var i=this._zr.painter;return i.renderToString({useViewBox:e.useViewBox})},t.prototype.getSvgDataURL=function(){if($.svgSupported){var e=this._zr,i=e.storage.getDisplayList();return M(i,function(n){n.stopAnimation(null,!0)}),e.painter.toDataURL()}},t.prototype.getDataURL=function(e){if(this._disposed){this.id;return}e=e||{};var i=e.excludeComponents,n=this._model,a=[],o=this;M(i,function(u){n.eachComponent({mainType:u},function(l){var f=o._componentsMap[l.__viewId];f.group.ignore||(a.push(f),f.group.ignore=!0)})});var s=this._zr.painter.getType()==="svg"?this.getSvgDataURL():this.renderToCanvas(e).toDataURL("image/"+(e&&e.type||"png"));return M(a,function(u){u.group.ignore=!1}),s},t.prototype.getConnectedDataURL=function(e){if(this._disposed){this.id;return}var i=e.type==="svg",n=this.group,a=Math.min,o=Math.max,s=1/0;if(Fv[n]){var u=s,l=s,f=-s,h=-s,c=[],v=e&&e.pixelRatio||this.getDevicePixelRatio();M(dn,function(_,S){if(_.group===n){var b=i?_.getZr().painter.getSvgDom().innerHTML:_.renderToCanvas(K(e)),w=_.getDom().getBoundingClientRect();u=a(w.left,u),l=a(w.top,l),f=o(w.right,f),h=o(w.bottom,h),c.push({dom:b,left:w.left,top:w.top})}}),u*=v,l*=v,f*=v,h*=v;var d=f-u,y=h-l,p=xi.createCanvas(),g=Nf(p,{renderer:i?"svg":"canvas"});if(g.resize({width:d,height:y}),i){var m="";return M(c,function(_){var S=_.left-u,b=_.top-l;m+='<g transform="translate('+S+","+b+')">'+_.dom+"</g>"}),g.painter.getSvgRoot().innerHTML=m,e.connectedBackgroundColor&&g.painter.setBackgroundColor(e.connectedBackgroundColor),g.refreshImmediately(),g.painter.toDataURL()}else return e.connectedBackgroundColor&&g.add(new Bt({shape:{x:0,y:0,width:d,height:y},style:{fill:e.connectedBackgroundColor}})),M(c,function(_){var S=new Wr({style:{x:_.left*v-u,y:_.top*v-l,image:_.dom}});g.add(S)}),g.refreshImmediately(),p.toDataURL("image/"+(e&&e.type||"png"))}else return this.getDataURL(e)},t.prototype.convertToPixel=function(e,i){return Bs(this,"convertToPixel",e,i)},t.prototype.convertFromPixel=function(e,i){return Bs(this,"convertFromPixel",e,i)},t.prototype.containPixel=function(e,i){if(this._disposed){this.id;return}var n=this._model,a,o=as(n,e);return M(o,function(s,u){u.indexOf("Models")>=0&&M(s,function(l){var f=l.coordinateSystem;if(f&&f.containPoint)a=a||!!f.containPoint(i);else if(u==="seriesModels"){var h=this._chartsMap[l.__viewId];h&&h.containPoint&&(a=a||h.containPoint(i,l))}},this)},this),!!a},t.prototype.getVisual=function(e,i){var n=this._model,a=as(n,e,{defaultMainType:"series"}),o=a.seriesModel,s=o.getData(),u=a.hasOwnProperty("dataIndexInside")?a.dataIndexInside:a.hasOwnProperty("dataIndex")?s.indexOfRawIndex(a.dataIndex):null;return u!=null?$b(s,u,i):Xb(s,i)},t.prototype.getViewOfComponentModel=function(e){return this._componentsMap[e.__viewId]},t.prototype.getViewOfSeriesModel=function(e){return this._chartsMap[e.__viewId]},t.prototype._initEvents=function(){var e=this;M(Gx,function(i){var n=function(a){var o=e.getModel(),s=a.target,u,l=i==="globalout";if(l?u={}:s&&rn(s,function(d){var y=it(d);if(y&&y.dataIndex!=null){var p=y.dataModel||o.getSeriesByIndex(y.seriesIndex);return u=p&&p.getDataParams(y.dataIndex,y.dataType,s)||{},!0}else if(y.eventData)return u=k({},y.eventData),!0},!0),u){var f=u.componentType,h=u.componentIndex;(f==="markLine"||f==="markPoint"||f==="markArea")&&(f="series",h=u.seriesIndex);var c=f&&h!=null&&o.getComponent(f,h),v=c&&e[c.mainType==="series"?"_chartsMap":"_componentsMap"][c.__viewId];u.event=a,u.type=i,e._$eventProcessor.eventInfo={targetEl:s,packedEvent:u,model:c,view:v},e.trigger(i,u)}};n.zrEventfulCallAtLast=!0,e._zr.on(i,n,e)}),M(cn,function(i,n){e._messageCenter.on(n,function(a){this.trigger(n,a)},e)}),M(["selectchanged"],function(i){e._messageCenter.on(i,function(n){this.trigger(i,n)},e)}),Zb(this._messageCenter,this,this._api)},t.prototype.isDisposed=function(){return this._disposed},t.prototype.clear=function(){if(this._disposed){this.id;return}this.setOption({series:[]},!0)},t.prototype.dispose=function(){if(this._disposed){this.id;return}this._disposed=!0;var e=this.getDom();e&&Sd(this.getDom(),Xl,"");var i=this,n=i._api,a=i._model;M(i._componentsViews,function(o){o.dispose(a,n)}),M(i._chartsViews,function(o){o.dispose(a,n)}),i._zr.dispose(),i._dom=i._model=i._chartsMap=i._componentsMap=i._chartsViews=i._componentsViews=i._scheduler=i._api=i._zr=i._throttledZrFlush=i._theme=i._coordSysMgr=i._messageCenter=null,delete dn[i.id]},t.prototype.resize=function(e){if(!this[St]){if(this._disposed){this.id;return}this._zr.resize(e);var i=this._model;if(this._loadingFX&&this._loadingFX.resize(),!!i){var n=i.resetOption("media"),a=e&&e.silent;this[It]&&(a==null&&(a=this[It].silent),n=!0,this[It]=null),this[St]=!0;try{n&&ai(this),He.update.call(this,{type:"resize",animation:k({duration:0},e&&e.animation)})}catch(o){throw this[St]=!1,o}this[St]=!1,Vi.call(this,a),Gi.call(this,a)}}},t.prototype.showLoading=function(e,i){if(this._disposed){this.id;return}if(H(e)&&(i=e,e=""),e=e||"default",this.hideLoading(),!!Vu[e]){var n=Vu[e](this._api,i),a=this._zr;this._loadingFX=n,a.add(n)}},t.prototype.hideLoading=function(){if(this._disposed){this.id;return}this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},t.prototype.makeActionFromEvent=function(e){var i=k({},e);return i.type=cn[e.type],i},t.prototype.dispatchAction=function(e,i){if(this._disposed){this.id;return}if(H(i)||(i={silent:!!i}),!!lo[e.type]&&this._model){if(this[St]){this._pendingActions.push(e);return}var n=i.silent;Fs.call(this,e,n);var a=i.flush;a?this._zr.flush():a!==!1&&$.browser.weChat&&this._throttledZrFlush(),Vi.call(this,n),Gi.call(this,n)}},t.prototype.updateLabelLayout=function(){fe.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},t.prototype.appendData=function(e){if(this._disposed){this.id;return}var i=e.seriesIndex,n=this.getModel(),a=n.getSeriesByIndex(i);a.appendData(e),this._scheduler.unfinished=!0,this.getZr().wakeUp()},t.internalField=function(){ai=function(h){var c=h._scheduler;c.restorePipelines(h._model),c.prepareStageTasks(),Os(h,!0),Os(h,!1),c.plan()},Os=function(h,c){for(var v=h._model,d=h._scheduler,y=c?h._componentsViews:h._chartsViews,p=c?h._componentsMap:h._chartsMap,g=h._zr,m=h._api,_=0;_<y.length;_++)y[_].__alive=!1;c?v.eachComponent(function(w,x){w!=="series"&&S(x)}):v.eachSeries(S);function S(w){var x=w.__requireNewView;w.__requireNewView=!1;var D="_ec_"+w.id+"_"+w.type,T=!x&&p[D];if(!T){var C=_e(w.type),A=c?er.getClass(C.main,C.sub):tr.getClass(C.sub);T=new A,T.init(v,m),p[D]=T,y.push(T),g.add(T.group)}w.__viewId=T.__id=D,T.__alive=!0,T.__model=w,T.group.__ecComponentInfo={mainType:w.mainType,index:w.componentIndex},!c&&d.prepareView(T,w,v,m)}for(var _=0;_<y.length;){var b=y[_];b.__alive?_++:(!c&&b.renderTask.dispose(),g.remove(b.group),b.dispose(v,m),y.splice(_,1),p[b.__id]===b&&delete p[b.__id],b.__id=b.group.__ecComponentInfo=null)}},ya=function(h,c,v,d,y){var p=h._model;if(p.setUpdatePayload(v),!d){M([].concat(h._componentsViews).concat(h._chartsViews),b);return}var g={};g[d+"Id"]=v[d+"Id"],g[d+"Index"]=v[d+"Index"],g[d+"Name"]=v[d+"Name"];var m={mainType:d,query:g};y&&(m.subType=y);var _=v.excludeSeriesId,S;_!=null&&(S=Z(),M(Lt(_),function(w){var x=Se(w,null);x!=null&&S.set(x,!0)})),p&&p.eachComponent(m,function(w){var x=S&&S.get(w.id)!=null;if(!x)if(gh(v))if(w instanceof Dn)v.type===Er&&!v.notBlur&&!w.get(["emphasis","disabled"])&&_1(w,v,h._api);else{var D=ml(w.mainType,w.componentIndex,v.name,h._api),T=D.focusSelf,C=D.dispatchers;v.type===Er&&T&&!v.notBlur&&bu(w.mainType,w.componentIndex,h._api),C&&M(C,function(A){v.type===Er?Ja(A):ja(A)})}else Cu(v)&&w instanceof Dn&&(b1(w,v,h._api),ch(w),Yt(h))},h),p&&p.eachComponent(m,function(w){var x=S&&S.get(w.id)!=null;x||b(h[d==="series"?"_chartsMap":"_componentsMap"][w.__viewId])},h);function b(w){w&&w.__alive&&w[c]&&w[c](w.__model,p,h._api,v)}},He={prepareAndUpdate:function(h){ai(this),He.update.call(this,h,{optionChanged:h.newOption!=null})},update:function(h,c){var v=this._model,d=this._api,y=this._zr,p=this._coordSysMgr,g=this._scheduler;if(v){v.setUpdatePayload(h),g.restoreData(v,h),g.performSeriesTasks(v),p.create(v,d),g.performDataProcessorTasks(v,h),Ns(this,v),p.update(v,d),e(v),g.performVisualTasks(v,h),zs(this,v,d,h,c);var m=v.get("backgroundColor")||"transparent",_=v.get("darkMode");y.setBackgroundColor(m),_!=null&&_!=="auto"&&y.setDarkMode(_),fe.trigger("afterupdate",v,d)}},updateTransform:function(h){var c=this,v=this._model,d=this._api;if(v){v.setUpdatePayload(h);var y=[];v.eachComponent(function(g,m){if(g!=="series"){var _=c.getViewOfComponentModel(m);if(_&&_.__alive)if(_.updateTransform){var S=_.updateTransform(m,v,d,h);S&&S.update&&y.push(_)}else y.push(_)}});var p=Z();v.eachSeries(function(g){var m=c._chartsMap[g.__viewId];if(m.updateTransform){var _=m.updateTransform(g,v,d,h);_&&_.update&&p.set(g.uid,1)}else p.set(g.uid,1)}),e(v),this._scheduler.performVisualTasks(v,h,{setDirty:!0,dirtyMap:p}),ma(this,v,d,h,{},p),fe.trigger("afterupdate",v,d)}},updateView:function(h){var c=this._model;c&&(c.setUpdatePayload(h),tr.markUpdateMethod(h,"updateView"),e(c),this._scheduler.performVisualTasks(c,h,{setDirty:!0}),zs(this,c,this._api,h,{}),fe.trigger("afterupdate",c,this._api))},updateVisual:function(h){var c=this,v=this._model;v&&(v.setUpdatePayload(h),v.eachSeries(function(d){d.getData().clearAllVisual()}),tr.markUpdateMethod(h,"updateVisual"),e(v),this._scheduler.performVisualTasks(v,h,{visualType:"visual",setDirty:!0}),v.eachComponent(function(d,y){if(d!=="series"){var p=c.getViewOfComponentModel(y);p&&p.__alive&&p.updateVisual(y,v,c._api,h)}}),v.eachSeries(function(d){var y=c._chartsMap[d.__viewId];y.updateVisual(d,v,c._api,h)}),fe.trigger("afterupdate",v,this._api))},updateLayout:function(h){He.update.call(this,h)}},Bs=function(h,c,v,d){if(h._disposed){h.id;return}for(var y=h._model,p=h._coordSysMgr.getCoordinateSystems(),g,m=as(y,v),_=0;_<p.length;_++){var S=p[_];if(S[c]&&(g=S[c](y,m,d))!=null)return g}},Ns=function(h,c){var v=h._chartsMap,d=h._scheduler;c.eachSeries(function(y){d.updateStreamModes(y,v[y.__viewId])})},Fs=function(h,c){var v=this,d=this.getModel(),y=h.type,p=h.escapeConnect,g=lo[y],m=g.actionInfo,_=(m.update||"update").split(":"),S=_.pop(),b=_[0]!=null&&_e(_[0]);this[St]=!0;var w=[h],x=!1;h.batch&&(x=!0,w=G(h.batch,function(P){return P=tt(k({},P),h),P.batch=null,P}));var D=[],T,C=Cu(h),A=gh(h);if(A&&Wd(this._api),M(w,function(P){if(T=g.action(P,v._model,v._api),T=T||k({},P),T.type=m.event||T.type,D.push(T),A){var E=cl(h),R=E.queryOptionMap,V=E.mainTypeSpecified,N=V?R.keys()[0]:"series";ya(v,S,P,N),Yt(v)}else C?(ya(v,S,P,"series"),Yt(v)):b&&ya(v,S,P,b.main,b.sub)}),S!=="none"&&!A&&!C&&!b)try{this[It]?(ai(this),He.update.call(this,h),this[It]=null):He[S].call(this,h)}catch(P){throw this[St]=!1,P}if(x?T={type:m.event||y,escapeConnect:p,batch:D}:T=D[0],this[St]=!1,!c){var L=this._messageCenter;if(L.trigger(T.type,T),C){var I={type:"selectchanged",escapeConnect:p,selected:x1(d),isFromClick:h.isFromClick||!1,fromAction:h.type,fromActionPayload:h};L.trigger(I.type,I)}}},Vi=function(h){for(var c=this._pendingActions;c.length;){var v=c.shift();Fs.call(this,v,h)}},Gi=function(h){!h&&this.trigger("updated")},Rv=function(h,c){h.on("rendered",function(v){c.trigger("rendered",v),h.animation.isFinished()&&!c[It]&&!c._scheduler.unfinished&&!c._pendingActions.length&&c.trigger("finished")})},kv=function(h,c){h.on("mouseover",function(v){var d=v.target,y=rn(d,Tu);y&&(S1(y,v,c._api),Yt(c))}).on("mouseout",function(v){var d=v.target,y=rn(d,Tu);y&&(w1(y,v,c._api),Yt(c))}).on("click",function(v){var d=v.target,y=rn(d,function(m){return it(m).dataIndex!=null},!0);if(y){var p=y.selected?"unselect":"select",g=it(y);c._api.dispatchAction({type:p,dataType:g.dataType,dataIndexInside:g.dataIndex,seriesIndex:g.seriesIndex,isFromClick:!0})}})};function e(h){h.clearColorPalette(),h.eachSeries(function(c){c.clearColorPalette()})}function i(h){var c=[],v=[],d=!1;if(h.eachComponent(function(m,_){var S=_.get("zlevel")||0,b=_.get("z")||0,w=_.getZLevelKey();d=d||!!w,(m==="series"?v:c).push({zlevel:S,z:b,idx:_.componentIndex,type:m,key:w})}),d){var y=c.concat(v),p,g;Aa(y,function(m,_){return m.zlevel===_.zlevel?m.z-_.z:m.zlevel-_.zlevel}),M(y,function(m){var _=h.getComponent(m.type,m.idx),S=m.zlevel,b=m.key;p!=null&&(S=Math.max(p,S)),b?(S===p&&b!==g&&S++,g=b):g&&(S===p&&S++,g=""),p=S,_.setZLevel(S)})}}zs=function(h,c,v,d,y){i(c),Ov(h,c,v,d,y),M(h._chartsViews,function(p){p.__alive=!1}),ma(h,c,v,d,y),M(h._chartsViews,function(p){p.__alive||p.remove(c,v)})},Ov=function(h,c,v,d,y,p){M(p||h._componentsViews,function(g){var m=g.__model;l(m,g),g.render(m,c,v,d),s(m,g),f(m,g)})},ma=function(h,c,v,d,y,p){var g=h._scheduler;y=k(y||{},{updatedSeries:c.getSeries()}),fe.trigger("series:beforeupdate",c,v,y);var m=!1;c.eachSeries(function(_){var S=h._chartsMap[_.__viewId];S.__alive=!0;var b=S.renderTask;g.updatePayload(b,d),l(_,S),p&&p.get(_.uid)&&b.dirty(),b.perform(g.getPerformArgs(b))&&(m=!0),S.group.silent=!!_.get("silent"),o(_,S),ch(_)}),g.unfinished=m||g.unfinished,fe.trigger("series:layoutlabels",c,v,y),fe.trigger("series:transition",c,v,y),c.eachSeries(function(_){var S=h._chartsMap[_.__viewId];s(_,S),f(_,S)}),a(h,c),fe.trigger("series:afterupdate",c,v,y)},Yt=function(h){h[Rs]=!0,h.getZr().wakeUp()},Nv=function(h){h[Rs]&&(h.getZr().storage.traverse(function(c){fn(c)||n(c)}),h[Rs]=!1)};function n(h){for(var c=[],v=h.currentStates,d=0;d<v.length;d++){var y=v[d];y==="emphasis"||y==="blur"||y==="select"||c.push(y)}h.selected&&h.states.select&&c.push("select"),h.hoverState===_o&&h.states.emphasis?c.push("emphasis"):h.hoverState===mo&&h.states.blur&&c.push("blur"),h.useStates(c)}function a(h,c){var v=h._zr,d=v.storage,y=0;d.traverse(function(p){p.isGroup||y++}),y>c.get("hoverLayerThreshold")&&!$.node&&!$.worker&&c.eachSeries(function(p){if(!p.preventUsingHoverLayer){var g=h._chartsMap[p.__viewId];g.__alive&&g.eachRendered(function(m){m.states.emphasis&&(m.states.emphasis.hoverLayer=!0)})}})}function o(h,c){var v=h.get("blendMode")||null;c.eachRendered(function(d){d.isGroup||(d.style.blend=v)})}function s(h,c){if(!h.preventAutoZ){var v=h.get("z")||0,d=h.get("zlevel")||0;c.eachRendered(function(y){return u(y,v,d,-1/0),!0})}}function u(h,c,v,d){var y=h.getTextContent(),p=h.getTextGuideLine(),g=h.isGroup;if(g)for(var m=h.childrenRef(),_=0;_<m.length;_++)d=Math.max(u(m[_],c,v,d),d);else h.z=c,h.zlevel=v,d=Math.max(h.z2,d);if(y&&(y.z=c,y.zlevel=v,isFinite(d)&&(y.z2=d+2)),p){var S=h.textGuideLineConfig;p.z=c,p.zlevel=v,isFinite(d)&&(p.z2=d+(S&&S.showAbove?1:-1))}return d}function l(h,c){c.eachRendered(function(v){if(!fn(v)){var d=v.getTextContent(),y=v.getTextGuideLine();v.stateTransition&&(v.stateTransition=null),d&&d.stateTransition&&(d.stateTransition=null),y&&y.stateTransition&&(y.stateTransition=null),v.hasState()?(v.prevStates=v.currentStates,v.clearStates()):v.prevStates&&(v.prevStates=null)}})}function f(h,c){var v=h.getModel("stateAnimation"),d=h.isAnimationEnabled(),y=v.get("duration"),p=y>0?{duration:y,delay:v.get("delay"),easing:v.get("easing")}:null;c.eachRendered(function(g){if(g.states&&g.states.emphasis){if(fn(g))return;if(g instanceof ot&&L1(g),g.__dirty){var m=g.prevStates;m&&g.useStates(m)}if(d){g.stateTransition=p;var _=g.getTextContent(),S=g.getTextGuideLine();_&&(_.stateTransition=p),S&&(S.stateTransition=p)}g.__dirty&&n(g)}})}Bv=function(h){return new(function(c){O(v,c);function v(){return c!==null&&c.apply(this,arguments)||this}return v.prototype.getCoordinateSystems=function(){return h._coordSysMgr.getCoordinateSystems()},v.prototype.getComponentByElement=function(d){for(;d;){var y=d.__ecComponentInfo;if(y!=null)return h._model.getComponent(y.mainType,y.index);d=d.parent}},v.prototype.enterEmphasis=function(d,y){Ja(d,y),Yt(h)},v.prototype.leaveEmphasis=function(d,y){ja(d,y),Yt(h)},v.prototype.enterBlur=function(d){m1(d),Yt(h)},v.prototype.leaveBlur=function(d){zd(d),Yt(h)},v.prototype.enterSelect=function(d){Hd(d),Yt(h)},v.prototype.leaveSelect=function(d){Vd(d),Yt(h)},v.prototype.getModel=function(){return h.getModel()},v.prototype.getViewOfComponentModel=function(d){return h.getViewOfComponentModel(d)},v.prototype.getViewOfSeriesModel=function(d){return h.getViewOfSeriesModel(d)},v}(Up))(h)},Og=function(h){function c(v,d){for(var y=0;y<v.length;y++){var p=v[y];p[ks]=d}}M(cn,function(v,d){h._messageCenter.on(d,function(y){if(Fv[h.group]&&h[ks]!==Ev){if(y&&y.escapeConnect)return;var p=h.makeActionFromEvent(y),g=[];M(dn,function(m){m!==h&&m.group===h.group&&g.push(m)}),c(g,Ev),M(g,function(m){m[ks]!==Hx&&m.dispatchAction(p)}),c(g,Vx)}})})}}(),t}(xe),$l=Bg.prototype;$l.on=Pg("on");$l.off=Pg("off");$l.one=function(r,t,e){var i=this;function n(){for(var a=[],o=0;o<arguments.length;o++)a[o]=arguments[o];t&&t.apply&&t.apply(this,a),i.off(r,n)}this.on.call(this,r,n,e)};var Gx=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];var lo={},cn={},zu=[],Hu=[],fo=[],Ng={},Vu={},dn={},Fv={},Wx=+new Date-0,Xl="_echarts_instance_";function Ux(r,t,e){var i=!(e&&e.ssr);if(i){var n=Yx(r);if(n)return n}var a=new Bg(r,t,e);return a.id="ec_"+Wx++,dn[a.id]=a,i&&Sd(r,Xl,a.id),Og(a),fe.trigger("afterinit",a),a}function Yx(r){return dn[n_(r,Xl)]}function Fg(r,t){Ng[r]=t}function zg(r){nt(Hu,r)<0&&Hu.push(r)}function Hg(r,t){ql(zu,r,t,Ex)}function $x(r){Zl("afterinit",r)}function Xx(r){Zl("afterupdate",r)}function Zl(r,t){fe.on(r,t)}function Ai(r,t,e){U(t)&&(e=t,t="");var i=H(r)?r.type:[r,r={event:t}][0];r.event=(r.event||i).toLowerCase(),t=r.event,!cn[t]&&(Ie(Iv.test(i)&&Iv.test(t)),lo[i]||(lo[i]={action:e,actionInfo:r}),cn[t]=i)}function Zx(r,t){Nl.register(r,t)}function qx(r,t){ql(fo,r,t,Dg,"layout")}function Yr(r,t){ql(fo,r,t,Ag,"visual")}var zv=[];function ql(r,t,e,i,n){if((U(t)||H(t))&&(e=t,t=i),!(nt(zv,e)>=0)){zv.push(e);var a=gg.wrapStageHandler(e,n);a.__prio=t,a.__raw=e,r.push(a)}}function Vg(r,t){Vu[r]=t}function Kx(r,t,e){var i=Dx("registerMap");i&&i(r,t,e)}var Qx=Qw;Yr(Yl,Db);Yr(Io,Ab);Yr(Io,Lb);Yr(Yl,Ub);Yr(Io,Yb);Yr(Lg,Tx);zg($p);Hg(Px,Rw);Vg("default",Pb);Ai({type:Er,event:Er,update:Er},Ot);Ai({type:Oa,event:Oa,update:Oa},Ot);Ai({type:sn,event:sn,update:sn},Ot);Ai({type:Ba,event:Ba,update:Ba},Ot);Ai({type:un,event:un,update:un},Ot);Fg("light",Vb);Fg("dark",Gb);function Wi(r){return r==null?0:r.length||1}function Hv(r){return r}var Jx=function(){function r(t,e,i,n,a,o){this._old=t,this._new=e,this._oldKeyGetter=i||Hv,this._newKeyGetter=n||Hv,this.context=a,this._diffModeMultiple=o==="multiple"}return r.prototype.add=function(t){return this._add=t,this},r.prototype.update=function(t){return this._update=t,this},r.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},r.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},r.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},r.prototype.remove=function(t){return this._remove=t,this},r.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},r.prototype._executeOneToOne=function(){var t=this._old,e=this._new,i={},n=new Array(t.length),a=new Array(e.length);this._initIndexMap(t,null,n,"_oldKeyGetter"),this._initIndexMap(e,i,a,"_newKeyGetter");for(var o=0;o<t.length;o++){var s=n[o],u=i[s],l=Wi(u);if(l>1){var f=u.shift();u.length===1&&(i[s]=u[0]),this._update&&this._update(f,o)}else l===1?(i[s]=null,this._update&&this._update(u,o)):this._remove&&this._remove(o)}this._performRestAdd(a,i)},r.prototype._executeMultiple=function(){var t=this._old,e=this._new,i={},n={},a=[],o=[];this._initIndexMap(t,i,a,"_oldKeyGetter"),this._initIndexMap(e,n,o,"_newKeyGetter");for(var s=0;s<a.length;s++){var u=a[s],l=i[u],f=n[u],h=Wi(l),c=Wi(f);if(h>1&&c===1)this._updateManyToOne&&this._updateManyToOne(f,l),n[u]=null;else if(h===1&&c>1)this._updateOneToMany&&this._updateOneToMany(f,l),n[u]=null;else if(h===1&&c===1)this._update&&this._update(f,l),n[u]=null;else if(h>1&&c>1)this._updateManyToMany&&this._updateManyToMany(f,l),n[u]=null;else if(h>1)for(var v=0;v<h;v++)this._remove&&this._remove(l[v]);else this._remove&&this._remove(l)}this._performRestAdd(o,n)},r.prototype._performRestAdd=function(t,e){for(var i=0;i<t.length;i++){var n=t[i],a=e[n],o=Wi(a);if(o>1)for(var s=0;s<o;s++)this._add&&this._add(a[s]);else o===1&&this._add&&this._add(a);e[n]=null}},r.prototype._initIndexMap=function(t,e,i,n){for(var a=this._diffModeMultiple,o=0;o<t.length;o++){var s="_ec_"+this[n](t[o],o);if(a||(i[o]=s),!!e){var u=e[s],l=Wi(u);l===0?(e[s]=o,a&&i.push(s)):l===1?e[s]=[u,o]:u.push(o)}}},r}();const jx=Jx;var tT=function(){function r(t,e){this._encode=t,this._schema=e}return r.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},r.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},r}();function eT(r,t){var e={},i=e.encode={},n=Z(),a=[],o=[],s={};M(r.dimensions,function(c){var v=r.getDimensionInfo(c),d=v.coordDim;if(d){var y=v.coordDimIndex;Hs(i,d)[y]=c,v.isExtraCoord||(n.set(d,1),iT(v.type)&&(a[0]=c),Hs(s,d)[y]=r.getDimensionIndex(v.name)),v.defaultTooltip&&o.push(c)}Np.each(function(p,g){var m=Hs(i,g),_=v.otherDims[g];_!=null&&_!==!1&&(m[_]=v.name)})});var u=[],l={};n.each(function(c,v){var d=i[v];l[v]=d[0],u=u.concat(d)}),e.dataDimsOnCoord=u,e.dataDimIndicesOnCoord=G(u,function(c){return r.getDimensionInfo(c).storeDimIndex}),e.encodeFirstDimNotExtra=l;var f=i.label;f&&f.length&&(a=f.slice());var h=i.tooltip;return h&&h.length?o=h.slice():o.length||(o=a.slice()),i.defaultedLabel=a,i.defaultedTooltip=o,e.userOutput=new tT(s,t),e}function Hs(r,t){return r.hasOwnProperty(t)||(r[t]=[]),r[t]}function rT(r){return r==="category"?"ordinal":r==="time"?"time":"float"}function iT(r){return!(r==="ordinal"||r==="time")}var nT=function(){function r(t){this.otherDims={},t!=null&&k(this,t)}return r}();const Ha=nT;var aT=yt(),oT={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},Gg=function(){function r(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return r.prototype.isDimensionOmitted=function(){return this._dimOmitted},r.prototype._updateDimOmitted=function(t){this._dimOmitted=t,t&&(this._dimNameMap||(this._dimNameMap=Yg(this.source)))},r.prototype.getSourceDimensionIndex=function(t){return X(this._dimNameMap.get(t),-1)},r.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},r.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=Zp(this.source),i=!$g(t),n="",a=[],o=0,s=0;o<t;o++){var u=void 0,l=void 0,f=void 0,h=this.dimensions[s];if(h&&h.storeDimIndex===o)u=e?h.name:null,l=h.type,f=h.ordinalMeta,s++;else{var c=this.getSourceDimension(o);c&&(u=e?c.name:null,l=c.type)}a.push({property:u,type:l,ordinalMeta:f}),e&&u!=null&&(!h||!h.isCalculationCoord)&&(n+=i?u.replace(/\`/g,"`1").replace(/\$/g,"`2"):u),n+="$",n+=oT[l]||"f",f&&(n+=f.uid),n+="$"}var v=this.source,d=[v.seriesLayoutBy,v.startIndex,n].join("$$");return{dimensions:a,hash:d}},r.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,i=0;e<this._fullDimCount;e++){var n=void 0,a=this.dimensions[i];if(a&&a.storeDimIndex===e)a.isCalculationCoord||(n=a.name),i++;else{var o=this.getSourceDimension(e);o&&(n=o.name)}t.push(n)}return t},r.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},r}();function Wg(r){return r instanceof Gg}function Ug(r){for(var t=Z(),e=0;e<(r||[]).length;e++){var i=r[e],n=H(i)?i.name:i;n!=null&&t.get(n)==null&&t.set(n,e)}return t}function Yg(r){var t=aT(r);return t.dimNameMap||(t.dimNameMap=Ug(r.dimensionsDefine))}function $g(r){return r>30}var Ui=H,Ve=G,sT=typeof Int32Array>"u"?Array:Int32Array,uT="e\0\0",Vv=-1,lT=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],fT=["_approximateExtent"],Gv,_a,Yi,$i,Vs,Xi,Gs,hT=function(){function r(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","minmaxDownSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","minmaxDownSample","lttbDownSample"];var i,n=!1;Wg(t)?(i=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(n=!0,i=t),i=i||["x","y"];for(var a={},o=[],s={},u=!1,l={},f=0;f<i.length;f++){var h=i[f],c=z(h)?new Ha({name:h}):h instanceof Ha?h:new Ha(h),v=c.name;c.type=c.type||"float",c.coordDim||(c.coordDim=v,c.coordDimIndex=0);var d=c.otherDims=c.otherDims||{};o.push(v),a[v]=c,l[v]!=null&&(u=!0),c.createInvertedIndices&&(s[v]=[]),d.itemName===0&&(this._nameDimIdx=f),d.itemId===0&&(this._idDimIdx=f),n&&(c.storeDimIndex=f)}if(this.dimensions=o,this._dimInfos=a,this._initGetDimensionInfo(u),this.hostModel=e,this._invertedIndicesMap=s,this._dimOmitted){var y=this._dimIdxToName=Z();M(o,function(p){y.set(a[p].storeDimIndex,p)})}}return r.prototype.getDimension=function(t){var e=this._recognizeDimIndex(t);if(e==null)return t;if(e=t,!this._dimOmitted)return this.dimensions[e];var i=this._dimIdxToName.get(e);if(i!=null)return i;var n=this._schema.getSourceDimension(e);if(n)return n.name},r.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);if(e!=null)return e;if(t==null)return-1;var i=this._getDimInfo(t);return i?i.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},r.prototype._recognizeDimIndex=function(t){if(vt(t)||t!=null&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},r.prototype._getStoreDimIndex=function(t){var e=this.getDimensionIndex(t);return e},r.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},r.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(i){return e.hasOwnProperty(i)?e[i]:void 0}:function(i){return e[i]}},r.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},r.prototype.mapDimension=function(t,e){var i=this._dimSummary;if(e==null)return i.encodeFirstDimNotExtra[t];var n=i.encode[t];return n?n[e]:null},r.prototype.mapDimensionsAll=function(t){var e=this._dimSummary,i=e.encode[t];return(i||[]).slice()},r.prototype.getStore=function(){return this._store},r.prototype.initData=function(t,e,i){var n=this,a;if(t instanceof Pu&&(a=t),!a){var o=this.dimensions,s=Fl(t)||Nt(t)?new qp(t,o.length):t;a=new Pu;var u=Ve(o,function(l){return{type:n._dimInfos[l].type,property:l}});a.initData(s,u,i)}this._store=a,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,a.count()),this._dimSummary=eT(this,this._schema),this.userOutput=this._dimSummary.userOutput},r.prototype.appendData=function(t){var e=this._store.appendData(t);this._doInit(e[0],e[1])},r.prototype.appendValues=function(t,e){var i=this._store.appendValues(t,e&&e.length),n=i.start,a=i.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var s=n;s<a;s++){var u=s-n;this._nameList[s]=e[u],o&&Gs(this,s)}},r.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,i=0;i<e.length;i++){var n=this._dimInfos[e[i]];n.ordinalMeta&&t.collectOrdinalMeta(n.storeDimIndex,n.ordinalMeta)}},r.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return this._idDimIdx==null&&t.getSource().sourceFormat!==je&&!t.fillStorage},r.prototype._doInit=function(t,e){if(!(t>=e)){var i=this._store,n=i.getProvider();this._updateOrdinalMeta();var a=this._nameList,o=this._idList,s=n.getSource().sourceFormat,u=s===ne;if(u&&!n.pure)for(var l=[],f=t;f<e;f++){var h=n.getItem(f,l);if(!this.hasItemOption&&X0(h)&&(this.hasItemOption=!0),h){var c=h.name;a[f]==null&&c!=null&&(a[f]=Se(c,null));var v=h.id;o[f]==null&&v!=null&&(o[f]=Se(v,null))}}if(this._shouldMakeIdFromName())for(var f=t;f<e;f++)Gs(this,f);Gv(this)}},r.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},r.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},r.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},r.prototype.setCalculationInfo=function(t,e){Ui(t)?k(this._calculationInfo,t):this._calculationInfo[t]=e},r.prototype.getName=function(t){var e=this.getRawIndex(t),i=this._nameList[e];return i==null&&this._nameDimIdx!=null&&(i=Yi(this,this._nameDimIdx,e)),i==null&&(i=""),i},r.prototype._getCategory=function(t,e){var i=this._store.get(t,e),n=this._store.getOrdinalMeta(t);return n?n.categories[i]:i},r.prototype.getId=function(t){return _a(this,this.getRawIndex(t))},r.prototype.count=function(){return this._store.count()},r.prototype.get=function(t,e){var i=this._store,n=this._dimInfos[t];if(n)return i.get(n.storeDimIndex,e)},r.prototype.getByRawIndex=function(t,e){var i=this._store,n=this._dimInfos[t];if(n)return i.getByRawIndex(n.storeDimIndex,e)},r.prototype.getIndices=function(){return this._store.getIndices()},r.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},r.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},r.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},r.prototype.getValues=function(t,e){var i=this,n=this._store;return B(t)?n.getValues(Ve(t,function(a){return i._getStoreDimIndex(a)}),e):n.getValues(t)},r.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,i=0,n=e.length;i<n;i++)if(isNaN(this._store.get(e[i],t)))return!1;return!0},r.prototype.indexOfName=function(t){for(var e=0,i=this._store.count();e<i;e++)if(this.getName(e)===t)return e;return-1},r.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},r.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},r.prototype.rawIndexOf=function(t,e){var i=t&&this._invertedIndicesMap[t],n=i&&i[e];return n==null||isNaN(n)?Vv:n},r.prototype.indicesOfNearest=function(t,e,i){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,i)},r.prototype.each=function(t,e,i){U(t)&&(i=e,e=t,t=[]);var n=i||this,a=Ve($i(t),this._getStoreDimIndex,this);this._store.each(a,n?ht(e,n):e)},r.prototype.filterSelf=function(t,e,i){U(t)&&(i=e,e=t,t=[]);var n=i||this,a=Ve($i(t),this._getStoreDimIndex,this);return this._store=this._store.filter(a,n?ht(e,n):e),this},r.prototype.selectRange=function(t){var e=this,i={},n=lt(t);return M(n,function(a){var o=e._getStoreDimIndex(a);i[o]=t[a]}),this._store=this._store.selectRange(i),this},r.prototype.mapArray=function(t,e,i){U(t)&&(i=e,e=t,t=[]),i=i||this;var n=[];return this.each(t,function(){n.push(e&&e.apply(this,arguments))},i),n},r.prototype.map=function(t,e,i,n){var a=i||n||this,o=Ve($i(t),this._getStoreDimIndex,this),s=Xi(this);return s._store=this._store.map(o,a?ht(e,a):e),s},r.prototype.modify=function(t,e,i,n){var a=i||n||this,o=Ve($i(t),this._getStoreDimIndex,this);this._store.modify(o,a?ht(e,a):e)},r.prototype.downSample=function(t,e,i,n){var a=Xi(this);return a._store=this._store.downSample(this._getStoreDimIndex(t),e,i,n),a},r.prototype.minmaxDownSample=function(t,e){var i=Xi(this);return i._store=this._store.minmaxDownSample(this._getStoreDimIndex(t),e),i},r.prototype.lttbDownSample=function(t,e){var i=Xi(this);return i._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),i},r.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},r.prototype.getItemModel=function(t){var e=this.hostModel,i=this.getRawDataItem(t);return new At(i,e,e&&e.ecModel)},r.prototype.diff=function(t){var e=this;return new jx(t?t.getStore().getIndices():[],this.getStore().getIndices(),function(i){return _a(t,i)},function(i){return _a(e,i)})},r.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},r.prototype.setVisual=function(t,e){this._visual=this._visual||{},Ui(t)?k(this._visual,t):this._visual[t]=e},r.prototype.getItemVisual=function(t,e){var i=this._itemVisuals[t],n=i&&i[e];return n??this.getVisual(e)},r.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},r.prototype.ensureUniqueItemVisual=function(t,e){var i=this._itemVisuals,n=i[t];n||(n=i[t]={});var a=n[e];return a==null&&(a=this.getVisual(e),B(a)?a=a.slice():Ui(a)&&(a=k({},a)),n[e]=a),a},r.prototype.setItemVisual=function(t,e,i){var n=this._itemVisuals[t]||{};this._itemVisuals[t]=n,Ui(e)?k(n,e):n[e]=i},r.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},r.prototype.setLayout=function(t,e){Ui(t)?k(this._layout,t):this._layout[t]=e},r.prototype.getLayout=function(t){return this._layout[t]},r.prototype.getItemLayout=function(t){return this._itemLayouts[t]},r.prototype.setItemLayout=function(t,e,i){this._itemLayouts[t]=i?k(this._itemLayouts[t]||{},e):e},r.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},r.prototype.setItemGraphicEl=function(t,e){var i=this.hostModel&&this.hostModel.seriesIndex;u1(i,this.dataType,t,e),this._graphicEls[t]=e},r.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},r.prototype.eachItemGraphicEl=function(t,e){M(this._graphicEls,function(i,n){i&&t&&t.call(e,i,n)})},r.prototype.cloneShallow=function(t){return t||(t=new r(this._schema?this._schema:Ve(this.dimensions,this._getDimInfo,this),this.hostModel)),Vs(t,this),t._store=this._store,t},r.prototype.wrapMethod=function(t,e){var i=this[t];U(i)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var n=i.apply(this,arguments);return e.apply(this,[n].concat(el(arguments)))})},r.internalField=function(){Gv=function(t){var e=t._invertedIndicesMap;M(e,function(i,n){var a=t._dimInfos[n],o=a.ordinalMeta,s=t._store;if(o){i=e[n]=new sT(o.categories.length);for(var u=0;u<i.length;u++)i[u]=Vv;for(var u=0;u<s.count();u++)i[s.get(a.storeDimIndex,u)]=u}})},Yi=function(t,e,i){return Se(t._getCategory(e,i),null)},_a=function(t,e){var i=t._idList[e];return i==null&&t._idDimIdx!=null&&(i=Yi(t,t._idDimIdx,e)),i==null&&(i=uT+e),i},$i=function(t){return B(t)||(t=t!=null?[t]:[]),t},Xi=function(t){var e=new r(t._schema?t._schema:Ve(t.dimensions,t._getDimInfo,t),t.hostModel);return Vs(e,t),e},Vs=function(t,e){M(lT.concat(e.__wrappedMethods||[]),function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t.__wrappedMethods=e.__wrappedMethods,M(fT,function(i){t[i]=K(e[i])}),t._calculationInfo=k({},e._calculationInfo)},Gs=function(t,e){var i=t._nameList,n=t._idList,a=t._nameDimIdx,o=t._idDimIdx,s=i[e],u=n[e];if(s==null&&a!=null&&(i[e]=s=Yi(t,a,e)),u==null&&o!=null&&(n[e]=u=Yi(t,o,e)),u==null&&s!=null){var l=t._nameRepeatCount,f=l[s]=(l[s]||0)+1;u=s,f>1&&(u+="__ec__"+f),n[e]=u}}}(),r}();const vT=hT;function cT(r,t){Fl(r)||(r=zl(r)),t=t||{};var e=t.coordDimensions||[],i=t.dimensionsDefine||r.dimensionsDefine||[],n=Z(),a=[],o=pT(r,e,i,t.dimensionsCount),s=t.canOmitUnusedDimensions&&$g(o),u=i===r.dimensionsDefine,l=u?Yg(r):Ug(i),f=t.encodeDefine;!f&&t.encodeDefaulter&&(f=t.encodeDefaulter(r,o));for(var h=Z(f),c=new tg(o),v=0;v<c.length;v++)c[v]=-1;function d(T){var C=c[T];if(C<0){var A=i[T],L=H(A)?A:{name:A},I=new Ha,P=L.name;P!=null&&l.get(P)!=null&&(I.name=I.displayName=P),L.type!=null&&(I.type=L.type),L.displayName!=null&&(I.displayName=L.displayName);var E=a.length;return c[T]=E,I.storeDimIndex=T,a.push(I),I}return a[C]}if(!s)for(var v=0;v<o;v++)d(v);h.each(function(T,C){var A=Lt(T).slice();if(A.length===1&&!z(A[0])&&A[0]<0){h.set(C,!1);return}var L=h.set(C,[]);M(A,function(I,P){var E=z(I)?l.get(I):I;E!=null&&E<o&&(L[P]=E,p(d(E),C,P))})});var y=0;M(e,function(T){var C,A,L,I;if(z(T))C=T,I={};else{I=T,C=I.name;var P=I.ordinalMeta;I.ordinalMeta=null,I=k({},I),I.ordinalMeta=P,A=I.dimsDef,L=I.otherDims,I.name=I.coordDim=I.coordDimIndex=I.dimsDef=I.otherDims=null}var E=h.get(C);if(E!==!1){if(E=Lt(E),!E.length)for(var R=0;R<(A&&A.length||1);R++){for(;y<o&&d(y).coordDim!=null;)y++;y<o&&E.push(y++)}M(E,function(V,N){var F=d(V);if(u&&I.type!=null&&(F.type=I.type),p(tt(F,I),C,N),F.name==null&&A){var Y=A[N];!H(Y)&&(Y={name:Y}),F.name=F.displayName=Y.name,F.defaultTooltip=Y.defaultTooltip}L&&tt(F.otherDims,L)})}});function p(T,C,A){Np.get(C)!=null?T.otherDims[C]=A:(T.coordDim=C,T.coordDimIndex=A,n.set(C,!0))}var g=t.generateCoord,m=t.generateCoordCount,_=m!=null;m=g?m||1:0;var S=g||"value";function b(T){T.name==null&&(T.name=T.coordDim)}if(s)M(a,function(T){b(T)}),a.sort(function(T,C){return T.storeDimIndex-C.storeDimIndex});else for(var w=0;w<o;w++){var x=d(w),D=x.coordDim;D==null&&(x.coordDim=gT(S,n,_),x.coordDimIndex=0,(!g||m<=0)&&(x.isExtraCoord=!0),m--),b(x),x.type==null&&(Vp(r,w)===Vt.Must||x.isExtraCoord&&(x.otherDims.itemName!=null||x.otherDims.seriesName!=null))&&(x.type="ordinal")}return dT(a),new Gg({source:r,dimensions:a,fullDimensionCount:o,dimensionOmitted:s})}function dT(r){for(var t=Z(),e=0;e<r.length;e++){var i=r[e],n=i.name,a=t.get(n)||0;a>0&&(i.name=n+(a-1)),a++,t.set(n,a)}}function pT(r,t,e,i){var n=Math.max(r.dimensionsDetectedCount||1,t.length,e.length,i||0);return M(t,function(a){var o;H(a)&&(o=a.dimsDef)&&(n=Math.max(n,o.length))}),n}function gT(r,t,e){if(e||t.hasKey(r)){for(var i=0;t.hasKey(r+i);)i++;r+=i}return t.set(r,!0),r}var yT=function(){function r(t){this.coordSysDims=[],this.axisMap=Z(),this.categoryAxisMap=Z(),this.coordSysName=t}return r}();function mT(r){var t=r.get("coordinateSystem"),e=new yT(t),i=_T[t];if(i)return i(r,e,e.axisMap,e.categoryAxisMap),e}var _T={cartesian2d:function(r,t,e,i){var n=r.getReferringComponents("xAxis",he).models[0],a=r.getReferringComponents("yAxis",he).models[0];t.coordSysDims=["x","y"],e.set("x",n),e.set("y",a),oi(n)&&(i.set("x",n),t.firstCategoryDimIndex=0),oi(a)&&(i.set("y",a),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=1))},singleAxis:function(r,t,e,i){var n=r.getReferringComponents("singleAxis",he).models[0];t.coordSysDims=["single"],e.set("single",n),oi(n)&&(i.set("single",n),t.firstCategoryDimIndex=0)},polar:function(r,t,e,i){var n=r.getReferringComponents("polar",he).models[0],a=n.findAxisModel("radiusAxis"),o=n.findAxisModel("angleAxis");t.coordSysDims=["radius","angle"],e.set("radius",a),e.set("angle",o),oi(a)&&(i.set("radius",a),t.firstCategoryDimIndex=0),oi(o)&&(i.set("angle",o),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=1))},geo:function(r,t,e,i){t.coordSysDims=["lng","lat"]},parallel:function(r,t,e,i){var n=r.ecModel,a=n.getComponent("parallel",r.get("parallelIndex")),o=t.coordSysDims=a.dimensions.slice();M(a.parallelAxisIndex,function(s,u){var l=n.getComponent("parallelAxis",s),f=o[u];e.set(f,l),oi(l)&&(i.set(f,l),t.firstCategoryDimIndex==null&&(t.firstCategoryDimIndex=u))})}};function oi(r){return r.get("type")==="category"}function ST(r,t,e){e=e||{};var i=e.byIndex,n=e.stackedCoordDimension,a,o,s;wT(t)?a=t:(o=t.schema,a=o.dimensions,s=t.store);var u=!!(r&&r.get("stack")),l,f,h,c;if(M(a,function(m,_){z(m)&&(a[_]=m={name:m}),u&&!m.isExtraCoord&&(!i&&!l&&m.ordinalMeta&&(l=m),!f&&m.type!=="ordinal"&&m.type!=="time"&&(!n||n===m.coordDim)&&(f=m))}),f&&!i&&!l&&(i=!0),f){h="__\0ecstackresult_"+r.id,c="__\0ecstackedover_"+r.id,l&&(l.createInvertedIndices=!0);var v=f.coordDim,d=f.type,y=0;M(a,function(m){m.coordDim===v&&y++});var p={name:h,coordDim:v,coordDimIndex:y,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:a.length},g={name:c,coordDim:c,coordDimIndex:y+1,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:a.length+1};o?(s&&(p.storeDimIndex=s.ensureCalculationDimension(c,d),g.storeDimIndex=s.ensureCalculationDimension(h,d)),o.appendCalculationDimension(p),o.appendCalculationDimension(g)):(a.push(p),a.push(g))}return{stackedDimension:f&&f.name,stackedByDimension:l&&l.name,isStackedByIndex:i,stackedOverDimension:c,stackResultDimension:h}}function wT(r){return!Wg(r.schema)}function In(r,t){return!!t&&t===r.getCalculationInfo("stackedDimension")}function bT(r,t){return In(r,t)?r.getCalculationInfo("stackResultDimension"):t}function xT(r,t){var e=r.get("coordinateSystem"),i=Nl.get(e),n;return t&&t.coordSysDims&&(n=G(t.coordSysDims,function(a){var o={name:a},s=t.axisMap.get(a);if(s){var u=s.get("type");o.type=rT(u)}return o})),n||(n=i&&(i.getDimensionsInfo?i.getDimensionsInfo():i.dimensions.slice())||["x","y"]),n}function TT(r,t,e){var i,n;return e&&M(r,function(a,o){var s=a.coordDim,u=e.categoryAxisMap.get(s);u&&(i==null&&(i=o),a.ordinalMeta=u.getOrdinalMeta(),t&&(a.createInvertedIndices=!0)),a.otherDims.itemName!=null&&(n=!0)}),!n&&i!=null&&(r[i].otherDims.itemName=0),i}function CT(r,t,e){e=e||{};var i=t.getSourceManager(),n,a=!1;r?(a=!0,n=zl(r)):(n=i.getSource(),a=n.sourceFormat===ne);var o=mT(t),s=xT(t,o),u=e.useEncodeDefaulter,l=U(u)?u:u?ee(aw,s,t):null,f={coordDimensions:s,generateCoord:e.generateCoord,encodeDefine:t.getEncode(),encodeDefaulter:l,canOmitUnusedDimensions:!a},h=cT(n,f),c=TT(h.dimensions,e.createInvertedIndices,o),v=a?null:i.getSharedDataStore(h),d=ST(t,{schema:h,store:v}),y=new vT(h,t);y.setCalculationInfo(d);var p=c!=null&&MT(n)?function(g,m,_,S){return S===c?_:this.defaultDimValueGetter(g,m,_,S)}:null;return y.hasItemOption=!1,y.initData(a?n:v,null,p),y}function MT(r){if(r.sourceFormat===ne){var t=DT(r.data||[]);return!B(Nn(t))}}function DT(r){for(var t=0;t<r.length&&r[t]==null;)t++;return r[t]}var Xg=function(){function r(t){this._setting=t||{},this._extent=[1/0,-1/0]}return r.prototype.getSetting=function(t){return this._setting[t]},r.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},r.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.setExtent=function(t,e){var i=this._extent;isNaN(t)||(i[0]=t),isNaN(e)||(i[1]=e)},r.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},r.prototype.isBlank=function(){return this._isBlank},r.prototype.setBlank=function(t){this._isBlank=t},r}();po(Xg);const Oe=Xg;var AT=0,LT=function(){function r(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++AT}return r.createByAxisModel=function(t){var e=t.option,i=e.data,n=i&&G(i,PT);return new r({categories:n,needCollect:!n,deduplication:e.dedplication!==!1})},r.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},r.prototype.parseAndCollect=function(t){var e,i=this._needCollect;if(!z(t)&&!i)return t;if(i&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var n=this._getOrCreateMap();return e=n.get(t),e==null&&(i?(e=this.categories.length,this.categories[e]=t,n.set(t,e)):e=NaN),e},r.prototype._getOrCreateMap=function(){return this._map||(this._map=Z(this.categories))},r}();function PT(r){return H(r)&&r.value!=null?r.value:r+""}const Gu=LT;function Wu(r){return r.type==="interval"||r.type==="log"}function IT(r,t,e,i){var n={},a=r[1]-r[0],o=n.interval=dd(a/t,!0);e!=null&&o<e&&(o=n.interval=e),i!=null&&o>i&&(o=n.interval=i);var s=n.intervalPrecision=Zg(o),u=n.niceTickExtent=[pt(Math.ceil(r[0]/o)*o,s),pt(Math.floor(r[1]/o)*o,s)];return ET(u,r),n}function Ws(r){var t=Math.pow(10,vl(r)),e=r/t;return e?e===2?e=3:e===3?e=5:e*=2:e=1,pt(e*t)}function Zg(r){return Ae(r)+2}function Wv(r,t,e){r[t]=Math.max(Math.min(r[t],e[1]),e[0])}function ET(r,t){!isFinite(r[0])&&(r[0]=t[0]),!isFinite(r[1])&&(r[1]=t[1]),Wv(r,0,t),Wv(r,1,t),r[0]>r[1]&&(r[0]=r[1])}function Eo(r,t){return r>=t[0]&&r<=t[1]}function Ro(r,t){return t[1]===t[0]?.5:(r-t[0])/(t[1]-t[0])}function ko(r,t){return r*(t[1]-t[0])+t[0]}var qg=function(r){O(t,r);function t(e){var i=r.call(this,e)||this;i.type="ordinal";var n=i.getSetting("ordinalMeta");return n||(n=new Gu({})),B(n)&&(n=new Gu({categories:G(n,function(a){return H(a)?a.value:a})})),i._ordinalMeta=n,i._extent=i.getSetting("extent")||[0,n.categories.length-1],i}return t.prototype.parse=function(e){return e==null?NaN:z(e)?this._ordinalMeta.getOrdinal(e):Math.round(e)},t.prototype.contain=function(e){return e=this.parse(e),Eo(e,this._extent)&&this._ordinalMeta.categories[e]!=null},t.prototype.normalize=function(e){return e=this._getTickNumber(this.parse(e)),Ro(e,this._extent)},t.prototype.scale=function(e){return e=Math.round(ko(e,this._extent)),this.getRawOrdinalNumber(e)},t.prototype.getTicks=function(){for(var e=[],i=this._extent,n=i[0];n<=i[1];)e.push({value:n}),n++;return e},t.prototype.getMinorTicks=function(e){},t.prototype.setSortInfo=function(e){if(e==null){this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;return}for(var i=e.ordinalNumbers,n=this._ordinalNumbersByTick=[],a=this._ticksByOrdinalNumber=[],o=0,s=this._ordinalMeta.categories.length,u=Math.min(s,i.length);o<u;++o){var l=i[o];n[o]=l,a[l]=o}for(var f=0;o<s;++o){for(;a[f]!=null;)f++;n.push(f),a[f]=o}},t.prototype._getTickNumber=function(e){var i=this._ticksByOrdinalNumber;return i&&e>=0&&e<i.length?i[e]:e},t.prototype.getRawOrdinalNumber=function(e){var i=this._ordinalNumbersByTick;return i&&e>=0&&e<i.length?i[e]:e},t.prototype.getLabel=function(e){if(!this.isBlank()){var i=this.getRawOrdinalNumber(e.value),n=this._ordinalMeta.categories[i];return n==null?"":n+""}},t.prototype.count=function(){return this._extent[1]-this._extent[0]+1},t.prototype.unionExtentFromData=function(e,i){this.unionExtent(e.getApproximateExtent(i))},t.prototype.isInExtentRange=function(e){return e=this._getTickNumber(e),this._extent[0]<=e&&this._extent[1]>=e},t.prototype.getOrdinalMeta=function(){return this._ordinalMeta},t.prototype.calcNiceTicks=function(){},t.prototype.calcNiceExtent=function(){},t.type="ordinal",t}(Oe);Oe.registerClass(qg);const Kg=qg;var Tr=pt,Qg=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="interval",e._interval=0,e._intervalPrecision=2,e}return t.prototype.parse=function(e){return e},t.prototype.contain=function(e){return Eo(e,this._extent)},t.prototype.normalize=function(e){return Ro(e,this._extent)},t.prototype.scale=function(e){return ko(e,this._extent)},t.prototype.setExtent=function(e,i){var n=this._extent;isNaN(e)||(n[0]=parseFloat(e)),isNaN(i)||(n[1]=parseFloat(i))},t.prototype.unionExtent=function(e){var i=this._extent;e[0]<i[0]&&(i[0]=e[0]),e[1]>i[1]&&(i[1]=e[1]),this.setExtent(i[0],i[1])},t.prototype.getInterval=function(){return this._interval},t.prototype.setInterval=function(e){this._interval=e,this._niceExtent=this._extent.slice(),this._intervalPrecision=Zg(e)},t.prototype.getTicks=function(e){var i=this._interval,n=this._extent,a=this._niceExtent,o=this._intervalPrecision,s=[];if(!i)return s;var u=1e4;n[0]<a[0]&&(e?s.push({value:Tr(a[0]-i,o)}):s.push({value:n[0]}));for(var l=a[0];l<=a[1]&&(s.push({value:l}),l=Tr(l+i,o),l!==s[s.length-1].value);)if(s.length>u)return[];var f=s.length?s[s.length-1].value:a[1];return n[1]>f&&(e?s.push({value:Tr(f+i,o)}):s.push({value:n[1]})),s},t.prototype.getMinorTicks=function(e){for(var i=this.getTicks(!0),n=[],a=this.getExtent(),o=1;o<i.length;o++){for(var s=i[o],u=i[o-1],l=0,f=[],h=s.value-u.value,c=h/e;l<e-1;){var v=Tr(u.value+(l+1)*c);v>a[0]&&v<a[1]&&f.push(v),l++}n.push(f)}return n},t.prototype.getLabel=function(e,i){if(e==null)return"";var n=i&&i.precision;n==null?n=Ae(e.value)||0:n==="auto"&&(n=this._intervalPrecision);var a=Tr(e.value,n,!0);return Ep(a)},t.prototype.calcNiceTicks=function(e,i,n){e=e||5;var a=this._extent,o=a[1]-a[0];if(isFinite(o)){o<0&&(o=-o,a.reverse());var s=IT(a,e,i,n);this._intervalPrecision=s.intervalPrecision,this._interval=s.interval,this._niceExtent=s.niceTickExtent}},t.prototype.calcNiceExtent=function(e){var i=this._extent;if(i[0]===i[1])if(i[0]!==0){var n=Math.abs(i[0]);e.fixMax||(i[1]+=n/2),i[0]-=n/2}else i[1]=1;var a=i[1]-i[0];isFinite(a)||(i[0]=0,i[1]=1),this.calcNiceTicks(e.splitNumber,e.minInterval,e.maxInterval);var o=this._interval;e.fixMin||(i[0]=Tr(Math.floor(i[0]/o)*o)),e.fixMax||(i[1]=Tr(Math.ceil(i[1]/o)*o))},t.prototype.setNiceExtent=function(e,i){this._niceExtent=[e,i]},t.type="interval",t}(Oe);Oe.registerClass(Qg);const Vn=Qg;var Jg=typeof Float32Array<"u",RT=Jg?Float32Array:Array;function ci(r){return B(r)?Jg?new Float32Array(r):r:new RT(r)}var kT="__ec_stack_";function jg(r){return r.get("stack")||kT+r.seriesIndex}function ty(r){return r.dim+r.index}function OT(r,t){var e=[];return t.eachSeriesByType(r,function(i){HT(i)&&e.push(i)}),e}function BT(r){var t={};M(r,function(u){var l=u.coordinateSystem,f=l.getBaseAxis();if(!(f.type!=="time"&&f.type!=="value"))for(var h=u.getData(),c=f.dim+"_"+f.index,v=h.getDimensionIndex(h.mapDimension(f.dim)),d=h.getStore(),y=0,p=d.count();y<p;++y){var g=d.get(v,y);t[c]?t[c].push(g):t[c]=[g]}});var e={};for(var i in t)if(t.hasOwnProperty(i)){var n=t[i];if(n){n.sort(function(u,l){return u-l});for(var a=null,o=1;o<n.length;++o){var s=n[o]-n[o-1];s>0&&(a=a===null?s:Math.min(a,s))}e[i]=a}}return e}function NT(r){var t=BT(r),e=[];return M(r,function(i){var n=i.coordinateSystem,a=n.getBaseAxis(),o=a.getExtent(),s;if(a.type==="category")s=a.getBandWidth();else if(a.type==="value"||a.type==="time"){var u=a.dim+"_"+a.index,l=t[u],f=Math.abs(o[1]-o[0]),h=a.scale.getExtent(),c=Math.abs(h[1]-h[0]);s=l?f/c*l:f}else{var v=i.getData();s=Math.abs(o[1]-o[0])/v.count()}var d=Dt(i.get("barWidth"),s),y=Dt(i.get("barMaxWidth"),s),p=Dt(i.get("barMinWidth")||(VT(i)?.5:1),s),g=i.get("barGap"),m=i.get("barCategoryGap");e.push({bandWidth:s,barWidth:d,barMaxWidth:y,barMinWidth:p,barGap:g,barCategoryGap:m,axisKey:ty(a),stackId:jg(i)})}),FT(e)}function FT(r){var t={};M(r,function(i,n){var a=i.axisKey,o=i.bandWidth,s=t[a]||{bandWidth:o,remainedWidth:o,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},u=s.stacks;t[a]=s;var l=i.stackId;u[l]||s.autoWidthCount++,u[l]=u[l]||{width:0,maxWidth:0};var f=i.barWidth;f&&!u[l].width&&(u[l].width=f,f=Math.min(s.remainedWidth,f),s.remainedWidth-=f);var h=i.barMaxWidth;h&&(u[l].maxWidth=h);var c=i.barMinWidth;c&&(u[l].minWidth=c);var v=i.barGap;v!=null&&(s.gap=v);var d=i.barCategoryGap;d!=null&&(s.categoryGap=d)});var e={};return M(t,function(i,n){e[n]={};var a=i.stacks,o=i.bandWidth,s=i.categoryGap;if(s==null){var u=lt(a).length;s=Math.max(35-u*4,15)+"%"}var l=Dt(s,o),f=Dt(i.gap,1),h=i.remainedWidth,c=i.autoWidthCount,v=(h-l)/(c+(c-1)*f);v=Math.max(v,0),M(a,function(g){var m=g.maxWidth,_=g.minWidth;if(g.width){var S=g.width;m&&(S=Math.min(S,m)),_&&(S=Math.max(S,_)),g.width=S,h-=S+f*S,c--}else{var S=v;m&&m<S&&(S=Math.min(m,h)),_&&_>S&&(S=_),S!==v&&(g.width=S,h-=S+f*S,c--)}}),v=(h-l)/(c+(c-1)*f),v=Math.max(v,0);var d=0,y;M(a,function(g,m){g.width||(g.width=v),y=g,d+=g.width*(1+f)}),y&&(d-=y.width*f);var p=-d/2;M(a,function(g,m){e[n][m]=e[n][m]||{bandWidth:o,offset:p,width:g.width},p+=g.width*(1+f)})}),e}function zT(r,t,e){if(r&&t){var i=r[ty(t)];return i!=null&&e!=null?i[jg(e)]:i}}function HT(r){return r.coordinateSystem&&r.coordinateSystem.type==="cartesian2d"}function VT(r){return r.pipelineContext&&r.pipelineContext.large}var GT=function(r,t,e,i){for(;e<i;){var n=e+i>>>1;r[n][1]<t?e=n+1:i=n}return e},ey=function(r){O(t,r);function t(e){var i=r.call(this,e)||this;return i.type="time",i}return t.prototype.getLabel=function(e){var i=this.getSetting("useUTC");return xo(e.value,Rh[qS(_i(this._minLevelUnit))]||Rh.second,i,this.getSetting("locale"))},t.prototype.getFormattedLabel=function(e,i,n){var a=this.getSetting("useUTC"),o=this.getSetting("locale");return KS(e,i,n,o,a)},t.prototype.getTicks=function(){var e=this._interval,i=this._extent,n=[];if(!e)return n;n.push({value:i[0],level:0});var a=this.getSetting("useUTC"),o=qT(this._minLevelUnit,this._approxInterval,a,i);return n=n.concat(o),n.push({value:i[1],level:0}),n},t.prototype.calcNiceExtent=function(e){var i=this._extent;if(i[0]===i[1]&&(i[0]-=te,i[1]+=te),i[1]===-1/0&&i[0]===1/0){var n=new Date;i[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),i[0]=i[1]-te}this.calcNiceTicks(e.splitNumber,e.minInterval,e.maxInterval)},t.prototype.calcNiceTicks=function(e,i,n){e=e||10;var a=this._extent,o=a[1]-a[0];this._approxInterval=o/e,i!=null&&this._approxInterval<i&&(this._approxInterval=i),n!=null&&this._approxInterval>n&&(this._approxInterval=n);var s=Sa.length,u=Math.min(GT(Sa,this._approxInterval,0,s),s-1);this._interval=Sa[u][1],this._minLevelUnit=Sa[Math.max(u-1,0)][0]},t.prototype.parse=function(e){return vt(e)?e:+Ee(e)},t.prototype.contain=function(e){return Eo(this.parse(e),this._extent)},t.prototype.normalize=function(e){return Ro(this.parse(e),this._extent)},t.prototype.scale=function(e){return ko(e,this._extent)},t.type="time",t}(Vn),Sa=[["second",Pl],["minute",Il],["hour",hn],["quarter-day",hn*6],["half-day",hn*12],["day",te*1.2],["half-week",te*3.5],["week",te*7],["month",te*31],["quarter",te*95],["half-year",Eh/2],["year",Eh]];function WT(r,t,e,i){var n=Ee(t),a=Ee(e),o=function(d){return kh(n,d,i)===kh(a,d,i)},s=function(){return o("year")},u=function(){return s()&&o("month")},l=function(){return u()&&o("day")},f=function(){return l()&&o("hour")},h=function(){return f()&&o("minute")},c=function(){return h()&&o("second")},v=function(){return c()&&o("millisecond")};switch(r){case"year":return s();case"month":return u();case"day":return l();case"hour":return f();case"minute":return h();case"second":return c();case"millisecond":return v()}}function UT(r,t){return r/=te,r>16?16:r>7.5?7:r>3.5?4:r>1.5?2:1}function YT(r){var t=30*te;return r/=t,r>6?6:r>3?3:r>2?2:1}function $T(r){return r/=hn,r>12?12:r>6?6:r>3.5?4:r>2?2:1}function Uv(r,t){return r/=t?Il:Pl,r>30?30:r>20?20:r>15?15:r>10?10:r>5?5:r>2?2:1}function XT(r){return dd(r,!0)}function ZT(r,t,e){var i=new Date(r);switch(_i(t)){case"year":case"month":i[Mp(e)](0);case"day":i[Dp(e)](1);case"hour":i[Ap(e)](0);case"minute":i[Lp(e)](0);case"second":i[Pp(e)](0),i[Ip(e)](0)}return i.getTime()}function qT(r,t,e,i){var n=1e4,a=Tp,o=0;function s(C,A,L,I,P,E,R){for(var V=new Date(A),N=A,F=V[I]();N<L&&N<=i[1];)R.push({value:N}),F+=C,V[P](F),N=V.getTime();R.push({value:N,notAdd:!0})}function u(C,A,L){var I=[],P=!A.length;if(!WT(_i(C),i[0],i[1],e)){P&&(A=[{value:ZT(new Date(i[0]),C,e)},{value:i[1]}]);for(var E=0;E<A.length-1;E++){var R=A[E].value,V=A[E+1].value;if(R!==V){var N=void 0,F=void 0,Y=void 0,et=!1;switch(C){case"year":N=Math.max(1,Math.round(t/te/365)),F=El(e),Y=QS(e);break;case"half-year":case"quarter":case"month":N=YT(t),F=Si(e),Y=Mp(e);break;case"week":case"half-week":case"day":N=UT(t),F=To(e),Y=Dp(e),et=!0;break;case"half-day":case"quarter-day":case"hour":N=$T(t),F=bn(e),Y=Ap(e);break;case"minute":N=Uv(t,!0),F=Co(e),Y=Lp(e);break;case"second":N=Uv(t,!1),F=Mo(e),Y=Pp(e);break;case"millisecond":N=XT(t),F=Do(e),Y=Ip(e);break}s(N,R,V,F,Y,et,I),C==="year"&&L.length>1&&E===0&&L.unshift({value:L[0].value-N})}}for(var E=0;E<I.length;E++)L.push(I[E]);return I}}for(var l=[],f=[],h=0,c=0,v=0;v<a.length&&o++<n;++v){var d=_i(a[v]);if(ZS(a[v])){u(a[v],l[l.length-1]||[],f);var y=a[v+1]?_i(a[v+1]):null;if(d!==y){if(f.length){c=h,f.sort(function(C,A){return C.value-A.value});for(var p=[],g=0;g<f.length;++g){var m=f[g].value;(g===0||f[g-1].value!==m)&&(p.push(f[g]),m>=i[0]&&m<=i[1]&&h++)}var _=(i[1]-i[0])/t;if(h>_*1.5&&c>_/1.5||(l.push(p),h>_||r===a[v]))break}f=[]}}}for(var S=Tt(G(l,function(C){return Tt(C,function(A){return A.value>=i[0]&&A.value<=i[1]&&!A.notAdd})}),function(C){return C.length>0}),b=[],w=S.length-1,v=0;v<S.length;++v)for(var x=S[v],D=0;D<x.length;++D)b.push({value:x[D].value,level:w-v});b.sort(function(C,A){return C.value-A.value});for(var T=[],v=0;v<b.length;++v)(v===0||b[v].value!==b[v-1].value)&&T.push(b[v]);return T}Oe.registerClass(ey);const KT=ey;var Yv=Oe.prototype,pn=Vn.prototype,QT=pt,JT=Math.floor,jT=Math.ceil,wa=Math.pow,se=Math.log,Kl=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="log",e.base=10,e._originalScale=new Vn,e._interval=0,e}return t.prototype.getTicks=function(e){var i=this._originalScale,n=this._extent,a=i.getExtent(),o=pn.getTicks.call(this,e);return G(o,function(s){var u=s.value,l=pt(wa(this.base,u));return l=u===n[0]&&this._fixMin?ba(l,a[0]):l,l=u===n[1]&&this._fixMax?ba(l,a[1]):l,{value:l}},this)},t.prototype.setExtent=function(e,i){var n=se(this.base);e=se(Math.max(0,e))/n,i=se(Math.max(0,i))/n,pn.setExtent.call(this,e,i)},t.prototype.getExtent=function(){var e=this.base,i=Yv.getExtent.call(this);i[0]=wa(e,i[0]),i[1]=wa(e,i[1]);var n=this._originalScale,a=n.getExtent();return this._fixMin&&(i[0]=ba(i[0],a[0])),this._fixMax&&(i[1]=ba(i[1],a[1])),i},t.prototype.unionExtent=function(e){this._originalScale.unionExtent(e);var i=this.base;e[0]=se(e[0])/se(i),e[1]=se(e[1])/se(i),Yv.unionExtent.call(this,e)},t.prototype.unionExtentFromData=function(e,i){this.unionExtent(e.getApproximateExtent(i))},t.prototype.calcNiceTicks=function(e){e=e||10;var i=this._extent,n=i[1]-i[0];if(!(n===1/0||n<=0)){var a=U0(n),o=e/n*a;for(o<=.5&&(a*=10);!isNaN(a)&&Math.abs(a)<1&&Math.abs(a)>0;)a*=10;var s=[pt(jT(i[0]/a)*a),pt(JT(i[1]/a)*a)];this._interval=a,this._niceExtent=s}},t.prototype.calcNiceExtent=function(e){pn.calcNiceExtent.call(this,e),this._fixMin=e.fixMin,this._fixMax=e.fixMax},t.prototype.parse=function(e){return e},t.prototype.contain=function(e){return e=se(e)/se(this.base),Eo(e,this._extent)},t.prototype.normalize=function(e){return e=se(e)/se(this.base),Ro(e,this._extent)},t.prototype.scale=function(e){return e=ko(e,this._extent),wa(this.base,e)},t.type="log",t}(Oe),ry=Kl.prototype;ry.getMinorTicks=pn.getMinorTicks;ry.getLabel=pn.getLabel;function ba(r,t){return QT(r,Ae(t))}Oe.registerClass(Kl);const tC=Kl;var eC=function(){function r(t,e,i){this._prepareParams(t,e,i)}return r.prototype._prepareParams=function(t,e,i){i[1]<i[0]&&(i=[NaN,NaN]),this._dataMin=i[0],this._dataMax=i[1];var n=this._isOrdinal=t.type==="ordinal";this._needCrossZero=t.type==="interval"&&e.getNeedCrossZero&&e.getNeedCrossZero();var a=e.get("min",!0);a==null&&(a=e.get("startValue",!0));var o=this._modelMinRaw=a;U(o)?this._modelMinNum=xa(t,o({min:i[0],max:i[1]})):o!=="dataMin"&&(this._modelMinNum=xa(t,o));var s=this._modelMaxRaw=e.get("max",!0);if(U(s)?this._modelMaxNum=xa(t,s({min:i[0],max:i[1]})):s!=="dataMax"&&(this._modelMaxNum=xa(t,s)),n)this._axisDataLen=e.getCategories().length;else{var u=e.get("boundaryGap"),l=B(u)?u:[u||0,u||0];typeof l[0]=="boolean"||typeof l[1]=="boolean"?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[Br(l[0],1),Br(l[1],1)]}},r.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,i=this._dataMax,n=this._axisDataLen,a=this._boundaryGapInner,o=t?null:i-e||Math.abs(e),s=this._modelMinRaw==="dataMin"?e:this._modelMinNum,u=this._modelMaxRaw==="dataMax"?i:this._modelMaxNum,l=s!=null,f=u!=null;s==null&&(s=t?n?0:NaN:e-a[0]*o),u==null&&(u=t?n?n-1:NaN:i+a[1]*o),(s==null||!isFinite(s))&&(s=NaN),(u==null||!isFinite(u))&&(u=NaN);var h=Wa(s)||Wa(u)||t&&!n;this._needCrossZero&&(s>0&&u>0&&!l&&(s=0),s<0&&u<0&&!f&&(u=0));var c=this._determinedMin,v=this._determinedMax;return c!=null&&(s=c,l=!0),v!=null&&(u=v,f=!0),{min:s,max:u,minFixed:l,maxFixed:f,isBlank:h}},r.prototype.modifyDataMinMax=function(t,e){this[iC[t]]=e},r.prototype.setDeterminedMinMax=function(t,e){var i=rC[t];this[i]=e},r.prototype.freeze=function(){this.frozen=!0},r}(),rC={min:"_determinedMin",max:"_determinedMax"},iC={min:"_dataMin",max:"_dataMax"};function nC(r,t,e){var i=r.rawExtentInfo;return i||(i=new eC(r,t,e),r.rawExtentInfo=i,i)}function xa(r,t){return t==null?null:Wa(t)?NaN:r.parse(t)}function iy(r,t){var e=r.type,i=nC(r,t,r.getExtent()).calculate();r.setBlank(i.isBlank);var n=i.min,a=i.max,o=t.ecModel;if(o&&e==="time"){var s=OT("bar",o),u=!1;if(M(s,function(h){u=u||h.getBaseAxis()===t.axis}),u){var l=NT(s),f=aC(n,a,t,l);n=f.min,a=f.max}}return{extent:[n,a],fixMin:i.minFixed,fixMax:i.maxFixed}}function aC(r,t,e,i){var n=e.axis.getExtent(),a=Math.abs(n[1]-n[0]),o=zT(i,e.axis);if(o===void 0)return{min:r,max:t};var s=1/0;M(o,function(v){s=Math.min(v.offset,s)});var u=-1/0;M(o,function(v){u=Math.max(v.offset+v.width,u)}),s=Math.abs(s),u=Math.abs(u);var l=s+u,f=t-r,h=1-(s+u)/a,c=f/h-f;return t+=c*(u/l),r-=c*(s/l),{min:r,max:t}}function $v(r,t){var e=t,i=iy(r,e),n=i.extent,a=e.get("splitNumber");r instanceof tC&&(r.base=e.get("logBase"));var o=r.type,s=e.get("interval"),u=o==="interval"||o==="time";r.setExtent(n[0],n[1]),r.calcNiceExtent({splitNumber:a,fixMin:i.fixMin,fixMax:i.fixMax,minInterval:u?e.get("minInterval"):null,maxInterval:u?e.get("maxInterval"):null}),s!=null&&r.setInterval&&r.setInterval(s)}function oC(r,t){if(t=t||r.get("type"),t)switch(t){case"category":return new Kg({ordinalMeta:r.getOrdinalMeta?r.getOrdinalMeta():r.getCategories(),extent:[1/0,-1/0]});case"time":return new KT({locale:r.ecModel.getLocaleModel(),useUTC:r.ecModel.get("useUTC")});default:return new(Oe.getClass(t)||Vn)}}function sC(r){var t=r.scale.getExtent(),e=t[0],i=t[1];return!(e>0&&i>0||e<0&&i<0)}function Li(r){var t=r.getLabelModel().get("formatter"),e=r.type==="category"?r.scale.getExtent()[0]:null;return r.scale.type==="time"?function(i){return function(n,a){return r.scale.getFormattedLabel(n,a,i)}}(t):z(t)?function(i){return function(n){var a=r.scale.getLabel(n),o=i.replace("{value}",a??"");return o}}(t):U(t)?function(i){return function(n,a){return e!=null&&(a=n.value-e),i(Ql(r,n),a,n.level!=null?{level:n.level}:null)}}(t):function(i){return r.scale.getLabel(i)}}function Ql(r,t){return r.type==="category"?r.scale.getLabel(t):t.value}function uC(r){var t=r.model,e=r.scale;if(!(!t.get(["axisLabel","show"])||e.isBlank())){var i,n,a=e.getExtent();e instanceof Kg?n=e.count():(i=e.getTicks(),n=i.length);var o=r.getLabelModel(),s=Li(r),u,l=1;n>40&&(l=Math.ceil(n/40));for(var f=0;f<n;f+=l){var h=i?i[f]:{value:a[0]+f},c=s(h,f),v=o.getTextRect(c),d=lC(v,o.get("rotate")||0);u?u.union(d):u=d}return u}}function lC(r,t){var e=t*Math.PI/180,i=r.width,n=r.height,a=i*Math.abs(Math.cos(e))+Math.abs(n*Math.sin(e)),o=i*Math.abs(Math.sin(e))+Math.abs(n*Math.cos(e)),s=new J(r.x,r.y,a,o);return s}function Jl(r){var t=r.get("interval");return t??"auto"}function ny(r){return r.type==="category"&&Jl(r.getLabelModel())===0}function fC(r,t){var e={};return M(r.mapDimensionsAll(t),function(i){e[bT(r,i)]=!0}),lt(e)}var hC=function(){function r(){}return r.prototype.getNeedCrossZero=function(){var t=this.option;return!t.scale},r.prototype.getCoordSysModel=function(){},r}(),Xv=[],vC={registerPreprocessor:zg,registerProcessor:Hg,registerPostInit:$x,registerPostUpdate:Xx,registerUpdateLifecycle:Zl,registerAction:Ai,registerCoordinateSystem:Zx,registerLayout:qx,registerVisual:Yr,registerTransform:Qx,registerLoading:Vg,registerMap:Kx,registerImpl:Mx,PRIORITY:zx,ComponentModel:ft,ComponentView:er,SeriesModel:Dn,ChartView:tr,registerComponentModel:function(r){ft.registerClass(r)},registerComponentView:function(r){er.registerClass(r)},registerSeriesModel:function(r){Dn.registerClass(r)},registerChartView:function(r){tr.registerClass(r)},registerSubTypeDefaulter:function(r,t){ft.registerSubTypeDefaulter(r,t)},registerPainter:function(r,t){F0(r,t)}};function En(r){if(B(r)){M(r,function(t){En(t)});return}nt(Xv,r)>=0||(Xv.push(r),U(r)&&(r={install:r}),r.install(vC))}var Rn=yt();function ay(r,t){var e=G(t,function(i){return r.scale.parse(i)});return r.type==="time"&&e.length>0&&(e.sort(),e.unshift(e[0]),e.push(e[e.length-1])),e}function cC(r){var t=r.getLabelModel().get("customValues");if(t){var e=Li(r),i=r.scale.getExtent(),n=ay(r,t),a=Tt(n,function(o){return o>=i[0]&&o<=i[1]});return{labels:G(a,function(o){var s={value:o};return{formattedLabel:e(s),rawLabel:r.scale.getLabel(s),tickValue:o}})}}return r.type==="category"?pC(r):yC(r)}function dC(r,t){var e=r.getTickModel().get("customValues");if(e){var i=r.scale.getExtent(),n=ay(r,e);return{ticks:Tt(n,function(a){return a>=i[0]&&a<=i[1]})}}return r.type==="category"?gC(r,t):{ticks:G(r.scale.getTicks(),function(a){return a.value})}}function pC(r){var t=r.getLabelModel(),e=oy(r,t);return!t.get("show")||r.scale.isBlank()?{labels:[],labelCategoryInterval:e.labelCategoryInterval}:e}function oy(r,t){var e=sy(r,"labels"),i=Jl(t),n=uy(e,i);if(n)return n;var a,o;return U(i)?a=hy(r,i):(o=i==="auto"?mC(r):i,a=fy(r,o)),ly(e,i,{labels:a,labelCategoryInterval:o})}function gC(r,t){var e=sy(r,"ticks"),i=Jl(t),n=uy(e,i);if(n)return n;var a,o;if((!t.get("show")||r.scale.isBlank())&&(a=[]),U(i))a=hy(r,i,!0);else if(i==="auto"){var s=oy(r,r.getLabelModel());o=s.labelCategoryInterval,a=G(s.labels,function(u){return u.tickValue})}else o=i,a=fy(r,o,!0);return ly(e,i,{ticks:a,tickCategoryInterval:o})}function yC(r){var t=r.scale.getTicks(),e=Li(r);return{labels:G(t,function(i,n){return{level:i.level,formattedLabel:e(i,n),rawLabel:r.scale.getLabel(i),tickValue:i.value}})}}function sy(r,t){return Rn(r)[t]||(Rn(r)[t]=[])}function uy(r,t){for(var e=0;e<r.length;e++)if(r[e].key===t)return r[e].value}function ly(r,t,e){return r.push({key:t,value:e}),e}function mC(r){var t=Rn(r).autoInterval;return t??(Rn(r).autoInterval=r.calculateCategoryInterval())}function _C(r){var t=SC(r),e=Li(r),i=(t.axisRotate-t.labelRotate)/180*Math.PI,n=r.scale,a=n.getExtent(),o=n.count();if(a[1]-a[0]<1)return 0;var s=1;o>40&&(s=Math.max(1,Math.floor(o/40)));for(var u=a[0],l=r.dataToCoord(u+1)-r.dataToCoord(u),f=Math.abs(l*Math.cos(i)),h=Math.abs(l*Math.sin(i)),c=0,v=0;u<=a[1];u+=s){var d=0,y=0,p=ll(e({value:u}),t.font,"center","top");d=p.width*1.3,y=p.height*1.3,c=Math.max(c,d,7),v=Math.max(v,y,7)}var g=c/f,m=v/h;isNaN(g)&&(g=1/0),isNaN(m)&&(m=1/0);var _=Math.max(0,Math.floor(Math.min(g,m))),S=Rn(r.model),b=r.getExtent(),w=S.lastAutoInterval,x=S.lastTickCount;return w!=null&&x!=null&&Math.abs(w-_)<=1&&Math.abs(x-o)<=1&&w>_&&S.axisExtent0===b[0]&&S.axisExtent1===b[1]?_=w:(S.lastTickCount=o,S.lastAutoInterval=_,S.axisExtent0=b[0],S.axisExtent1=b[1]),_}function SC(r){var t=r.getLabelModel();return{axisRotate:r.getRotate?r.getRotate():r.isHorizontal&&!r.isHorizontal()?90:0,labelRotate:t.get("rotate")||0,font:t.getFont()}}function fy(r,t,e){var i=Li(r),n=r.scale,a=n.getExtent(),o=r.getLabelModel(),s=[],u=Math.max((t||0)+1,1),l=a[0],f=n.count();l!==0&&u>1&&f/u>2&&(l=Math.round(Math.ceil(l/u)*u));var h=ny(r),c=o.get("showMinLabel")||h,v=o.get("showMaxLabel")||h;c&&l!==a[0]&&y(a[0]);for(var d=l;d<=a[1];d+=u)y(d);v&&d-u!==a[1]&&y(a[1]);function y(p){var g={value:p};s.push(e?p:{formattedLabel:i(g),rawLabel:n.getLabel(g),tickValue:p})}return s}function hy(r,t,e){var i=r.scale,n=Li(r),a=[];return M(i.getTicks(),function(o){var s=i.getLabel(o),u=o.value;t(o.value,s)&&a.push(e?u:{formattedLabel:n(o),rawLabel:s,tickValue:u})}),a}var Zv=[0,1],wC=function(){function r(t,e,i){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=i||[0,0]}return r.prototype.contain=function(t){var e=this._extent,i=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]);return t>=i&&t<=n},r.prototype.containData=function(t){return this.scale.contain(t)},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.getPixelPrecision=function(t){return V0(t||this.scale.getExtent(),this._extent)},r.prototype.setExtent=function(t,e){var i=this._extent;i[0]=t,i[1]=e},r.prototype.dataToCoord=function(t,e){var i=this._extent,n=this.scale;return t=n.normalize(t),this.onBand&&n.type==="ordinal"&&(i=i.slice(),qv(i,n.count())),zf(t,Zv,i,e)},r.prototype.coordToData=function(t,e){var i=this._extent,n=this.scale;this.onBand&&n.type==="ordinal"&&(i=i.slice(),qv(i,n.count()));var a=zf(t,i,Zv,e);return this.scale.scale(a)},r.prototype.pointToData=function(t,e){},r.prototype.getTicksCoords=function(t){t=t||{};var e=t.tickModel||this.getTickModel(),i=dC(this,e),n=i.ticks,a=G(n,function(s){return{coord:this.dataToCoord(this.scale.type==="ordinal"?this.scale.getRawOrdinalNumber(s):s),tickValue:s}},this),o=e.get("alignWithLabel");return bC(this,a,o,t.clamp),a},r.prototype.getMinorTicksCoords=function(){if(this.scale.type==="ordinal")return[];var t=this.model.getModel("minorTick"),e=t.get("splitNumber");e>0&&e<100||(e=5);var i=this.scale.getMinorTicks(e),n=G(i,function(a){return G(a,function(o){return{coord:this.dataToCoord(o),tickValue:o}},this)},this);return n},r.prototype.getViewLabels=function(){return cC(this).labels},r.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},r.prototype.getTickModel=function(){return this.model.getModel("axisTick")},r.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),i=e[1]-e[0]+(this.onBand?1:0);i===0&&(i=1);var n=Math.abs(t[1]-t[0]);return Math.abs(n)/i},r.prototype.calculateCategoryInterval=function(){return _C(this)},r}();function qv(r,t){var e=r[1]-r[0],i=t,n=e/i/2;r[0]+=n,r[1]-=n}function bC(r,t,e,i){var n=t.length;if(!r.onBand||e||!n)return;var a=r.getExtent(),o,s;if(n===1)t[0].coord=a[0],o=t[1]={coord:a[1],tickValue:t[0].tickValue};else{var u=t[n-1].tickValue-t[0].tickValue,l=(t[n-1].coord-t[0].coord)/u;M(t,function(v){v.coord-=l/2});var f=r.scale.getExtent();s=1+f[1]-t[n-1].tickValue,o={coord:t[n-1].coord+l*s,tickValue:f[1]+1},t.push(o)}var h=a[0]>a[1];c(t[0].coord,a[0])&&(i?t[0].coord=a[0]:t.shift()),i&&c(a[0],t[0].coord)&&t.unshift({coord:a[0]}),c(a[1],o.coord)&&(i?o.coord=a[1]:t.pop()),i&&c(o.coord,a[1])&&t.push({coord:a[1]});function c(v,d){return v=pt(v),d=pt(d),h?v>d:v<d}}const xC=wC;function TC(r){for(var t=[],e=0;e<r.length;e++){var i=r[e];if(!i.defaultAttr.ignore){var n=i.label,a=n.getComputedTransform(),o=n.getBoundingRect(),s=!a||a[1]<1e-5&&a[2]<1e-5,u=n.style.margin||0,l=o.clone();l.applyTransform(a),l.x-=u/2,l.y-=u/2,l.width+=u,l.height+=u;var f=s?new to(o,a):null;t.push({label:n,labelLine:i.labelLine,rect:l,localRect:o,obb:f,priority:i.priority,defaultAttr:i.defaultAttr,layoutOption:i.computedLayoutOption,axisAligned:s,transform:a})}}return t}function CC(r){var t=[];r.sort(function(y,p){return p.priority-y.priority});var e=new J(0,0,0,0);function i(y){if(!y.ignore){var p=y.ensureState("emphasis");p.ignore==null&&(p.ignore=!1)}y.ignore=!0}for(var n=0;n<r.length;n++){var a=r[n],o=a.axisAligned,s=a.localRect,u=a.transform,l=a.label,f=a.labelLine;e.copy(a.rect),e.width-=.1,e.height-=.1,e.x+=.05,e.y+=.05;for(var h=a.obb,c=!1,v=0;v<t.length;v++){var d=t[v];if(e.intersect(d.rect)){if(o&&d.axisAligned){c=!0;break}if(d.obb||(d.obb=new to(d.localRect,d.transform)),h||(h=new to(s,u)),h.intersect(d.obb)){c=!0;break}}}c?(i(l),f&&i(f)):(l.attr("ignore",a.defaultAttr.ignore),f&&f.attr("ignore",a.defaultAttr.labelGuideIgnore),t.push(a))}}function Kv(r,t,e){var i=xi.createCanvas(),n=t.getWidth(),a=t.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=n+"px",o.height=a+"px",i.setAttribute("data-zr-dom-id",r)),i.width=n*e,i.height=a*e,i}var MC=function(r){O(t,r);function t(e,i,n){var a=r.call(this)||this;a.motionBlur=!1,a.lastFrameAlpha=.7,a.dpr=1,a.virtual=!1,a.config={},a.incremental=!1,a.zlevel=0,a.maxRepaintRectCount=5,a.__dirty=!0,a.__firstTimePaint=!0,a.__used=!1,a.__drawIndex=0,a.__startIndex=0,a.__endIndex=0,a.__prevStartIndex=null,a.__prevEndIndex=null;var o;n=n||qa,typeof e=="string"?o=Kv(e,i,n):H(e)&&(o=e,e=o.id),a.id=e,a.dom=o;var s=o.style;return s&&(Gc(o),o.onselectstart=function(){return!1},s.padding="0",s.margin="0",s.borderWidth="0"),a.painter=i,a.dpr=n,a}return t.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},t.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},t.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},t.prototype.setUnpainted=function(){this.__firstTimePaint=!0},t.prototype.createBackBuffer=function(){var e=this.dpr;this.domBack=Kv("back-"+this.id,this.painter,e),this.ctxBack=this.domBack.getContext("2d"),e!==1&&this.ctxBack.scale(e,e)},t.prototype.createRepaintRects=function(e,i,n,a){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var o=[],s=this.maxRepaintRectCount,u=!1,l=new J(0,0,0,0);function f(m){if(!(!m.isFinite()||m.isZero()))if(o.length===0){var _=new J(0,0,0,0);_.copy(m),o.push(_)}else{for(var S=!1,b=1/0,w=0,x=0;x<o.length;++x){var D=o[x];if(D.intersect(m)){var T=new J(0,0,0,0);T.copy(D),T.union(m),o[x]=T,S=!0;break}else if(u){l.copy(m),l.union(D);var C=m.width*m.height,A=D.width*D.height,L=l.width*l.height,I=L-C-A;I<b&&(b=I,w=x)}}if(u&&(o[w].union(m),S=!0),!S){var _=new J(0,0,0,0);_.copy(m),o.push(_)}u||(u=o.length>=s)}}for(var h=this.__startIndex;h<this.__endIndex;++h){var c=e[h];if(c){var v=c.shouldBePainted(n,a,!0,!0),d=c.__isRendered&&(c.__dirty&Gt||!v)?c.getPrevPaintRect():null;d&&f(d);var y=v&&(c.__dirty&Gt||!c.__isRendered)?c.getPaintRect():null;y&&f(y)}}for(var h=this.__prevStartIndex;h<this.__prevEndIndex;++h){var c=i[h],v=c&&c.shouldBePainted(n,a,!0,!0);if(c&&(!v||!c.__zr)&&c.__isRendered){var d=c.getPrevPaintRect();d&&f(d)}}var p;do{p=!1;for(var h=0;h<o.length;){if(o[h].isZero()){o.splice(h,1);continue}for(var g=h+1;g<o.length;)o[h].intersect(o[g])?(p=!0,o[h].union(o[g]),o.splice(g,1)):g++;h++}}while(p);return this._paintRects=o,o},t.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},t.prototype.resize=function(e,i){var n=this.dpr,a=this.dom,o=a.style,s=this.domBack;o&&(o.width=e+"px",o.height=i+"px"),a.width=e*n,a.height=i*n,s&&(s.width=e*n,s.height=i*n,n!==1&&this.ctxBack.scale(n,n))},t.prototype.clear=function(e,i,n){var a=this.dom,o=this.ctx,s=a.width,u=a.height;i=i||this.clearColor;var l=this.motionBlur&&!e,f=this.lastFrameAlpha,h=this.dpr,c=this;l&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(a,0,0,s/h,u/h));var v=this.domBack;function d(y,p,g,m){if(o.clearRect(y,p,g,m),i&&i!=="transparent"){var _=void 0;if(vo(i)){var S=i.global||i.__width===g&&i.__height===m;_=S&&i.__canvasGradient||Nu(o,i,{x:0,y:0,width:g,height:m}),i.__canvasGradient=_,i.__width=g,i.__height=m}else sm(i)&&(i.scaleX=i.scaleX||h,i.scaleY=i.scaleY||h,_=Fu(o,i,{dirty:function(){c.setUnpainted(),c.painter.refresh()}}));o.save(),o.fillStyle=_||i,o.fillRect(y,p,g,m),o.restore()}l&&(o.save(),o.globalAlpha=f,o.drawImage(v,y,p,g,m),o.restore())}!n||l?d(0,0,s,u):n.length&&M(n,function(y){d(y.x*h,y.y*h,y.width*h,y.height*h)})},t}(xe);const Us=MC;var Qv=1e5,Cr=314159,Ta=.01,DC=.001;function AC(r){return r?r.__builtin__?!0:!(typeof r.resize!="function"||typeof r.refresh!="function"):!1}function LC(r,t){var e=document.createElement("div");return e.style.cssText=["position:relative","width:"+r+"px","height:"+t+"px","padding:0","margin:0","border-width:0"].join(";")+";",e}var PC=function(){function r(t,e,i,n){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var a=!t.nodeName||t.nodeName.toUpperCase()==="CANVAS";this._opts=i=k({},i||{}),this.dpr=i.devicePixelRatio||qa,this._singleCanvas=a,this.root=t;var o=t.style;o&&(Gc(t),t.innerHTML=""),this.storage=e;var s=this._zlevelList;this._prevDisplayList=[];var u=this._layers;if(a){var f=t,h=f.width,c=f.height;i.width!=null&&(h=i.width),i.height!=null&&(c=i.height),this.dpr=i.devicePixelRatio||1,f.width=h*this.dpr,f.height=c*this.dpr,this._width=h,this._height=c;var v=new Us(f,this,this.dpr);v.__builtin__=!0,v.initContext(),u[Cr]=v,v.zlevel=Cr,s.push(Cr),this._domRoot=t}else{this._width=ga(t,0,i),this._height=ga(t,1,i);var l=this._domRoot=LC(this._width,this._height);t.appendChild(l)}}return r.prototype.getType=function(){return"canvas"},r.prototype.isSingleCanvas=function(){return this._singleCanvas},r.prototype.getViewportRoot=function(){return this._domRoot},r.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},r.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),i=this._prevDisplayList,n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,i,t,this._redrawId);for(var a=0;a<n.length;a++){var o=n[a],s=this._layers[o];if(!s.__builtin__&&s.refresh){var u=a===0?this._backgroundColor:null;s.refresh(u)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},r.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},r.prototype._paintHoverList=function(t){var e=t.length,i=this._hoverlayer;if(i&&i.clear(),!!e){for(var n={inHover:!0,viewWidth:this._width,viewHeight:this._height},a,o=0;o<e;o++){var s=t[o];s.__inHover&&(i||(i=this._hoverlayer=this.getLayer(Qv)),a||(a=i.ctx,a.save()),Pr(a,s,n,o===e-1))}a&&a.restore()}},r.prototype.getHoverLayer=function(){return this.getLayer(Qv)},r.prototype.paintOne=function(t,e){bg(t,e)},r.prototype._paintList=function(t,e,i,n){if(this._redrawId===n){i=i||!1,this._updateLayerStatus(t);var a=this._doPaintList(t,e,i),o=a.finished,s=a.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),s&&this._paintHoverList(t),o)this.eachLayer(function(l){l.afterBrush&&l.afterBrush()});else{var u=this;ou(function(){u._paintList(t,e,i,n)})}}},r.prototype._compositeManually=function(){var t=this.getLayer(Cr).ctx,e=this._domRoot.width,i=this._domRoot.height;t.clearRect(0,0,e,i),this.eachBuiltinLayer(function(n){n.virtual&&t.drawImage(n.dom,0,0,e,i)})},r.prototype._doPaintList=function(t,e,i){for(var n=this,a=[],o=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var u=this._zlevelList[s],l=this._layers[u];l.__builtin__&&l!==this._hoverlayer&&(l.__dirty||i)&&a.push(l)}for(var f=!0,h=!1,c=function(y){var p=a[y],g=p.ctx,m=o&&p.createRepaintRects(t,e,v._width,v._height),_=i?p.__startIndex:p.__drawIndex,S=!i&&p.incremental&&Date.now,b=S&&Date.now(),w=p.zlevel===v._zlevelList[0]?v._backgroundColor:null;if(p.__startIndex===p.__endIndex)p.clear(!1,w,m);else if(_===p.__startIndex){var x=t[_];(!x.incremental||!x.notClear||i)&&p.clear(!1,w,m)}_===-1&&(console.error("For some unknown reason. drawIndex is -1"),_=p.__startIndex);var D,T=function(I){var P={inHover:!1,allClipped:!1,prevEl:null,viewWidth:n._width,viewHeight:n._height};for(D=_;D<p.__endIndex;D++){var E=t[D];if(E.__inHover&&(h=!0),n._doPaintEl(E,p,o,I,P,D===p.__endIndex-1),S){var R=Date.now()-b;if(R>15)break}}P.prevElClipPaths&&g.restore()};if(m)if(m.length===0)D=p.__endIndex;else for(var C=v.dpr,A=0;A<m.length;++A){var L=m[A];g.save(),g.beginPath(),g.rect(L.x*C,L.y*C,L.width*C,L.height*C),g.clip(),T(L),g.restore()}else g.save(),T(),g.restore();p.__drawIndex=D,p.__drawIndex<p.__endIndex&&(f=!1)},v=this,d=0;d<a.length;d++)c(d);return $.wxa&&M(this._layers,function(y){y&&y.ctx&&y.ctx.draw&&y.ctx.draw()}),{finished:f,needsRefreshHover:h}},r.prototype._doPaintEl=function(t,e,i,n,a,o){var s=e.ctx;if(i){var u=t.getPaintRect();(!n||u&&u.intersect(n))&&(Pr(s,t,a,o),t.setPrevPaintRect(u))}else Pr(s,t,a,o)},r.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=Cr);var i=this._layers[t];return i||(i=new Us("zr_"+t,this,this.dpr),i.zlevel=t,i.__builtin__=!0,this._layerConfig[t]?at(i,this._layerConfig[t],!0):this._layerConfig[t-Ta]&&at(i,this._layerConfig[t-Ta],!0),e&&(i.virtual=e),this.insertLayer(t,i),i.initContext()),i},r.prototype.insertLayer=function(t,e){var i=this._layers,n=this._zlevelList,a=n.length,o=this._domRoot,s=null,u=-1;if(!i[t]&&AC(e)){if(a>0&&t>n[0]){for(u=0;u<a-1&&!(n[u]<t&&n[u+1]>t);u++);s=i[n[u]]}if(n.splice(u+1,0,t),i[t]=e,!e.virtual)if(s){var l=s.dom;l.nextSibling?o.insertBefore(e.dom,l.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.painter||(e.painter=this)}},r.prototype.eachLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n];t.call(e,this._layers[a],a)}},r.prototype.eachBuiltinLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],o=this._layers[a];o.__builtin__&&t.call(e,o,a)}},r.prototype.eachOtherLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],o=this._layers[a];o.__builtin__||t.call(e,o,a)}},r.prototype.getLayers=function(){return this._layers},r.prototype._updateLayerStatus=function(t){this.eachBuiltinLayer(function(h,c){h.__dirty=h.__used=!1});function e(h){a&&(a.__endIndex!==h&&(a.__dirty=!0),a.__endIndex=h)}if(this._singleCanvas)for(var i=1;i<t.length;i++){var n=t[i];if(n.zlevel!==t[i-1].zlevel||n.incremental){this._needsManuallyCompositing=!0;break}}var a=null,o=0,s,u;for(u=0;u<t.length;u++){var n=t[u],l=n.zlevel,f=void 0;s!==l&&(s=l,o=0),n.incremental?(f=this.getLayer(l+DC,this._needsManuallyCompositing),f.incremental=!0,o=1):f=this.getLayer(l+(o>0?Ta:0),this._needsManuallyCompositing),f.__builtin__||tl("ZLevel "+l+" has been used by unkown layer "+f.id),f!==a&&(f.__used=!0,f.__startIndex!==u&&(f.__dirty=!0),f.__startIndex=u,f.incremental?f.__drawIndex=-1:f.__drawIndex=u,e(u),a=f),n.__dirty&Gt&&!n.__inHover&&(f.__dirty=!0,f.incremental&&f.__drawIndex<0&&(f.__drawIndex=u))}e(u),this.eachBuiltinLayer(function(h,c){!h.__used&&h.getElementCount()>0&&(h.__dirty=!0,h.__startIndex=h.__endIndex=h.__drawIndex=0),h.__dirty&&h.__drawIndex<0&&(h.__drawIndex=h.__startIndex)})},r.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},r.prototype._clearLayer=function(t){t.clear()},r.prototype.setBackgroundColor=function(t){this._backgroundColor=t,M(this._layers,function(e){e.setUnpainted()})},r.prototype.configLayer=function(t,e){if(e){var i=this._layerConfig;i[t]?at(i[t],e,!0):i[t]=e;for(var n=0;n<this._zlevelList.length;n++){var a=this._zlevelList[n];if(a===t||a===t+Ta){var o=this._layers[a];at(o,i[t],!0)}}}},r.prototype.delLayer=function(t){var e=this._layers,i=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],i.splice(nt(i,t),1))},r.prototype.resize=function(t,e){if(this._domRoot.style){var i=this._domRoot;i.style.display="none";var n=this._opts,a=this.root;if(t!=null&&(n.width=t),e!=null&&(n.height=e),t=ga(a,0,n),e=ga(a,1,n),i.style.display="",this._width!==t||e!==this._height){i.style.width=t+"px",i.style.height=e+"px";for(var o in this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(t==null||e==null)return;this._width=t,this._height=e,this.getLayer(Cr).resize(t,e)}return this},r.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},r.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},r.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[Cr].dom;var e=new Us("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var i=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,a=e.dom.height;this.eachLayer(function(h){h.__builtin__?i.drawImage(h.dom,0,0,n,a):h.renderToCanvas&&(i.save(),h.renderToCanvas(i),i.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},s=this.storage.getDisplayList(!0),u=0,l=s.length;u<l;u++){var f=s[u];Pr(i,f,o,u===l-1)}return e.dom},r.prototype.getWidth=function(){return this._width},r.prototype.getHeight=function(){return this._height},r}();const IC=PC;function EC(r){r.registerPainter("canvas",IC)}var RC=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.hasSymbolVisual=!0,e}return t.prototype.getInitialData=function(e){return CT(null,this,{useEncodeDefaulter:!0})},t.prototype.getLegendIcon=function(e){var i=new Ut,n=Ln("line",0,e.itemHeight/2,e.itemWidth,0,e.lineStyle.stroke,!1);i.add(n),n.setStyle(e.lineStyle);var a=this.getData().getVisual("symbol"),o=this.getData().getVisual("symbolRotate"),s=a==="none"?"circle":a,u=e.itemHeight*.8,l=Ln(s,(e.itemWidth-u)/2,(e.itemHeight-u)/2,u,u,e.itemStyle.fill);i.add(l),l.setStyle(e.itemStyle);var f=e.iconRotate==="inherit"?o:e.iconRotate||0;return l.rotation=f*Math.PI/180,l.setOrigin([e.itemWidth/2,e.itemHeight/2]),s.indexOf("empty")>-1&&(l.style.stroke=l.style.fill,l.style.fill="#fff",l.style.lineWidth=2),i},t.type="series.line",t.dependencies=["grid","polar"],t.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1},t}(Dn);const kC=RC;function vy(r,t){var e=r.mapDimensionsAll("defaultedLabel"),i=e.length;if(i===1){var n=bi(r,t,e[0]);return n!=null?n+"":null}else if(i){for(var a=[],o=0;o<e.length;o++)a.push(bi(r,t,e[o]));return a.join(" ")}}function OC(r,t){var e=r.mapDimensionsAll("defaultedLabel");if(!B(t))return t+"";for(var i=[],n=0;n<e.length;n++){var a=r.getDimensionIndex(e[n]);a>=0&&i.push(t[a])}return i.join(" ")}var BC=function(r){O(t,r);function t(e,i,n,a){var o=r.call(this)||this;return o.updateData(e,i,n,a),o}return t.prototype._createSymbol=function(e,i,n,a,o){this.removeAll();var s=Ln(e,-1,-1,2,2,null,o);s.attr({z2:100,culling:!0,scaleX:a[0]/2,scaleY:a[1]/2}),s.drift=NC,this._symbolType=e,this.add(s)},t.prototype.stopSymbolAnimation=function(e){this.childAt(0).stopAnimation(null,e)},t.prototype.getSymbolType=function(){return this._symbolType},t.prototype.getSymbolPath=function(){return this.childAt(0)},t.prototype.highlight=function(){Ja(this.childAt(0))},t.prototype.downplay=function(){ja(this.childAt(0))},t.prototype.setZ=function(e,i){var n=this.childAt(0);n.zlevel=e,n.z=i},t.prototype.setDraggable=function(e,i){var n=this.childAt(0);n.draggable=e,n.cursor=!i&&e?"move":n.cursor},t.prototype.updateData=function(e,i,n,a){this.silent=!1;var o=e.getItemVisual(i,"symbol")||"circle",s=e.hostModel,u=t.getSymbolSize(e,i),l=o!==this._symbolType,f=a&&a.disableAnimation;if(l){var h=e.getItemVisual(i,"symbolKeepAspect");this._createSymbol(o,e,i,u,h)}else{var c=this.childAt(0);c.silent=!1;var v={scaleX:u[0]/2,scaleY:u[1]/2};f?c.attr(v):Hr(c,v,s,i),cS(c)}if(this._updateCommon(e,i,u,n,a),l){var c=this.childAt(0);if(!f){var v={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:c.style.opacity}};c.scaleX=c.scaleY=0,c.style.opacity=0,Hn(c,v,s,i)}}f&&this.childAt(0).stopAnimation("leave")},t.prototype._updateCommon=function(e,i,n,a,o){var s=this.childAt(0),u=e.hostModel,l,f,h,c,v,d,y,p,g;if(a&&(l=a.emphasisItemStyle,f=a.blurItemStyle,h=a.selectItemStyle,c=a.focus,v=a.blurScope,y=a.labelStatesModels,p=a.hoverScale,g=a.cursorStyle,d=a.emphasisDisabled),!a||e.hasItemOption){var m=a&&a.itemModel?a.itemModel:e.getItemModel(i),_=m.getModel("emphasis");l=_.getModel("itemStyle").getItemStyle(),h=m.getModel(["select","itemStyle"]).getItemStyle(),f=m.getModel(["blur","itemStyle"]).getItemStyle(),c=_.get("focus"),v=_.get("blurScope"),d=_.get("disabled"),y=Dl(m),p=_.getShallow("scale"),g=m.getShallow("cursor")}var S=e.getItemVisual(i,"symbolRotate");s.attr("rotation",(S||0)*Math.PI/180||0);var b=mg(e.getItemVisual(i,"symbolOffset"),n);b&&(s.x=b[0],s.y=b[1]),g&&s.attr("cursor",g);var w=e.getItemVisual(i,"style"),x=w.fill;if(s instanceof Wr){var D=s.style;s.useStyle(k({image:D.image,x:D.x,y:D.y,width:D.width,height:D.height},w))}else s.__isEmptyBrush?s.useStyle(k({},w)):s.useStyle(w),s.style.decal=null,s.setColor(x,o&&o.symbolInnerColor),s.style.strokeNoScale=!0;var T=e.getItemVisual(i,"liftZ"),C=this._z2;T!=null?C==null&&(this._z2=s.z2,s.z2+=T):C!=null&&(s.z2=C,this._z2=null);var A=o&&o.useNameLabel;mp(s,y,{labelFetcher:u,labelDataIndex:i,defaultText:L,inheritColor:x,defaultOpacity:w.opacity});function L(E){return A?e.getName(E):vy(e,E)}this._sizeX=n[0]/2,this._sizeY=n[1]/2;var I=s.ensureState("emphasis");I.style=l,s.ensureState("select").style=h,s.ensureState("blur").style=f;var P=p==null||p===!0?Math.max(1.1,3/this._sizeY):isFinite(p)&&p>0?+p:1;I.scaleX=this._sizeX*P,I.scaleY=this._sizeY*P,this.setSymbolScale(1),xu(this,c,v,d)},t.prototype.setSymbolScale=function(e){this.scaleX=this.scaleY=e},t.prototype.fadeOut=function(e,i,n){var a=this.childAt(0),o=it(this).dataIndex,s=n&&n.animation;if(this.silent=a.silent=!0,n&&n.fadeLabel){var u=a.getTextContent();u&&eo(u,{style:{opacity:0}},i,{dataIndex:o,removeOpt:s,cb:function(){a.removeTextContent()}})}else a.removeTextContent();eo(a,{style:{opacity:0},scaleX:0,scaleY:0},i,{dataIndex:o,cb:e,removeOpt:s})},t.getSymbolSize=function(e,i){return sx(e.getItemVisual(i,"symbolSize"))},t}(Ut);function NC(r,t){this.parent.drift(r,t)}const jl=BC;function Ys(r,t,e,i){return t&&!isNaN(t[0])&&!isNaN(t[1])&&!(i.isIgnore&&i.isIgnore(e))&&!(i.clipShape&&!i.clipShape.contain(t[0],t[1]))&&r.getItemVisual(e,"symbol")!=="none"}function Jv(r){return r!=null&&!H(r)&&(r={isIgnore:r}),r||{}}function jv(r){var t=r.hostModel,e=t.getModel("emphasis");return{emphasisItemStyle:e.getModel("itemStyle").getItemStyle(),blurItemStyle:t.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:t.getModel(["select","itemStyle"]).getItemStyle(),focus:e.get("focus"),blurScope:e.get("blurScope"),emphasisDisabled:e.get("disabled"),hoverScale:e.get("scale"),labelStatesModels:Dl(t),cursorStyle:t.get("cursor")}}var FC=function(){function r(t){this.group=new Ut,this._SymbolCtor=t||jl}return r.prototype.updateData=function(t,e){this._progressiveEls=null,e=Jv(e);var i=this.group,n=t.hostModel,a=this._data,o=this._SymbolCtor,s=e.disableAnimation,u=jv(t),l={disableAnimation:s},f=e.getSymbolPoint||function(h){return t.getItemLayout(h)};a||i.removeAll(),t.diff(a).add(function(h){var c=f(h);if(Ys(t,c,h,e)){var v=new o(t,h,u,l);v.setPosition(c),t.setItemGraphicEl(h,v),i.add(v)}}).update(function(h,c){var v=a.getItemGraphicEl(c),d=f(h);if(!Ys(t,d,h,e)){i.remove(v);return}var y=t.getItemVisual(h,"symbol")||"circle",p=v&&v.getSymbolType&&v.getSymbolType();if(!v||p&&p!==y)i.remove(v),v=new o(t,h,u,l),v.setPosition(d);else{v.updateData(t,h,u,l);var g={x:d[0],y:d[1]};s?v.attr(g):Hr(v,g,n)}i.add(v),t.setItemGraphicEl(h,v)}).remove(function(h){var c=a.getItemGraphicEl(h);c&&c.fadeOut(function(){i.remove(c)},n)}).execute(),this._getSymbolPoint=f,this._data=t},r.prototype.updateLayout=function(){var t=this,e=this._data;e&&e.eachItemGraphicEl(function(i,n){var a=t._getSymbolPoint(n);i.setPosition(a),i.markRedraw()})},r.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=jv(t),this._data=null,this.group.removeAll()},r.prototype.incrementalUpdate=function(t,e,i){this._progressiveEls=[],i=Jv(i);function n(u){u.isGroup||(u.incremental=!0,u.ensureState("emphasis").hoverLayer=!0)}for(var a=t.start;a<t.end;a++){var o=e.getItemLayout(a);if(Ys(e,o,a,i)){var s=new this._SymbolCtor(e,a,this._seriesScope);s.traverse(n),s.setPosition(o),this.group.add(s),e.setItemGraphicEl(a,s),this._progressiveEls.push(s)}}},r.prototype.eachRendered=function(t){Ml(this._progressiveEls||this.group,t)},r.prototype.remove=function(t){var e=this.group,i=this._data;i&&t?i.eachItemGraphicEl(function(n){n.fadeOut(function(){e.remove(n)},i.hostModel)}):e.removeAll()},r}();const zC=FC;function cy(r,t,e){var i=r.getBaseAxis(),n=r.getOtherAxis(i),a=HC(n,e),o=i.dim,s=n.dim,u=t.mapDimension(s),l=t.mapDimension(o),f=s==="x"||s==="radius"?1:0,h=G(r.dimensions,function(d){return t.mapDimension(d)}),c=!1,v=t.getCalculationInfo("stackResultDimension");return In(t,h[0])&&(c=!0,h[0]=v),In(t,h[1])&&(c=!0,h[1]=v),{dataDimsForPoint:h,valueStart:a,valueAxisDim:s,baseAxisDim:o,stacked:!!c,valueDim:u,baseDim:l,baseDataOffset:f,stackedOverDimension:t.getCalculationInfo("stackedOverDimension")}}function HC(r,t){var e=0,i=r.scale.getExtent();return t==="start"?e=i[0]:t==="end"?e=i[1]:vt(t)&&!isNaN(t)?e=t:i[0]>0?e=i[0]:i[1]<0&&(e=i[1]),e}function dy(r,t,e,i){var n=NaN;r.stacked&&(n=e.get(e.getCalculationInfo("stackedOverDimension"),i)),isNaN(n)&&(n=r.valueStart);var a=r.baseDataOffset,o=[];return o[a]=e.get(r.baseDim,i),o[1-a]=n,t.dataToPoint(o)}function VC(r,t){var e=[];return t.diff(r).add(function(i){e.push({cmd:"+",idx:i})}).update(function(i,n){e.push({cmd:"=",idx:n,idx1:i})}).remove(function(i){e.push({cmd:"-",idx:i})}).execute(),e}function GC(r,t,e,i,n,a,o,s){for(var u=VC(r,t),l=[],f=[],h=[],c=[],v=[],d=[],y=[],p=cy(n,t,o),g=r.getLayout("points")||[],m=t.getLayout("points")||[],_=0;_<u.length;_++){var S=u[_],b=!0,w=void 0,x=void 0;switch(S.cmd){case"=":w=S.idx*2,x=S.idx1*2;var D=g[w],T=g[w+1],C=m[x],A=m[x+1];(isNaN(D)||isNaN(T))&&(D=C,T=A),l.push(D,T),f.push(C,A),h.push(e[w],e[w+1]),c.push(i[x],i[x+1]),y.push(t.getRawIndex(S.idx1));break;case"+":var L=S.idx,I=p.dataDimsForPoint,P=n.dataToPoint([t.get(I[0],L),t.get(I[1],L)]);x=L*2,l.push(P[0],P[1]),f.push(m[x],m[x+1]);var E=dy(p,n,t,L);h.push(E[0],E[1]),c.push(i[x],i[x+1]),y.push(t.getRawIndex(L));break;case"-":b=!1}b&&(v.push(S),d.push(d.length))}d.sort(function(ct,ae){return y[ct]-y[ae]});for(var R=l.length,V=ci(R),N=ci(R),F=ci(R),Y=ci(R),et=[],_=0;_<d.length;_++){var j=d[_],st=_*2,ut=j*2;V[st]=l[ut],V[st+1]=l[ut+1],N[st]=f[ut],N[st+1]=f[ut+1],F[st]=h[ut],F[st+1]=h[ut+1],Y[st]=c[ut],Y[st+1]=c[ut+1],et[_]=v[j]}return{current:V,next:N,stackedOnCurrent:F,stackedOnNext:Y,status:et}}var Ge=Math.min,We=Math.max;function Rr(r,t){return isNaN(r)||isNaN(t)}function Uu(r,t,e,i,n,a,o,s,u){for(var l,f,h,c,v,d,y=e,p=0;p<i;p++){var g=t[y*2],m=t[y*2+1];if(y>=n||y<0)break;if(Rr(g,m)){if(u){y+=a;continue}break}if(y===e)r[a>0?"moveTo":"lineTo"](g,m),h=g,c=m;else{var _=g-l,S=m-f;if(_*_+S*S<.5){y+=a;continue}if(o>0){for(var b=y+a,w=t[b*2],x=t[b*2+1];w===g&&x===m&&p<i;)p++,b+=a,y+=a,w=t[b*2],x=t[b*2+1],g=t[y*2],m=t[y*2+1],_=g-l,S=m-f;var D=p+1;if(u)for(;Rr(w,x)&&D<i;)D++,b+=a,w=t[b*2],x=t[b*2+1];var T=.5,C=0,A=0,L=void 0,I=void 0;if(D>=i||Rr(w,x))v=g,d=m;else{C=w-l,A=x-f;var P=g-l,E=w-g,R=m-f,V=x-m,N=void 0,F=void 0;if(s==="x"){N=Math.abs(P),F=Math.abs(E);var Y=C>0?1:-1;v=g-Y*N*o,d=m,L=g+Y*F*o,I=m}else if(s==="y"){N=Math.abs(R),F=Math.abs(V);var et=A>0?1:-1;v=g,d=m-et*N*o,L=g,I=m+et*F*o}else N=Math.sqrt(P*P+R*R),F=Math.sqrt(E*E+V*V),T=F/(F+N),v=g-C*o*(1-T),d=m-A*o*(1-T),L=g+C*o*T,I=m+A*o*T,L=Ge(L,We(w,g)),I=Ge(I,We(x,m)),L=We(L,Ge(w,g)),I=We(I,Ge(x,m)),C=L-g,A=I-m,v=g-C*N/F,d=m-A*N/F,v=Ge(v,We(l,g)),d=Ge(d,We(f,m)),v=We(v,Ge(l,g)),d=We(d,Ge(f,m)),C=g-v,A=m-d,L=g+C*F/N,I=m+A*F/N}r.bezierCurveTo(h,c,v,d,g,m),h=L,c=I}else r.lineTo(g,m)}l=g,f=m,y+=a}return p}var py=function(){function r(){this.smooth=0,this.smoothConstraint=!0}return r}(),WC=function(r){O(t,r);function t(e){var i=r.call(this,e)||this;return i.type="ec-polyline",i}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new py},t.prototype.buildPath=function(e,i){var n=i.points,a=0,o=n.length/2;if(i.connectNulls){for(;o>0&&Rr(n[o*2-2],n[o*2-1]);o--);for(;a<o&&Rr(n[a*2],n[a*2+1]);a++);}for(;a<o;)a+=Uu(e,n,a,o,o,1,i.smooth,i.smoothMonotone,i.connectNulls)+1},t.prototype.getPointOn=function(e,i){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var n=this.path,a=n.data,o=Fr.CMD,s,u,l=i==="x",f=[],h=0;h<a.length;){var c=a[h++],v=void 0,d=void 0,y=void 0,p=void 0,g=void 0,m=void 0,_=void 0;switch(c){case o.M:s=a[h++],u=a[h++];break;case o.L:if(v=a[h++],d=a[h++],_=l?(e-s)/(v-s):(e-u)/(d-u),_<=1&&_>=0){var S=l?(d-u)*_+u:(v-s)*_+s;return l?[e,S]:[S,e]}s=v,u=d;break;case o.C:v=a[h++],d=a[h++],y=a[h++],p=a[h++],g=a[h++],m=a[h++];var b=l?Ya(s,v,y,g,e,f):Ya(u,d,p,m,e,f);if(b>0)for(var w=0;w<b;w++){var x=f[w];if(x<=1&&x>=0){var S=l?gt(u,d,p,m,x):gt(s,v,y,g,x);return l?[e,S]:[S,e]}}s=g,u=m;break}}},t}(ot),UC=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t}(py),YC=function(r){O(t,r);function t(e){var i=r.call(this,e)||this;return i.type="ec-polygon",i}return t.prototype.getDefaultShape=function(){return new UC},t.prototype.buildPath=function(e,i){var n=i.points,a=i.stackedOnPoints,o=0,s=n.length/2,u=i.smoothMonotone;if(i.connectNulls){for(;s>0&&Rr(n[s*2-2],n[s*2-1]);s--);for(;o<s&&Rr(n[o*2],n[o*2+1]);o++);}for(;o<s;){var l=Uu(e,n,o,s,s,1,i.smooth,u,i.connectNulls);Uu(e,a,o+l-1,l,s,-1,i.stackedOnSmooth,u,i.connectNulls),o+=l+1,e.closePath()}},t}(ot);function $C(r,t,e,i,n){var a=r.getArea(),o=a.x,s=a.y,u=a.width,l=a.height,f=e.get(["lineStyle","width"])||0;o-=f/2,s-=f/2,u+=f,l+=f,u=Math.ceil(u),o!==Math.floor(o)&&(o=Math.floor(o),u++);var h=new Bt({shape:{x:o,y:s,width:u,height:l}});if(t){var c=r.getBaseAxis(),v=c.isHorizontal(),d=c.inverse;v?(d&&(h.shape.x+=u),h.shape.width=0):(d||(h.shape.y+=l),h.shape.height=0);var y=U(n)?function(p){n(p,h)}:null;Hn(h,{shape:{width:u,height:l,x:o,y:s}},e,null,i,y)}return h}function XC(r,t,e){var i=r.getArea(),n=pt(i.r0,1),a=pt(i.r,1),o=new Sl({shape:{cx:pt(r.cx,1),cy:pt(r.cy,1),r0:n,r:a,startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}});if(t){var s=r.getBaseAxis().dim==="angle";s?o.shape.endAngle=i.startAngle:o.shape.r=n,Hn(o,{shape:{endAngle:i.endAngle,r:a}},e)}return o}function ZC(r,t){return r.type===t}function tc(r,t){if(r.length===t.length){for(var e=0;e<r.length;e++)if(r[e]!==t[e])return;return!0}}function ec(r){for(var t=1/0,e=1/0,i=-1/0,n=-1/0,a=0;a<r.length;){var o=r[a++],s=r[a++];isNaN(o)||(t=Math.min(o,t),i=Math.max(o,i)),isNaN(s)||(e=Math.min(s,e),n=Math.max(s,n))}return[[t,e],[i,n]]}function rc(r,t){var e=ec(r),i=e[0],n=e[1],a=ec(t),o=a[0],s=a[1];return Math.max(Math.abs(i[0]-o[0]),Math.abs(i[1]-o[1]),Math.abs(n[0]-s[0]),Math.abs(n[1]-s[1]))}function ic(r){return vt(r)?r:r?.5:0}function qC(r,t,e){if(!e.valueDim)return[];for(var i=t.count(),n=ci(i*2),a=0;a<i;a++){var o=dy(e,r,t,a);n[a*2]=o[0],n[a*2+1]=o[1]}return n}function Ue(r,t,e,i,n){var a=e.getBaseAxis(),o=a.dim==="x"||a.dim==="radius"?0:1,s=[],u=0,l=[],f=[],h=[],c=[];if(n){for(u=0;u<r.length;u+=2){var v=t||r;!isNaN(v[u])&&!isNaN(v[u+1])&&c.push(r[u],r[u+1])}r=c}for(u=0;u<r.length-2;u+=2)switch(h[0]=r[u+2],h[1]=r[u+3],f[0]=r[u],f[1]=r[u+1],s.push(f[0],f[1]),i){case"end":l[o]=h[o],l[1-o]=f[1-o],s.push(l[0],l[1]);break;case"middle":var d=(f[o]+h[o])/2,y=[];l[o]=y[o]=d,l[1-o]=f[1-o],y[1-o]=h[1-o],s.push(l[0],l[1]),s.push(y[0],y[1]);break;default:l[o]=f[o],l[1-o]=h[1-o],s.push(l[0],l[1])}return s.push(r[u++],r[u++]),s}function KC(r,t){var e=[],i=r.length,n,a;function o(f,h,c){var v=f.coord,d=(c-v)/(h.coord-v),y=s0(d,[f.color,h.color]);return{coord:c,color:y}}for(var s=0;s<i;s++){var u=r[s],l=u.coord;if(l<0)n=u;else if(l>t){a?e.push(o(a,u,t)):n&&e.push(o(n,u,0),o(n,u,t));break}else n&&(e.push(o(n,u,0)),n=null),e.push(u),a=u}return e}function QC(r,t,e){var i=r.getVisual("visualMeta");if(!(!i||!i.length||!r.count())&&t.type==="cartesian2d"){for(var n,a,o=i.length-1;o>=0;o--){var s=r.getDimensionInfo(i[o].dimension);if(n=s&&s.coordDim,n==="x"||n==="y"){a=i[o];break}}if(a){var u=t.getAxis(n),l=G(a.stops,function(_){return{coord:u.toGlobalCoord(u.dataToCoord(_.value)),color:_.color}}),f=l.length,h=a.outerColors.slice();f&&l[0].coord>l[f-1].coord&&(l.reverse(),h.reverse());var c=KC(l,n==="x"?e.getWidth():e.getHeight()),v=c.length;if(!v&&f)return l[0].coord<0?h[1]?h[1]:l[f-1].color:h[0]?h[0]:l[0].color;var d=10,y=c[0].coord-d,p=c[v-1].coord+d,g=p-y;if(g<.001)return"transparent";M(c,function(_){_.offset=(_.coord-y)/g}),c.push({offset:v?c[v-1].offset:.5,color:h[1]||"transparent"}),c.unshift({offset:v?c[0].offset:.5,color:h[0]||"transparent"});var m=new hp(0,0,0,0,c,!0);return m[n]=y,m[n+"2"]=p,m}}}function JC(r,t,e){var i=r.get("showAllSymbol"),n=i==="auto";if(!(i&&!n)){var a=e.getAxesByScale("ordinal")[0];if(a&&!(n&&jC(a,t))){var o=t.mapDimension(a.dim),s={};return M(a.getViewLabels(),function(u){var l=a.scale.getRawOrdinalNumber(u.tickValue);s[l]=1}),function(u){return!s.hasOwnProperty(t.get(o,u))}}}}function jC(r,t){var e=r.getExtent(),i=Math.abs(e[1]-e[0])/r.scale.count();isNaN(i)&&(i=0);for(var n=t.count(),a=Math.max(1,Math.round(n/5)),o=0;o<n;o+=a)if(jl.getSymbolSize(t,o)[r.isHorizontal()?1:0]*1.5>i)return!1;return!0}function tM(r,t){return isNaN(r)||isNaN(t)}function eM(r){for(var t=r.length/2;t>0&&tM(r[t*2-2],r[t*2-1]);t--);return t-1}function nc(r,t){return[r[t*2],r[t*2+1]]}function rM(r,t,e){for(var i=r.length/2,n=e==="x"?0:1,a,o,s=0,u=-1,l=0;l<i;l++)if(o=r[l*2+n],!(isNaN(o)||isNaN(r[l*2+1-n]))){if(l===0){a=o;continue}if(a<=t&&o>=t||a>=t&&o<=t){u=l;break}s=l,a=o}return{range:[s,u],t:(t-a)/(o-a)}}function gy(r){if(r.get(["endLabel","show"]))return!0;for(var t=0;t<we.length;t++)if(r.get([we[t],"endLabel","show"]))return!0;return!1}function $s(r,t,e,i){if(ZC(t,"cartesian2d")){var n=i.getModel("endLabel"),a=n.get("valueAnimation"),o=i.getData(),s={lastFrameIndex:0},u=gy(i)?function(v,d){r._endLabelOnDuring(v,d,o,s,a,n,t)}:null,l=t.getBaseAxis().isHorizontal(),f=$C(t,e,i,function(){var v=r._endLabel;v&&e&&s.originalX!=null&&v.attr({x:s.originalX,y:s.originalY})},u);if(!i.get("clip",!0)){var h=f.shape,c=Math.max(h.width,h.height);l?(h.y-=c,h.height+=c*2):(h.x-=c,h.width+=c*2)}return u&&u(1,f),f}else return XC(t,e,i)}function iM(r,t){var e=t.getBaseAxis(),i=e.isHorizontal(),n=e.inverse,a=i?n?"right":"left":"center",o=i?"middle":n?"top":"bottom";return{normal:{align:r.get("align")||a,verticalAlign:r.get("verticalAlign")||o}}}var nM=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.init=function(){var e=new Ut,i=new zC;this.group.add(i.group),this._symbolDraw=i,this._lineGroup=e,this._changePolyState=ht(this._changePolyState,this)},t.prototype.render=function(e,i,n){var a=e.coordinateSystem,o=this.group,s=e.getData(),u=e.getModel("lineStyle"),l=e.getModel("areaStyle"),f=s.getLayout("points")||[],h=a.type==="polar",c=this._coordSys,v=this._symbolDraw,d=this._polyline,y=this._polygon,p=this._lineGroup,g=!i.ssr&&e.get("animation"),m=!l.isEmpty(),_=l.get("origin"),S=cy(a,s,_),b=m&&qC(a,s,S),w=e.get("showSymbol"),x=e.get("connectNulls"),D=w&&!h&&JC(e,s,a),T=this._data;T&&T.eachItemGraphicEl(function(ct,ae){ct.__temp&&(o.remove(ct),T.setItemGraphicEl(ae,null))}),w||v.remove(),o.add(p);var C=h?!1:e.get("step"),A;a&&a.getArea&&e.get("clip",!0)&&(A=a.getArea(),A.width!=null?(A.x-=.1,A.y-=.1,A.width+=.2,A.height+=.2):A.r0&&(A.r0-=.5,A.r+=.5)),this._clipShapeForSymbol=A;var L=QC(s,a,n)||s.getVisual("style")[s.getVisual("drawType")];if(!(d&&c.type===a.type&&C===this._step))w&&v.updateData(s,{isIgnore:D,clipShape:A,disableAnimation:!0,getSymbolPoint:function(ct){return[f[ct*2],f[ct*2+1]]}}),g&&this._initSymbolLabelAnimation(s,a,A),C&&(b&&(b=Ue(b,f,a,C,x)),f=Ue(f,null,a,C,x)),d=this._newPolyline(f),m?y=this._newPolygon(f,b):y&&(p.remove(y),y=this._polygon=null),h||this._initOrUpdateEndLabel(e,a,Gr(L)),p.setClipPath($s(this,a,!0,e));else{m&&!y?y=this._newPolygon(f,b):y&&!m&&(p.remove(y),y=this._polygon=null),h||this._initOrUpdateEndLabel(e,a,Gr(L));var I=p.getClipPath();if(I){var P=$s(this,a,!1,e);Hn(I,{shape:P.shape},e)}else p.setClipPath($s(this,a,!0,e));w&&v.updateData(s,{isIgnore:D,clipShape:A,disableAnimation:!0,getSymbolPoint:function(ct){return[f[ct*2],f[ct*2+1]]}}),(!tc(this._stackedOnPoints,b)||!tc(this._points,f))&&(g?this._doUpdateAnimation(s,b,a,n,C,_,x):(C&&(b&&(b=Ue(b,f,a,C,x)),f=Ue(f,null,a,C,x)),d.setShape({points:f}),y&&y.setShape({points:f,stackedOnPoints:b})))}var E=e.getModel("emphasis"),R=E.get("focus"),V=E.get("blurScope"),N=E.get("disabled");if(d.useStyle(tt(u.getLineStyle(),{fill:"none",stroke:L,lineJoin:"bevel"})),ph(d,e,"lineStyle"),d.style.lineWidth>0&&e.get(["emphasis","lineStyle","width"])==="bolder"){var F=d.getState("emphasis").style;F.lineWidth=+d.style.lineWidth+1}it(d).seriesIndex=e.seriesIndex,xu(d,R,V,N);var Y=ic(e.get("smooth")),et=e.get("smoothMonotone");if(d.setShape({smooth:Y,smoothMonotone:et,connectNulls:x}),y){var j=s.getCalculationInfo("stackedOnSeries"),st=0;y.useStyle(tt(l.getAreaStyle(),{fill:L,opacity:.7,lineJoin:"bevel",decal:s.getVisual("style").decal})),j&&(st=ic(j.get("smooth"))),y.setShape({smooth:Y,stackedOnSmooth:st,smoothMonotone:et,connectNulls:x}),ph(y,e,"areaStyle"),it(y).seriesIndex=e.seriesIndex,xu(y,R,V,N)}var ut=this._changePolyState;s.eachItemGraphicEl(function(ct){ct&&(ct.onHoverStateChange=ut)}),this._polyline.onHoverStateChange=ut,this._data=s,this._coordSys=a,this._stackedOnPoints=b,this._points=f,this._step=C,this._valueOrigin=_,e.get("triggerLineEvent")&&(this.packEventData(e,d),y&&this.packEventData(e,y))},t.prototype.packEventData=function(e,i){it(i).eventData={componentType:"series",componentSubType:"line",componentIndex:e.componentIndex,seriesIndex:e.seriesIndex,seriesName:e.name,seriesType:"line"}},t.prototype.highlight=function(e,i,n,a){var o=e.getData(),s=Nr(o,a);if(this._changePolyState("emphasis"),!(s instanceof Array)&&s!=null&&s>=0){var u=o.getLayout("points"),l=o.getItemGraphicEl(s);if(!l){var f=u[s*2],h=u[s*2+1];if(isNaN(f)||isNaN(h)||this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(f,h))return;var c=e.get("zlevel")||0,v=e.get("z")||0;l=new jl(o,s),l.x=f,l.y=h,l.setZ(c,v);var d=l.getSymbolPath().getTextContent();d&&(d.zlevel=c,d.z=v,d.z2=this._polyline.z2+1),l.__temp=!0,o.setItemGraphicEl(s,l),l.stopSymbolAnimation(!0),this.group.add(l)}l.highlight()}else tr.prototype.highlight.call(this,e,i,n,a)},t.prototype.downplay=function(e,i,n,a){var o=e.getData(),s=Nr(o,a);if(this._changePolyState("normal"),s!=null&&s>=0){var u=o.getItemGraphicEl(s);u&&(u.__temp?(o.setItemGraphicEl(s,null),this.group.remove(u)):u.downplay())}else tr.prototype.downplay.call(this,e,i,n,a)},t.prototype._changePolyState=function(e){var i=this._polygon;fh(this._polyline,e),i&&fh(i,e)},t.prototype._newPolyline=function(e){var i=this._polyline;return i&&this._lineGroup.remove(i),i=new WC({shape:{points:e},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(i),this._polyline=i,i},t.prototype._newPolygon=function(e,i){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new YC({shape:{points:e,stackedOnPoints:i},segmentIgnoreThreshold:2}),this._lineGroup.add(n),this._polygon=n,n},t.prototype._initSymbolLabelAnimation=function(e,i,n){var a,o,s=i.getBaseAxis(),u=s.inverse;i.type==="cartesian2d"?(a=s.isHorizontal(),o=!1):i.type==="polar"&&(a=s.dim==="angle",o=!0);var l=e.hostModel,f=l.get("animationDuration");U(f)&&(f=f(null));var h=l.get("animationDelay")||0,c=U(h)?h(null):h;e.eachItemGraphicEl(function(v,d){var y=v;if(y){var p=[v.x,v.y],g=void 0,m=void 0,_=void 0;if(n)if(o){var S=n,b=i.pointToCoord(p);a?(g=S.startAngle,m=S.endAngle,_=-b[1]/180*Math.PI):(g=S.r0,m=S.r,_=b[0])}else{var w=n;a?(g=w.x,m=w.x+w.width,_=v.x):(g=w.y+w.height,m=w.y,_=v.y)}var x=m===g?0:(_-g)/(m-g);u&&(x=1-x);var D=U(h)?h(d):f*x+c,T=y.getSymbolPath(),C=T.getTextContent();y.attr({scaleX:0,scaleY:0}),y.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:D}),C&&C.animateFrom({style:{opacity:0}},{duration:300,delay:D}),T.disableLabelAnimation=!0}})},t.prototype._initOrUpdateEndLabel=function(e,i,n){var a=e.getModel("endLabel");if(gy(e)){var o=e.getData(),s=this._polyline,u=o.getLayout("points");if(!u){s.removeTextContent(),this._endLabel=null;return}var l=this._endLabel;l||(l=this._endLabel=new ie({z2:200}),l.ignoreClip=!0,s.setTextContent(this._endLabel),s.disableLabelAnimation=!0);var f=eM(u);f>=0&&(mp(s,Dl(e,"endLabel"),{inheritColor:n,labelFetcher:e,labelDataIndex:f,defaultText:function(h,c,v){return v!=null?OC(o,v):vy(o,h)},enableTextSetter:!0},iM(a,i)),s.textConfig.position=null)}else this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},t.prototype._endLabelOnDuring=function(e,i,n,a,o,s,u){var l=this._endLabel,f=this._polyline;if(l){e<1&&a.originalX==null&&(a.originalX=l.x,a.originalY=l.y);var h=n.getLayout("points"),c=n.hostModel,v=c.get("connectNulls"),d=s.get("precision"),y=s.get("distance")||0,p=u.getBaseAxis(),g=p.isHorizontal(),m=p.inverse,_=i.shape,S=m?g?_.x:_.y+_.height:g?_.x+_.width:_.y,b=(g?y:0)*(m?-1:1),w=(g?0:-y)*(m?-1:1),x=g?"x":"y",D=rM(h,S,x),T=D.range,C=T[1]-T[0],A=void 0;if(C>=1){if(C>1&&!v){var L=nc(h,T[0]);l.attr({x:L[0]+b,y:L[1]+w}),o&&(A=c.getRawValue(T[0]))}else{var L=f.getPointOn(S,x);L&&l.attr({x:L[0]+b,y:L[1]+w});var I=c.getRawValue(T[0]),P=c.getRawValue(T[1]);o&&(A=o_(n,d,I,P,D.t))}a.lastFrameIndex=T[0]}else{var E=e===1||a.lastFrameIndex>0?T[0]:0,L=nc(h,E);o&&(A=c.getRawValue(E)),l.attr({x:L[0]+b,y:L[1]+w})}if(o){var R=_p(l);typeof R.setLabelText=="function"&&R.setLabelText(A)}}},t.prototype._doUpdateAnimation=function(e,i,n,a,o,s,u){var l=this._polyline,f=this._polygon,h=e.hostModel,c=GC(this._data,e,this._stackedOnPoints,i,this._coordSys,n,this._valueOrigin),v=c.current,d=c.stackedOnCurrent,y=c.next,p=c.stackedOnNext;if(o&&(d=Ue(c.stackedOnCurrent,c.current,n,o,u),v=Ue(c.current,null,n,o,u),p=Ue(c.stackedOnNext,c.next,n,o,u),y=Ue(c.next,null,n,o,u)),rc(v,y)>3e3||f&&rc(d,p)>3e3){l.stopAnimation(),l.setShape({points:y}),f&&(f.stopAnimation(),f.setShape({points:y,stackedOnPoints:p}));return}l.shape.__points=c.current,l.shape.points=v;var g={shape:{points:y}};c.current!==v&&(g.shape.__points=c.next),l.stopAnimation(),Hr(l,g,h),f&&(f.setShape({points:v,stackedOnPoints:d}),f.stopAnimation(),Hr(f,{shape:{stackedOnPoints:p}},h),l.shape.points!==f.shape.points&&(f.shape.points=l.shape.points));for(var m=[],_=c.status,S=0;S<_.length;S++){var b=_[S].cmd;if(b==="="){var w=e.getItemGraphicEl(_[S].idx1);w&&m.push({el:w,ptIdx:S})}}l.animators&&l.animators.length&&l.animators[0].during(function(){f&&f.dirtyShape();for(var x=l.shape.__points,D=0;D<m.length;D++){var T=m[D].el,C=m[D].ptIdx*2;T.x=x[C],T.y=x[C+1],T.markRedraw()}})},t.prototype.remove=function(e){var i=this.group,n=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),n&&n.eachItemGraphicEl(function(a,o){a.__temp&&(i.remove(a),n.setItemGraphicEl(o,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},t.type="line",t}(tr);const aM=nM;function oM(r,t){return{seriesType:r,plan:ug(),reset:function(e){var i=e.getData(),n=e.coordinateSystem,a=e.pipelineContext,o=t||a.large;if(n){var s=G(n.dimensions,function(v){return i.mapDimension(v)}).slice(0,2),u=s.length,l=i.getCalculationInfo("stackResultDimension");In(i,s[0])&&(s[0]=l),In(i,s[1])&&(s[1]=l);var f=i.getStore(),h=i.getDimensionIndex(s[0]),c=i.getDimensionIndex(s[1]);return u&&{progress:function(v,d){for(var y=v.end-v.start,p=o&&ci(y*u),g=[],m=[],_=v.start,S=0;_<v.end;_++){var b=void 0;if(u===1){var w=f.get(h,_);b=n.dataToPoint(w,null,m)}else g[0]=f.get(h,_),g[1]=f.get(c,_),b=n.dataToPoint(g,null,m);o?(p[S++]=b[0],p[S++]=b[1]):d.setItemLayout(_,b.slice())}o&&d.setLayout("points",p)}}}}}}var sM={average:function(r){for(var t=0,e=0,i=0;i<r.length;i++)isNaN(r[i])||(t+=r[i],e++);return e===0?NaN:t/e},sum:function(r){for(var t=0,e=0;e<r.length;e++)t+=r[e]||0;return t},max:function(r){for(var t=-1/0,e=0;e<r.length;e++)r[e]>t&&(t=r[e]);return isFinite(t)?t:NaN},min:function(r){for(var t=1/0,e=0;e<r.length;e++)r[e]<t&&(t=r[e]);return isFinite(t)?t:NaN},nearest:function(r){return r[0]}},uM=function(r){return Math.round(r.length/2)};function lM(r){return{seriesType:r,reset:function(t,e,i){var n=t.getData(),a=t.get("sampling"),o=t.coordinateSystem,s=n.count();if(s>10&&o.type==="cartesian2d"&&a){var u=o.getBaseAxis(),l=o.getOtherAxis(u),f=u.getExtent(),h=i.getDevicePixelRatio(),c=Math.abs(f[1]-f[0])*(h||1),v=Math.round(s/c);if(isFinite(v)&&v>1){a==="lttb"?t.setData(n.lttbDownSample(n.mapDimension(l.dim),1/v)):a==="minmax"&&t.setData(n.minmaxDownSample(n.mapDimension(l.dim),1/v));var d=void 0;z(a)?d=sM[a]:U(a)&&(d=a),d&&t.setData(n.downSample(n.mapDimension(l.dim),1/v,d,uM))}}}}}function fM(r){r.registerChartView(aM),r.registerSeriesModel(kC),r.registerLayout(oM("line",!0)),r.registerVisual({seriesType:"line",reset:function(t){var e=t.getData(),i=t.getModel("lineStyle").getLineStyle();i&&!i.stroke&&(i.stroke=e.getVisual("style").fill),e.setVisual("legendLineStyle",i)}}),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,lM("line"))}var hM=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.type="grid",t.dependencies=["xAxis","yAxis"],t.layoutMode="box",t.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"},t}(ft);const vM=hM;var Yu=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",he).models[0]},t.type="cartesian2dAxis",t}(ft);be(Yu,hC);var yy={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,showMinLine:!0,showMaxLine:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},cM=at({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},yy),tf=at({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},yy),dM=at({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},tf),pM=tt({logBase:10},tf);const gM={category:cM,value:tf,time:dM,log:pM};var yM={value:1,category:1,time:1,log:1};function ac(r,t,e,i){M(yM,function(n,a){var o=at(at({},gM[a],!0),i,!0),s=function(u){O(l,u);function l(){var f=u!==null&&u.apply(this,arguments)||this;return f.type=t+"Axis."+a,f}return l.prototype.mergeDefaultAndTheme=function(f,h){var c=xn(this),v=c?Ol(f):{},d=h.getTheme();at(f,d.get(a+"Axis")),at(f,this.getDefaultOption()),f.type=oc(f),c&&Tn(f,v,c)},l.prototype.optionUpdated=function(){var f=this.option;f.type==="category"&&(this.__ordinalMeta=Gu.createByAxisModel(this))},l.prototype.getCategories=function(f){var h=this.option;if(h.type==="category")return f?h.data:this.__ordinalMeta.categories},l.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},l.type=t+"Axis."+a,l.defaultOption=o,l}(e);r.registerComponentModel(s)}),r.registerSubTypeDefaulter(t+"Axis",oc)}function oc(r){return r.type||(r.data?"category":"value")}var mM=function(){function r(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}return r.prototype.getAxis=function(t){return this._axes[t]},r.prototype.getAxes=function(){return G(this._dimList,function(t){return this._axes[t]},this)},r.prototype.getAxesByScale=function(t){return t=t.toLowerCase(),Tt(this.getAxes(),function(e){return e.scale.type===t})},r.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},r}();const _M=mM;var $u=["x","y"];function sc(r){return r.type==="interval"||r.type==="time"}var SM=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="cartesian2d",e.dimensions=$u,e}return t.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var e=this.getAxis("x").scale,i=this.getAxis("y").scale;if(!(!sc(e)||!sc(i))){var n=e.getExtent(),a=i.getExtent(),o=this.dataToPoint([n[0],a[0]]),s=this.dataToPoint([n[1],a[1]]),u=n[1]-n[0],l=a[1]-a[0];if(!(!u||!l)){var f=(s[0]-o[0])/u,h=(s[1]-o[1])/l,c=o[0]-n[0]*f,v=o[1]-a[0]*h,d=this._transform=[f,0,0,h,c,v];this._invTransform=al([],d)}}},t.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},t.prototype.containPoint=function(e){var i=this.getAxis("x"),n=this.getAxis("y");return i.contain(i.toLocalCoord(e[0]))&&n.contain(n.toLocalCoord(e[1]))},t.prototype.containData=function(e){return this.getAxis("x").containData(e[0])&&this.getAxis("y").containData(e[1])},t.prototype.containZone=function(e,i){var n=this.dataToPoint(e),a=this.dataToPoint(i),o=this.getArea(),s=new J(n[0],n[1],a[0]-n[0],a[1]-n[1]);return o.intersect(s)},t.prototype.dataToPoint=function(e,i,n){n=n||[];var a=e[0],o=e[1];if(this._transform&&a!=null&&isFinite(a)&&o!=null&&isFinite(o))return re(n,e,this._transform);var s=this.getAxis("x"),u=this.getAxis("y");return n[0]=s.toGlobalCoord(s.dataToCoord(a,i)),n[1]=u.toGlobalCoord(u.dataToCoord(o,i)),n},t.prototype.clampData=function(e,i){var n=this.getAxis("x").scale,a=this.getAxis("y").scale,o=n.getExtent(),s=a.getExtent(),u=n.parse(e[0]),l=a.parse(e[1]);return i=i||[],i[0]=Math.min(Math.max(Math.min(o[0],o[1]),u),Math.max(o[0],o[1])),i[1]=Math.min(Math.max(Math.min(s[0],s[1]),l),Math.max(s[0],s[1])),i},t.prototype.pointToData=function(e,i){var n=[];if(this._invTransform)return re(n,e,this._invTransform);var a=this.getAxis("x"),o=this.getAxis("y");return n[0]=a.coordToData(a.toLocalCoord(e[0]),i),n[1]=o.coordToData(o.toLocalCoord(e[1]),i),n},t.prototype.getOtherAxis=function(e){return this.getAxis(e.dim==="x"?"y":"x")},t.prototype.getArea=function(e){e=e||0;var i=this.getAxis("x").getGlobalExtent(),n=this.getAxis("y").getGlobalExtent(),a=Math.min(i[0],i[1])-e,o=Math.min(n[0],n[1])-e,s=Math.max(i[0],i[1])-a+e,u=Math.max(n[0],n[1])-o+e;return new J(a,o,s,u)},t}(_M),wM=function(r){O(t,r);function t(e,i,n,a,o){var s=r.call(this,e,i,n)||this;return s.index=0,s.type=a||"value",s.position=o||"bottom",s}return t.prototype.isHorizontal=function(){var e=this.position;return e==="top"||e==="bottom"},t.prototype.getGlobalExtent=function(e){var i=this.getExtent();return i[0]=this.toGlobalCoord(i[0]),i[1]=this.toGlobalCoord(i[1]),e&&i[0]>i[1]&&i.reverse(),i},t.prototype.pointToData=function(e,i){return this.coordToData(this.toLocalCoord(e[this.dim==="x"?0:1]),i)},t.prototype.setCategorySortInfo=function(e){if(this.type!=="category")return!1;this.model.option.categorySortInfo=e,this.scale.setSortInfo(e)},t}(xC);const bM=wM;function Xu(r,t,e){e=e||{};var i=r.coordinateSystem,n=t.axis,a={},o=n.getAxesOnZeroOf()[0],s=n.position,u=o?"onZero":s,l=n.dim,f=i.getRect(),h=[f.x,f.x+f.width,f.y,f.y+f.height],c={left:0,right:1,top:0,bottom:1,onZero:2},v=t.get("offset")||0,d=l==="x"?[h[2]-v,h[3]+v]:[h[0]-v,h[1]+v];if(o){var y=o.toGlobalCoord(o.dataToCoord(0));d[c.onZero]=Math.max(Math.min(y,d[1]),d[0])}a.position=[l==="y"?d[c[u]]:h[0],l==="x"?d[c[u]]:h[3]],a.rotation=Math.PI/2*(l==="x"?0:1);var p={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=p[s],a.labelOffset=o?d[c[s]]-d[c.onZero]:0,t.get(["axisTick","inside"])&&(a.tickDirection=-a.tickDirection),yn(e.labelInside,t.get(["axisLabel","inside"]))&&(a.labelDirection=-a.labelDirection);var g=t.get(["axisLabel","rotate"]);return a.labelRotate=u==="top"?-g:g,a.z2=1,a}function uc(r){return r.get("coordinateSystem")==="cartesian2d"}function lc(r){var t={xAxisModel:null,yAxisModel:null};return M(t,function(e,i){var n=i.replace(/Model$/,""),a=r.getReferringComponents(n,he).models[0];t[i]=a}),t}var Xs=Math.log;function xM(r,t,e){var i=Vn.prototype,n=i.getTicks.call(e),a=i.getTicks.call(e,!0),o=n.length-1,s=i.getInterval.call(e),u=iy(r,t),l=u.extent,f=u.fixMin,h=u.fixMax;if(r.type==="log"){var c=Xs(r.base);l=[Xs(l[0])/c,Xs(l[1])/c]}r.setExtent(l[0],l[1]),r.calcNiceExtent({splitNumber:o,fixMin:f,fixMax:h});var v=i.getExtent.call(r);f&&(l[0]=v[0]),h&&(l[1]=v[1]);var d=i.getInterval.call(r),y=l[0],p=l[1];if(f&&h)d=(p-y)/o;else if(f)for(p=l[0]+d*o;p<l[1]&&isFinite(p)&&isFinite(l[1]);)d=Ws(d),p=l[0]+d*o;else if(h)for(y=l[1]-d*o;y>l[0]&&isFinite(y)&&isFinite(l[0]);)d=Ws(d),y=l[1]-d*o;else{var g=r.getTicks().length-1;g>o&&(d=Ws(d));var m=d*o;p=Math.ceil(l[1]/d)*d,y=pt(p-m),y<0&&l[0]>=0?(y=0,p=pt(m)):p>0&&l[1]<=0&&(p=0,y=-pt(m))}var _=(n[0].value-a[0].value)/s,S=(n[o].value-a[o].value)/s;i.setExtent.call(r,y+d*_,p+d*S),i.setInterval.call(r,d),(_||S)&&i.setNiceExtent.call(r,y+d,p-d)}var TM=function(){function r(t,e,i){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=$u,this._initCartesian(t,e,i),this.model=t}return r.prototype.getRect=function(){return this._rect},r.prototype.update=function(t,e){var i=this._axesMap;this._updateScale(t,this.model);function n(o){var s,u=lt(o),l=u.length;if(l){for(var f=[],h=l-1;h>=0;h--){var c=+u[h],v=o[c],d=v.model,y=v.scale;Wu(y)&&d.get("alignTicks")&&d.get("interval")==null?f.push(v):($v(y,d),Wu(y)&&(s=v))}f.length&&(s||(s=f.pop(),$v(s.scale,s.model)),M(f,function(p){xM(p.scale,p.model,s.scale)}))}}n(i.x),n(i.y);var a={};M(i.x,function(o){fc(i,"y",o,a)}),M(i.y,function(o){fc(i,"x",o,a)}),this.resize(this.model,e)},r.prototype.resize=function(t,e,i){var n=t.getBoxLayoutParams(),a=!i&&t.get("containLabel"),o=kl(n,{width:e.getWidth(),height:e.getHeight()});this._rect=o;var s=this._axesList;u(),a&&(M(s,function(l){if(!l.model.get(["axisLabel","inside"])){var f=uC(l);if(f){var h=l.isHorizontal()?"height":"width",c=l.model.get(["axisLabel","margin"]);o[h]-=f[h]+c,l.position==="top"?o.y+=f.height+c:l.position==="left"&&(o.x+=f.width+c)}}}),u()),M(this._coordsList,function(l){l.calcAffineTransform()});function u(){M(s,function(l){var f=l.isHorizontal(),h=f?[0,o.width]:[0,o.height],c=l.inverse?1:0;l.setExtent(h[c],h[1-c]),CM(l,f?o.x:o.y)})}},r.prototype.getAxis=function(t,e){var i=this._axesMap[t];if(i!=null)return i[e||0]},r.prototype.getAxes=function(){return this._axesList.slice()},r.prototype.getCartesian=function(t,e){if(t!=null&&e!=null){var i="x"+t+"y"+e;return this._coordsMap[i]}H(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,a=this._coordsList;n<a.length;n++)if(a[n].getAxis("x").index===t||a[n].getAxis("y").index===e)return a[n]},r.prototype.getCartesians=function(){return this._coordsList.slice()},r.prototype.convertToPixel=function(t,e,i){var n=this._findConvertTarget(e);return n.cartesian?n.cartesian.dataToPoint(i):n.axis?n.axis.toGlobalCoord(n.axis.dataToCoord(i)):null},r.prototype.convertFromPixel=function(t,e,i){var n=this._findConvertTarget(e);return n.cartesian?n.cartesian.pointToData(i):n.axis?n.axis.coordToData(n.axis.toLocalCoord(i)):null},r.prototype._findConvertTarget=function(t){var e=t.seriesModel,i=t.xAxisModel||e&&e.getReferringComponents("xAxis",he).models[0],n=t.yAxisModel||e&&e.getReferringComponents("yAxis",he).models[0],a=t.gridModel,o=this._coordsList,s,u;if(e)s=e.coordinateSystem,nt(o,s)<0&&(s=null);else if(i&&n)s=this.getCartesian(i.componentIndex,n.componentIndex);else if(i)u=this.getAxis("x",i.componentIndex);else if(n)u=this.getAxis("y",n.componentIndex);else if(a){var l=a.coordinateSystem;l===this&&(s=this._coordsList[0])}return{cartesian:s,axis:u}},r.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},r.prototype._initCartesian=function(t,e,i){var n=this,a=this,o={left:!1,right:!1,top:!1,bottom:!1},s={x:{},y:{}},u={x:0,y:0};if(e.eachComponent("xAxis",l("x"),this),e.eachComponent("yAxis",l("y"),this),!u.x||!u.y){this._axesMap={},this._axesList=[];return}this._axesMap=s,M(s.x,function(f,h){M(s.y,function(c,v){var d="x"+h+"y"+v,y=new SM(d);y.master=n,y.model=t,n._coordsMap[d]=y,n._coordsList.push(y),y.addAxis(f),y.addAxis(c)})});function l(f){return function(h,c){if(Zs(h,t)){var v=h.get("position");f==="x"?v!=="top"&&v!=="bottom"&&(v=o.bottom?"top":"bottom"):v!=="left"&&v!=="right"&&(v=o.left?"right":"left"),o[v]=!0;var d=new bM(f,oC(h),[0,0],h.get("type"),v),y=d.type==="category";d.onBand=y&&h.get("boundaryGap"),d.inverse=h.get("inverse"),h.axis=d,d.model=h,d.grid=a,d.index=c,a._axesList.push(d),s[f][c]=d,u[f]++}}}},r.prototype._updateScale=function(t,e){M(this._axesList,function(n){if(n.scale.setExtent(1/0,-1/0),n.type==="category"){var a=n.model.get("categorySortInfo");n.scale.setSortInfo(a)}}),t.eachSeries(function(n){if(uc(n)){var a=lc(n),o=a.xAxisModel,s=a.yAxisModel;if(!Zs(o,e)||!Zs(s,e))return;var u=this.getCartesian(o.componentIndex,s.componentIndex),l=n.getData(),f=u.getAxis("x"),h=u.getAxis("y");i(l,f),i(l,h)}},this);function i(n,a){M(fC(n,a.dim),function(o){a.scale.unionExtentFromData(n,o)})}},r.prototype.getTooltipAxes=function(t){var e=[],i=[];return M(this.getCartesians(),function(n){var a=t!=null&&t!=="auto"?n.getAxis(t):n.getBaseAxis(),o=n.getOtherAxis(a);nt(e,a)<0&&e.push(a),nt(i,o)<0&&i.push(o)}),{baseAxes:e,otherAxes:i}},r.create=function(t,e){var i=[];return t.eachComponent("grid",function(n,a){var o=new r(n,t,e);o.name="grid_"+a,o.resize(n,e,!0),n.coordinateSystem=o,i.push(o)}),t.eachSeries(function(n){if(uc(n)){var a=lc(n),o=a.xAxisModel,s=a.yAxisModel,u=o.getCoordSysModel(),l=u.coordinateSystem;n.coordinateSystem=l.getCartesian(o.componentIndex,s.componentIndex)}}),i},r.dimensions=$u,r}();function Zs(r,t){return r.getCoordSysModel()===t}function fc(r,t,e,i){e.getAxesOnZeroOf=function(){return a?[a]:[]};var n=r[t],a,o=e.model,s=o.get(["axisLine","onZero"]),u=o.get(["axisLine","onZeroAxisIndex"]);if(!s)return;if(u!=null)hc(n[u])&&(a=n[u]);else for(var l in n)if(n.hasOwnProperty(l)&&hc(n[l])&&!i[f(n[l])]){a=n[l];break}a&&(i[f(a)]=!0);function f(h){return h.dim+"_"+h.index}}function hc(r){return r&&r.type!=="category"&&r.type!=="time"&&sC(r)}function CM(r,t){var e=r.getExtent(),i=e[0]+e[1];r.toGlobalCoord=r.dim==="x"?function(n){return n+t}:function(n){return i-n+t},r.toLocalCoord=r.dim==="x"?function(n){return n-t}:function(n){return i-n+t}}const MM=TM;var qe=Math.PI,kr=function(){function r(t,e){this.group=new Ut,this.opt=e,this.axisModel=t,tt(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var i=new Ut({x:e.position[0],y:e.position[1],rotation:e.rotation});i.updateTransform(),this._transformGroup=i}return r.prototype.hasBuilder=function(t){return!!vc[t]},r.prototype.add=function(t){vc[t](this.opt,this.axisModel,this.group,this._transformGroup)},r.prototype.getGroup=function(){return this.group},r.innerTextLayout=function(t,e,i){var n=cd(e-t),a,o;return Ka(n)?(o=i>0?"top":"bottom",a="center"):Ka(n-qe)?(o=i>0?"bottom":"top",a="center"):(o="middle",n>0&&n<qe?a=i>0?"right":"left":a=i>0?"left":"right"),{rotation:n,textAlign:a,textVerticalAlign:o}},r.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},r.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},r}(),vc={axisLine:function(r,t,e,i){var n=t.get(["axisLine","show"]);if(n==="auto"&&r.handleAutoShown&&(n=r.handleAutoShown("axisLine")),!!n){var a=t.axis.getExtent(),o=i.transform,s=[a[0],0],u=[a[1],0],l=s[0]>u[0];o&&(re(s,s,o),re(u,u,o));var f=k({lineCap:"round"},t.getModel(["axisLine","lineStyle"]).getLineStyle()),h=new zr({shape:{x1:s[0],y1:s[1],x2:u[0],y2:u[1]},style:f,strokeContainThreshold:r.strokeContainThreshold||5,silent:!0,z2:1});wn(h.shape,h.style.lineWidth),h.anid="line",e.add(h);var c=t.get(["axisLine","symbol"]);if(c!=null){var v=t.get(["axisLine","symbolSize"]);z(c)&&(c=[c,c]),(z(v)||vt(v))&&(v=[v,v]);var d=mg(t.get(["axisLine","symbolOffset"])||0,v),y=v[0],p=v[1];M([{rotate:r.rotation+Math.PI/2,offset:d[0],r:0},{rotate:r.rotation-Math.PI/2,offset:d[1],r:Math.sqrt((s[0]-u[0])*(s[0]-u[0])+(s[1]-u[1])*(s[1]-u[1]))}],function(g,m){if(c[m]!=="none"&&c[m]!=null){var _=Ln(c[m],-y/2,-p/2,y,p,f.stroke,!0),S=g.r+g.offset,b=l?u:s;_.attr({rotation:g.rotate,x:b[0]+S*Math.cos(r.rotation),y:b[1]-S*Math.sin(r.rotation),silent:!0,z2:11}),e.add(_)}})}}},axisTickLabel:function(r,t,e,i){var n=LM(e,i,t,r),a=IM(e,i,t,r);if(AM(t,a,n),PM(e,i,t,r.tickDirection),t.get(["axisLabel","hideOverlap"])){var o=TC(G(a,function(s){return{label:s,priority:s.z2,defaultAttr:{ignore:s.ignore}}}));CC(o)}},axisName:function(r,t,e,i){var n=yn(r.axisName,t.get("name"));if(n){var a=t.get("nameLocation"),o=r.nameDirection,s=t.getModel("nameTextStyle"),u=t.get("nameGap")||0,l=t.axis.getExtent(),f=l[0]>l[1]?-1:1,h=[a==="start"?l[0]-f*u:a==="end"?l[1]+f*u:(l[0]+l[1])/2,dc(a)?r.labelOffset+o*u:0],c,v=t.get("nameRotate");v!=null&&(v=v*qe/180);var d;dc(a)?c=kr.innerTextLayout(r.rotation,v??r.rotation,o):(c=DM(r.rotation,a,v||0,l),d=r.axisNameAvailableWidth,d!=null&&(d=Math.abs(d/Math.sin(c.rotation)),!isFinite(d)&&(d=null)));var y=s.getFont(),p=t.get("nameTruncate",!0)||{},g=p.ellipsis,m=yn(r.nameTruncateMaxWidth,p.maxWidth,d),_=new ie({x:h[0],y:h[1],rotation:c.rotation,silent:kr.isLabelSilent(t),style:Vr(s,{text:n,font:y,overflow:"truncate",width:m,ellipsis:g,fill:s.getTextColor()||t.get(["axisLine","lineStyle","color"]),align:s.get("align")||c.textAlign,verticalAlign:s.get("verticalAlign")||c.textVerticalAlign}),z2:1});if(Cl({el:_,componentModel:t,itemName:n}),_.__fullText=n,_.anid="name",t.get("triggerEvent")){var S=kr.makeAxisEventDataBase(t);S.targetType="axisName",S.name=n,it(_).eventData=S}i.add(_),_.updateTransform(),e.add(_),_.decomposeTransform()}}};function DM(r,t,e,i){var n=cd(e-r),a,o,s=i[0]>i[1],u=t==="start"&&!s||t!=="start"&&s;return Ka(n-qe/2)?(o=u?"bottom":"top",a="center"):Ka(n-qe*1.5)?(o=u?"top":"bottom",a="center"):(o="middle",n<qe*1.5&&n>qe/2?a=u?"left":"right":a=u?"right":"left"),{rotation:n,textAlign:a,textVerticalAlign:o}}function AM(r,t,e){if(!ny(r.axis)){var i=r.get(["axisLabel","showMinLabel"]),n=r.get(["axisLabel","showMaxLabel"]);t=t||[],e=e||[];var a=t[0],o=t[1],s=t[t.length-1],u=t[t.length-2],l=e[0],f=e[1],h=e[e.length-1],c=e[e.length-2];i===!1?($t(a),$t(l)):cc(a,o)&&(i?($t(o),$t(f)):($t(a),$t(l))),n===!1?($t(s),$t(h)):cc(u,s)&&(n?($t(u),$t(c)):($t(s),$t(h)))}}function $t(r){r&&(r.ignore=!0)}function cc(r,t){var e=r&&r.getBoundingRect().clone(),i=t&&t.getBoundingRect().clone();if(!(!e||!i)){var n=il([]);return nl(n,n,-r.rotation),e.applyTransform(gi([],n,r.getLocalTransform())),i.applyTransform(gi([],n,t.getLocalTransform())),e.intersect(i)}}function dc(r){return r==="middle"||r==="center"}function my(r,t,e,i,n){for(var a=[],o=[],s=[],u=0;u<r.length;u++){var l=r[u].coord;o[0]=l,o[1]=0,s[0]=l,s[1]=e,t&&(re(o,o,t),re(s,s,t));var f=new zr({shape:{x1:o[0],y1:o[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0});wn(f.shape,f.style.lineWidth),f.anid=n+"_"+r[u].tickValue,a.push(f)}return a}function LM(r,t,e,i){var n=e.axis,a=e.getModel("axisTick"),o=a.get("show");if(o==="auto"&&i.handleAutoShown&&(o=i.handleAutoShown("axisTick")),!(!o||n.scale.isBlank())){for(var s=a.getModel("lineStyle"),u=i.tickDirection*a.get("length"),l=n.getTicksCoords(),f=my(l,t.transform,u,tt(s.getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])}),"ticks"),h=0;h<f.length;h++)r.add(f[h]);return f}}function PM(r,t,e,i){var n=e.axis,a=e.getModel("minorTick");if(!(!a.get("show")||n.scale.isBlank())){var o=n.getMinorTicksCoords();if(o.length)for(var s=a.getModel("lineStyle"),u=i*a.get("length"),l=tt(s.getLineStyle(),tt(e.getModel("axisTick").getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])})),f=0;f<o.length;f++)for(var h=my(o[f],t.transform,u,l,"minorticks_"+f),c=0;c<h.length;c++)r.add(h[c])}}function IM(r,t,e,i){var n=e.axis,a=yn(i.axisLabelShow,e.get(["axisLabel","show"]));if(!(!a||n.scale.isBlank())){var o=e.getModel("axisLabel"),s=o.get("margin"),u=n.getViewLabels(),l=(yn(i.labelRotate,o.get("rotate"))||0)*qe/180,f=kr.innerTextLayout(i.rotation,l,i.labelDirection),h=e.getCategories&&e.getCategories(!0),c=[],v=kr.isLabelSilent(e),d=e.get("triggerEvent");return M(u,function(y,p){var g=n.scale.type==="ordinal"?n.scale.getRawOrdinalNumber(y.tickValue):y.tickValue,m=y.formattedLabel,_=y.rawLabel,S=o;if(h&&h[g]){var b=h[g];H(b)&&b.textStyle&&(S=new At(b.textStyle,o,e.ecModel))}var w=S.getTextColor()||e.get(["axisLine","lineStyle","color"]),x=n.dataToCoord(g),D=S.getShallow("align",!0)||f.textAlign,T=X(S.getShallow("alignMinLabel",!0),D),C=X(S.getShallow("alignMaxLabel",!0),D),A=S.getShallow("verticalAlign",!0)||S.getShallow("baseline",!0)||f.textVerticalAlign,L=X(S.getShallow("verticalAlignMinLabel",!0),A),I=X(S.getShallow("verticalAlignMaxLabel",!0),A),P=new ie({x,y:i.labelOffset+i.labelDirection*s,rotation:f.rotation,silent:v,z2:10+(y.level||0),style:Vr(S,{text:m,align:p===0?T:p===u.length-1?C:D,verticalAlign:p===0?L:p===u.length-1?I:A,fill:U(w)?w(n.type==="category"?_:n.type==="value"?g+"":g,p):w})});if(P.anid="label_"+g,Cl({el:P,componentModel:e,itemName:m,formatterParamsExtra:{isTruncated:function(){return P.isTruncated},value:_,tickIndex:p}}),d){var E=kr.makeAxisEventDataBase(e);E.targetType="axisLabel",E.value=_,E.tickIndex=p,n.type==="category"&&(E.dataIndex=g),it(P).eventData=E}t.add(P),P.updateTransform(),c.push(P),r.add(P),P.decomposeTransform()}),c}}const _y=kr;function EM(r,t){var e={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return RM(e,r,t),e.seriesInvolved&&OM(e,r),e}function RM(r,t,e){var i=t.getComponent("tooltip"),n=t.getComponent("axisPointer"),a=n.get("link",!0)||[],o=[];M(e.getCoordinateSystems(),function(s){if(!s.axisPointerEnabled)return;var u=kn(s.model),l=r.coordSysAxesInfo[u]={};r.coordSysMap[u]=s;var f=s.model,h=f.getModel("tooltip",i);if(M(s.getAxes(),ee(y,!1,null)),s.getTooltipAxes&&i&&h.get("show")){var c=h.get("trigger")==="axis",v=h.get(["axisPointer","type"])==="cross",d=s.getTooltipAxes(h.get(["axisPointer","axis"]));(c||v)&&M(d.baseAxes,ee(y,v?"cross":!0,c)),v&&M(d.otherAxes,ee(y,"cross",!1))}function y(p,g,m){var _=m.model.getModel("axisPointer",n),S=_.get("show");if(!(!S||S==="auto"&&!p&&!Zu(_))){g==null&&(g=_.get("triggerTooltip")),_=p?kM(m,h,n,t,p,g):_;var b=_.get("snap"),w=_.get("triggerEmphasis"),x=kn(m.model),D=g||b||m.type==="category",T=r.axesInfo[x]={key:x,axis:m,coordSys:s,axisPointerModel:_,triggerTooltip:g,triggerEmphasis:w,involveSeries:D,snap:b,useHandle:Zu(_),seriesModels:[],linkGroup:null};l[x]=T,r.seriesInvolved=r.seriesInvolved||D;var C=BM(a,m);if(C!=null){var A=o[C]||(o[C]={axesInfo:{}});A.axesInfo[x]=T,A.mapper=a[C].mapper,T.linkGroup=A}}}})}function kM(r,t,e,i,n,a){var o=t.getModel("axisPointer"),s=["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],u={};M(s,function(c){u[c]=K(o.get(c))}),u.snap=r.type!=="category"&&!!a,o.get("type")==="cross"&&(u.type="line");var l=u.label||(u.label={});if(l.show==null&&(l.show=!1),n==="cross"){var f=o.get(["label","show"]);if(l.show=f??!0,!a){var h=u.lineStyle=o.get("crossStyle");h&&tt(l,h.textStyle)}}return r.model.getModel("axisPointer",new At(u,e,i))}function OM(r,t){t.eachSeries(function(e){var i=e.coordinateSystem,n=e.get(["tooltip","trigger"],!0),a=e.get(["tooltip","show"],!0);!i||n==="none"||n===!1||n==="item"||a===!1||e.get(["axisPointer","show"],!0)===!1||M(r.coordSysAxesInfo[kn(i.model)],function(o){var s=o.axis;i.getAxis(s.dim)===s&&(o.seriesModels.push(e),o.seriesDataCount==null&&(o.seriesDataCount=0),o.seriesDataCount+=e.getData().count())})})}function BM(r,t){for(var e=t.model,i=t.dim,n=0;n<r.length;n++){var a=r[n]||{};if(qs(a[i+"AxisId"],e.id)||qs(a[i+"AxisIndex"],e.componentIndex)||qs(a[i+"AxisName"],e.name))return n}}function qs(r,t){return r==="all"||B(r)&&nt(r,t)>=0||r===t}function NM(r){var t=ef(r);if(t){var e=t.axisPointerModel,i=t.axis.scale,n=e.option,a=e.get("status"),o=e.get("value");o!=null&&(o=i.parse(o));var s=Zu(e);a==null&&(n.status=s?"show":"hide");var u=i.getExtent().slice();u[0]>u[1]&&u.reverse(),(o==null||o>u[1])&&(o=u[1]),o<u[0]&&(o=u[0]),n.value=o,s&&(n.status=t.axis.scale.isBlank()?"hide":"show")}}function ef(r){var t=(r.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return t&&t.axesInfo[kn(r)]}function FM(r){var t=ef(r);return t&&t.axisPointerModel}function Zu(r){return!!r.get(["handle","show"])}function kn(r){return r.type+"||"+r.id}var pc={},zM=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n,a){this.axisPointerClass&&NM(e),r.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(e,n,!0)},t.prototype.updateAxisPointer=function(e,i,n,a){this._doUpdateAxisPointerClass(e,n,!1)},t.prototype.remove=function(e,i){var n=this._axisPointer;n&&n.remove(i)},t.prototype.dispose=function(e,i){this._disposeAxisPointer(i),r.prototype.dispose.apply(this,arguments)},t.prototype._doUpdateAxisPointerClass=function(e,i,n){var a=t.getAxisPointerClass(this.axisPointerClass);if(a){var o=FM(e);o?(this._axisPointer||(this._axisPointer=new a)).render(e,o,i,n):this._disposeAxisPointer(i)}},t.prototype._disposeAxisPointer=function(e){this._axisPointer&&this._axisPointer.dispose(e),this._axisPointer=null},t.registerAxisPointerClass=function(e,i){pc[e]=i},t.getAxisPointerClass=function(e){return e&&pc[e]},t.type="axis",t}(er);const Sy=zM;var qu=yt();function HM(r,t,e,i){var n=e.axis;if(!n.scale.isBlank()){var a=e.getModel("splitArea"),o=a.getModel("areaStyle"),s=o.get("color"),u=i.coordinateSystem.getRect(),l=n.getTicksCoords({tickModel:a,clamp:!0});if(l.length){var f=s.length,h=qu(r).splitAreaColors,c=Z(),v=0;if(h)for(var d=0;d<l.length;d++){var y=h.get(l[d].tickValue);if(y!=null){v=(y+(f-1)*d)%f;break}}var p=n.toGlobalCoord(l[0].coord),g=o.getAreaStyle();s=B(s)?s:[s];for(var d=1;d<l.length;d++){var m=n.toGlobalCoord(l[d].coord),_=void 0,S=void 0,b=void 0,w=void 0;n.isHorizontal()?(_=p,S=u.y,b=m-_,w=u.height,p=_+b):(_=u.x,S=p,b=u.width,w=m-S,p=S+w);var x=l[d-1].tickValue;x!=null&&c.set(x,v),t.add(new Bt({anid:x!=null?"area_"+x:null,shape:{x:_,y:S,width:b,height:w},style:tt({fill:s[v]},g),autoBatch:!0,silent:!0})),v=(v+1)%f}qu(r).splitAreaColors=c}}}function VM(r){qu(r).splitAreaColors=null}var GM=["axisLine","axisTickLabel","axisName"],WM=["splitArea","splitLine","minorSplitLine"],wy=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.axisPointerClass="CartesianAxisPointer",e}return t.prototype.render=function(e,i,n,a){this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new Ut,this.group.add(this._axisGroup),!!e.get("show")){var s=e.getCoordSysModel(),u=Xu(s,e),l=new _y(e,k({handleAutoShown:function(h){for(var c=s.coordinateSystem.getCartesians(),v=0;v<c.length;v++)if(Wu(c[v].getOtherAxis(e.axis).scale))return!0;return!1}},u));M(GM,l.add,l),this._axisGroup.add(l.getGroup()),M(WM,function(h){e.get([h,"show"])&&UM[h](this,this._axisGroup,e,s)},this);var f=a&&a.type==="changeAxisOrder"&&a.isInitSort;f||pp(o,this._axisGroup,e),r.prototype.render.call(this,e,i,n,a)}},t.prototype.remove=function(){VM(this)},t.type="cartesianAxis",t}(Sy),UM={splitLine:function(r,t,e,i){var n=e.axis;if(!n.scale.isBlank()){var a=e.getModel("splitLine"),o=a.getModel("lineStyle"),s=o.get("color"),u=a.get("showMinLine")!==!1,l=a.get("showMaxLine")!==!1;s=B(s)?s:[s];for(var f=i.coordinateSystem.getRect(),h=n.isHorizontal(),c=0,v=n.getTicksCoords({tickModel:a}),d=[],y=[],p=o.getLineStyle(),g=0;g<v.length;g++){var m=n.toGlobalCoord(v[g].coord);if(!(g===0&&!u||g===v.length-1&&!l)){var _=v[g].tickValue;h?(d[0]=m,d[1]=f.y,y[0]=m,y[1]=f.y+f.height):(d[0]=f.x,d[1]=m,y[0]=f.x+f.width,y[1]=m);var S=c++%s.length,b=new zr({anid:_!=null?"line_"+_:null,autoBatch:!0,shape:{x1:d[0],y1:d[1],x2:y[0],y2:y[1]},style:tt({stroke:s[S]},p),silent:!0});wn(b.shape,p.lineWidth),t.add(b)}}}},minorSplitLine:function(r,t,e,i){var n=e.axis,a=e.getModel("minorSplitLine"),o=a.getModel("lineStyle"),s=i.coordinateSystem.getRect(),u=n.isHorizontal(),l=n.getMinorTicksCoords();if(l.length)for(var f=[],h=[],c=o.getLineStyle(),v=0;v<l.length;v++)for(var d=0;d<l[v].length;d++){var y=n.toGlobalCoord(l[v][d].coord);u?(f[0]=y,f[1]=s.y,h[0]=y,h[1]=s.y+s.height):(f[0]=s.x,f[1]=y,h[0]=s.x+s.width,h[1]=y);var p=new zr({anid:"minor_line_"+l[v][d].tickValue,autoBatch:!0,shape:{x1:f[0],y1:f[1],x2:h[0],y2:h[1]},style:c,silent:!0});wn(p.shape,c.lineWidth),t.add(p)}},splitArea:function(r,t,e,i){HM(r,t,e,i)}},by=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="xAxis",t}(wy),YM=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=by.type,e}return t.type="yAxis",t}(wy),$M=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="grid",e}return t.prototype.render=function(e,i){this.group.removeAll(),e.get("show")&&this.group.add(new Bt({shape:e.coordinateSystem.getRect(),style:tt({fill:e.get("backgroundColor")},e.getItemStyle()),silent:!0,z2:-1}))},t.type="grid",t}(er),gc={offset:0};function XM(r){r.registerComponentView($M),r.registerComponentModel(vM),r.registerCoordinateSystem("cartesian2d",MM),ac(r,"x",Yu,gc),ac(r,"y",Yu,gc),r.registerComponentView(by),r.registerComponentView(YM),r.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})}var Dr=yt(),yc=K,Ks=ht,ZM=function(){function r(){this._dragging=!1,this.animationThreshold=15}return r.prototype.render=function(t,e,i,n){var a=e.get("value"),o=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=i,!(!n&&this._lastValue===a&&this._lastStatus===o)){this._lastValue=a,this._lastStatus=o;var s=this._group,u=this._handle;if(!o||o==="hide"){s&&s.hide(),u&&u.hide();return}s&&s.show(),u&&u.show();var l={};this.makeElOption(l,a,t,e,i);var f=l.graphicKey;f!==this._lastGraphicKey&&this.clear(i),this._lastGraphicKey=f;var h=this._moveAnimation=this.determineAnimation(t,e);if(!s)s=this._group=new Ut,this.createPointerEl(s,l,t,e),this.createLabelEl(s,l,t,e),i.getZr().add(s);else{var c=ee(mc,e,h);this.updatePointerEl(s,l,c),this.updateLabelEl(s,l,c,e)}Sc(s,e,!0),this._renderHandle(a)}},r.prototype.remove=function(t){this.clear(t)},r.prototype.dispose=function(t){this.clear(t)},r.prototype.determineAnimation=function(t,e){var i=e.get("animation"),n=t.axis,a=n.type==="category",o=e.get("snap");if(!o&&!a)return!1;if(i==="auto"||i==null){var s=this.animationThreshold;if(a&&n.getBandWidth()>s)return!0;if(o){var u=ef(t).seriesDataCount,l=n.getExtent();return Math.abs(l[0]-l[1])/u>s}return!1}return i===!0},r.prototype.makeElOption=function(t,e,i,n,a){},r.prototype.createPointerEl=function(t,e,i,n){var a=e.pointer;if(a){var o=Dr(t).pointerEl=new AS[a.type](yc(e.pointer));t.add(o)}},r.prototype.createLabelEl=function(t,e,i,n){if(e.label){var a=Dr(t).labelEl=new ie(yc(e.label));t.add(a),_c(a,n)}},r.prototype.updatePointerEl=function(t,e,i){var n=Dr(t).pointerEl;n&&e.pointer&&(n.setStyle(e.pointer.style),i(n,{shape:e.pointer.shape}))},r.prototype.updateLabelEl=function(t,e,i,n){var a=Dr(t).labelEl;a&&(a.setStyle(e.label.style),i(a,{x:e.label.x,y:e.label.y}),_c(a,n))},r.prototype._renderHandle=function(t){if(!(this._dragging||!this.updateHandleTransform)){var e=this._axisPointerModel,i=this._api.getZr(),n=this._handle,a=e.getModel("handle"),o=e.get("status");if(!a.get("show")||!o||o==="hide"){n&&i.remove(n),this._handle=null;return}var s;this._handle||(s=!0,n=this._handle=gp(a.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(l){Uc(l.event)},onmousedown:Ks(this._onHandleDragMove,this,0,0),drift:Ks(this._onHandleDragMove,this),ondragend:Ks(this._onHandleDragEnd,this)}),i.add(n)),Sc(n,e,!1),n.setStyle(a.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var u=a.get("size");B(u)||(u=[u,u]),n.scaleX=u[0]/2,n.scaleY=u[1]/2,fg(this,"_doDispatchAxisPointer",a.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,s)}},r.prototype._moveHandleToValue=function(t,e){mc(this._axisPointerModel,!e&&this._moveAnimation,this._handle,Qs(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},r.prototype._onHandleDragMove=function(t,e){var i=this._handle;if(i){this._dragging=!0;var n=this.updateHandleTransform(Qs(i),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=n,i.stopAnimation(),i.attr(Qs(n)),Dr(i).lastProp=null,this._doDispatchAxisPointer()}},r.prototype._doDispatchAxisPointer=function(){var t=this._handle;if(t){var e=this._payloadInfo,i=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:i.axis.dim,axisIndex:i.componentIndex}]})}},r.prototype._onHandleDragEnd=function(){this._dragging=!1;var t=this._handle;if(t){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},r.prototype.clear=function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),i=this._group,n=this._handle;e&&i&&(this._lastGraphicKey=null,i&&e.remove(i),n&&e.remove(n),this._group=null,this._handle=null,this._payloadInfo=null),ku(this,"_doDispatchAxisPointer")},r.prototype.doClear=function(){},r.prototype.buildLabel=function(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}},r}();function mc(r,t,e,i){xy(Dr(e).lastProp,i)||(Dr(e).lastProp=i,t?Hr(e,i,r):(e.stopAnimation(),e.attr(i)))}function xy(r,t){if(H(r)&&H(t)){var e=!0;return M(t,function(i,n){e=e&&xy(r[n],i)}),!!e}else return r===t}function _c(r,t){r[t.get(["label","show"])?"show":"hide"]()}function Qs(r){return{x:r.x||0,y:r.y||0,rotation:r.rotation||0}}function Sc(r,t,e){var i=t.get("z"),n=t.get("zlevel");r&&r.traverse(function(a){a.type!=="group"&&(i!=null&&(a.z=i),n!=null&&(a.zlevel=n),a.silent=e)})}const qM=ZM;function KM(r){var t=r.get("type"),e=r.getModel(t+"Style"),i;return t==="line"?(i=e.getLineStyle(),i.fill=null):t==="shadow"&&(i=e.getAreaStyle(),i.stroke=null),i}function QM(r,t,e,i,n){var a=e.get("value"),o=Ty(a,t.axis,t.ecModel,e.get("seriesDataIndices"),{precision:e.get(["label","precision"]),formatter:e.get(["label","formatter"])}),s=e.getModel("label"),u=Rl(s.get("padding")||0),l=s.getFont(),f=ll(o,l),h=n.position,c=f.width+u[1]+u[3],v=f.height+u[0]+u[2],d=n.align;d==="right"&&(h[0]-=c),d==="center"&&(h[0]-=c/2);var y=n.verticalAlign;y==="bottom"&&(h[1]-=v),y==="middle"&&(h[1]-=v/2),JM(h,c,v,i);var p=s.get("backgroundColor");(!p||p==="auto")&&(p=t.get(["axisLine","lineStyle","color"])),r.label={x:h[0],y:h[1],style:Vr(s,{text:o,font:l,fill:s.getTextColor(),padding:u,backgroundColor:p}),z2:10}}function JM(r,t,e,i){var n=i.getWidth(),a=i.getHeight();r[0]=Math.min(r[0]+t,n)-t,r[1]=Math.min(r[1]+e,a)-e,r[0]=Math.max(r[0],0),r[1]=Math.max(r[1],0)}function Ty(r,t,e,i,n){r=t.scale.parse(r);var a=t.scale.getLabel({value:r},{precision:n.precision}),o=n.formatter;if(o){var s={value:Ql(t,{value:r}),axisDimension:t.dim,axisIndex:t.index,seriesData:[]};M(i,function(u){var l=e.getSeriesByIndex(u.seriesIndex),f=u.dataIndexInside,h=l&&l.getDataParams(f);h&&s.seriesData.push(h)}),z(o)?a=o.replace("{value}",a):U(o)&&(a=o(s))}return a}function Cy(r,t,e){var i=pi();return nl(i,i,e.rotation),au(i,i,e.position),Tl([r.dataToCoord(t),(e.labelOffset||0)+(e.labelDirection||1)*(e.labelMargin||0)],i)}function jM(r,t,e,i,n,a){var o=_y.innerTextLayout(e.rotation,0,e.labelDirection);e.labelMargin=n.get(["label","margin"]),QM(t,i,n,a,{position:Cy(i.axis,r,e),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function tD(r,t,e){return e=e||0,{x1:r[e],y1:r[1-e],x2:t[e],y2:t[1-e]}}function eD(r,t,e){return e=e||0,{x:r[e],y:r[1-e],width:t[e],height:t[1-e]}}var rD=function(r){O(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.makeElOption=function(e,i,n,a,o){var s=n.axis,u=s.grid,l=a.get("type"),f=wc(u,s).getOtherAxis(s).getGlobalExtent(),h=s.toGlobalCoord(s.dataToCoord(i,!0));if(l&&l!=="none"){var c=KM(a),v=iD[l](s,h,f);v.style=c,e.graphicKey=v.type,e.pointer=v}var d=Xu(u.model,n);jM(i,e,d,n,a,o)},t.prototype.getHandleTransform=function(e,i,n){var a=Xu(i.axis.grid.model,i,{labelInside:!1});a.labelMargin=n.get(["handle","margin"]);var o=Cy(i.axis,e,a);return{x:o[0],y:o[1],rotation:a.rotation+(a.labelDirection<0?Math.PI:0)}},t.prototype.updateHandleTransform=function(e,i,n,a){var o=n.axis,s=o.grid,u=o.getGlobalExtent(!0),l=wc(s,o).getOtherAxis(o).getGlobalExtent(),f=o.dim==="x"?0:1,h=[e.x,e.y];h[f]+=i[f],h[f]=Math.min(u[1],h[f]),h[f]=Math.max(u[0],h[f]);var c=(l[1]+l[0])/2,v=[c,c];v[f]=h[f];var d=[{verticalAlign:"middle"},{align:"center"}];return{x:h[0],y:h[1],rotation:e.rotation,cursorPoint:v,tooltipOption:d[f]}},t}(qM);function wc(r,t){var e={};return e[t.dim+"AxisIndex"]=t.index,r.getCartesian(e)}var iD={line:function(r,t,e){var i=tD([t,e[0]],[t,e[1]],bc(r));return{type:"Line",subPixelOptimize:!0,shape:i}},shadow:function(r,t,e){var i=Math.max(1,r.getBandWidth()),n=e[1]-e[0];return{type:"Rect",shape:eD([t-i/2,e[0]],[i,n],bc(r))}}};function bc(r){return r.dim==="x"?0:1}const nD=rD;var aD=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="axisPointer",t.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}},t}(ft);const oD=aD;var Le=yt(),sD=M;function My(r,t,e){if(!$.node){var i=t.getZr();Le(i).records||(Le(i).records={}),uD(i,t);var n=Le(i).records[r]||(Le(i).records[r]={});n.handler=e}}function uD(r,t){if(Le(r).initialized)return;Le(r).initialized=!0,e("click",ee(xc,"click")),e("mousemove",ee(xc,"mousemove")),e("globalout",fD);function e(i,n){r.on(i,function(a){var o=hD(t);sD(Le(r).records,function(s){s&&n(s,a,o.dispatchAction)}),lD(o.pendings,t)})}}function lD(r,t){var e=r.showTip.length,i=r.hideTip.length,n;e?n=r.showTip[e-1]:i&&(n=r.hideTip[i-1]),n&&(n.dispatchAction=null,t.dispatchAction(n))}function fD(r,t,e){r.handler("leave",null,e)}function xc(r,t,e,i){t.handler(r,e,i)}function hD(r){var t={showTip:[],hideTip:[]},e=function(i){var n=t[i.type];n?n.push(i):(i.dispatchAction=e,r.dispatchAction(i))};return{dispatchAction:e,pendings:t}}function Ku(r,t){if(!$.node){var e=t.getZr(),i=(Le(e).records||{})[r];i&&(Le(e).records[r]=null)}}var vD=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n){var a=i.getComponent("tooltip"),o=e.get("triggerOn")||a&&a.get("triggerOn")||"mousemove|click";My("axisPointer",n,function(s,u,l){o!=="none"&&(s==="leave"||o.indexOf(s)>=0)&&l({type:"updateAxisPointer",currTrigger:s,x:u&&u.offsetX,y:u&&u.offsetY})})},t.prototype.remove=function(e,i){Ku("axisPointer",i)},t.prototype.dispose=function(e,i){Ku("axisPointer",i)},t.type="axisPointer",t}(er);const cD=vD;function Dy(r,t){var e=[],i=r.seriesIndex,n;if(i==null||!(n=t.getSeriesByIndex(i)))return{point:[]};var a=n.getData(),o=Nr(a,r);if(o==null||o<0||B(o))return{point:[]};var s=a.getItemGraphicEl(o),u=n.coordinateSystem;if(n.getTooltipPosition)e=n.getTooltipPosition(o)||[];else if(u&&u.dataToPoint)if(r.isStacked){var l=u.getBaseAxis(),f=u.getOtherAxis(l),h=f.dim,c=l.dim,v=h==="x"||h==="radius"?1:0,d=a.mapDimension(c),y=[];y[v]=a.get(d,o),y[1-v]=a.get(a.getCalculationInfo("stackResultDimension"),o),e=u.dataToPoint(y)||[]}else e=u.dataToPoint(a.getValues(G(u.dimensions,function(g){return a.mapDimension(g)}),o))||[];else if(s){var p=s.getBoundingRect().clone();p.applyTransform(s.transform),e=[p.x+p.width/2,p.y+p.height/2]}return{point:e,el:s}}var Tc=yt();function dD(r,t,e){var i=r.currTrigger,n=[r.x,r.y],a=r,o=r.dispatchAction||ht(e.dispatchAction,e),s=t.getComponent("axisPointer").coordSysAxesInfo;if(s){Va(n)&&(n=Dy({seriesIndex:a.seriesIndex,dataIndex:a.dataIndex},t).point);var u=Va(n),l=a.axesInfo,f=s.axesInfo,h=i==="leave"||Va(n),c={},v={},d={list:[],map:{}},y={showPointer:ee(gD,v),showTooltip:ee(yD,d)};M(s.coordSysMap,function(g,m){var _=u||g.containPoint(n);M(s.coordSysAxesInfo[m],function(S,b){var w=S.axis,x=wD(l,S);if(!h&&_&&(!l||x)){var D=x&&x.value;D==null&&!u&&(D=w.pointToData(n)),D!=null&&Cc(S,D,y,!1,c)}})});var p={};return M(f,function(g,m){var _=g.linkGroup;_&&!v[m]&&M(_.axesInfo,function(S,b){var w=v[b];if(S!==g&&w){var x=w.value;_.mapper&&(x=g.axis.scale.parse(_.mapper(x,Mc(S),Mc(g)))),p[g.key]=x}})}),M(p,function(g,m){Cc(f[m],g,y,!0,c)}),mD(v,f,c),_D(d,n,r,o),SD(f,o,e),c}}function Cc(r,t,e,i,n){var a=r.axis;if(!(a.scale.isBlank()||!a.containData(t))){if(!r.involveSeries){e.showPointer(r,t);return}var o=pD(t,r),s=o.payloadBatch,u=o.snapToValue;s[0]&&n.seriesIndex==null&&k(n,s[0]),!i&&r.snap&&a.containData(u)&&u!=null&&(t=u),e.showPointer(r,t,s),e.showTooltip(r,o,u)}}function pD(r,t){var e=t.axis,i=e.dim,n=r,a=[],o=Number.MAX_VALUE,s=-1;return M(t.seriesModels,function(u,l){var f=u.getData().mapDimensionsAll(i),h,c;if(u.getAxisTooltipData){var v=u.getAxisTooltipData(f,r,e);c=v.dataIndices,h=v.nestestValue}else{if(c=u.getData().indicesOfNearest(f[0],r,e.type==="category"?.5:null),!c.length)return;h=u.getData().get(f[0],c[0])}if(!(h==null||!isFinite(h))){var d=r-h,y=Math.abs(d);y<=o&&((y<o||d>=0&&s<0)&&(o=y,s=d,n=h,a.length=0),M(c,function(p){a.push({seriesIndex:u.seriesIndex,dataIndexInside:p,dataIndex:u.getData().getRawIndex(p)})}))}}),{payloadBatch:a,snapToValue:n}}function gD(r,t,e,i){r[t.key]={value:e,payloadBatch:i}}function yD(r,t,e,i){var n=e.payloadBatch,a=t.axis,o=a.model,s=t.axisPointerModel;if(!(!t.triggerTooltip||!n.length)){var u=t.coordSys.model,l=kn(u),f=r.map[l];f||(f=r.map[l]={coordSysId:u.id,coordSysIndex:u.componentIndex,coordSysType:u.type,coordSysMainType:u.mainType,dataByAxis:[]},r.list.push(f)),f.dataByAxis.push({axisDim:a.dim,axisIndex:o.componentIndex,axisType:o.type,axisId:o.id,value:i,valueLabelOpt:{precision:s.get(["label","precision"]),formatter:s.get(["label","formatter"])},seriesDataIndices:n.slice()})}}function mD(r,t,e){var i=e.axesInfo=[];M(t,function(n,a){var o=n.axisPointerModel.option,s=r[a];s?(!n.useHandle&&(o.status="show"),o.value=s.value,o.seriesDataIndices=(s.payloadBatch||[]).slice()):!n.useHandle&&(o.status="hide"),o.status==="show"&&i.push({axisDim:n.axis.dim,axisIndex:n.axis.model.componentIndex,value:o.value})})}function _D(r,t,e,i){if(Va(t)||!r.list.length){i({type:"hideTip"});return}var n=((r.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:t[0],y:t[1],tooltipOption:e.tooltipOption,position:e.position,dataIndexInside:n.dataIndexInside,dataIndex:n.dataIndex,seriesIndex:n.seriesIndex,dataByCoordSys:r.list})}function SD(r,t,e){var i=e.getZr(),n="axisPointerLastHighlights",a=Tc(i)[n]||{},o=Tc(i)[n]={};M(r,function(l,f){var h=l.axisPointerModel.option;h.status==="show"&&l.triggerEmphasis&&M(h.seriesDataIndices,function(c){var v=c.seriesIndex+" | "+c.dataIndex;o[v]=c})});var s=[],u=[];M(a,function(l,f){!o[f]&&u.push(l)}),M(o,function(l,f){!a[f]&&s.push(l)}),u.length&&e.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:u}),s.length&&e.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:s})}function wD(r,t){for(var e=0;e<(r||[]).length;e++){var i=r[e];if(t.axis.dim===i.axisDim&&t.axis.model.componentIndex===i.axisIndex)return i}}function Mc(r){var t=r.axis.model,e={},i=e.axisDim=r.axis.dim;return e.axisIndex=e[i+"AxisIndex"]=t.componentIndex,e.axisName=e[i+"AxisName"]=t.name,e.axisId=e[i+"AxisId"]=t.id,e}function Va(r){return!r||r[0]==null||isNaN(r[0])||r[1]==null||isNaN(r[1])}function Ay(r){Sy.registerAxisPointerClass("CartesianAxisPointer",nD),r.registerComponentModel(oD),r.registerComponentView(cD),r.registerPreprocessor(function(t){if(t){(!t.axisPointer||t.axisPointer.length===0)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!B(e)&&(t.axisPointer.link=[e])}}),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=EM(t,e)}),r.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},dD)}function bD(r){En(XM),En(Ay)}var xD=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="tooltip",t.dependencies=["axisPointer"],t.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}},t}(ft);const TD=xD;function Ly(r){var t=r.get("confine");return t!=null?!!t:r.get("renderMode")==="richText"}function Py(r){if($.domSupported){for(var t=document.documentElement.style,e=0,i=r.length;e<i;e++)if(r[e]in t)return r[e]}}var Iy=Py(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),CD=Py(["webkitTransition","transition","OTransition","MozTransition","msTransition"]);function Ey(r,t){if(!r)return t;t=Rp(t,!0);var e=r.indexOf(t);return r=e===-1?t:"-"+r.slice(0,e)+"-"+t,r.toLowerCase()}function MD(r,t){var e=r.currentStyle||document.defaultView&&document.defaultView.getComputedStyle(r);return e?t?e[t]:e:null}var DD=Ey(CD,"transition"),rf=Ey(Iy,"transform"),AD="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+($.transform3dSupported?"will-change:transform;":"");function LD(r){return r=r==="left"?"right":r==="right"?"left":r==="top"?"bottom":"top",r}function PD(r,t,e){if(!z(e)||e==="inside")return"";var i=r.get("backgroundColor"),n=r.get("borderWidth");t=Gr(t);var a=LD(e),o=Math.max(Math.round(n)*1.5,6),s="",u=rf+":",l;nt(["left","right"],a)>-1?(s+="top:50%",u+="translateY(-50%) rotate("+(l=a==="left"?-225:-45)+"deg)"):(s+="left:50%",u+="translateX(-50%) rotate("+(l=a==="top"?225:45)+"deg)");var f=l*Math.PI/180,h=o+n,c=h*Math.abs(Math.cos(f))+h*Math.abs(Math.sin(f)),v=Math.round(((c-Math.SQRT2*n)/2+Math.SQRT2*n-(c-h)/2)*100)/100;s+=";"+a+":-"+v+"px";var d=t+" solid "+n+"px;",y=["position:absolute;width:"+o+"px;height:"+o+"px;z-index:-1;",s+";"+u+";","border-bottom:"+d,"border-right:"+d,"background-color:"+i+";"];return'<div style="'+y.join("")+'"></div>'}function ID(r,t){var e="cubic-bezier(0.23,1,0.32,1)",i=" "+r/2+"s "+e,n="opacity"+i+",visibility"+i;return t||(i=" "+r+"s "+e,n+=$.transformSupported?","+rf+i:",left"+i+",top"+i),DD+":"+n}function Dc(r,t,e){var i=r.toFixed(0)+"px",n=t.toFixed(0)+"px";if(!$.transformSupported)return e?"top:"+n+";left:"+i+";":[["top",n],["left",i]];var a=$.transform3dSupported,o="translate"+(a?"3d":"")+"("+i+","+n+(a?",0":"")+")";return e?"top:0;left:0;"+rf+":"+o+";":[["top",0],["left",0],[Iy,o]]}function ED(r){var t=[],e=r.get("fontSize"),i=r.getTextColor();i&&t.push("color:"+i),t.push("font:"+r.getFont());var n=X(r.get("lineHeight"),Math.round(e*3/2));e&&t.push("line-height:"+n+"px");var a=r.get("textShadowColor"),o=r.get("textShadowBlur")||0,s=r.get("textShadowOffsetX")||0,u=r.get("textShadowOffsetY")||0;return a&&o&&t.push("text-shadow:"+s+"px "+u+"px "+o+"px "+a),M(["decoration","align"],function(l){var f=r.get(l);f&&t.push("text-"+l+":"+f)}),t.join(";")}function RD(r,t,e){var i=[],n=r.get("transitionDuration"),a=r.get("backgroundColor"),o=r.get("shadowBlur"),s=r.get("shadowColor"),u=r.get("shadowOffsetX"),l=r.get("shadowOffsetY"),f=r.getModel("textStyle"),h=sg(r,"html"),c=u+"px "+l+"px "+o+"px "+s;return i.push("box-shadow:"+c),t&&n&&i.push(ID(n,e)),a&&i.push("background-color:"+a),M(["width","color","radius"],function(v){var d="border-"+v,y=Rp(d),p=r.get(y);p!=null&&i.push(d+":"+p+(v==="color"?"":"px"))}),i.push(ED(f)),h!=null&&i.push("padding:"+Rl(h).join("px ")+"px"),i.join(";")+";"}function Ac(r,t,e,i,n){var a=t&&t.painter;if(e){var o=a&&a.getViewportRoot();o&&Tm(r,o,e,i,n)}else{r[0]=i,r[1]=n;var s=a&&a.getViewportRootOffset();s&&(r[0]+=s.offsetLeft,r[1]+=s.offsetTop)}r[2]=r[0]/t.getWidth(),r[3]=r[1]/t.getHeight()}var kD=function(){function r(t,e){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._alwaysShowContent=!1,this._firstShow=!0,this._longHide=!0,$.wxa)return null;var i=document.createElement("div");i.domBelongToZr=!0,this.el=i;var n=this._zr=t.getZr(),a=e.appendTo,o=a&&(z(a)?document.querySelector(a):gn(a)?a:U(a)&&a(t.getDom()));Ac(this._styleCoord,n,o,t.getWidth()/2,t.getHeight()/2),(o||t.getDom()).appendChild(i),this._api=t,this._container=o;var s=this;i.onmouseenter=function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0},i.onmousemove=function(u){if(u=u||window.event,!s._enterable){var l=n.handler,f=n.painter.getViewportRoot();Zt(f,u,!0),l.dispatch("mousemove",u)}},i.onmouseleave=function(){s._inContent=!1,s._enterable&&s._show&&s.hideLater(s._hideDelay)}}return r.prototype.update=function(t){if(!this._container){var e=this._api.getDom(),i=MD(e,"position"),n=e.style;n.position!=="absolute"&&i!=="absolute"&&(n.position="relative")}var a=t.get("alwaysShowContent");a&&this._moveIfResized(),this._alwaysShowContent=a,this.el.className=t.get("className")||""},r.prototype.show=function(t,e){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var i=this.el,n=i.style,a=this._styleCoord;i.innerHTML?n.cssText=AD+RD(t,!this._firstShow,this._longHide)+Dc(a[0],a[1],!0)+("border-color:"+Gr(e)+";")+(t.get("extraCssText")||"")+(";pointer-events:"+(this._enterable?"auto":"none")):n.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},r.prototype.setContent=function(t,e,i,n,a){var o=this.el;if(t==null){o.innerHTML="";return}var s="";if(z(a)&&i.get("trigger")==="item"&&!Ly(i)&&(s=PD(i,n,a)),z(t))o.innerHTML=t+s;else if(t){o.innerHTML="",B(t)||(t=[t]);for(var u=0;u<t.length;u++)gn(t[u])&&t[u].parentNode!==o&&o.appendChild(t[u]);if(s&&o.childNodes.length){var l=document.createElement("div");l.innerHTML=s,o.appendChild(l)}}},r.prototype.setEnterable=function(t){this._enterable=t},r.prototype.getSize=function(){var t=this.el;return t?[t.offsetWidth,t.offsetHeight]:[0,0]},r.prototype.moveTo=function(t,e){if(this.el){var i=this._styleCoord;if(Ac(i,this._zr,this._container,t,e),i[0]!=null&&i[1]!=null){var n=this.el.style,a=Dc(i[0],i[1]);M(a,function(o){n[o[0]]=o[1]})}}},r.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},r.prototype.hide=function(){var t=this,e=this.el.style;e.visibility="hidden",e.opacity="0",$.transform3dSupported&&(e.willChange=""),this._show=!1,this._longHideTimeout=setTimeout(function(){return t._longHide=!0},500)},r.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(ht(this.hide,this),t)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var t=this.el.parentNode;t&&t.removeChild(this.el),this.el=this._container=null},r}();const OD=kD;var BD=function(){function r(t){this._show=!1,this._styleCoord=[0,0,0,0],this._alwaysShowContent=!1,this._enterable=!0,this._zr=t.getZr(),Pc(this._styleCoord,this._zr,t.getWidth()/2,t.getHeight()/2)}return r.prototype.update=function(t){var e=t.get("alwaysShowContent");e&&this._moveIfResized(),this._alwaysShowContent=e},r.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},r.prototype.setContent=function(t,e,i,n,a){var o=this;H(t)&&Rt(""),this.el&&this._zr.remove(this.el);var s=i.getModel("textStyle");this.el=new ie({style:{rich:e.richTextStyles,text:t,lineHeight:22,borderWidth:1,borderColor:n,textShadowColor:s.get("textShadowColor"),fill:i.get(["textStyle","color"]),padding:sg(i,"richText"),verticalAlign:"top",align:"left"},z:i.get("z")}),M(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],function(l){o.el.style[l]=i.get(l)}),M(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],function(l){o.el.style[l]=s.get(l)||0}),this._zr.add(this.el);var u=this;this.el.on("mouseover",function(){u._enterable&&(clearTimeout(u._hideTimeout),u._show=!0),u._inContent=!0}),this.el.on("mouseout",function(){u._enterable&&u._show&&u.hideLater(u._hideDelay),u._inContent=!1})},r.prototype.setEnterable=function(t){this._enterable=t},r.prototype.getSize=function(){var t=this.el,e=this.el.getBoundingRect(),i=Lc(t.style);return[e.width+i.left+i.right,e.height+i.top+i.bottom]},r.prototype.moveTo=function(t,e){var i=this.el;if(i){var n=this._styleCoord;Pc(n,this._zr,t,e),t=n[0],e=n[1];var a=i.style,o=Xe(a.borderWidth||0),s=Lc(a);i.x=t+o+s.left,i.y=e+o+s.top,i.markRedraw()}},r.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},r.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},r.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(ht(this.hide,this),t)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){this._zr.remove(this.el)},r}();function Xe(r){return Math.max(0,r)}function Lc(r){var t=Xe(r.shadowBlur||0),e=Xe(r.shadowOffsetX||0),i=Xe(r.shadowOffsetY||0);return{left:Xe(t-e),right:Xe(t+e),top:Xe(t-i),bottom:Xe(t+i)}}function Pc(r,t,e,i){r[0]=e,r[1]=i,r[2]=r[0]/t.getWidth(),r[3]=r[1]/t.getHeight()}const ND=BD;var FD=new Bt({shape:{x:-1,y:-1,width:2,height:2}}),zD=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.init=function(e,i){if(!($.node||!i.getDom())){var n=e.getComponent("tooltip"),a=this._renderMode=a_(n.get("renderMode"));this._tooltipContent=a==="richText"?new ND(i):new OD(i,{appendTo:n.get("appendToBody",!0)?"body":n.get("appendTo",!0)})}},t.prototype.render=function(e,i,n){if(!($.node||!n.getDom())){this.group.removeAll(),this._tooltipModel=e,this._ecModel=i,this._api=n;var a=this._tooltipContent;a.update(e),a.setEnterable(e.get("enterable")),this._initGlobalListener(),this._keepShow(),this._renderMode!=="richText"&&e.get("transitionDuration")?fg(this,"_updatePosition",50,"fixRate"):ku(this,"_updatePosition")}},t.prototype._initGlobalListener=function(){var e=this._tooltipModel,i=e.get("triggerOn");My("itemTooltip",this._api,ht(function(n,a,o){i!=="none"&&(i.indexOf(n)>=0?this._tryShow(a,o):n==="leave"&&this._hide(o))},this))},t.prototype._keepShow=function(){var e=this._tooltipModel,i=this._ecModel,n=this._api,a=e.get("triggerOn");if(this._lastX!=null&&this._lastY!=null&&a!=="none"&&a!=="click"){var o=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){!n.isDisposed()&&o.manuallyShowTip(e,i,n,{x:o._lastX,y:o._lastY,dataByCoordSys:o._lastDataByCoordSys})})}},t.prototype.manuallyShowTip=function(e,i,n,a){if(!(a.from===this.uid||$.node||!n.getDom())){var o=Ic(a,n);this._ticket="";var s=a.dataByCoordSys,u=WD(a,i,n);if(u){var l=u.el.getBoundingRect().clone();l.applyTransform(u.el.transform),this._tryShow({offsetX:l.x+l.width/2,offsetY:l.y+l.height/2,target:u.el,position:a.position,positionDefault:"bottom"},o)}else if(a.tooltip&&a.x!=null&&a.y!=null){var f=FD;f.x=a.x,f.y=a.y,f.update(),it(f).tooltipConfig={name:null,option:a.tooltip},this._tryShow({offsetX:a.x,offsetY:a.y,target:f},o)}else if(s)this._tryShow({offsetX:a.x,offsetY:a.y,position:a.position,dataByCoordSys:s,tooltipOption:a.tooltipOption},o);else if(a.seriesIndex!=null){if(this._manuallyAxisShowTip(e,i,n,a))return;var h=Dy(a,i),c=h.point[0],v=h.point[1];c!=null&&v!=null&&this._tryShow({offsetX:c,offsetY:v,target:h.el,position:a.position,positionDefault:"bottom"},o)}else a.x!=null&&a.y!=null&&(n.dispatchAction({type:"updateAxisPointer",x:a.x,y:a.y}),this._tryShow({offsetX:a.x,offsetY:a.y,position:a.position,target:n.getZr().findHover(a.x,a.y).target},o))}},t.prototype.manuallyHideTip=function(e,i,n,a){var o=this._tooltipContent;this._tooltipModel&&o.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,a.from!==this.uid&&this._hide(Ic(a,n))},t.prototype._manuallyAxisShowTip=function(e,i,n,a){var o=a.seriesIndex,s=a.dataIndex,u=i.getComponent("axisPointer").coordSysAxesInfo;if(!(o==null||s==null||u==null)){var l=i.getSeriesByIndex(o);if(l){var f=l.getData(),h=Zi([f.getItemModel(s),l,(l.coordinateSystem||{}).model],this._tooltipModel);if(h.get("trigger")==="axis")return n.dispatchAction({type:"updateAxisPointer",seriesIndex:o,dataIndex:s,position:a.position}),!0}}},t.prototype._tryShow=function(e,i){var n=e.target,a=this._tooltipModel;if(a){this._lastX=e.offsetX,this._lastY=e.offsetY;var o=e.dataByCoordSys;if(o&&o.length)this._showAxisTooltip(o,e);else if(n){var s=it(n);if(s.ssrType==="legend")return;this._lastDataByCoordSys=null;var u,l;rn(n,function(f){if(it(f).dataIndex!=null)return u=f,!0;if(it(f).tooltipConfig!=null)return l=f,!0},!0),u?this._showSeriesItemTooltip(e,u,i):l?this._showComponentItemTooltip(e,l,i):this._hide(i)}else this._lastDataByCoordSys=null,this._hide(i)}},t.prototype._showOrMove=function(e,i){var n=e.get("showDelay");i=ht(i,this),clearTimeout(this._showTimout),n>0?this._showTimout=setTimeout(i,n):i()},t.prototype._showAxisTooltip=function(e,i){var n=this._ecModel,a=this._tooltipModel,o=[i.offsetX,i.offsetY],s=Zi([i.tooltipOption],a),u=this._renderMode,l=[],f=Mn("section",{blocks:[],noHeader:!0}),h=[],c=new Is;M(e,function(m){M(m.dataByAxis,function(_){var S=n.getComponent(_.axisDim+"Axis",_.axisIndex),b=_.value;if(!(!S||b==null)){var w=Ty(b,S.axis,n,_.seriesDataIndices,_.valueLabelOpt),x=Mn("section",{header:w,noHeader:!me(w),sortBlocks:!0,blocks:[]});f.blocks.push(x),M(_.seriesDataIndices,function(D){var T=n.getSeriesByIndex(D.seriesIndex),C=D.dataIndexInside,A=T.getDataParams(C);if(!(A.dataIndex<0)){A.axisDim=_.axisDim,A.axisIndex=_.axisIndex,A.axisType=_.axisType,A.axisId=_.axisId,A.axisValue=Ql(S.axis,{value:b}),A.axisValueLabel=w,A.marker=c.makeTooltipMarker("item",Gr(A.color),u);var L=tv(T.formatTooltip(C,!0,null)),I=L.frag;if(I){var P=Zi([T],a).get("valueFormatter");x.blocks.push(P?k({valueFormatter:P},I):I)}L.text&&h.push(L.text),l.push(A)}})}})}),f.blocks.reverse(),h.reverse();var v=i.position,d=s.get("order"),y=av(f,c,u,d,n.get("useUTC"),s.get("textStyle"));y&&h.unshift(y);var p=u==="richText"?`

`:"<br/>",g=h.join(p);this._showOrMove(s,function(){this._updateContentNotChangedOnAxis(e,l)?this._updatePosition(s,v,o[0],o[1],this._tooltipContent,l):this._showTooltipContent(s,g,l,Math.random()+"",o[0],o[1],v,null,c)})},t.prototype._showSeriesItemTooltip=function(e,i,n){var a=this._ecModel,o=it(i),s=o.seriesIndex,u=a.getSeriesByIndex(s),l=o.dataModel||u,f=o.dataIndex,h=o.dataType,c=l.getData(h),v=this._renderMode,d=e.positionDefault,y=Zi([c.getItemModel(f),l,u&&(u.coordinateSystem||{}).model],this._tooltipModel,d?{position:d}:null),p=y.get("trigger");if(!(p!=null&&p!=="item")){var g=l.getDataParams(f,h),m=new Is;g.marker=m.makeTooltipMarker("item",Gr(g.color),v);var _=tv(l.formatTooltip(f,!1,h)),S=y.get("order"),b=y.get("valueFormatter"),w=_.frag,x=w?av(b?k({valueFormatter:b},w):w,m,v,S,a.get("useUTC"),y.get("textStyle")):_.text,D="item_"+l.name+"_"+f;this._showOrMove(y,function(){this._showTooltipContent(y,x,g,D,e.offsetX,e.offsetY,e.position,e.target,m)}),n({type:"showTip",dataIndexInside:f,dataIndex:c.getRawIndex(f),seriesIndex:s,from:this.uid})}},t.prototype._showComponentItemTooltip=function(e,i,n){var a=this._renderMode==="html",o=it(i),s=o.tooltipConfig,u=s.option||{},l=u.encodeHTMLContent;if(z(u)){var f=u;u={content:f,formatter:f},l=!0}l&&a&&u.content&&(u=K(u),u.content=Et(u.content));var h=[u],c=this._ecModel.getComponent(o.componentMainType,o.componentIndex);c&&h.push(c),h.push({formatter:u.content});var v=e.positionDefault,d=Zi(h,this._tooltipModel,v?{position:v}:null),y=d.get("content"),p=Math.random()+"",g=new Is;this._showOrMove(d,function(){var m=K(d.get("formatterParams")||{});this._showTooltipContent(d,y,m,p,e.offsetX,e.offsetY,e.position,i,g)}),n({type:"showTip",from:this.uid})},t.prototype._showTooltipContent=function(e,i,n,a,o,s,u,l,f){if(this._ticket="",!(!e.get("showContent")||!e.get("show"))){var h=this._tooltipContent;h.setEnterable(e.get("enterable"));var c=e.get("formatter");u=u||e.get("position");var v=i,d=this._getNearestPoint([o,s],n,e.get("trigger"),e.get("borderColor")),y=d.color;if(c)if(z(c)){var p=e.ecModel.get("useUTC"),g=B(n)?n[0]:n,m=g&&g.axisType&&g.axisType.indexOf("time")>=0;v=c,m&&(v=xo(g.axisValue,v,p)),v=kp(v,n,!0)}else if(U(c)){var _=ht(function(S,b){S===this._ticket&&(h.setContent(b,f,e,y,u),this._updatePosition(e,u,o,s,h,n,l))},this);this._ticket=a,v=c(n,a,_)}else v=c;h.setContent(v,f,e,y,u),h.show(e,y),this._updatePosition(e,u,o,s,h,n,l)}},t.prototype._getNearestPoint=function(e,i,n,a){if(n==="axis"||B(i))return{color:a||(this._renderMode==="html"?"#fff":"none")};if(!B(i))return{color:a||i.color||i.borderColor}},t.prototype._updatePosition=function(e,i,n,a,o,s,u){var l=this._api.getWidth(),f=this._api.getHeight();i=i||e.get("position");var h=o.getSize(),c=e.get("align"),v=e.get("verticalAlign"),d=u&&u.getBoundingRect().clone();if(u&&d.applyTransform(u.transform),U(i)&&(i=i([n,a],s,o.el,d,{viewSize:[l,f],contentSize:h.slice()})),B(i))n=Dt(i[0],l),a=Dt(i[1],f);else if(H(i)){var y=i;y.width=h[0],y.height=h[1];var p=kl(y,{width:l,height:f});n=p.x,a=p.y,c=null,v=null}else if(z(i)&&u){var g=GD(i,d,h,e.get("borderWidth"));n=g[0],a=g[1]}else{var g=HD(n,a,o,l,f,c?null:20,v?null:20);n=g[0],a=g[1]}if(c&&(n-=Ec(c)?h[0]/2:c==="right"?h[0]:0),v&&(a-=Ec(v)?h[1]/2:v==="bottom"?h[1]:0),Ly(e)){var g=VD(n,a,o,l,f);n=g[0],a=g[1]}o.moveTo(n,a)},t.prototype._updateContentNotChangedOnAxis=function(e,i){var n=this._lastDataByCoordSys,a=this._cbParamsList,o=!!n&&n.length===e.length;return o&&M(n,function(s,u){var l=s.dataByAxis||[],f=e[u]||{},h=f.dataByAxis||[];o=o&&l.length===h.length,o&&M(l,function(c,v){var d=h[v]||{},y=c.seriesDataIndices||[],p=d.seriesDataIndices||[];o=o&&c.value===d.value&&c.axisType===d.axisType&&c.axisId===d.axisId&&y.length===p.length,o&&M(y,function(g,m){var _=p[m];o=o&&g.seriesIndex===_.seriesIndex&&g.dataIndex===_.dataIndex}),a&&M(c.seriesDataIndices,function(g){var m=g.seriesIndex,_=i[m],S=a[m];_&&S&&S.data!==_.data&&(o=!1)})})}),this._lastDataByCoordSys=e,this._cbParamsList=i,!!o},t.prototype._hide=function(e){this._lastDataByCoordSys=null,e({type:"hideTip",from:this.uid})},t.prototype.dispose=function(e,i){$.node||!i.getDom()||(ku(this,"_updatePosition"),this._tooltipContent.dispose(),Ku("itemTooltip",i))},t.type="tooltip",t}(er);function Zi(r,t,e){var i=t.ecModel,n;e?(n=new At(e,i,i),n=new At(t.option,n,i)):n=t;for(var a=r.length-1;a>=0;a--){var o=r[a];o&&(o instanceof At&&(o=o.get("tooltip",!0)),z(o)&&(o={formatter:o}),o&&(n=new At(o,n,i)))}return n}function Ic(r,t){return r.dispatchAction||ht(t.dispatchAction,t)}function HD(r,t,e,i,n,a,o){var s=e.getSize(),u=s[0],l=s[1];return a!=null&&(r+u+a+2>i?r-=u+a:r+=a),o!=null&&(t+l+o>n?t-=l+o:t+=o),[r,t]}function VD(r,t,e,i,n){var a=e.getSize(),o=a[0],s=a[1];return r=Math.min(r+o,i)-o,t=Math.min(t+s,n)-s,r=Math.max(r,0),t=Math.max(t,0),[r,t]}function GD(r,t,e,i){var n=e[0],a=e[1],o=Math.ceil(Math.SQRT2*i)+8,s=0,u=0,l=t.width,f=t.height;switch(r){case"inside":s=t.x+l/2-n/2,u=t.y+f/2-a/2;break;case"top":s=t.x+l/2-n/2,u=t.y-a-o;break;case"bottom":s=t.x+l/2-n/2,u=t.y+f+o;break;case"left":s=t.x-n-o,u=t.y+f/2-a/2;break;case"right":s=t.x+l+o,u=t.y+f/2-a/2}return[s,u]}function Ec(r){return r==="center"||r==="middle"}function WD(r,t,e){var i=cl(r).queryOptionMap,n=i.keys()[0];if(!(!n||n==="series")){var a=Fn(t,n,i.get(n),{useDefault:!1,enableAll:!1,enableNone:!1}),o=a.models[0];if(o){var s=e.getViewOfComponentModel(o),u;if(s.group.traverse(function(l){var f=it(l).tooltipConfig;if(f&&f.name===r.name)return u=l,!0}),u)return{componentMainType:n,componentIndex:o.componentIndex,el:u}}}}const UD=zD;function YD(r){En(Ay),r.registerComponentModel(TD),r.registerComponentView(UD),r.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},Ot),r.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},Ot)}var $D=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.layoutMode={type:"box",ignoreSize:!0},e}return t.type="title",t.defaultOption={z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bold",color:"#464646"},subtextStyle:{fontSize:12,color:"#6E7079"}},t}(ft),XD=function(r){O(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n){if(this.group.removeAll(),!!e.get("show")){var a=this.group,o=e.getModel("textStyle"),s=e.getModel("subtextStyle"),u=e.get("textAlign"),l=X(e.get("textBaseline"),e.get("textVerticalAlign")),f=new ie({style:Vr(o,{text:e.get("text"),fill:o.getTextColor()},{disableBox:!0}),z2:10}),h=f.getBoundingRect(),c=e.get("subtext"),v=new ie({style:Vr(s,{text:c,fill:s.getTextColor(),y:h.height+e.get("itemGap"),verticalAlign:"top"},{disableBox:!0}),z2:10}),d=e.get("link"),y=e.get("sublink"),p=e.get("triggerEvent",!0);f.silent=!d&&!p,v.silent=!y&&!p,d&&f.on("click",function(){Bh(d,"_"+e.get("target"))}),y&&v.on("click",function(){Bh(y,"_"+e.get("subtarget"))}),it(f).eventData=it(v).eventData=p?{componentType:"title",componentIndex:e.componentIndex}:null,a.add(f),c&&a.add(v);var g=a.getBoundingRect(),m=e.getBoxLayoutParams();m.width=g.width,m.height=g.height;var _=kl(m,{width:n.getWidth(),height:n.getHeight()},e.get("padding"));u||(u=e.get("left")||e.get("right"),u==="middle"&&(u="center"),u==="right"?_.x+=_.width:u==="center"&&(_.x+=_.width/2)),l||(l=e.get("top")||e.get("bottom"),l==="center"&&(l="middle"),l==="bottom"?_.y+=_.height:l==="middle"&&(_.y+=_.height/2),l=l||"top"),a.x=_.x,a.y=_.y,a.markRedraw();var S={align:u,verticalAlign:l};f.setStyle(S),v.setStyle(S),g=a.getBoundingRect();var b=_.margin,w=e.getItemStyle(["color","opacity"]);w.fill=e.get("backgroundColor");var x=new Bt({shape:{x:g.x-b[3],y:g.y-b[0],width:g.width+b[1]+b[3],height:g.height+b[0]+b[2],r:e.get("borderRadius")},style:w,subPixelOptimize:!0,silent:!0});a.add(x)}},t.type="title",t}(er);function ZD(r){r.registerComponentModel($D),r.registerComponentView(XD)}const qD=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function KD(r){function t(i){return(...n)=>{if(!r.value)throw new Error("ECharts is not initialized yet.");return r.value[i].apply(r.value,n)}}function e(){const i=Object.create(null);return qD.forEach(n=>{i[n]=t(n)}),i}return e()}function QD(r,t,e){Ma([e,r,t],([i,n,a],o,s)=>{let u=null;if(i&&n&&a){const{offsetWidth:l,offsetHeight:f}=i,h=a===!0?{}:a,{throttle:c=100,onResize:v}=h;let d=!1;const y=()=>{n.resize(),v==null||v()},p=c?Ul(y,c):y;u=new ResizeObserver(()=>{!d&&(d=!0,i.offsetWidth===l&&i.offsetHeight===f)||p()}),u.observe(i)}s(()=>{u&&(u.disconnect(),u=null)})})}const JD={autoresize:[Boolean,Object]},jD=/^on[^a-z]/,Ry=r=>jD.test(r);function tA(r){const t={};for(const e in r)Ry(e)||(t[e]=r[e]);return t}function Ga(r,t){const e=Vy(r)?Oc(r):r;return e&&typeof e=="object"&&"value"in e?e.value||t:e||t}const eA="ecLoadingOptions";function rA(r,t,e){const i=Ca(eA,{}),n=Mr(()=>({...Ga(i,{}),...e==null?void 0:e.value}));kc(()=>{const a=r.value;a&&(t.value?a.showLoading(n.value):a.hideLoading())})}const iA={loading:Boolean,loadingOptions:Object};let qi=null;const ky="x-vue-echarts";function nA(){if(qi!=null)return qi;if(typeof HTMLElement>"u"||typeof customElements>"u")return qi=!1;try{new Function("tag","class EChartsElement extends HTMLElement{__dispose=null;disconnectedCallback(){this.__dispose&&(this.__dispose(),this.__dispose=null)}}customElements.get(tag)==null&&customElements.define(tag,EChartsElement);")(ky)}catch{return qi=!1}return qi=!0}document.head.appendChild(document.createElement("style")).textContent=`x-vue-echarts{display:block;width:100%;height:100%;min-width:0}
`;const aA=nA(),oA="ecTheme",sA="ecInitOptions",uA="ecUpdateOptions",Rc=/(^&?~?!?)native:/;var lA=Oy({name:"echarts",props:{option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean,...JD,...iA},emits:{},inheritAttrs:!1,setup(r,{attrs:t}){const e=Oo(),i=Oo(),n=Oo(),a=Ca(oA,null),o=Ca(sA,null),s=Ca(uA,null),{autoresize:u,manualUpdate:l,loading:f,loadingOptions:h}=By(r),c=Mr(()=>n.value||r.option||null),v=Mr(()=>r.theme||Ga(a,{})),d=Mr(()=>r.initOptions||Ga(o,{})),y=Mr(()=>r.updateOptions||Ga(s,{})),p=Mr(()=>tA(t)),g={},m=Ny().proxy.$listeners,_={};m?Object.keys(m).forEach(T=>{Rc.test(T)?g[T.replace(Rc,"$1")]=m[T]:_[T]=m[T]}):Object.keys(t).filter(T=>Ry(T)).forEach(T=>{let C=T.charAt(2).toLowerCase()+T.slice(3);if(C.indexOf("native:")===0){const A=`on${C.charAt(7).toUpperCase()}${C.slice(8)}`;g[A]=t[T];return}C.substring(C.length-4)==="Once"&&(C=`~${C.substring(0,C.length-4)}`),_[C]=t[T]});function S(T){if(!e.value)return;const C=i.value=Ux(e.value,v.value,d.value);r.group&&(C.group=r.group),Object.keys(_).forEach(I=>{let P=_[I];if(!P)return;let E=I.toLowerCase();E.charAt(0)==="~"&&(E=E.substring(1),P.__once__=!0);let R=C;if(E.indexOf("zr:")===0&&(R=C.getZr(),E=E.substring(3)),P.__once__){delete P.__once__;const V=P;P=(...N)=>{V(...N),R.off(E,P)}}R.on(E,P)});function A(){C&&!C.isDisposed()&&C.resize()}function L(){const I=T||c.value;I&&C.setOption(I,y.value)}u.value?Gy(()=>{A(),L()}):L()}function b(T,C){r.manualUpdate&&(n.value=T),i.value?i.value.setOption(T,C||{}):S(T)}function w(){i.value&&(i.value.dispose(),i.value=void 0)}let x=null;Ma(l,T=>{typeof x=="function"&&(x(),x=null),T||(x=Ma(()=>r.option,(C,A)=>{C&&(i.value?i.value.setOption(C,{notMerge:C!==A,...y.value}):S())},{deep:!0}))},{immediate:!0}),Ma([v,d],()=>{w(),S()},{deep:!0}),kc(()=>{r.group&&i.value&&(i.value.group=r.group)});const D=KD(i);return rA(i,f,h),QD(i,u,e),Fy(()=>{S()}),zy(()=>{aA&&e.value?e.value.__dispose=w:w()}),{chart:i,root:e,setOption:b,nonEventAttrs:p,nativeListeners:g,...D}},render(){const r={...this.nonEventAttrs,...this.nativeListeners};return r.ref="root",r.class=r.class?["echarts"].concat(r.class):"echarts",Hy(ky,r)}});const fA={class:"progress-chart-container"},hA={class:"progress-content"},vA={key:0,class:"progress-loading"},cA={key:1,class:"chart-wrapper"},dA={__name:"ProgressChart",props:{userProgress:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(r){En([EC,fM,ZD,YD,bD]);const t=r,e=[{key:"0",name:"创建简历",index:0},{key:"1",name:"知识学习",index:1},{key:"2",name:"HR模拟面试",index:2},{key:"3",name:"技术模拟面试",index:3},{key:"4",name:"正式面试",index:4}],i=()=>({currentStage:"1",userId:1}),n=Mr(()=>{var s;const a=t.userProgress||i(),o=a&&((s=e.find(u=>u.key===a.currentStage))==null?void 0:s.index)||0;return{tooltip:{trigger:"item",formatter:function(u){const l=e[u.dataIndex];let f="未完成";return u.dataIndex<o?f="已完成":u.dataIndex===o&&(f="当前进度"),`${l.name}<br/>状态: ${f}`}},grid:{left:"5%",right:"5%",top:"10%",bottom:"20%"},xAxis:{type:"category",data:e.map(u=>u.name),axisLabel:{interval:0,rotate:0,fontSize:11,color:"#666"},axisLine:{lineStyle:{color:"#e0e0e0"}}},yAxis:{type:"value",show:!1,min:0,max:100},series:[{name:"进度",type:"line",data:e.map((u,l)=>l<o?100:l===o?50:0),lineStyle:{width:3,color:"#409EFF"},symbol:"circle",symbolSize:10,itemStyle:{color:function(u){return u.dataIndex<o?"#67C23A":u.dataIndex===o?"#E6A23C":"#C0C4CC"}}}]}});return(a,o)=>{const s=Wy("el-skeleton");return Bo(),No("div",fA,[o[1]||(o[1]=Gn("div",{class:"progress-header"},[Gn("h3",null,"我的求职进度"),Gn("p",{class:"progress-desc"},"跟踪您的求职进展，一步步走向成功")],-1)),Gn("div",hA,[r.loading?(Bo(),No("div",vA,[of(s,{rows:3,animated:""})])):(Bo(),No("div",cA,[of(Oc(lA),{class:"chart",option:n.value,autoresize:""},null,8,["option"]),o[0]||(o[0]=Uy('<div class="progress-legend" data-v-7a5857be><div class="legend-item" data-v-7a5857be><span class="legend-dot completed" data-v-7a5857be></span><span class="legend-text" data-v-7a5857be>已完成</span></div><div class="legend-item" data-v-7a5857be><span class="legend-dot current" data-v-7a5857be></span><span class="legend-text" data-v-7a5857be>当前进度</span></div><div class="legend-item" data-v-7a5857be><span class="legend-dot pending" data-v-7a5857be></span><span class="legend-text" data-v-7a5857be>未完成</span></div></div>',1))]))])])}}},yA=Yy(dA,[["__scopeId","data-v-7a5857be"]]);export{yA as P};
