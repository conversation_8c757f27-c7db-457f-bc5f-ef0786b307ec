package com.bimowu.unified.login.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * SSO客户端应用信息
 */
@Data
public class SsoClient implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 客户端ID
     */
    private String clientId;
    
    /**
     * 客户端密钥
     */
    private String clientSecret;
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 应用URL
     */
    private String appUrl;
    
    /**
     * 回调URL
     */
    private String redirectUrl;
    
    /**
     * 退出登录回调URL
     */
    private String logoutUrl;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 状态：0-正常，1-禁用
     */
    private Integer status;
} 