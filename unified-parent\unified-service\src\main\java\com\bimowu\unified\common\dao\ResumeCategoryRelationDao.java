package com.bimowu.unified.common.dao;

import com.bimowu.unified.common.model.ResumeCategoryRelation;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 简历与职位类别关联数据库操作接口类
 */
@Repository
public interface ResumeCategoryRelationDao {
    
    /**
     * 根据用户ID和职位类别ID查询关联记录
     */
    ResumeCategoryRelation selectByUserIdAndCatId(@Param("userId") Long userId, @Param("catId") Long catId);
    
    /**
     * 根据用户ID查询所有关联记录
     */
    List<ResumeCategoryRelation> selectByUserId(@Param("userId") Long userId);
    
    /**
     * 根据简历ID查询关联记录
     */
    ResumeCategoryRelation selectByResumeId(@Param("resumeId") Long resumeId);
    
    /**
     * 插入关联记录
     */
    int insert(ResumeCategoryRelation record);
    
    /**
     * 更新关联记录
     */
    int update(ResumeCategoryRelation record);
}
