16:48:02.839 [main] INFO  com.bimowu.UnifiedWebApplication - Starting UnifiedWebApplication on su with PID 13976 (C:\Users\<USER>\Desktop\unified-platform\unified-parent\unified-web\target\classes started by 14629 in C:\Users\<USER>\Desktop\unified-platform\unified-parent)
16:48:02.848 [main] INFO  com.bimowu.UnifiedWebApplication - The following profiles are active: dev
16:48:03.718 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-7092"]
16:48:03.725 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
16:48:03.725 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.16]
16:48:03.737 [main] INFO  org.apache.catalina.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [C:\Program Files (x86)\Java\jdk-1.8\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Aibote;D:\Aibote (1)\Aibote;D:\Aibote (1)\Aibote\node_modules\.bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Windows\system32;D:\program files (x86)\Git\cmd;D:\program files (x86)\Docker\resources\bin;D:\program files (x86)\;d:\program files (x86)\cursor\resources\app\bin;C:\Program Files\CursorModifier;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\program files (x86)\cursor\resources\app\bin;D:\gradle-7.3.3-bin\gradle-7.3.3\bin;D:\apache-maven-3.2.3-bin\apache-maven-3.2.3-bin\apache-maven-3.2.3\bin;C:\Program Files\Java\jdk-17\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\program files (x86)\IntelliJ IDEA 2025.1\bin;;D:\program files (x86)\Kiro\bin;D:\program files (x86)\Microsoft VS Code\bin;.]
16:48:03.863 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/unified] - Initializing Spring embedded WebApplicationContext
16:48:04.716 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-7092"]
16:48:04.741 [main] INFO  com.bimowu.UnifiedWebApplication - Started UnifiedWebApplication in 2.178 seconds (JVM running for 2.88)
18:41:45.215 [Thread-3] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
18:41:45.244 [Thread-3] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-0} closing ...
