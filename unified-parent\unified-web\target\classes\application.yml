server:
  port: 7092
  servlet:
    context-path: /unified
  tomcat:
    max-connections: 10000
    max-threads: 10000
spring:
  profiles:
    active: dev
mybatis:
  mapper-locations: classpath*:com/bimowu/unified/common/mappers/*.xml
  type-aliases-package: com.bimowu.interview.common.model
oss:
  endpoint: oss-cn-beijing.aliyuncs.com
  accessKeyId: LTAI5tLmkL7ebnMBvdmZ1eF7
  accessKeySecret: ******************************
  bucketName: bmw-pic
# 微信公众号配置
wechat:
  mock: false # 开发环境使用模拟数据
  token: bimowu_wxd02c60a3f1e44342
  appid: wxd02c60a3f1e44342
  appsecret: e130032df9a49aa8bf77cf1715641121
sms:
  daily-limit: 5
