import{P as x}from"./ProgressChart-d69135bf.js";import{_ as h}from"./index-9768669e.js";import{r as C,j as D,k as l,l as p,C as _,D as e,F as n,G as t,L as S,M as w,J as u,I as P}from"./vendor-477e13dd.js";const b={class:"demo-container"},N={class:"demo-content"},B={class:"stage-buttons"},I={class:"chart-container"},T={class:"progress-info"},V={class:"info-item"},F={__name:"ProgressDemo",setup(H){const d=[{key:"0",name:"创建简历",index:0,description:"完善个人简历信息，准备求职材料"},{key:"1",name:"知识学习",index:1,description:"学习相关技能和知识，提升专业能力"},{key:"2",name:"HR模拟面试",index:2,description:"进行HR面试模拟，熟悉面试流程"},{key:"3",name:"技术模拟面试",index:3,description:"进行技术面试模拟，检验专业技能"},{key:"4",name:"正式面试",index:4,description:"参加正式面试，展示个人能力"}],o=C("1"),m=D(()=>({currentStage:o.value,userId:1,createTime:new Date,updateTime:new Date})),g=a=>{o.value=a},v=()=>{const a=d.find(s=>s.key===o.value);return a?a.name:"未知阶段"},f=()=>{const a=d.find(s=>s.key===o.value);return a?a.description:"暂无描述"};return(a,s)=>{const y=l("el-button"),c=l("el-card"),i=l("el-col"),k=l("el-row");return p(),_("div",b,[s[4]||(s[4]=e("div",{class:"demo-header"},[e("h1",null,"我的进度 - 演示页面"),e("p",null,"展示不同阶段的求职进度效果")],-1)),e("div",N,[n(k,{gutter:20},{default:t(()=>[n(i,{span:24},{default:t(()=>[n(c,{class:"stage-selector"},{header:t(()=>s[0]||(s[0]=[e("div",{class:"card-header"},[e("span",null,"选择当前进度阶段")],-1)])),default:t(()=>[e("div",B,[(p(),_(S,null,w(d,r=>n(y,{key:r.key,type:o.value===r.key?"primary":"default",onClick:L=>g(r.key),class:"stage-btn"},{default:t(()=>[P(u(r.name),1)]),_:2},1032,["type","onClick"])),64))])]),_:1})]),_:1}),n(i,{span:24},{default:t(()=>[n(c,{class:"progress-card"},{header:t(()=>s[1]||(s[1]=[e("div",{class:"card-header"},[e("span",null,"进度图表展示")],-1)])),default:t(()=>[e("div",I,[n(x,{"user-progress":m.value,loading:!1},null,8,["user-progress"])])]),_:1})]),_:1}),n(i,{span:24},{default:t(()=>[n(c,{class:"info-card"},{header:t(()=>s[2]||(s[2]=[e("div",{class:"card-header"},[e("span",null,"进度说明")],-1)])),default:t(()=>[e("div",T,[e("div",V,[e("h4",null,"当前阶段："+u(v()),1),e("p",null,u(f()),1)]),s[3]||(s[3]=e("div",{class:"color-legend"},[e("h4",null,"颜色说明："),e("div",{class:"legend-items"},[e("div",{class:"legend-item"},[e("span",{class:"color-dot green"}),e("span",null,"绿色 - 已完成的阶段")]),e("div",{class:"legend-item"},[e("span",{class:"color-dot yellow"}),e("span",null,"黄色 - 当前进行的阶段")]),e("div",{class:"legend-item"},[e("span",{class:"color-dot gray"}),e("span",null,"灰色 - 未开始的阶段")])])],-1))])]),_:1})]),_:1})]),_:1})])])}}},G=h(F,[["__scopeId","data-v-5f5a235a"]]);export{G as default};
