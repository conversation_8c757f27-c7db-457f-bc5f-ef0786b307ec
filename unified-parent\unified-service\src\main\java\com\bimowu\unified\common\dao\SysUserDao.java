package com.bimowu.unified.common.dao;

import com.bimowu.unified.common.model.SysUser;
import com.bimowu.unified.utils.bean.CommonQueryBean;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * SysUser数据库操作接口类
 */
@Repository
public interface SysUserDao {

    /**
     * 查询（根据主键ID查询）
     */
    SysUser selectByPrimaryKey(@Param("id") Long id);

    /**
     * 根据用户名查询用户
     */
    SysUser selectByUsername(@Param("username") String username);

    /**
     * 根据手机号查询用户
     */
    SysUser selectByMobile(@Param("mobile") String mobile);

    /**
     * 删除（根据主键ID删除）
     */
    int deleteByPrimaryKey(@Param("id") Long id);

    /**
     * 添加
     */
    int insert(SysUser record);

    /**
     * 修改（匹配有值的字段）
     */
    int updateByPrimaryKeySelective(SysUser record);

    /**
     * list分页查询
     */
    List<SysUser> list4Page(@Param("record") SysUser record, @Param("commonQueryParam") CommonQueryBean query);

    /**
     * count查询
     */
    long count(@Param("record") SysUser record);

    /**
     * list查询
     */
    List<SysUser> list(@Param("record") SysUser record);
} 