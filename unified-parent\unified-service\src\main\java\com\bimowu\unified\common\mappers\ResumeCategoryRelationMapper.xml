<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.unified.common.dao.ResumeCategoryRelationDao">
    
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.bimowu.unified.common.model.ResumeCategoryRelation">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="resume_id" property="resumeId" jdbcType="BIGINT"/>
        <result column="cat_id" property="catId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="INTEGER"/>
    </resultMap>
    
    <!-- 所有列 -->
    <sql id="Base_Column_List">
        id, user_id, resume_id, cat_id, create_time, update_time, is_delete
    </sql>
    
    <!-- 根据用户ID和职位类别ID查询关联记录 -->
    <select id="selectByUserIdAndCatId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM resume_category_relation
        WHERE user_id = #{userId,jdbcType=BIGINT} 
        AND cat_id = #{catId,jdbcType=BIGINT} 
        AND is_delete = 0
        LIMIT 1
    </select>
    
    <!-- 根据用户ID查询所有关联记录 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM resume_category_relation
        WHERE user_id = #{userId,jdbcType=BIGINT} AND is_delete = 0
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据简历ID查询关联记录 -->
    <select id="selectByResumeId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM resume_category_relation
        WHERE resume_id = #{resumeId,jdbcType=BIGINT} AND is_delete = 0
        LIMIT 1
    </select>
    
    <!-- 插入关联记录 -->
    <insert id="insert" parameterType="com.bimowu.unified.common.model.ResumeCategoryRelation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO resume_category_relation (
            user_id, resume_id, cat_id, create_time, update_time, is_delete
        ) VALUES (
            #{userId,jdbcType=BIGINT},
            #{resumeId,jdbcType=BIGINT},
            #{catId,jdbcType=BIGINT},
            NOW(),
            NOW(),
            0
        )
    </insert>
    
    <!-- 更新关联记录 -->
    <update id="update" parameterType="com.bimowu.unified.common.model.ResumeCategoryRelation">
        UPDATE resume_category_relation
        <set>
            <if test="resumeId != null">
                resume_id = #{resumeId,jdbcType=BIGINT},
            </if>
            <if test="catId != null">
                cat_id = #{catId,jdbcType=BIGINT},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT} AND is_delete = 0
    </update>
    
</mapper>
