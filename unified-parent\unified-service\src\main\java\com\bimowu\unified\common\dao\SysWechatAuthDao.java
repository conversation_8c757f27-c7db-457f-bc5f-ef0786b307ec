package com.bimowu.unified.common.dao;

import com.bimowu.unified.common.model.SysWechatAuth;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * SysWechatAuth数据库操作接口类
 */
@Repository
public interface SysWechatAuthDao {

    /**
     * 查询（根据主键ID查询）
     */
    SysWechatAuth selectByPrimaryKey(@Param("id") Long id);

    /**
     * 根据场景值查询
     */
    SysWechatAuth selectBySceneStr(@Param("sceneStr") String sceneStr);

    /**
     * 根据用户ID查询最新的授权记录
     */
    SysWechatAuth selectLatestByUserId(@Param("userId") Long userId);

    /**
     * 根据OpenID查询
     */
    SysWechatAuth selectByOpenid(@Param("openid") String openid);

    /**
     * 删除（根据主键ID删除）
     */
    int deleteByPrimaryKey(@Param("id") Long id);

    /**
     * 添加
     */
    int insert(SysWechatAuth record);

    /**
     * 修改（匹配有值的字段）
     */
    int updateByPrimaryKeySelective(SysWechatAuth record);
} 