package com.bimowu.unified.common.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 职位类别实体类
 */
@Data
public class ResumeCategory {
    
    /**
     * 主键ID
     */
    private Long catId;
    
    /**
     * 职位类别名称
     */
    private String name;
    
    /**
     * 职位类别编码
     */
    private String code;
    
    /**
     * 职位类别描述
     */
    private String description;
    
    /**
     * 排序号
     */
    private Integer sortOrder;
    
    /**
     * 状态(0-禁用,1-启用)
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 创建人
     */
    private String createAt;
    
    /**
     * 删除标识(0-未删除,1-已删除)
     */
    private Integer isDelete;
}
