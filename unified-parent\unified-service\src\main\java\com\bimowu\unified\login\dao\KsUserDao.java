package com.bimowu.unified.login.dao;

import com.bimowu.unified.login.model.KsUser;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * KsUser数据库操作接口类
 */
@Repository
public interface KsUserDao {

    /**
     * 查询（根据主键ID查询）
     */
    KsUser selectByPrimaryKey(@Param("uId") Long uId);

    /**
     * 根据用户名(昵称)查询用户
     */
    KsUser selectByNickname(@Param("nickname") String nickname);

    /**
     * 根据手机号查询用户
     */
    KsUser selectByPhone(@Param("phone") String phone);

    /**
     * 根据邮箱查询用户
     */
    KsUser selectByEmail(@Param("email") String email);
    
    /**
     * 更新用户信息（选择性更新）
     */
    int updateByPrimaryKeySelective(KsUser record);
    
    /**
     * 插入用户
     */
    int insert(KsUser record);
} 