<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bimowu.unified.login.dao.KsUserDao">
    <resultMap id="BaseResultMap" type="com.bimowu.unified.login.model.KsUser">
        <id column="u_id" property="uId" jdbcType="BIGINT"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="nickname" property="nickname" jdbcType="VARCHAR"/>
        <result column="passwd" property="passwd" jdbcType="VARCHAR"/>
        <result column="pass_type" property="passType" jdbcType="INTEGER"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="state" property="state" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="lasr_login_time" property="lasrLoginTime" jdbcType="TIMESTAMP"/>
        <result column="memo" property="memo" jdbcType="VARCHAR"/>
        <result column="user_level" property="userLevel" jdbcType="TINYINT"/>
        <result column="foreign_user_id" property="foreignUserId" jdbcType="VARCHAR"/>
        <result column="direction_type_id" property="directionTypeId" jdbcType="BIGINT"/>
        <result column="is_check" property="isCheck" jdbcType="INTEGER"/>
        <result column="school_type" property="schoolType" jdbcType="INTEGER"/>
        <result column="school_name" property="schoolName" jdbcType="VARCHAR"/>
        <result column="user_type" property="userType" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        u_id, email, nickname, passwd, pass_type, phone, state, create_time, update_time, lasr_login_time, 
        memo, user_level, foreign_user_id, direction_type_id, is_check, school_type, school_name, user_type
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from ks_user
        where u_id = #{uId,jdbcType=BIGINT}
    </select>

    <select id="selectByNickname" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from ks_user
        where nickname = #{nickname,jdbcType=VARCHAR}
    </select>

    <select id="selectByPhone" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from ks_user
        where phone = #{phone,jdbcType=VARCHAR}
    </select>

    <select id="selectByEmail" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from ks_user
        where email = #{email,jdbcType=VARCHAR}
    </select>
    
    <insert id="insert" parameterType="com.bimowu.unified.login.model.KsUser" useGeneratedKeys="true" keyProperty="uId">
        insert into ks_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="email != null">
                email,
            </if>
            <if test="nickname != null">
                nickname,
            </if>
            <if test="passwd != null">
                passwd,
            </if>
            <if test="passType != null">
                pass_type,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="lasrLoginTime != null">
                lasr_login_time,
            </if>
            <if test="memo != null">
                memo,
            </if>
            <if test="userLevel != null">
                user_level,
            </if>
            <if test="foreignUserId != null">
                foreign_user_id,
            </if>
            <if test="directionTypeId != null">
                direction_type_id,
            </if>
            <if test="isCheck != null">
                is_check,
            </if>
            <if test="schoolType != null">
                school_type,
            </if>
            <if test="schoolName != null">
                school_name,
            </if>
            <if test="userType != null">
                user_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="nickname != null">
                #{nickname,jdbcType=VARCHAR},
            </if>
            <if test="passwd != null">
                #{passwd,jdbcType=VARCHAR},
            </if>
            <if test="passType != null">
                #{passType,jdbcType=INTEGER},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lasrLoginTime != null">
                #{lasrLoginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="memo != null">
                #{memo,jdbcType=VARCHAR},
            </if>
            <if test="userLevel != null">
                #{userLevel,jdbcType=TINYINT},
            </if>
            <if test="foreignUserId != null">
                #{foreignUserId,jdbcType=VARCHAR},
            </if>
            <if test="directionTypeId != null">
                #{directionTypeId,jdbcType=BIGINT},
            </if>
            <if test="isCheck != null">
                #{isCheck,jdbcType=INTEGER},
            </if>
            <if test="schoolType != null">
                #{schoolType,jdbcType=INTEGER},
            </if>
            <if test="schoolName != null">
                #{schoolName,jdbcType=VARCHAR},
            </if>
            <if test="userType != null">
                #{userType,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.bimowu.unified.login.model.KsUser">
        update ks_user
        <set>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="nickname != null">
                nickname = #{nickname,jdbcType=VARCHAR},
            </if>
            <if test="passwd != null">
                passwd = #{passwd,jdbcType=VARCHAR},
            </if>
            <if test="passType != null">
                pass_type = #{passType,jdbcType=INTEGER},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lasrLoginTime != null">
                lasr_login_time = #{lasrLoginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="memo != null">
                memo = #{memo,jdbcType=VARCHAR},
            </if>
            <if test="userLevel != null">
                user_level = #{userLevel,jdbcType=TINYINT},
            </if>
            <if test="foreignUserId != null">
                foreign_user_id = #{foreignUserId,jdbcType=VARCHAR},
            </if>
            <if test="directionTypeId != null">
                direction_type_id = #{directionTypeId,jdbcType=BIGINT},
            </if>
            <if test="isCheck != null">
                is_check = #{isCheck,jdbcType=INTEGER},
            </if>
            <if test="schoolType != null">
                school_type = #{schoolType,jdbcType=INTEGER},
            </if>
            <if test="schoolName != null">
                school_name = #{schoolName,jdbcType=VARCHAR},
            </if>
            <if test="userType != null">
                user_type = #{userType,jdbcType=TINYINT},
            </if>
        </set>
        where u_id = #{uId,jdbcType=BIGINT}
    </update>
</mapper> 