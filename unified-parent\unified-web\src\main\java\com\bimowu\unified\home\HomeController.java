package com.bimowu.unified.home;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.common.dao.SysTodoDao;
import com.bimowu.unified.common.model.SysNews;
import com.bimowu.unified.common.model.SysSystem;
import com.bimowu.unified.common.model.SysTodo;
import com.bimowu.unified.common.service.HomeService;
import com.bimowu.unified.common.service.ResumeProgressService;
import com.bimowu.unified.common.utils.TokenUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 首页控制器
 */
@RestController
@RequestMapping("/")
@Slf4j
public class HomeController {

    @Autowired
    private HomeService homeService;
    
    @Autowired
    private SysTodoDao sysTodoDao;
    
    @Autowired
    private ResumeProgressService resumeProgressService;
    
    @Value("${interview.url}")
    private String interviewUrl;

    /**
     * 获取待办列表
     */
    @GetMapping("/todo/list")
    public BaseResponse<List<SysTodo>> getTodoList(HttpServletRequest request) {
        log.info("获取待办列表");
        Object userId = request.getHeader("userId");
        if (userId == null) {
            return BaseResponse.OK;
        }
        return homeService.getTodoList(Long.valueOf(userId.toString()));
    }
    
    /**
     * 完成待办事项
     */
    @PostMapping("/todo/complete/{id}")
    public BaseResponse<String> completeTodo(@PathVariable("id") Long id, HttpServletRequest request) {
        log.info("完成待办事项: {}", id);
        Long userId = Long.valueOf(request.getHeader("userId"));
        if (userId == null) {
            return BaseResponse.error(401, "未登录或登录已过期");
        }
        SysTodo todo = sysTodoDao.selectByPrimaryKey(id);
        
        // 验证待办事项是否属于当前用户
        if (todo != null && !userId.equals(todo.getUserId())) {
            return BaseResponse.error(403, "无权操作此待办事项");
        }
        
        if (todo != null) {
            // 更新待办状态为已完成
            todo.setStatus(1);
            sysTodoDao.updateByPrimaryKeySelective(todo);
            
            // 如果是知识学习待办(todo_type=1)，则创建HR面试待办并更新用户进度
            if (todo.getTodoType() != null && todo.getTodoType() == 1) {
                log.info("完成知识学习待办，创建HR面试待办任务");
                // 更新用户进度为HR面试阶段，使用相同的简历ID
                resumeProgressService.updateProgress(userId.intValue(), "2", todo.getResumeId());
                // 创建HR面试待办任务，使用相同的简历ID
                resumeProgressService.createHrInterviewTodo(userId, interviewUrl, todo.getResumeId());
            }
            
            return BaseResponse.ok("操作成功");
        } else {
            return BaseResponse.error(404, "待办事项不存在");
        }
    }

    /**
     * 获取新闻列表
     */
    @GetMapping("/news/list")
    public BaseResponse<List<SysNews>> getNewsList() {
        log.info("获取新闻列表");
        return homeService.getNewsList();
    }

    /**
     * 获取系统列表
     */
    @GetMapping("/system/list")
    public BaseResponse<List<SysSystem>> getSystemList() {
        log.info("获取系统列表");
        return homeService.getSystemList();
    }
} 