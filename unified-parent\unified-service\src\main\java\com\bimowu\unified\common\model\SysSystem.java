package com.bimowu.unified.common.model;

import lombok.Data;

import java.util.Date;

/**
 * 系统实体类
 */
@Data
public class SysSystem {

    /**
     * 系统ID
     */
    private Long id;

    /**
     * 系统名称
     */
    private String name;

    /**
     * 系统描述
     */
    private String description;

    /**
     * 系统图标
     */
    private String icon;

    /**
     * 系统URL
     */
    private String url;

    /**
     * 背景图片
     */
    private String bgImage;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 