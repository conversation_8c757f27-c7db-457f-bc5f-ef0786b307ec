package com.bimowu.unified.common.service.impl;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.common.dao.SysSystemDao;
import com.bimowu.unified.common.dao.SysTodoDao;
import com.bimowu.unified.common.model.SysNews;
import com.bimowu.unified.common.model.SysSystem;
import com.bimowu.unified.common.model.SysTodo;
import com.bimowu.unified.common.service.HomeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 首页服务实现类
 */
@Service
@Slf4j
public class HomeServiceImpl implements HomeService {

    @Autowired
    private SysTodoDao sysTodoDao;
    
    @Autowired
    private SysSystemDao sysSystemDao;

    @Override
    public BaseResponse<List<SysTodo>> getTodoList(Long userId) {
        log.info("获取待办列表: {}", userId);
        
        // 从数据库获取未完成的待办事项
        List<SysTodo> todoList = sysTodoDao.selectUnfinishedByUserId(userId);
        
        return BaseResponse.ok(todoList);
    }

    @Override
    public BaseResponse<List<SysNews>> getNewsList() {
        log.info("获取新闻列表");
        
        // 模拟数据
        List<SysNews> newsList = new ArrayList<>();
        
        SysNews news1 = new SysNews();
        news1.setId(1L);
        news1.setTitle("公司2023年第三季度业绩公布");
        news1.setContent("公司2023年第三季度业绩公布，营收增长30%");
        news1.setAuthor("行政部");
        news1.setPublishTime(parseDate("2023-09-10"));
        news1.setStatus(1);
        news1.setCreateTime(new Date());
        news1.setUpdateTime(new Date());
        newsList.add(news1);
        
        SysNews news2 = new SysNews();
        news2.setId(2L);
        news2.setTitle("新版OA系统上线通知");
        news2.setContent("新版OA系统将于下周一上线，请各位同事提前熟悉");
        news2.setAuthor("IT部");
        news2.setPublishTime(parseDate("2023-09-08"));
        news2.setStatus(1);
        news2.setCreateTime(new Date());
        news2.setUpdateTime(new Date());
        newsList.add(news2);
        
        SysNews news3 = new SysNews();
        news3.setId(3L);
        news3.setTitle("中秋节放假安排");
        news3.setContent("中秋节放假安排：9月29日至10月1日放假，共3天");
        news3.setAuthor("人事部");
        news3.setPublishTime(parseDate("2023-09-05"));
        news3.setStatus(1);
        news3.setCreateTime(new Date());
        news3.setUpdateTime(new Date());
        newsList.add(news3);
        
        SysNews news4 = new SysNews();
        news4.setId(4L);
        news4.setTitle("新员工入职培训计划");
        news4.setContent("新员工入职培训将于本月15日开始，为期3天");
        news4.setAuthor("培训部");
        news4.setPublishTime(parseDate("2023-09-01"));
        news4.setStatus(1);
        news4.setCreateTime(new Date());
        news4.setUpdateTime(new Date());
        newsList.add(news4);
        
        return BaseResponse.ok(newsList);
    }

    @Override
    public BaseResponse<List<SysSystem>> getSystemList() {
        log.info("获取系统列表");
        
        // 从数据库获取已启用的系统列表
        List<SysSystem> systemList = sysSystemDao.selectAllEnabled();
        
        return BaseResponse.ok(systemList);
    }
    
    /**
     * 解析日期
     */
    private Date parseDate(String dateStr) {
        try {
            return new SimpleDateFormat("yyyy-MM-dd").parse(dateStr);
        } catch (Exception e) {
            return new Date();
        }
    }
} 