package com.bimowu.unified.common.service.impl;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.common.dao.SysNewsDao;
import com.bimowu.unified.common.dao.SysSystemDao;
import com.bimowu.unified.common.dao.SysTodoDao;
import com.bimowu.unified.common.model.SysNews;
import com.bimowu.unified.common.model.SysSystem;
import com.bimowu.unified.common.model.SysTodo;
import com.bimowu.unified.common.service.HomeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 首页服务实现类
 */
@Service
@Slf4j
public class HomeServiceImpl implements HomeService {

    @Autowired
    private SysTodoDao sysTodoDao;
    
    @Autowired
    private SysSystemDao sysSystemDao;
    
    @Autowired
    private SysNewsDao sysNewsDao;

    @Override
    public BaseResponse<List<SysTodo>> getTodoList(Long userId) {
        log.info("获取待办列表: {}", userId);
        
        // 从数据库获取未完成的待办事项
        List<SysTodo> todoList = sysTodoDao.selectUnfinishedByUserId(userId);
        
        return BaseResponse.ok(todoList);
    }

    @Override
    public BaseResponse<List<SysNews>> getNewsList() {
        log.info("获取新闻列表");
        
        // 从数据库获取已发布的新闻列表
        List<SysNews> newsList = sysNewsDao.selectAllPublished();
        
        return BaseResponse.ok(newsList);
    }

    @Override
    public BaseResponse<List<SysSystem>> getSystemList() {
        log.info("获取系统列表");
        
        // 从数据库获取已启用的系统列表
        List<SysSystem> systemList = sysSystemDao.selectAllEnabled();
        
        return BaseResponse.ok(systemList);
    }
    
    /**
     * 解析日期
     */
    private Date parseDate(String dateStr) {
        try {
            return new SimpleDateFormat("yyyy-MM-dd").parse(dateStr);
        } catch (Exception e) {
            return new Date();
        }
    }
} 