package com.bimowu.unified.login.service;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.login.model.KsUser;

import java.util.Map;

/**
 * 登录服务接口
 */
public interface LoginService {

    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    BaseResponse<KsUser> getUserInfo(Long userId);
    
    /**
     * 修改密码
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 修改结果
     */
    BaseResponse<String> updatePassword(Long userId, String oldPassword, String newPassword);
    
    /**
     * 找回密码
     * @param mobile 手机号
     * @param verifyCode 验证码
     * @param newPassword 新密码
     * @return 重置结果
     */
    BaseResponse<String> resetPassword(String mobile, String verifyCode, String newPassword);
    
    /**
     * 修改用户名
     * @param userId 用户ID
     * @param newUsername 新用户名
     * @return 修改结果
     */
    BaseResponse<String> updateUsername(Long userId, String newUsername);
    

} 