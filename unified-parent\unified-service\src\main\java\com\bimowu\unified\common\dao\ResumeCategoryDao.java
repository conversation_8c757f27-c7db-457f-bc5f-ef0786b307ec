package com.bimowu.unified.common.dao;

import com.bimowu.unified.common.model.ResumeCategory;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 职位类别数据库操作接口类
 */
@Repository
public interface ResumeCategoryDao {
    
    /**
     * 查询所有启用的职位类别
     */
    List<ResumeCategory> selectEnabledCategories();
    
    /**
     * 根据ID查询职位类别
     */
    ResumeCategory selectById(@Param("catId") Long catId);
    
    /**
     * 根据编码查询职位类别
     */
    ResumeCategory selectByCode(@Param("code") String code);
    
    /**
     * 根据用户ID查询用户的职位类别列表
     */
    List<ResumeCategory> selectByUserId(@Param("userId") Long userId);
}
