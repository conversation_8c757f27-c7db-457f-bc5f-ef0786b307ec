import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  base: '/sso/',
  plugins: [
    vue(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  build: {
    outDir: 'dist-sso',
    // 在构建时添加环境变量替换
    rollupOptions: {
      output: {
        manualChunks: {
          'vendor': ['vue', 'vue-router', 'axios', 'element-plus']
        }
      }
    }
  },
  server: {
    proxy: {
      '/unified': {
        target: 'https://ceping.bimowo.com/unified',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/unified/, '')
      }
    }
  }
}) 