<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.unified.common.dao.SysUserDao">
    <resultMap id="BaseResultMap" type="com.bimowu.unified.common.model.SysUser">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="username" jdbcType="VARCHAR" property="username" />
        <result column="password" jdbcType="VARCHAR" property="password" />
        <result column="mobile" jdbcType="VARCHAR" property="mobile" />
        <result column="email" jdbcType="VARCHAR" property="email" />
        <result column="real_name" jdbcType="VARCHAR" property="realName" />
        <result column="avatar" jdbcType="VARCHAR" property="avatar" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, username, password, mobile, email, real_name, avatar, status, create_time, update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_user
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByUsername" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_user
        where username = #{username,jdbcType=VARCHAR}
    </select>

    <select id="selectByMobile" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_user
        where mobile = #{mobile,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from sys_user
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.bimowu.unified.common.model.SysUser" useGeneratedKeys="true" keyProperty="id">
        insert into sys_user (
            username, password, mobile, email,
            real_name, avatar, status, create_time,
            update_time
        )
        values (
            #{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR},
            #{realName,jdbcType=VARCHAR}, #{avatar,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.bimowu.unified.common.model.SysUser">
        update sys_user
        <set>
            <if test="username != null">
                username = #{username,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="realName != null">
                real_name = #{realName,jdbcType=VARCHAR},
            </if>
            <if test="avatar != null">
                avatar = #{avatar,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <sql id="findPageWhere">
        <where>
            <if test="record.id != null">
                and id = #{record.id,jdbcType=BIGINT}
            </if>
            <if test="record.username != null and record.username != ''">
                and username like concat('%', #{record.username,jdbcType=VARCHAR}, '%')
            </if>
            <if test="record.mobile != null and record.mobile != ''">
                and mobile like concat('%', #{record.mobile,jdbcType=VARCHAR}, '%')
            </if>
            <if test="record.email != null and record.email != ''">
                and email like concat('%', #{record.email,jdbcType=VARCHAR}, '%')
            </if>
            <if test="record.realName != null and record.realName != ''">
                and real_name like concat('%', #{record.realName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="record.status != null">
                and status = #{record.status,jdbcType=INTEGER}
            </if>
        </where>
    </sql>

    <select id="list4Page" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_user
        <include refid="findPageWhere" />
        <if test="commonQueryParam != null">
            <if test="commonQueryParam.sort != null and commonQueryParam.sort != ''">
                order by ${commonQueryParam.sort} ${commonQueryParam.order}
            </if>
            <if test="commonQueryParam.sort == null or commonQueryParam.sort == ''">
                order by id desc
            </if>
        </if>
        <if test="commonQueryParam == null">
            order by id desc
        </if>
        <if test="commonQueryParam != null">
            <if test="commonQueryParam.start != null and commonQueryParam.pageSize != null">
                limit #{commonQueryParam.start}, #{commonQueryParam.pageSize}
            </if>
        </if>
    </select>

    <select id="count" parameterType="com.bimowu.unified.common.model.SysUser" resultType="java.lang.Long">
        select count(*) from sys_user
        <include refid="findPageWhere" />
    </select>

    <select id="list" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_user
        <include refid="findPageWhere" />
        order by id desc
    </select>
</mapper> 