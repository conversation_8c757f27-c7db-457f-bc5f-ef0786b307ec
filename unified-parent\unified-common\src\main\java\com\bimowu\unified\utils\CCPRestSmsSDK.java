package com.bimowu.unified.utils;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
@Slf4j
public class CCPRestSmsSDK {
	private static final String ALIYUN_MESSAGE_TEMPLET = "SMS_489200241";
	private static final String ALIYUN_MESSAGE_SIGNNAME = "笔墨屋";
	private static final String ALIBABA_CLOUD_ACCESS_KEY_ID = "LTAI5tEm5vMpTW7AB2BjHtYD";
	private static final String ALIBABA_CLOUD_ACCESS_KEY_SECRET = "******************************";

	public static Map<String, Object> sendTemplateSMS(String to,String code) {
		Map<String, Object> result = new HashMap<>();
		result.put("statusCode","000000");
		try {
			JSONObject json = new JSONObject();
			json.put("code", code);
			SendSmsRequest sendSmsRequest = new SendSmsRequest()
					.setPhoneNumbers(to)
					.setSignName(ALIYUN_MESSAGE_SIGNNAME)
					.setTemplateCode(ALIYUN_MESSAGE_TEMPLET)
					.setTemplateParam(json.toJSONString());
			Config config = new Config()
					// 配置 AccessKey ID，请确保代码运行环境配置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
					.setAccessKeyId(ALIBABA_CLOUD_ACCESS_KEY_ID)
					// 配置 AccessKey Secret，请确保代码运行环境配置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
					.setAccessKeySecret(ALIBABA_CLOUD_ACCESS_KEY_SECRET);
			config.endpoint = "dysmsapi.aliyuncs.com";
			Client client = new Client(config);
			SendSmsResponse response = client.sendSms(sendSmsRequest);
			log.info("短信验证码发送结果:{}",response.getBody().message);
			if(!StringUtils.equals(response.getBody().getCode(),"OK")){
				result.put("statusCode","-1");
			}
		} catch (Exception e) {
			result.put("statusCode","-1");
		}
		return result;
	}

}