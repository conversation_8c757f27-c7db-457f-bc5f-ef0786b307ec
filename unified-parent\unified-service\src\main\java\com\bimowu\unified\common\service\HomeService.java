package com.bimowu.unified.common.service;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.common.model.SysNews;
import com.bimowu.unified.common.model.SysSystem;
import com.bimowu.unified.common.model.SysTodo;

import java.util.List;

/**
 * 首页服务接口
 */
public interface HomeService {

    /**
     * 获取待办列表
     * @param userId 用户ID
     * @return 待办列表
     */
    BaseResponse<List<SysTodo>> getTodoList(Long userId);

    /**
     * 获取新闻列表
     * @return 新闻列表
     */
    BaseResponse<List<SysNews>> getNewsList();

    /**
     * 获取系统列表
     * @return 系统列表
     */
    BaseResponse<List<SysSystem>> getSystemList();
} 