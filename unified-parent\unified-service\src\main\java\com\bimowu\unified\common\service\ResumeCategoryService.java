package com.bimowu.unified.common.service;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.common.model.ResumeCategory;

import java.util.List;

/**
 * 职位类别服务接口
 */
public interface ResumeCategoryService {
    
    /**
     * 获取所有启用的职位类别
     * @return 职位类别列表
     */
    BaseResponse<List<ResumeCategory>> getAllEnabledCategories();
    
    /**
     * 根据用户ID获取用户的职位类别列表
     * @param userId 用户ID
     * @return 职位类别列表
     */
    BaseResponse<List<ResumeCategory>> getCategoriesByUserId(Long userId);
}
