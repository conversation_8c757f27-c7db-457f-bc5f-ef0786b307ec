import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'

// 判断是否是直接访问文件系统上的index.html
const isDirectFileAccess = window.location.protocol === 'file:' ||
    (window.location.hostname === 'ceping.bimowo.com' &&
        window.location.pathname.endsWith('/index.html'));

// 如果是直接访问文件系统上的index.html，使用hash模式路由，否则使用history模式
const router = createRouter({
  history: isDirectFileAccess
      ? createWebHashHistory(import.meta.env.BASE_URL)
      : createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/Login.vue')
    },
    {
      path: '/home',
      name: 'home',
      component: () => import('../views/Home.vue')
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/Profile.vue')
    }
  ]
})

router.beforeEach((to, from, next) => {
  const isAuthenticated = localStorage.getItem('token')

  if (isAuthenticated || to.path === '/login' || to.path === '/home') {
    next()
  } else {
    next('/login')
  }
})

export default router 