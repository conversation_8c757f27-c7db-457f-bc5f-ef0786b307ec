import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'

// 判断是否是直接访问文件系统上的index.html
const isDirectFileAccess = window.location.protocol === 'file:' ||
    (window.location.hostname === 'ceping.bimowo.com' &&
        window.location.pathname.endsWith('/index.html'));

// 如果是直接访问文件系统上的index.html，使用hash模式路由，否则使用history模式
const router = createRouter({
  history: isDirectFileAccess
      ? createWebHashHistory(import.meta.env.BASE_URL)
      : createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: to => {
        // 检查是否有token，如果有则跳转到首页，否则跳转到登录页
        const token = localStorage.getItem('token')
        return token ? '/home' : '/login'
      }
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/Login.vue')
    },
    {
      path: '/home',
      name: 'home',
      component: () => import('../views/Home.vue')
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/Profile.vue')
    },
    {
      path: '/progress-demo',
      name: 'progress-demo',
      component: () => import('../views/ProgressDemo.vue')
    }
  ]
})

router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  const isAuthenticated = !!token

  // 如果访问登录页且已登录，重定向到首页
  if (to.path === '/login' && isAuthenticated) {
    next('/home')
    return
  }

  // 如果访问需要认证的页面但未登录，重定向到登录页
  if (!isAuthenticated && to.path !== '/login') {
    next('/login')
    return
  }

  // 其他情况正常通过
  next()
})

export default router 