<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.unified.login.dao.SsoClientDao">
  <resultMap id="BaseResultMap" type="com.bimowu.unified.login.model.SsoClient">
    <id column="client_id" jdbcType="VARCHAR" property="clientId" />
    <result column="client_secret" jdbcType="VARCHAR" property="clientSecret" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="app_url" jdbcType="VARCHAR" property="appUrl" />
    <result column="redirect_url" jdbcType="VARCHAR" property="redirectUrl" />
    <result column="logout_url" jdbcType="VARCHAR" property="logoutUrl" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>
  
  <sql id="Base_Column_List">
    client_id, client_secret, app_name, app_url, redirect_url, logout_url, create_time, update_time, status
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sso_client
    where client_id = #{clientId,jdbcType=VARCHAR}
  </select>
  
  <select id="selectByAppName" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sso_client
    where app_name = #{appName,jdbcType=VARCHAR}
  </select>
  
  <select id="selectAll" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sso_client
    where status = 0
  </select>
  
  <insert id="insert" parameterType="com.bimowu.unified.login.model.SsoClient">
    insert into sso_client (client_id, client_secret, app_name, 
      app_url, redirect_url, logout_url, create_time, 
      update_time, status)
    values (#{clientId,jdbcType=VARCHAR}, #{clientSecret,jdbcType=VARCHAR}, #{appName,jdbcType=VARCHAR}, 
      #{appUrl,jdbcType=VARCHAR}, #{redirectUrl,jdbcType=VARCHAR}, #{logoutUrl,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER})
  </insert>
  
  <update id="updateByPrimaryKey" parameterType="com.bimowu.unified.login.model.SsoClient">
    update sso_client
    set client_secret = #{clientSecret,jdbcType=VARCHAR},
      app_name = #{appName,jdbcType=VARCHAR},
      app_url = #{appUrl,jdbcType=VARCHAR},
      redirect_url = #{redirectUrl,jdbcType=VARCHAR},
      logout_url = #{logoutUrl,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER}
    where client_id = #{clientId,jdbcType=VARCHAR}
  </update>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.bimowu.unified.login.model.SsoClient">
    update sso_client
    <set>
      <if test="clientSecret != null">
        client_secret = #{clientSecret,jdbcType=VARCHAR},
      </if>
      <if test="appName != null">
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="appUrl != null">
        app_url = #{appUrl,jdbcType=VARCHAR},
      </if>
      <if test="redirectUrl != null">
        redirect_url = #{redirectUrl,jdbcType=VARCHAR},
      </if>
      <if test="logoutUrl != null">
        logout_url = #{logoutUrl,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
    </set>
    where client_id = #{clientId,jdbcType=VARCHAR}
  </update>
</mapper> 