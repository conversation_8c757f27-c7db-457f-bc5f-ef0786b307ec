<template>
  <div class="login-container">
    <!-- 顶部导航栏 -->
    <div class="header-bar">
      <div class="logo-title">笔墨屋统一认证服务平台</div>

    </div>

    <div class="login-content">
      <!-- 左侧内容 -->
      <div class="login-left">
        <div class="welcome-text">
          <h1>欢迎使用笔墨屋统一认证服务平台</h1>
          <p>一站式登录，畅享所有系统服务</p>
        </div>
      </div>

      <!-- 右侧登录框 -->
      <div class="login-right">
        <div class="login-form-container">
          <h3 class="login-title">账号登录</h3>

          <el-tabs v-model="activeTab" class="login-tabs">
            <el-tab-pane label="账号密码登录" name="password">
              <el-form ref="passwordFormRef" :model="passwordForm" :rules="passwordRules" class="login-form">
                <el-form-item prop="username">
                  <el-input v-model="passwordForm.username" placeholder="请输入手机号/邮箱/用户名" prefix-icon="el-icon-user" />
                </el-form-item>
                <el-form-item prop="password">
                  <el-input v-model="passwordForm.password" type="password" placeholder="请输入密码" prefix-icon="el-icon-lock" show-password />
                </el-form-item>
                <div class="captcha-row" v-if="needCaptcha">
                  <el-input v-model="passwordForm.captcha" placeholder="请输入验证码" class="captcha-input" />
                  <div class="captcha-img" @click="refreshCaptcha" title="点击刷新验证码">
                    <img :src="passwordForm.captchaUrl" alt="验证码" style="width: 100%; height: 100%; object-fit: cover;" />
                  </div>
                </div>
                <div class="form-actions">
                  <el-checkbox v-model="rememberMe">记住密码</el-checkbox>
                  <a href="#" class="forgot-link">忘记密码?</a>
                </div>
                <el-form-item>
                  <el-button type="primary" :loading="loading" class="login-button" @click="handlePasswordLogin">登录</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <el-tab-pane label="手机号登录" name="mobile">
              <el-form ref="mobileFormRef" :model="mobileForm" :rules="mobileRules" class="login-form">
                <el-form-item prop="mobile">
                  <el-input v-model="mobileForm.mobile" placeholder="请输入手机号" prefix-icon="el-icon-mobile" />
                </el-form-item>
                <!-- 新增图片验证码输入框和图片 -->
                <div class="captcha-row">
                  <el-input v-model="mobileForm.captcha" placeholder="请输入图片验证码" class="captcha-input" />
                  <div class="captcha-img" @click="refreshMobileCaptcha" title="点击刷新验证码">
                    <img :src="mobileForm.captchaUrl" alt="图片验证码" style="width: 100%; height: 100%; object-fit: cover;" />
                  </div>
                </div>
                <el-form-item prop="verifyCode">
                  <div class="verify-code-container">
                    <el-input v-model="mobileForm.verifyCode" placeholder="请输入验证码" prefix-icon="el-icon-message" />
                    <el-button
                        type="primary"
                        class="verify-code-button"
                        :disabled="isCodeSent"
                        @click="handleSendVerifyCode"
                    >
                      {{ codeButtonText }}
                    </el-button>
                  </div>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" :loading="loading" class="login-button" @click="handleMobileLogin">登录</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>

          <div class="login-actions">
            <el-button type="text" class="register-link"></el-button>
            <el-button type="text" class="direct-link" @click="goToHome">直接进入首页</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onBeforeUnmount, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '../store'
import { ElMessage } from 'element-plus'
import { loginByPassword, loginByMobile, getVerifyCode, verifyCaptcha } from '../api/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 表单引用
const passwordFormRef = ref(null)
const mobileFormRef = ref(null)

// 加载状态
const loading = ref(false)

// 当前激活的标签页
const activeTab = ref('password')

// 是否需要验证码
const needCaptcha = ref(false)

// 记住密码
const rememberMe = ref(false)

// 账号密码登录表单
const passwordForm = reactive({
  username: '',
  password: '',
  clientId: 'sso_client',
  redirectUrl: '',
  captcha: '',
  captchaUrl: ''
})

// 手机号登录表单
const mobileForm = reactive({
  mobile: '',
  verifyCode: '',
  captcha: '', // 新增图片验证码字段
  clientId: 'sso_client',
  redirectUrl: '',
  captchaUrl: '' // 新增图片验证码图片url
})

// 验证码相关
const isCodeSent = ref(false)
const codeButtonText = ref('获取验证码')
let countdownTimer = null
let countdown = 60

// 账号密码登录验证规则
const passwordRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ],
  captcha: [
    { required: false, message: '请输入验证码', trigger: 'blur' }
  ]
}

// 手机号登录验证规则
const mobileRules = {
  mobile: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  verifyCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码长度应为6位', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入图片验证码', trigger: 'blur' }
  ]
}

// 刷新图片验证码（手机号登录用）
const refreshMobileCaptcha = () => {
  mobileForm.captchaUrl = `/unified/sso/captcha?t=${new Date().getTime()}`
}

// 初始化表单数据
onMounted(() => {
  // 记住密码功能：自动填充
  const savedUsername = localStorage.getItem('rememberedUsername')
  const savedPassword = localStorage.getItem('rememberedPassword')
  if (savedUsername && savedPassword) {
    passwordForm.username = savedUsername
    passwordForm.password = savedPassword
    rememberMe.value = true
  }
  // 从URL参数中获取clientId
  const clientId = route.query.clientId
  if (clientId) {
    passwordForm.clientId = clientId
    mobileForm.clientId = clientId
  }

  // 从URL参数中获取redirectUrl
  const redirectUrl = route.query.redirectUrl
  if (redirectUrl) {
    passwordForm.redirectUrl = redirectUrl
    mobileForm.redirectUrl = redirectUrl
  }

  // 初始加载验证码
  needCaptcha.value = true // 默认显示验证码
  refreshCaptcha()
  refreshMobileCaptcha()
})

// 刷新验证码
const refreshCaptcha = () => {
  // 使用完整的API路径，包括api前缀
  passwordForm.captchaUrl = `/unified/sso/captcha?t=${new Date().getTime()}`
  ElMessage({
    message: '验证码已刷新',
    type: 'info',
    duration: 1000
  })
}

// 账号密码登录
const handlePasswordLogin = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()

    // 检查是否需要验证验证码
    if (needCaptcha.value) {
      if (!passwordForm.captcha) {
        ElMessage.warning('请输入验证码')
        return
      }

      // 验证图形验证码
      try {
        const captchaRes = await verifyCaptcha(passwordForm.captcha)
        if (captchaRes.code !== 0) {
          ElMessage.error('验证码错误')
          refreshCaptcha()
          return
        }
      } catch (error) {
        console.error('验证码验证失败:', error)
        ElMessage.error('验证码验证失败')
        refreshCaptcha()
        return
      }
    }

    loading.value = true

    const res = await loginByPassword(passwordForm)
    userStore.setToken(res.data.token)
    userStore.setUserInfo(res.data.userInfo)

    // 记住密码逻辑
    if (rememberMe.value) {
      localStorage.setItem('rememberedUsername', passwordForm.username)
      localStorage.setItem('rememberedPassword', passwordForm.password)
    } else {
      localStorage.removeItem('rememberedUsername')
      localStorage.removeItem('rememberedPassword')
    }

    ElMessage.success('登录成功')

    // 如果后端返回重定向URL，则跳转到该URL
    if (res.data.redirectUrl) {
      window.location.href = res.data.redirectUrl
    }
    // 如果请求中包含redirectUrl参数但后端没有返回，则使用请求中的redirectUrl
    else if (passwordForm.redirectUrl) {
      window.location.href = passwordForm.redirectUrl
    } else {
      router.push('/home')
    }
  } catch (error) {
    console.error('登录失败:', error)
    // 登录失败后显示验证码
    needCaptcha.value = true
    refreshCaptcha()
  } finally {
    loading.value = false
  }
}

// 手机号登录
const handleMobileLogin = async () => {
  if (!mobileFormRef.value) return

  try {
    await mobileFormRef.value.validate()
    loading.value = true

    const res = await loginByMobile(mobileForm)
    userStore.setToken(res.data.token)
    userStore.setUserInfo(res.data.userInfo)

    ElMessage.success('登录成功')

    // 如果后端返回重定向URL，则跳转到该URL
    if (res.data.redirectUrl) {
      window.location.href = res.data.redirectUrl
    }
    // 如果请求中包含redirectUrl参数但后端没有返回，则使用请求中的redirectUrl
    else if (mobileForm.redirectUrl) {
      window.location.href = mobileForm.redirectUrl
    } else {
      router.push('/home')
    }
  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}

// 发送验证码
const handleSendVerifyCode = async () => {
  if (!mobileForm.mobile) {
    ElMessage.warning('请输入手机号')
    return
  }
  if (!/^1[3-9]\d{9}$/.test(mobileForm.mobile)) {
    ElMessage.warning('请输入正确的手机号')
    return
  }
  if (!mobileForm.captcha) {
    ElMessage.warning('请输入图片验证码')
    return
  }
  // 校验图片验证码
  try {
    const captchaRes = await verifyCaptcha(mobileForm.captcha)
    if (captchaRes.code !== 0) {
      ElMessage.error('图片验证码错误')
      refreshMobileCaptcha()
      return
    }
  } catch (error) {
    ElMessage.error('图片验证码校验失败')
    refreshMobileCaptcha()
    return
  }
  try {
    await getVerifyCode(mobileForm.mobile)
    startCountdown()
    ElMessage.success('验证码已发送')
  } catch (error) {
    console.error('发送验证码失败:', error)
  }
}

// 开始倒计时
const startCountdown = () => {
  isCodeSent.value = true
  countdown = 60
  codeButtonText.value = `${countdown}秒后重新获取`

  countdownTimer = setInterval(() => {
    countdown--
    codeButtonText.value = `${countdown}秒后重新获取`

    if (countdown <= 0) {
      clearInterval(countdownTimer)
      isCodeSent.value = false
      codeButtonText.value = '获取验证码'
    }
  }, 1000)
}

// 直接进入首页
const goToHome = () => {
  router.push('/home')
}

// 跳转到注册页
const goToRegister = () => {
  ElMessage.info('注册功能开发中')
}

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<style scoped>
.login-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://bmw-pic.oss-cn-beijing.aliyuncs.com/e7e619d5e087b8e1913c619881246800.jpeg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* 添加一个遮罩层，使背景图片更加柔和 */
.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 0;
}

/* 顶部导航栏样式 */
.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 30px;
  background-color: rgba(13, 58, 104, 0.8);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.logo-title {
  font-size: 18px;
  font-weight: bold;
}

.help-link {
  color: white;
  text-decoration: none;
  font-size: 14px;
}

.help-link:hover {
  text-decoration: underline;
}

/* 登录内容区域 */
.login-content {
  flex: 1;
  display: flex;
  padding: 20px;
  position: relative;
  z-index: 1;
}

/* 左侧内容样式 */
.login-left {
  flex: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  padding: 0 50px;
}

.welcome-text {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-text h1 {
  font-size: 36px;
  font-weight: 600;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

.welcome-text p {
  font-size: 20px;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.slogan {
  text-align: left;
  width: 100%;
  margin-bottom: 30px;
}

.slogan h2 {
  font-size: 32px;
  margin-bottom: 15px;
  font-weight: 500;
}

.slogan-sub {
  display: flex;
  align-items: center;
  font-size: 18px;
}

.arrow-line {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.arrow-dot {
  width: 8px;
  height: 8px;
  background-color: white;
  border-radius: 50%;
}

.arrow {
  width: 30px;
  height: 2px;
  background-color: white;
  position: relative;
  margin-left: 5px;
}

.arrow:after {
  content: '';
  position: absolute;
  right: 0;
  top: -3px;
  width: 8px;
  height: 8px;
  border-top: 2px solid white;
  border-right: 2px solid white;
  transform: rotate(45deg);
}

.illustration {
  width: 100%;
  max-width: 500px;
  margin-top: 20px;
}

.illustration img {
  width: 100%;
  height: auto;
  max-height: 300px;
  object-fit: contain;
}

/* 右侧登录框样式 */
.login-right {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-form-container {
  width: 100%;
  max-width: 400px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(5px);
}

.login-title {
  text-align: center;
  margin-bottom: 25px;
  color: #333;
  font-size: 22px;
  font-weight: 500;
}

.login-tabs {
  width: 100%;
}

.login-form {
  margin-top: 20px;
}

.captcha-row {
  display: flex;
  margin-bottom: 20px;
}

.captcha-input {
  flex: 1;
}

.captcha-img {
  width: 100px;
  margin-left: 10px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.captcha-img img {
  width: 100%;
  height: 100%;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.forgot-link {
  color: #1890ff;
  font-size: 14px;
  text-decoration: none;
}

.forgot-link:hover {
  text-decoration: underline;
}

.login-button {
  width: 100%;
  height: 40px;
  font-size: 16px;
  background: #1890ff;
  border: none;
  border-radius: 4px;
}

.login-button:hover {
  background: #40a9ff;
}

.verify-code-container {
  display: flex;
}

.verify-code-button {
  margin-left: 10px;
  width: 120px;
}

.login-actions {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}

.register-link, .direct-link {
  color: #1890ff;
  font-size: 14px;
}

.register-link:hover, .direct-link:hover {
  color: #40a9ff;
  text-decoration: underline;
}

:deep(.el-tabs__nav) {
  width: 100%;
  display: flex;
}

:deep(.el-tabs__item) {
  flex: 1;
  text-align: center;
}

:deep(.el-tabs__active-bar) {
  background-color: #1890ff;
}

:deep(.el-tabs__item.is-active) {
  color: #1890ff;
}

:deep(.el-input__inner) {
  height: 40px;
}

@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
  }

  .login-left {
    flex: 1;
    padding: 20px;
    margin-bottom: 20px;
  }

  .login-right {
    flex: 1;
  }

  .login-form-container {
    padding: 20px;
  }

  .slogan h2 {
    font-size: 24px;
  }

  .slogan-sub {
    font-size: 16px;
  }

  .illustration {
    max-width: 300px;
  }
}
</style> 