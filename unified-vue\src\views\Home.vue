<template>
  <div class="home-container">
    <!-- 顶部导航栏 -->
    <el-header class="header">
      <div class="header-content">
        <div class="logo">
          <i class="el-icon-platform-eleme logo-icon"></i>
          笔墨屋统一认证服务平台
        </div>
        <div class="user-info">
          <template v-if="isLoggedIn">
            <el-dropdown trigger="click">
              <span class="el-dropdown-link">
                <el-avatar :size="32" class="user-avatar">{{ userInfo.nickname ? userInfo.nickname.substring(0, 1) : 'U' }}</el-avatar>
                欢迎，{{ userInfo.nickname }} <el-icon class="el-icon--right"><arrow-down /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="goToProfile">
                    <el-icon><user /></el-icon> 个人中心
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleLogout">
                    <el-icon><switch-button /></el-icon> 退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template v-else>
            <el-button type="primary" @click="goToLogin" class="login-button">
              <el-icon><user /></el-icon> 登录
            </el-button>
          </template>
        </div>
      </div>
    </el-header>

    <!-- 页面主体 -->
    <el-main class="main">
      <!-- 顶部横幅 -->
      <div class="banner">
        <div class="banner-content">
          <h1 class="banner-title">欢迎使用笔墨屋统一认证服务平台</h1>
          <p class="banner-desc">一站式登录，畅享所有系统服务</p>
        </div>
      </div>

      <div class="container">
        <!-- 上部分：待办和新闻 -->
        <div class="top-section">
          <el-row :gutter="20">
            <!-- 左侧：待办列表 -->
            <el-col :span="12">
              <el-card class="card" shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span><i class="el-icon-document-checked card-icon"></i> 待办事项</span>
                  </div>
                </template>
                <div class="list-container">
                  <el-empty v-if="todoList.length === 0" description="暂无待办事项" />
                  <div v-else class="list-item" v-for="item in todoList" :key="item.id">
                    <div class="item-title" @click="handleTodoClick(item)">
                      <el-icon><document /></el-icon> {{ item.content }}
                    </div>
                    <div class="item-info">
                      <span class="item-date">{{ item.date || '今天' }}</span>
                      <el-tag size="small" effect="plain" type="info">{{ item.status || '待处理' }}</el-tag>
                      <el-button v-if="item.todoType === 1" size="small" type="success" @click="confirmTodoComplete(item)">完成</el-button>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>

            <!-- 右侧：我的进度 -->
            <el-col :span="12">
              <el-card class="card" shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span><i class="el-icon-trend-charts card-icon"></i> 我的进度</span>
                  </div>
                </template>
                <div class="progress-container">
                  <div v-if="!isLoggedIn" class="progress-empty">
                    <el-empty description="请先登录查看进度" />
                  </div>
                  <div v-else class="progress-chart">
                    <ProgressChart
                      :user-progress="userProgress"
                      :loading="progressLoading"
                    />
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 下部分：系统卡片 -->
        <div class="bottom-section">
          <div class="section-header">
            <h2 class="section-title"><i class="el-icon-menu"></i> 系统导航</h2>
            <div class="section-divider"></div>
          </div>
          <el-row :gutter="30">
            <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="system in systemList" :key="system.id">
              <el-card
                  class="system-card"
                  shadow="hover"
                  @click="handleSystemClick(system)"
                  :style="(system.bgImage) ? `background-image: url(${system.bgImage}); background-size: cover; background-position: center;` : ''"
                  :class="{
                  'resume-system': system.name === '墨仔简历系统',
                  'interview-system': system.name === '墨仔面试系统',
                }"
              >
                <div class="system-content" style="background: rgba(255,255,255,0.85); border-radius: 8px; padding: 24px 0;">
                  <div class="system-icon">
                    <el-icon :size="32"><component :is="getSystemIcon(system.name)" /></el-icon>
                  </div>
                  <div class="system-name">{{ system.name }}</div>
                  <div class="system-desc">{{ system.description }}</div>
                  <div class="system-action">
                    <el-button type="primary" size="small" round>进入系统</el-button>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-main>

    <!-- 页脚 -->
    <el-footer class="footer">
      <div class="footer-content">
        <div class="footer-copyright">
          © 2025 笔墨屋统一认证服务平台 - 版权所有
        </div>
      </div>
    </el-footer>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../store'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getTodoList, getSystems, completeTodo, getUserProgress } from '../api/home'
import { logout } from '../api/user'
import {
  ArrowDown, User, SwitchButton, Document, Reading,
  Promotion, Calendar, Briefcase, Monitor, Connection,
  Notebook, Headset, PieChart, Compass
} from '@element-plus/icons-vue'
import request from '../utils/request'
import ProgressChart from '../components/ProgressChart.vue'

const router = useRouter()
const userStore = useUserStore()

// 用户信息
const userInfo = computed(() => userStore.userInfo)
const isLoggedIn = computed(() => userStore.isLoggedIn)

// 待办列表
const todoList = ref([])

// 系统列表
const systemList = ref([])

// 进度相关数据
const progressLoading = ref(false)
const userProgress = ref(null)



// 定时器引用
let tokenCheckTimer = null



// Token有效性检查
const checkTokenValidity = async () => {
  // 只有登录状态才检查token有效性
  if (userStore.isLoggedIn) {
    try {
      // 调用validateToken接口检查token是否有效
      await request({
        url: '/sso/validateToken',
        method: 'get'
      })

      // 如果请求成功，token有效，不需要其他操作
      console.log('Token验证成功，继续保持登录状态')
    } catch (error) {
      console.error('Token验证失败:', error)

      // 检查错误类型，只有在真正的token失效时才处理
      if (error.message && (error.message.includes('token') || error.message.includes('登录'))) {
        // Token已失效，清除用户信息并显示提示
        userStore.logout()
        ElMessage.warning('登录已过期，请重新登录')

        // 刷新页面数据状态
        todoList.value = []
      }
    }
  }
}

// 根据系统名称获取对应的图标
const getSystemIcon = (name) => {
  const iconMap = {
    '墨仔简历系统': 'Document',
    '墨仔面试系统': 'Headset',
    '面试喵': 'Notebook',
    '默认': 'Monitor'
  }
  return iconMap[name] || iconMap['默认']
}

// 获取待办列表
const fetchTodoList = async () => {
  try {
    const res = await getTodoList()
    todoList.value = res.data || []
  } catch (error) {
    console.error('获取待办列表失败:', error)
    // 未登录状态下，设置为空数组
    if (error.response && error.response.status === 401) {
      todoList.value = []
    }
  }
}

// 获取用户进度
const fetchUserProgress = async () => {
  if (!userStore.isLoggedIn) {
    return
  }

  try {
    progressLoading.value = true
    const res = await getUserProgress()
    userProgress.value = res.data || null
  } catch (error) {
    console.error('获取用户进度失败:', error)
    // 如果获取失败，设置默认进度（未开始）
    userProgress.value = null
  } finally {
    progressLoading.value = false
  }
}

// 获取系统列表
const fetchSystemList = async () => {
  try {
    const res = await getSystems()
    systemList.value = res.data || []
  } catch (error) {
    console.error('获取系统列表失败:', error)
  }
}

// 确认完成学习待办事项
const confirmTodoComplete = (todo) => {
  ElMessageBox.confirm('您确定已完成相关知识的学习了吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    handleTodoComplete(todo)
  }).catch(() => {})
}

// 完成待办事项
const handleTodoComplete = async (todo) => {
  try {
    await completeTodo(todo.id)
    ElMessage.success(`已完成待办事项：${todo.content}`)
    // 刷新待办列表
    await fetchTodoList()
  } catch (error) {
    console.error('完成待办事项失败:', error)
    ElMessage.error('操作失败，请重试')
  }
}

// 点击系统卡片
const handleSystemClick = (system) => {
  window.open(system.url, '_blank')
}

// 点击待办事项标题
const handleTodoClick = (todo) => {
  if (todo.url) {
    window.open(todo.url, '_blank')
  }
}

// 退出登录
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      localStorage.setItem('logoutFlag', '1') // 主动退出标记
      await logout()
      userStore.logout()
      ElMessage.success('已退出登录')
      // 不跳转，保留在首页
    } catch (error) {
      console.error('退出登录失败:', error)
      userStore.logout() // 即使API调用失败，也清除本地存储的用户信息
      ElMessage.warning('退出登录失败，但已清除本地登录状态')
      // 不跳转，保留在首页
    }
  }).catch(() => {})
}

// 跳转到登录页
const goToLogin = () => {
  router.push({
    path: '/login',
    query: { clientId: 'sso_client' }
  })
}

// 跳转到个人中心
const goToProfile = () => {
  router.push('/profile')
}

// 组件挂载时获取数据
onMounted(() => {
  // 首次检查token有效性
  checkTokenValidity()

  // 设置定时器，每5分钟检查一次token有效性
  tokenCheckTimer = setInterval(checkTokenValidity, 5 * 60 * 1000)

  // 只有登录状态才获取待办列表和用户进度
  if (userStore.isLoggedIn) {
    fetchTodoList()
    fetchUserProgress()
  } else {
    // 未登录状态下设置为空数组
    todoList.value = []
  }

  // 系统列表可以正常获取
  fetchSystemList()
})

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  if (tokenCheckTimer) {
    clearInterval(tokenCheckTimer)
    tokenCheckTimer = null
  }
})
</script>

<script>
export default {
  components: {
    ProgressChart
  }
}
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  background-image: url('https://bmw-pic.oss-cn-beijing.aliyuncs.com/e7e619d5e087b8e1913c619881246800.jpeg');
  background-size: cover;
  background-attachment: fixed;
  background-position: center;
}

/* 添加一个遮罩层，使背景图片更加柔和 */
.home-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 0;
}

/* 顶部导航栏 */
.header {
  background-color: rgba(13, 58, 104, 0.8);
  color: #fff;
  padding: 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  position: sticky;
  top: 0;
  z-index: 100;
  height: 60px;
  line-height: 60px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
  max-width: 1200px;
  margin: 0 auto;
  height: 100%;
}

.logo {
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.logo-icon {
  margin-right: 10px;
  font-size: 24px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.el-dropdown-link {
  cursor: pointer;
  color: #fff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  background-color: #fff;
  color: #1890ff;
  font-weight: bold;
  margin-right: 8px;
}

.login-button {
  border-radius: 20px;
  padding: 8px 20px;
}

/* 横幅区域 */
.banner {
  background-color: rgba(13, 58, 104, 0.7);
  color: white;
  padding: 40px 0;
  margin-bottom: 30px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.banner-content {
  max-width: 800px;
  margin: 0 auto;
}

.banner-title {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 10px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.banner-desc {
  font-size: 18px;
  opacity: 0.9;
}

/* 主体内容 */
.main {
  flex: 1;
  padding: 0 0 30px 0;
  position: relative;
  z-index: 1;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.top-section {
  margin-bottom: 40px;
}

/* 卡片样式 */
.card {
  height: 400px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: none;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(5px);
}

.card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(to right, rgba(245, 247, 250, 0.8), rgba(255, 255, 255, 0.8));
  border-bottom: none;
  padding: 15px 20px;
}

.card-icon {
  margin-right: 8px;
  color: #1890ff;
}

.card-more {
  color: #1890ff;
  font-size: 14px;
}

.list-container {
  height: 320px;
  overflow-y: auto;
  padding: 0;
}

.list-container::-webkit-scrollbar {
  width: 6px;
}

.list-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.list-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.list-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.progress-container {
  height: 320px;
  padding: 0;
}

.progress-empty {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-chart {
  width: 100%;
  height: 100%;
}

.list-item {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  transition: all 0.2s ease;
  width: 100%;
  box-sizing: border-box;
}

.list-item:nth-child(2n) {
  background-color: rgba(247, 247, 247, 0.7);
}

.list-item:hover {
  background-color: rgba(24, 144, 255, 0.05);
}

.list-item:last-child {
  border-bottom: none;
}

.item-title {
  font-size: 16px;
  margin-bottom: 8px;
  cursor: pointer;
  color: #303133;
  font-weight: 500;
  width: 100%;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  line-height: 1.5;
  word-wrap: break-word;
  word-break: break-all;
}

.item-title:hover {
  color: #1890ff;
}

.item-info {
  font-size: 12px;
  color: #909399;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.item-info .el-button {
  margin-left: 10px;
  padding: 4px 10px;
  height: 24px;
}

.item-date, .item-author {
  display: inline-block;
}

/* 系统导航区域 */
.bottom-section {
  margin-top: 60px;
}

.section-header {
  margin-bottom: 30px;
  text-align: center;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 15px;
  display: inline-flex;
  align-items: center;
  gap: 10px;
}

.section-divider {
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, #1890ff, #36cfc9);
  margin: 0 auto;
  border-radius: 3px;
}

.system-card {
  min-height: 260px;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  transition: box-shadow 0.2s;
  background-color: #f7fafd;
}
.system-card:hover {
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.15);
}
.system-content {
  position: relative;
  z-index: 1;
  text-align: center;
  padding: 24px 0;
  border-radius: 8px;
}
.system-icon {
  margin-bottom: 16px;
}
.system-name {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
}
.system-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
}
.system-action {
  margin-top: 12px;
}

/* 页脚 */
.footer {
  background-color: rgba(13, 58, 104, 0.8);
  color: rgba(255, 255, 255, 0.65);
  text-align: center;
  padding: 30px 0;
  margin-top: 30px;
  position: relative;
  z-index: 1;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}

.footer-links {
  margin-bottom: 15px;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
  margin: 0 15px;
  text-decoration: none;
  transition: color 0.3s;
}

.footer-links a:hover {
  color: #fff;
}

.footer-copyright {
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .banner {
    padding: 30px 0;
  }

  .banner-title {
    font-size: 24px;
  }

  .banner-desc {
    font-size: 16px;
  }

  .main {
    padding: 0 0 20px 0;
  }

  .container {
    padding: 0 15px;
  }

  .card {
    height: 350px;
  }

  .list-container {
    height: 270px;
  }

  .system-card {
    height: 180px;
  }
}
</style> 