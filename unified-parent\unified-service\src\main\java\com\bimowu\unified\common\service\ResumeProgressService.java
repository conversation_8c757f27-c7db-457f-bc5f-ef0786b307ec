package com.bimowu.unified.common.service;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.common.model.ResumeProgress;

/**
 * 简历进度服务接口
 */
public interface ResumeProgressService {
    
    /**
     * 获取用户的简历进度
     * @param userId 用户ID
     * @return 简历进度信息
     */
    BaseResponse<ResumeProgress> getProgressByUserId(Integer userId);
    
    /**
     * 创建或更新用户的简历进度
     * @param userId 用户ID
     * @param currentStage 当前阶段
     * @return 操作结果
     */
    BaseResponse<String> updateProgress(Integer userId, String currentStage);
    
    /**
     * 创建或更新用户的简历进度
     * @param userId 用户ID
     * @param currentStage 当前阶段
     * @param resumeId 简历ID
     * @return 操作结果
     */
    BaseResponse<String> updateProgress(Integer userId, String currentStage, Long resumeId);
    
    /**
     * 创建HR面试待办任务
     * @param userId 用户ID
     * @param systemUrl 系统URL
     * @return 操作结果
     */
    BaseResponse<String> createHrInterviewTodo(Long userId, String systemUrl);
    
    /**
     * 创建HR面试待办任务
     * @param userId 用户ID
     * @param systemUrl 系统URL
     * @param resumeId 简历ID
     * @return 操作结果
     */
    BaseResponse<String> createHrInterviewTodo(Long userId, String systemUrl, Long resumeId);

    /**
     * 根据用户ID和职位类别ID获取简历进度
     * @param userId 用户ID
     * @param catId 职位类别ID
     * @return 简历进度信息
     */
    BaseResponse<ResumeProgress> getProgressByUserIdAndCatId(Integer userId, Long catId);
} 