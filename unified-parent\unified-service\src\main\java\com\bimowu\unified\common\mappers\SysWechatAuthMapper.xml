<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.unified.common.dao.SysWechatAuthDao">
    <resultMap id="BaseResultMap" type="com.bimowu.unified.common.model.SysWechatAuth">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="scene_str" jdbcType="VARCHAR" property="sceneStr" />
        <result column="qrcode_url" jdbcType="VARCHAR" property="qrcodeUrl" />
        <result column="openid" jdbcType="VARCHAR" property="openid" />
        <result column="unionid" jdbcType="VARCHAR" property="unionid" />
        <result column="nickname" jdbcType="VARCHAR" property="nickname" />
        <result column="subscribe" jdbcType="INTEGER" property="subscribe" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, scene_str, qrcode_url, openid, unionid, nickname, subscribe, status, expire_time, create_time, update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_wechat_auth
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectBySceneStr" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_wechat_auth
        where scene_str = #{sceneStr,jdbcType=VARCHAR}
    </select>

    <select id="selectLatestByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_wechat_auth
        where user_id = #{userId,jdbcType=BIGINT}
        order by create_time desc
        limit 1
    </select>

    <select id="selectByOpenid" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_wechat_auth
        where openid = #{openid,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from sys_wechat_auth
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.bimowu.unified.common.model.SysWechatAuth" useGeneratedKeys="true" keyProperty="id">
        insert into sys_wechat_auth (
            user_id, scene_str, qrcode_url, openid, unionid, nickname, 
            subscribe, status, expire_time
        )
        values (
            #{userId,jdbcType=BIGINT},
            #{sceneStr,jdbcType=VARCHAR},
            #{qrcodeUrl,jdbcType=VARCHAR},
            #{openid,jdbcType=VARCHAR},
            #{unionid,jdbcType=VARCHAR},
            #{nickname,jdbcType=VARCHAR},
            #{subscribe,jdbcType=INTEGER},
            #{status,jdbcType=INTEGER},
            #{expireTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.bimowu.unified.common.model.SysWechatAuth">
        update sys_wechat_auth
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="sceneStr != null">
                scene_str = #{sceneStr,jdbcType=VARCHAR},
            </if>
            <if test="qrcodeUrl != null">
                qrcode_url = #{qrcodeUrl,jdbcType=VARCHAR},
            </if>
            <if test="openid != null">
                openid = #{openid,jdbcType=VARCHAR},
            </if>
            <if test="unionid != null">
                unionid = #{unionid,jdbcType=VARCHAR},
            </if>
            <if test="nickname != null">
                nickname = #{nickname,jdbcType=VARCHAR},
            </if>
            <if test="subscribe != null">
                subscribe = #{subscribe,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="expireTime != null">
                expire_time = #{expireTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper> 