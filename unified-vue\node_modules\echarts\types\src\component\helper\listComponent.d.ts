/**
 * Layout list like component.
 * It will box layout each items in group of component and then position the whole group in the viewport
 * @param {module:zrender/group/Group} group
 * @param {module:echarts/model/Component} componentModel
 * @param {module:echarts/ExtensionAPI}
 */
export declare function layout(group: any, componentModel: any, api: any): void;
export declare function makeBackground(rect: any, componentModel: any): any;
