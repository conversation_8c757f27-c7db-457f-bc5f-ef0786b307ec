package com.bimowu.unified.common.dao;

import com.bimowu.unified.common.model.ResumeProgress;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * ResumeProgress数据库操作接口类
 */
@Repository
public interface ResumeProgressDao {
    
    /**
     * 插入记录
     */
    int insert(ResumeProgress record);
    
    /**
     * 更新记录
     */
    int update(ResumeProgress record);
    
    /**
     * 更新进度阶段
     */
    int updateStage(@Param("id") Long id, @Param("currentStage") String currentStage);
    
    /**
     * 逻辑删除
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据ID查询记录
     */
    ResumeProgress selectById(@Param("id") Long id);
    
    /**
     * 根据用户ID查询记录
     */
    ResumeProgress selectByUserId(@Param("userId") Integer userId);
    
    /**
     * 根据简历ID查询记录
     */
    ResumeProgress selectByResumeId(@Param("resumeId") Long resumeId);
    
    /**
     * 根据条件查询记录
     */
    List<ResumeProgress> selectByCondition(Map<String, Object> params);
} 