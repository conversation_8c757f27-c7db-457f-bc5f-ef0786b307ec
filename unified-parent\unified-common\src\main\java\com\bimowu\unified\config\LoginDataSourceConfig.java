package com.bimowu.unified.config;

import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.Properties;

/**
 * 登录数据源MyBatis配置
 */
@Configuration
@MapperScan(basePackages = {"com.bimowu.unified.login.dao"}, 
            sqlSessionFactoryRef = "loginSqlSessionFactory",
            sqlSessionTemplateRef = "loginSqlSessionTemplate")
@EnableTransactionManagement
public class LoginDataSourceConfig {
    
    @Autowired
    @Qualifier("loginDataSource")
    private DataSource loginDataSource;
    
    @Autowired
    @Qualifier("loginTransactionManager")
    private PlatformTransactionManager loginTransactionManager;
    
    /**
     * 登录数据源的MyBatis配置
     */
    @Bean(name = "loginMybatisConfig")
    public org.apache.ibatis.session.Configuration loginMybatisConfig() {
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        // 开启驼峰命名
        configuration.setMapUnderscoreToCamelCase(true);
        // 允许JDBC生成主键
        configuration.setUseGeneratedKeys(true);
        // 配置默认的执行器
        configuration.setDefaultExecutorType(org.apache.ibatis.session.ExecutorType.REUSE);
        // 设置超时时间
        configuration.setDefaultStatementTimeout(25000);
        
        return configuration;
    }
    
    /**
     * 登录数据源的SqlSessionFactory配置
     * 这个方法在DataSourceConfig中已经定义，但这里可以添加更多配置
     */
    @Bean(name = "loginSqlSessionFactoryBean")
    public SqlSessionFactoryBean loginSqlSessionFactoryBean() throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(loginDataSource);
        bean.setConfiguration(loginMybatisConfig());
        
        // 设置类型别名包
        bean.setTypeAliasesPackage("com.bimowu.unified.login.model");
        
        // 设置mapper.xml文件位置
        bean.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources("classpath*:com/bimowu/unified/login/mappers/*.xml"));
        
        // 设置分页插件等其他插件
        Properties properties = new Properties();
        properties.setProperty("reasonable", "true");
        properties.setProperty("supportMethodsArguments", "true");
        properties.setProperty("returnPageInfo", "check");
        properties.setProperty("params", "count=countSql");
        
        return bean;
    }
} 