import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '../router'

// 根据当前部署环境确定baseURL
// 在开发环境使用/api，在生产环境直接使用/unified
const isProduction = process.env.NODE_ENV === 'production'
// 检查是否在bimowo.com域名下访问
const isBimowoHost = window.location.hostname === 'ceping.bimowo.com'
// 检查是否是直接访问文件系统
const isDirectFileAccess = window.location.protocol === 'file:' || window.location.pathname.endsWith('/index.html')

// 设置基础URL
let baseURL = isProduction ? '/unified' : '/api'

// 如果是直接访问文件系统或者index.html，使用完整的API URL
if (isProduction && (isDirectFileAccess || !isBimowoHost)) {
    // baseURL = 'https://ceping.bimowo.com/unified'
    baseURL = 'http://127.0.0.1:7092/unified'
}

const service = axios.create({
    baseURL,  // 使用上面定义的变量
    timeout: 10000,
    withCredentials: true // 允许跨域请求携带凭证
})

// 请求拦截器
service.interceptors.request.use(
    config => {
        const token = localStorage.getItem('token')
        if (token) {
            // 使用token作为请求头，适配SSO接口
            config.headers['token'] = token
        }
        return config
    },
    error => {
        console.error('请求错误：', error)
        return Promise.reject(error)
    }
)

// 响应拦截器
service.interceptors.response.use(
    response => {
        const res = response.data

        // 如果返回的状态码不是0，说明接口请求有问题
        if (res.code !== 0) {
            // 只在非401时弹出error
            if (res.code !== 401) {
                ElMessage({
                    message: res.message || '请求出错',
                    type: 'error',
                    duration: 3 * 1000
                })
            }
            // 如果是未登录状态，跳转到登录页
            if (res.code === 401) {
                // 检查是否为主动退出
                if (!localStorage.getItem('logoutFlag')) {
                    ElMessage.warning('登录已过期，请重新登录')
                } else {
                    localStorage.removeItem('logoutFlag')
                }
                localStorage.removeItem('token')
                localStorage.removeItem('userInfo')

                // 只有当前不在登录页时才跳转
                if (router.currentRoute.value.path !== '/login') {
                    router.push('/login')
                }
            }
            return Promise.reject(new Error(res.message || '请求出错'))
        } else {
            return res
        }
    },
    error => {
        console.error('响应错误：', error)
        ElMessage({
            message: error.message || '请求失败',
            type: 'error',
            duration: 3 * 1000
        })
        return Promise.reject(error)
    }
)

export default service 