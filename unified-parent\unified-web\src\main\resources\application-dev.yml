spring:
  datasource:
    # 主数据源配置 - 用于除登录外的所有接口
    primary:
      url: jdbc:mysql://************:3306/interview?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Hongkong
      username: deploy
      password: d123456d
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
    # 登录数据源配置 - 仅用于登录接口
    login:
      url: jdbc:mysql://************:3306/kstudy?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Hongkong
      username: deploy
      password: d123456d
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
  redis: # Redis数据库索引（默认为0）
    database: '0'
    host: ************  #Redis服务器地址
    port: 6379  # Redis服务器连接端口
    password: HuAxIad0e37EE6054667bd9745d5400ecabc
    jedis:
      pool:
        max-active: 10000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1  # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10000 # 连接池中的最大空闲连接
        min-idle: 0  # 连接池中的最小空闲连接
    timeout: 10000 # 连接超时时间（毫秒）
swagger:
  enable: true
test:
  env: dev
login:
  loginUrl: http://localhost:5173/
interview:
  url: https://ceping.bimowo.com/interview-ai/index.html#/selection
sms:
  daily-limit: 5