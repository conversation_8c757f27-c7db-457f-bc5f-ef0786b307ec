import{y as le,j as D,r as k,A as re,B as ce,k as l,l as c,C as m,F as s,G as t,E as g,D as e,I as r,p,K as f,J as d,L as E,M as F,N as H,O as V,P as ie,Q as de,R as ue,H as _e,S as me,T as pe,U as ge}from"./vendor-477e13dd.js";import{s as v,u as fe,b as ve}from"./index-23439adc.js";import{P as j}from"./ProgressChart-d69135bf.js";import{_ as ye}from"./index-9768669e.js";function he(){return v({url:"/todo/list",method:"get"})}function ke(L){return v({url:`/todo/complete/${L}`,method:"post"})}function we(){return v({url:"/system/list",method:"get"})}function be(){return v({url:"/progress/user",method:"get"})}const Ce={class:"home-container"},Te={class:"header-content"},Ie={class:"user-info"},xe={class:"el-dropdown-link"},Le={class:"container"},Be={class:"top-section"},Se={class:"list-container"},ze=["onClick"],Pe={class:"item-info"},Me={class:"item-date"},Ne={class:"card-header"},Ue={class:"progress-container"},$e={key:0,class:"progress-empty"},De={key:1,class:"progress-chart"},Ee={class:"bottom-section"},Fe={class:"system-content",style:{background:"rgba(255,255,255,0.85)","border-radius":"8px",padding:"24px 0"}},He={class:"system-icon"},Ve={class:"system-name"},je={class:"system-desc"},Oe={class:"system-action"},Re={components:{ProgressChart:j}},qe=Object.assign(Re,{__name:"Home",setup(L){const w=le(),i=fe(),b=D(()=>i.userInfo),B=D(()=>i.isLoggedIn),u=k([]),S=k([]),C=k(!1),T=k(null);let y=null;const z=async()=>{if(i.isLoggedIn)try{await v({url:"/sso/validateToken",method:"get"}),console.log("Token验证成功，继续保持登录状态")}catch(o){console.error("Token验证失败:",o),o.message&&(o.message.includes("token")||o.message.includes("登录"))&&(i.logout(),g.warning("登录已过期，请重新登录"),u.value=[])}},O=o=>{const n={墨仔简历系统:"Document",墨仔面试系统:"Headset",面试喵:"Notebook",默认:"Monitor"};return n[o]||n.默认},P=async()=>{try{const o=await he();u.value=o.data||[]}catch(o){console.error("获取待办列表失败:",o),o.response&&o.response.status===401&&(u.value=[])}},R=async()=>{if(i.isLoggedIn)try{C.value=!0;const o=await be();T.value=o.data||null}catch(o){console.error("获取用户进度失败:",o),T.value=null}finally{C.value=!1}},q=async()=>{try{const o=await we();S.value=o.data||[]}catch(o){console.error("获取系统列表失败:",o)}},A=o=>{H.confirm("您确定已完成相关知识的学习了吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then(()=>{G(o)}).catch(()=>{})},G=async o=>{try{await ke(o.id),g.success(`已完成待办事项：${o.content}`),await P()}catch(n){console.error("完成待办事项失败:",n),g.error("操作失败，请重试")}},J=o=>{window.open(o.url,"_blank")},K=o=>{o.url&&window.open(o.url,"_blank")},Q=()=>{H.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{localStorage.setItem("logoutFlag","1"),await ve(),i.logout(),g.success("已退出登录")}catch(o){console.error("退出登录失败:",o),i.logout(),g.warning("退出登录失败，但已清除本地登录状态")}}).catch(()=>{})},W=()=>{w.push({path:"/login",query:{clientId:"sso_client"}})},X=()=>{w.push("/profile")},Y=()=>{w.push("/progress-demo")};return re(()=>{z(),y=setInterval(z,5*60*1e3),i.isLoggedIn?(P(),R()):u.value=[],q()}),ce(()=>{y&&(clearInterval(y),y=null)}),(o,n)=>{const Z=l("el-avatar"),_=l("el-icon"),M=l("el-dropdown-item"),ee=l("el-dropdown-menu"),oe=l("el-dropdown"),h=l("el-button"),se=l("el-header"),N=l("el-empty"),te=l("el-tag"),I=l("el-card"),x=l("el-col"),U=l("el-row"),ne=l("el-main"),ae=l("el-footer");return c(),m("div",Ce,[s(se,{class:"header"},{default:t(()=>[e("div",Te,[n[3]||(n[3]=e("div",{class:"logo"},[e("i",{class:"el-icon-platform-eleme logo-icon"}),r(" 笔墨屋统一认证服务平台 ")],-1)),e("div",Ie,[B.value?(c(),p(oe,{key:0,trigger:"click"},{dropdown:t(()=>[s(ee,null,{default:t(()=>[s(M,{onClick:X},{default:t(()=>[s(_,null,{default:t(()=>[s(f(V))]),_:1}),n[0]||(n[0]=r(" 个人中心 "))]),_:1,__:[0]}),s(M,{onClick:Q},{default:t(()=>[s(_,null,{default:t(()=>[s(f(ie))]),_:1}),n[1]||(n[1]=r(" 退出登录 "))]),_:1,__:[1]})]),_:1})]),default:t(()=>[e("span",xe,[s(Z,{size:32,class:"user-avatar"},{default:t(()=>[r(d(b.value.nickname?b.value.nickname.substring(0,1):"U"),1)]),_:1}),r(" 欢迎，"+d(b.value.nickname)+" ",1),s(_,{class:"el-icon--right"},{default:t(()=>[s(f(de))]),_:1})])]),_:1})):(c(),p(h,{key:1,type:"primary",onClick:W,class:"login-button"},{default:t(()=>[s(_,null,{default:t(()=>[s(f(V))]),_:1}),n[2]||(n[2]=r(" 登录 "))]),_:1,__:[2]}))])])]),_:1}),s(ne,{class:"main"},{default:t(()=>[n[10]||(n[10]=e("div",{class:"banner"},[e("div",{class:"banner-content"},[e("h1",{class:"banner-title"},"欢迎使用笔墨屋统一认证服务平台"),e("p",{class:"banner-desc"},"一站式登录，畅享所有系统服务")])],-1)),e("div",Le,[e("div",Be,[s(U,{gutter:20},{default:t(()=>[s(x,{span:12},{default:t(()=>[s(I,{class:"card",shadow:"hover"},{header:t(()=>n[4]||(n[4]=[e("div",{class:"card-header"},[e("span",null,[e("i",{class:"el-icon-document-checked card-icon"}),r(" 待办事项")])],-1)])),default:t(()=>[e("div",Se,[u.value.length===0?(c(),p(N,{key:0,description:"暂无待办事项"})):(c(!0),m(E,{key:1},F(u.value,a=>(c(),m("div",{class:"list-item",key:a.id},[e("div",{class:"item-title",onClick:$=>K(a)},[s(_,null,{default:t(()=>[s(f(ue))]),_:1}),r(" "+d(a.content),1)],8,ze),e("div",Pe,[e("span",Me,d(a.date||"今天"),1),s(te,{size:"small",effect:"plain",type:"info"},{default:t(()=>[r(d(a.status||"待处理"),1)]),_:2},1024),a.todoType===1?(c(),p(h,{key:0,size:"small",type:"success",onClick:$=>A(a)},{default:t(()=>n[5]||(n[5]=[r("完成")])),_:2,__:[5]},1032,["onClick"])):_e("",!0)])]))),128))])]),_:1})]),_:1}),s(x,{span:12},{default:t(()=>[s(I,{class:"card",shadow:"hover"},{header:t(()=>[e("div",Ne,[n[7]||(n[7]=e("span",null,[e("i",{class:"el-icon-trend-charts card-icon"}),r(" 我的进度")],-1)),s(h,{type:"text",size:"small",onClick:Y,style:{color:"#409EFF"}},{default:t(()=>n[6]||(n[6]=[r(" 查看演示 ")])),_:1,__:[6]})])]),default:t(()=>[e("div",Ue,[B.value?(c(),m("div",De,[s(j,{"user-progress":T.value,loading:C.value},null,8,["user-progress","loading"])])):(c(),m("div",$e,[s(N,{description:"请先登录查看进度"})]))])]),_:1})]),_:1})]),_:1})]),e("div",Ee,[n[9]||(n[9]=e("div",{class:"section-header"},[e("h2",{class:"section-title"},[e("i",{class:"el-icon-menu"}),r(" 系统导航")]),e("div",{class:"section-divider"})],-1)),s(U,{gutter:30},{default:t(()=>[(c(!0),m(E,null,F(S.value,a=>(c(),p(x,{xs:24,sm:12,md:8,lg:6,key:a.id},{default:t(()=>[s(I,{class:me(["system-card",{"resume-system":a.name==="墨仔简历系统","interview-system":a.name==="墨仔面试系统"}]),shadow:"hover",onClick:$=>J(a),style:pe(a.bgImage?`background-image: url(${a.bgImage}); background-size: cover; background-position: center;`:"")},{default:t(()=>[e("div",Fe,[e("div",He,[s(_,{size:32},{default:t(()=>[(c(),p(ge(O(a.name))))]),_:2},1024)]),e("div",Ve,d(a.name),1),e("div",je,d(a.description),1),e("div",Oe,[s(h,{type:"primary",size:"small",round:""},{default:t(()=>n[8]||(n[8]=[r("进入系统")])),_:1,__:[8]})])])]),_:2},1032,["onClick","style","class"])]),_:2},1024))),128))]),_:1})])])]),_:1,__:[10]}),s(ae,{class:"footer"},{default:t(()=>n[11]||(n[11]=[e("div",{class:"footer-content"},[e("div",{class:"footer-copyright"}," © 2025 笔墨屋统一认证服务平台 - 版权所有 ")],-1)])),_:1,__:[11]})])}}}),Qe=ye(qe,[["__scopeId","data-v-dd16b5f7"]]);export{Qe as default};
