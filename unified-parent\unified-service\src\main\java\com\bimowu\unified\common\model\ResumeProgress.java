package com.bimowu.unified.common.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 进度管理实体类
 */
@Data
public class ResumeProgress {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 简历ID
     */
    private Long resumeId;
    
    /**
     * 当前进行到的阶段
     * 0：创建简历
     * 1：知识学习
     * 2：hr面试
     * 3：技术面试
     * 4：正式面试
     */
    private String currentStage;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 删除标识（0：未删除；1：已删除）
     */
    private Integer isDeleted;
} 