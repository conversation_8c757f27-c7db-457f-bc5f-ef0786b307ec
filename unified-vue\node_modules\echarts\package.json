{"name": "echarts", "version": "5.6.0", "description": "Apache ECharts is a powerful, interactive charting and data visualization library for browser", "license": "Apache-2.0", "keywords": ["echarts", "data-visualization", "charts", "charting-library", "visualization", "apache", "data-viz", "canvas", "svg"], "main": "dist/echarts.js", "module": "index.js", "jsdelivr": "dist/echarts.min.js", "types": "index.d.ts", "homepage": "https://echarts.apache.org", "bugs": {"url": "https://github.com/apache/echarts/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/apache/echarts.git"}, "sideEffects": ["index.js", "index.blank.js", "index.common.js", "index.simple.js", "lib/echarts.js", "lib/chart/*.js", "lib/component/*.js", "extension/**/*.js", "theme/*.js", "i18n/*.js"], "scripts": {"build": "node build/build.js --type all,common,simple --min", "build:esm": "node build/build.js --type all --min --format esm", "build:i18n": "node build/build-i18n.js", "build:lib": "node build/build.js --prepublish", "build:extension": "node build/build.js --type extension", "build:ssr": "node build/build.js --type ssr", "dev:fast": "node build/build-i18n.js && node build/dev-fast.js", "dev": "npx -y concurrently -n build,server \"npm run dev:fast\" \"npx -y http-server -c-1 -s -o test\"", "prepare": "npm run build:lib && husky install", "release": "npm run build:lib && npm run build:i18n && npm run build && npm run build:esm && npm run build:extension && npm run build:ssr", "help": "node build/build.js --help", "test:visual": "node test/runTest/server.js", "test": "npx jest --config test/ut/jest.config.cjs", "test:single": "npx jest --config test/ut/jest.config.cjs --coverage=false -t", "test:single:debug": "npx --node-arg=--inspect-brk jest --runInBand --config test/ut/jest.config.cjs --coverage=false -t", "test:dts": "node build/testDts.js", "mktest": "node test/build/mktest.js", "mktest:help": "node test/build/mktest.js -h", "checktype": "tsc --noEmit", "lint": "npx eslint --cache --cache-location node_modules/.cache/eslint src/**/*.ts ssr/client/src/**/*.ts extension-src/**/*.ts", "lint:fix": "npx eslint --fix src/**/*.ts extension-src/**/*.ts", "lint:dist": "echo 'It might take a while. Please wait ...' && npx jshint --config .jshintrc-dist dist/echarts.js"}, "dependencies": {"tslib": "2.3.0", "zrender": "5.6.1"}, "devDependencies": {"@babel/code-frame": "7.10.4", "@babel/core": "7.3.4", "@babel/types": "7.10.5", "@definitelytyped/typescript-versions": "^0.1.1", "@definitelytyped/utils": "0.0.188", "@lang/rollup-plugin-dts": "2.0.2", "@microsoft/api-extractor": "7.31.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chalk": "^3.0.0", "commander": "2.11.0", "dtslint": "^4.0.5", "esbuild": "^0.8.39", "eslint": "^7.15.0", "fs-extra": "^10.0.0", "globby": "11.0.0", "husky": "^8.0.3", "jest": "^26.6.1", "jest-canvas-mock": "^2.5.0", "jshint": "2.13.5", "magic-string": "^0.25.7", "open": "6.4.0", "rollup": "2.34.2", "rollup-plugin-terser": "^7.0.2", "seedrandom": "3.0.3", "semver": "6.3.0", "terser": "^5.16.1", "ts-jest": "^26.4.3", "typescript": "4.4.3"}, "type": "module", "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "require": "./dist/echarts.js"}, "./core": "./core.js", "./core.js": "./core.js", "./charts": "./charts.js", "./charts.js": "./charts.js", "./components": "./components.js", "./components.js": "./components.js", "./features": "./features.js", "./features.js": "./features.js", "./renderers": "./renderers.js", "./renderers.js": "./renderers.js", "./index.blank": "./index.blank.js", "./index.blank.js": "./index.blank.js", "./index.common": "./index.common.js", "./index.common.js": "./index.common.js", "./index.simple": "./index.simple.js", "./index.simple.js": "./index.simple.js", "./index": "./index.js", "./index.js": "./index.js", "./theme/*": "./theme/*", "./i18n/*": "./i18n/*", "./types/dist/charts": "./types/dist/charts.d.ts", "./types/dist/components": "./types/dist/components.d.ts", "./types/dist/core": "./types/dist/core.d.ts", "./types/dist/echarts": "./types/dist/echarts.d.ts", "./types/dist/features": "./types/dist/features.d.ts", "./types/dist/option": "./types/dist/option.d.ts", "./types/dist/renderers": "./types/dist/renderers.d.ts", "./types/dist/shared": "./types/dist/shared.d.ts", "./ssr/client/index": {"types": "./ssr/client/index.d.ts", "import": "./ssr/client/index.js", "require": "./ssr/client/dist/index.js"}, "./extension/dataTool": "./extension/dataTool/index.js", "./extension/dataTool/index": "./extension/dataTool/index.js", "./extension/dataTool/index.js": "./extension/dataTool/index.js", "./extension/dataTool/gexf": "./extension/dataTool/gexf.js", "./extension/dataTool/gexf.js": "./extension/dataTool/gexf.js", "./extension/dataTool/prepareBoxplotData": "./extension/dataTool/prepareBoxplotData.js", "./extension/dataTool/prepareBoxplotData.js": "./extension/dataTool/prepareBoxplotData.js", "./extension/bmap/bmap": "./extension/bmap/bmap.js", "./extension/bmap/bmap.js": "./extension/bmap/bmap.js", "./lib/echarts": "./lib/echarts.js", "./lib/echarts.js": "./lib/echarts.js", "./lib/extension": "./lib/extension.js", "./lib/extension.js": "./lib/extension.js", "./lib/chart/bar": "./lib/chart/bar.js", "./lib/chart/boxplot": "./lib/chart/boxplot.js", "./lib/chart/candlestick": "./lib/chart/candlestick.js", "./lib/chart/custom": "./lib/chart/custom.js", "./lib/chart/effectScatter": "./lib/chart/effectScatter.js", "./lib/chart/funnel": "./lib/chart/funnel.js", "./lib/chart/gauge": "./lib/chart/gauge.js", "./lib/chart/graph": "./lib/chart/graph.js", "./lib/chart/heatmap": "./lib/chart/heatmap.js", "./lib/chart/line": "./lib/chart/line.js", "./lib/chart/lines": "./lib/chart/lines.js", "./lib/chart/map": "./lib/chart/map.js", "./lib/chart/parallel": "./lib/chart/parallel.js", "./lib/chart/pictorialBar": "./lib/chart/pictorialBar.js", "./lib/chart/pie": "./lib/chart/pie.js", "./lib/chart/radar": "./lib/chart/radar.js", "./lib/chart/sankey": "./lib/chart/sankey.js", "./lib/chart/scatter": "./lib/chart/scatter.js", "./lib/chart/sunburst": "./lib/chart/sunburst.js", "./lib/chart/themeRiver": "./lib/chart/themeRiver.js", "./lib/chart/tree": "./lib/chart/tree.js", "./lib/chart/treemap": "./lib/chart/treemap.js", "./lib/component/aria": "./lib/component/aria.js", "./lib/component/axisPointer": "./lib/component/axisPointer.js", "./lib/component/brush": "./lib/component/brush.js", "./lib/component/calendar": "./lib/component/calendar.js", "./lib/component/dataZoom": "./lib/component/dataZoom.js", "./lib/component/dataZoomInside": "./lib/component/dataZoomInside.js", "./lib/component/dataZoomSelect": "./lib/component/dataZoomSelect.js", "./lib/component/dataZoomSlider": "./lib/component/dataZoomSlider.js", "./lib/component/dataset": "./lib/component/dataset.js", "./lib/component/geo": "./lib/component/geo.js", "./lib/component/graphic": "./lib/component/graphic.js", "./lib/component/grid": "./lib/component/grid.js", "./lib/component/gridSimple": "./lib/component/gridSimple.js", "./lib/component/legend": "./lib/component/legend.js", "./lib/component/legendPlain": "./lib/component/legendPlain.js", "./lib/component/legendScroll": "./lib/component/legendScroll.js", "./lib/component/markArea": "./lib/component/markArea.js", "./lib/component/markLine": "./lib/component/markLine.js", "./lib/component/markPoint": "./lib/component/markPoint.js", "./lib/component/parallel": "./lib/component/parallel.js", "./lib/component/polar": "./lib/component/polar.js", "./lib/component/radar": "./lib/component/radar.js", "./lib/component/singleAxis": "./lib/component/singleAxis.js", "./lib/component/timeline": "./lib/component/timeline.js", "./lib/component/title": "./lib/component/title.js", "./lib/component/toolbox": "./lib/component/toolbox.js", "./lib/component/tooltip": "./lib/component/tooltip.js", "./lib/component/transform": "./lib/component/transform.js", "./lib/component/visualMap": "./lib/component/visualMap.js", "./lib/component/visualMapContinuous": "./lib/component/visualMapContinuous.js", "./lib/component/visualMapPiecewise": "./lib/component/visualMapPiecewise.js", "./dist/echarts.common": "./dist/echarts.common.js", "./dist/echarts.common.min": "./dist/echarts.common.min.js", "./dist/echarts.esm": "./dist/echarts.esm.mjs", "./dist/echarts.esm.min": "./dist/echarts.esm.min.mjs", "./dist/echarts": "./dist/echarts.js", "./dist/echarts.min": "./dist/echarts.min.js", "./dist/echarts.simple": "./dist/echarts.simple.js", "./dist/echarts.simple.min": "./dist/echarts.simple.min.js", "./dist/extension/bmap": "./dist/extension/bmap.js", "./dist/extension/bmap.min": "./dist/extension/bmap.min.js", "./dist/extension/dataTool": "./dist/extension/dataTool.js", "./dist/extension/dataTool.min": "./dist/extension/dataTool.min.js", "./*": "./*"}}