# 统一认证系统（SSO）使用说明

## 系统概述

本系统是一个基于Redis的SSO（单点登录）统一认证系统，提供了用户登录、验证、注销等功能，可供外部系统接入使用。

## 技术架构

- 后端框架：Spring Boot
- 数据库：MySQL
- 缓存：Redis
- 认证方式：Token

## 接口说明

### 1. 注册客户端应用

```
POST /unified/sso/registerClient
```

请求参数：
```json
{
  "appName": "应用名称",
  "appUrl": "应用URL",
  "redirectUrl": "回调URL"
}
```

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "clientId": "client_xxx",
    "clientSecret": "xxx"
  }
}
```

### 2. 用户登录

```
POST /unified/sso/login
```

请求参数：
```json
{
  "username": "用户名/邮箱/手机号",
  "password": "密码",
  "clientId": "客户端ID"
}
```

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "token": "xxx",
    "refreshToken": "xxx",
    "expiresIn": 7200,
    "userInfo": {
      "id": 1,
      "nickname": "用户昵称",
      "avatar": "头像URL",
      "mobile": "手机号",
      "email": "邮箱",
      "gender": 1,
      "lastLoginTime": "2023-01-01 12:00:00"
    }
  }
}
```

### 3. 手机号登录

```
POST /unified/sso/mobileLogin
```

请求参数：
```json
{
  "mobile": "手机号",
  "verifyCode": "验证码",
  "clientId": "客户端ID"
}
```

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "token": "xxx",
    "refreshToken": "xxx",
    "expiresIn": 7200,
    "userInfo": {
      "id": 1,
      "nickname": "用户昵称",
      "avatar": "头像URL",
      "mobile": "手机号",
      "email": "邮箱",
      "gender": 1,
      "lastLoginTime": "2023-01-01 12:00:00"
    }
  }
}
```

### 4. 获取短信验证码

```
GET /unified/sso/verifyCode?mobile=手机号
```

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": "验证码发送成功"
}
```

### 5. 验证token

```
GET /unified/sso/validateToken?token=xxx
```

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "userId": 1,
    "userInfo": {
      "id": 1,
      "nickname": "用户昵称",
      "avatar": "头像URL",
      "mobile": "手机号",
      "email": "邮箱",
      "gender": 1,
      "lastLoginTime": "2023-01-01 12:00:00"
    },
    "clientId": "client_xxx"
  }
}
```

### 6. 刷新token

```
POST /unified/sso/refreshToken
```

请求参数：
```json
{
  "token": "旧token",
  "clientId": "客户端ID"
}
```

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "token": "新token",
    "refreshToken": "新refreshToken",
    "expiresIn": 7200,
    "userInfo": {
      "id": 1,
      "nickname": "用户昵称",
      "avatar": "头像URL",
      "mobile": "手机号",
      "email": "邮箱",
      "gender": 1,
      "lastLoginTime": "2023-01-01 12:00:00"
    }
  }
}
```

### 7. 退出登录

```
POST /unified/sso/logout?token=xxx
```

响应结果：
```json
{
  "code": 0,
  "msg": "success",
  "data": "退出成功"
}
```

## 接入流程

1. 调用注册客户端应用接口，获取clientId和clientSecret
2. 使用clientId调用登录接口，获取token
3. 使用token调用业务接口
4. token过期后，使用refreshToken获取新的token
5. 用户退出时，调用退出登录接口

## 错误码说明

- 0: 成功
- 1000: 无效的token
- 1001: 用户不存在
- 1002: 密码错误
- 1003: 账号已禁用
- 1004: 验证码错误或已过期
- 1100: 无效的客户端ID
- 1101: 客户端ID不匹配
- 1102: 应用名称已存在
- 1200: 手机号不能为空

## 部署说明

1. 创建数据库表：执行 `unified-parent/unified-web/src/main/resources/sql/sso_client.sql` 脚本
2. 配置Redis连接：修改 `application-dev.yml` 中的Redis配置
3. 启动应用：运行 `UnifiedWebApplication.java` 