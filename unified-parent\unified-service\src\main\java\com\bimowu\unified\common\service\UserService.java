package com.bimowu.unified.common.service;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.common.model.SysUser;
import com.bimowu.unified.common.model.SysWechatAuth;

import java.util.Map;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 生成微信绑定二维码
     * @param userId 用户ID
     * @return 二维码信息
     */
    BaseResponse<Map<String, Object>> generateWechatQrCode(Long userId);
    
    /**
     * 查询微信绑定状态
     * @param userId 用户ID
     * @return 绑定状态
     */
    BaseResponse<Map<String, Object>> checkWechatBindStatus(Long userId);
    
    /**
     * 微信扫码回调处理
     * @param sceneStr 场景值
     * @param openid 微信OpenID
     * @param unionid 微信UnionID
     * @param nickname 微信昵称
     * @param subscribe 是否关注
     * @return 处理结果
     */
    BaseResponse<String> handleWechatScan(String sceneStr, String openid, String unionid, String nickname, Integer subscribe);

} 