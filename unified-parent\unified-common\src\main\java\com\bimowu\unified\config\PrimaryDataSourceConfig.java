package com.bimowu.unified.config;

import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.TransactionManagementConfigurer;

import javax.sql.DataSource;
import java.util.Properties;

/**
 * 主数据源MyBatis配置
 */
@Configuration
@MapperScan(basePackages = {"com.bimowu.unified.common.dao"}, 
            sqlSessionFactoryRef = "primarySqlSessionFactory",
            sqlSessionTemplateRef = "primarySqlSessionTemplate")
@EnableTransactionManagement
public class PrimaryDataSourceConfig implements TransactionManagementConfigurer {

    @Autowired
    @Qualifier("primaryDataSource")
    private DataSource primaryDataSource;
    
    @Autowired
    @Qualifier("primaryTransactionManager")
    private PlatformTransactionManager primaryTransactionManager;
    
    /**
     * 返回主数据源的事务管理器作为默认事务管理器
     */
    @Override
    public PlatformTransactionManager annotationDrivenTransactionManager() {
        return primaryTransactionManager;
    }
    
    /**
     * 主数据源的MyBatis配置
     */
    @Bean(name = "primaryMybatisConfig")
    @Primary
    public org.apache.ibatis.session.Configuration primaryMybatisConfig() {
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        // 开启驼峰命名
        configuration.setMapUnderscoreToCamelCase(true);
        // 允许JDBC生成主键
        configuration.setUseGeneratedKeys(true);
        // 配置默认的执行器
        configuration.setDefaultExecutorType(org.apache.ibatis.session.ExecutorType.REUSE);
        // 设置超时时间
        configuration.setDefaultStatementTimeout(25000);
        
        return configuration;
    }
    
    /**
     * 主数据源的SqlSessionFactory配置
     * 这个方法在DataSourceConfig中已经定义，但这里可以添加更多配置
     */
    @Bean(name = "primarySqlSessionFactoryBean")
    @Primary
    public SqlSessionFactoryBean primarySqlSessionFactoryBean() throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(primaryDataSource);
        bean.setConfiguration(primaryMybatisConfig());
        
        // 设置类型别名包
        bean.setTypeAliasesPackage("com.bimowu.unified.common.model");
        
        // 设置mapper.xml文件位置
        bean.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources("classpath*:com/bimowu/unified/common/mappers/*.xml"));
        
        // 设置分页插件等其他插件
        Properties properties = new Properties();
        properties.setProperty("reasonable", "true");
        properties.setProperty("supportMethodsArguments", "true");
        properties.setProperty("returnPageInfo", "check");
        properties.setProperty("params", "count=countSql");
        
        return bean;
    }
} 