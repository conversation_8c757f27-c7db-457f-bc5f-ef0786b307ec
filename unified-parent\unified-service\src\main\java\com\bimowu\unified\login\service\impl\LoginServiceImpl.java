package com.bimowu.unified.login.service.impl;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.base.Constant;
import com.bimowu.unified.common.model.SysUser;
import com.bimowu.unified.common.service.UserService;
import com.bimowu.unified.common.utils.RedisUtils;
import com.bimowu.unified.common.utils.TokenUtils;
import com.bimowu.unified.login.dao.KsUserDao;
import com.bimowu.unified.login.model.KsUser;
import com.bimowu.unified.login.service.LoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 登录服务实现类
 */
@Service
@Slf4j
public class LoginServiceImpl implements LoginService {

    @Autowired
    private KsUserDao ksUserDao;

    @Autowired
    private RedisUtils redisUtils;
    // 模拟存储token
    private static final Map<String, Long> TOKEN_MAP = new ConcurrentHashMap<>();


    @Override
    public BaseResponse<KsUser> getUserInfo(Long userId) {
        log.info("获取用户信息(ks_user): {}", userId);
        
        // 查询用户
        KsUser user = ksUserDao.selectByPrimaryKey(userId);
        if (user == null) {
            return BaseResponse.error(1001, "用户不存在");
        }
        
        // 清除敏感信息
        user.setPasswd(null);
        
        return BaseResponse.ok(user);
    }
    
    @Override
    public BaseResponse<String> updatePassword(Long userId, String oldPassword, String newPassword) {
        log.info("修改密码(ks_user): {}", userId);
        
        // 查询用户
        KsUser user = ksUserDao.selectByPrimaryKey(userId);
        if (user == null) {
            return BaseResponse.error(1001, "用户不存在");
        }
        
        // 校验旧密码
        String encryptOldPassword = DigestUtils.md5DigestAsHex(oldPassword.getBytes());
        if (!encryptOldPassword.equals(user.getPasswd())) {
            return BaseResponse.error(1002, "旧密码错误");
        }
        
        // 更新密码
        String encryptNewPassword = DigestUtils.md5DigestAsHex(newPassword.getBytes());
        user.setPasswd(encryptNewPassword);
        user.setUpdateTime(new Date());
        ksUserDao.updateByPrimaryKeySelective(user);
        
        return BaseResponse.ok("密码修改成功");
    }
    
    @Override
    public BaseResponse<String> resetPassword(String mobile, String verifyCode, String newPassword) {
        log.info("找回密码(ks_user): {}", mobile);

        String cacheKey = Constant.VERIFY_CODE_PREFIX + mobile;
        Object cachedCode = redisUtils.get(cacheKey);
        if (cachedCode == null || !cachedCode.toString().equals(verifyCode)) {
            return BaseResponse.error(1004, "验证码错误或已过期");
        }
        
        // 查询用户
        KsUser user = ksUserDao.selectByPhone(mobile);
        if (user == null) {
            return BaseResponse.error(1001, "用户不存在");
        }

        // 更新密码
        String encryptNewPassword = DigestUtils.md5DigestAsHex(newPassword.getBytes());
        user.setPasswd(encryptNewPassword);
        user.setUpdateTime(new Date());
        ksUserDao.updateByPrimaryKeySelective(user);

        // 清除验证码
        redisUtils.del(cacheKey);

        return BaseResponse.ok("密码重置成功");
    }
    
    @Override
    public BaseResponse<String> updateUsername(Long userId, String newUsername) {
        log.info("修改用户名(ks_user): {}", userId);
        
        // 查询用户
        KsUser user = ksUserDao.selectByPrimaryKey(userId);
        if (user == null) {
            return BaseResponse.error(1001, "用户不存在");
        }
        
        // 检查新用户名是否已存在
        KsUser existUser = ksUserDao.selectByNickname(newUsername);
        if (existUser != null && !existUser.getUId().equals(userId)) {
            return BaseResponse.error(1005, "用户名已存在");
        }
        
        // 更新用户名
        user.setNickname(newUsername);
        user.setUpdateTime(new Date());
        ksUserDao.updateByPrimaryKeySelective(user);
        
        return BaseResponse.ok("用户名修改成功");
    }

} 