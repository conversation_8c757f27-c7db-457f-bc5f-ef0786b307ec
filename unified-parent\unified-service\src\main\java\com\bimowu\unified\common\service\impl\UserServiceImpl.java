package com.bimowu.unified.common.service.impl;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.common.dao.SysWechatAuthDao;
import com.bimowu.unified.common.model.SysWechatAuth;
import com.bimowu.unified.common.service.UserService;
import com.bimowu.unified.common.service.WechatService;
import com.bimowu.unified.login.dao.KsUserDao;
import com.bimowu.unified.login.model.KsUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 用户服务实现类
 */
@Service
@Slf4j
public class UserServiceImpl implements UserService {

    @Autowired
    private KsUserDao ksUserDao;

    @Autowired
    private SysWechatAuthDao sysWechatAuthDao;
    
    @Autowired
    private WechatService wechatService;

    // 微信公众号二维码URL前缀（实际项目中应该配置在配置文件中）
    private static final String WECHAT_QRCODE_URL_PREFIX = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=";

    
    @Override
    public BaseResponse<Map<String, Object>> generateWechatQrCode(Long userId) {
        log.info("生成微信绑定二维码: {}", userId);
        
        // 查询用户
        KsUser user = ksUserDao.selectByPrimaryKey(userId);
        if (user == null) {
            return BaseResponse.error(1001, "用户不存在");
        }
        
        // 生成场景值字符串
        String sceneStr = "bind_" + userId + "_" + UUID.randomUUID().toString().substring(0, 8);
        
        // 计算过期时间（30分钟）
        int expireSeconds = 30 * 60;
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, expireSeconds);
        Date expireTime = calendar.getTime();
        
        // 调用微信服务创建临时二维码
        Map<String, String> qrcodeResult = wechatService.createTempQrcode(sceneStr, expireSeconds);
        String qrcodeUrl = qrcodeResult.get("url");
        
        // 创建微信授权记录
        SysWechatAuth wechatAuth = new SysWechatAuth();
        wechatAuth.setUserId(userId);
        wechatAuth.setSceneStr(sceneStr);
        wechatAuth.setQrcodeUrl(qrcodeUrl);
        wechatAuth.setSubscribe(0);
        wechatAuth.setStatus(0);
        wechatAuth.setExpireTime(expireTime);
        sysWechatAuthDao.insert(wechatAuth);
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("qrcodeUrl", qrcodeUrl);
        result.put("sceneStr", sceneStr);
        result.put("expireTime", expireTime);
        
        return BaseResponse.ok(result);
    }
    
    @Override
    public BaseResponse<Map<String, Object>> checkWechatBindStatus(Long userId) {
        log.info("查询微信绑定状态: {}", userId);
        
        // 查询最新的授权记录
        SysWechatAuth wechatAuth = sysWechatAuthDao.selectLatestByUserId(userId);
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        if (wechatAuth != null) {
            result.put("bindStatus", wechatAuth.getStatus());
            result.put("wechatNickname", wechatAuth.getNickname());
            result.put("sceneStr", wechatAuth.getSceneStr());
            result.put("qrcodeUrl", wechatAuth.getQrcodeUrl());
            result.put("expireTime", wechatAuth.getExpireTime());
            result.put("subscribe", wechatAuth.getSubscribe() != null && wechatAuth.getSubscribe() == 1);
        }
        
        return BaseResponse.ok(result);
    }
    
    @Override
    public BaseResponse<String> handleWechatScan(String sceneStr, String openid, String unionid, String nickname, Integer subscribe) {
        log.info("处理微信扫码回调: {}, {}, {}", sceneStr, openid, subscribe);
        
        // 查询授权记录
        SysWechatAuth wechatAuth = sysWechatAuthDao.selectBySceneStr(sceneStr);
        if (wechatAuth == null) {
            return BaseResponse.error(1006, "无效的场景值");
        }
        
        // 更新授权记录
        wechatAuth.setOpenid(openid);
        wechatAuth.setUnionid(unionid);
        wechatAuth.setNickname(nickname);
        wechatAuth.setSubscribe(subscribe);
        
        // 如果已关注，则更新绑定状态
        if (subscribe != null && subscribe == 1) {
            wechatAuth.setStatus(1);
        }
        
        sysWechatAuthDao.updateByPrimaryKeySelective(wechatAuth);
        
        return BaseResponse.ok("处理成功");
    }
} 