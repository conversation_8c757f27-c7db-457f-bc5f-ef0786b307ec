<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.unified.common.dao.ResumeCategoryDao">
    
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.bimowu.unified.common.model.ResumeCategory">
        <id column="cat_id" property="catId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_at" property="createAt" jdbcType="VARCHAR"/>
        <result column="is_delete" property="isDelete" jdbcType="INTEGER"/>
    </resultMap>
    
    <!-- 所有列 -->
    <sql id="Base_Column_List">
        cat_id, name, code, description, sort_order, status, create_time, update_time, create_at, is_delete
    </sql>
    
    <!-- 查询所有启用的职位类别 -->
    <select id="selectEnabledCategories" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM resume_category
        WHERE status = 1 AND is_delete = 0
        ORDER BY sort_order ASC, cat_id ASC
    </select>
    
    <!-- 根据ID查询职位类别 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM resume_category
        WHERE cat_id = #{catId,jdbcType=BIGINT} AND is_delete = 0
    </select>
    
    <!-- 根据编码查询职位类别 -->
    <select id="selectByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM resume_category
        WHERE code = #{code,jdbcType=VARCHAR} AND is_delete = 0
    </select>
    
    <!-- 根据用户ID查询用户的职位类别列表 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT rc.<include refid="Base_Column_List"/>
        FROM resume_category rc
        INNER JOIN resume_category_relation rcr ON rc.cat_id = rcr.cat_id
        WHERE rcr.user_id = #{userId,jdbcType=BIGINT} 
        AND rc.status = 1 AND rc.is_delete = 0 AND rcr.is_delete = 0
        ORDER BY rc.sort_order ASC, rc.cat_id ASC
    </select>
    
</mapper>
