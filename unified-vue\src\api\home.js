import request from '../utils/request'

// 获取待办列表
export function getTodoList() {
  return request({
    url: '/todo/list',
    method: 'get'
  })
}

// 完成待办事项
export function completeTodo(id) {
  return request({
    url: `/todo/complete/${id}`,
    method: 'post'
  })
}

// 获取新闻列表
export function getNews() {
  return request({
    url: '/news/list',
    method: 'get'
  })
}

// 获取系统列表
export function getSystems() {
  return request({
    url: '/system/list',
    method: 'get'
  })
} 