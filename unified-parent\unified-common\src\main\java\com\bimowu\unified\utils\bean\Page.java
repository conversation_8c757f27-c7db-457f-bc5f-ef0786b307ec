package com.bimowu.unified.utils.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class Page<T> implements Serializable {

	private static final long serialVersionUID = 1L;
	private static final int DEF_START_PAGE = 1;
	private static final int DEF_PAGE_SIZE = 10;

	/**
	 * 总数
	 */
	private Integer totalNum = 0;

	/**
	 * 总页数
	 */
	private Integer totalPage = 0;

	/**
	 * 当前页
	 */
	private Integer pageNum = 1;
	/**
	 * 每页数
	 */
	private Integer pageSize = 15;
	/**
	 * 结果
	 */
	private List<T> resultList = new ArrayList<T>();

	public Page() {
		super();
	}

	public Page(Integer pageNum, Integer pageSize) {
		this();
		this.pageNum = this.formatPageNo(pageNum);
		this.pageSize = this.formatPageSize(pageSize);
	}

	public Page(Integer totalNum, Integer pageNum, Integer pageSize, List<T> resultList) {
		this(pageNum, pageSize);
		this.totalNum = totalNum == null ? 0 : totalNum;
		if (resultList != null) {
			this.resultList = resultList;
		}
		this.totalPage =
				this.pageSize == null || this.pageSize == 0 || this.totalNum == null ? null :
						(this.totalNum
								+ this.pageSize - 1)
								/ this.pageSize;
	}

	public Integer getTotalNum() {
		return totalNum;
	}

	public void setTotalNum(Integer totalNum) {
		this.totalNum = totalNum == null ? 0 : totalNum;
		this.totalPage =
				this.pageSize == null || this.pageSize == 0 || this.totalNum == null ? null :
						(this.totalNum
								+ this.pageSize - 1)
								/ this.pageSize;
	}

	public Integer getTotalPage() {
		return totalPage;
	}

	public void setTotalPage(Integer totalPage) {
		this.totalPage = totalPage;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public List<T> getResultList() {
		return resultList;
	}

	public void setResultList(List<T> resultList) {
		this.resultList = resultList;
	}

	private int formatPageNo(Integer page) {
		return page == null || page < DEF_START_PAGE ? DEF_START_PAGE : page;
	}

	private int formatPageSize(Integer pageSize) {
		return pageSize == null || pageSize < 0 ? DEF_PAGE_SIZE : pageSize;
	}
}
