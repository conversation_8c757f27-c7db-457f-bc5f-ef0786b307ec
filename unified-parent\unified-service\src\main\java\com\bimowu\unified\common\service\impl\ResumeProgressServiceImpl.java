package com.bimowu.unified.common.service.impl;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.common.dao.ResumeProgressDao;
import com.bimowu.unified.common.dao.SysTodoDao;
import com.bimowu.unified.common.dao.ResumeCategoryRelationDao;
import com.bimowu.unified.common.model.ResumeProgress;
import com.bimowu.unified.common.model.SysTodo;
import com.bimowu.unified.common.model.ResumeCategoryRelation;
import com.bimowu.unified.common.service.ResumeProgressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 简历进度服务实现类
 */
@Service
@Slf4j
public class ResumeProgressServiceImpl implements ResumeProgressService {

    @Autowired
    private ResumeProgressDao resumeProgressDao;

    @Autowired
    private SysTodoDao sysTodoDao;

    @Autowired
    private ResumeCategoryRelationDao resumeCategoryRelationDao;

    @Override
    public BaseResponse<ResumeProgress> getProgressByUserId(Integer userId) {
        log.info("获取用户的简历进度: {}", userId);

        ResumeProgress progress = resumeProgressDao.selectByUserId(userId);

        if (progress == null) {
            // 如果用户没有进度记录，返回默认进度（创建简历阶段）
            progress = new ResumeProgress();
            progress.setUserId(userId);
            progress.setCurrentStage("0"); // 默认从创建简历开始
            progress.setCreateTime(new Date());
            progress.setUpdateTime(new Date());
            progress.setIsDeleted(0);
            log.info("用户没有进度记录，返回默认进度: {}", progress);
        }

        return BaseResponse.ok(progress);
    }

    @Override
    @Transactional
    public BaseResponse<String> updateProgress(Integer userId, String currentStage) {
        log.info("更新用户的简历进度: {}, 阶段: {}", userId, currentStage);
        
        ResumeProgress progress = resumeProgressDao.selectByUserId(userId);
        
        if (progress == null) {
            // 如果不存在，则创建新的进度记录
            progress = new ResumeProgress();
            progress.setUserId(userId);
            progress.setCurrentStage(currentStage);
            progress.setIsDeleted(0);
            resumeProgressDao.insert(progress);
            log.info("创建用户简历进度: {}", progress);
        } else {
            // 如果存在，则更新进度
            progress.setCurrentStage(currentStage);
            resumeProgressDao.update(progress);
            log.info("更新用户简历进度: {}", progress);
        }
        
        return BaseResponse.ok("更新简历进度成功");
    }
    
    @Override
    @Transactional
    public BaseResponse<String> updateProgress(Integer userId, String currentStage, Long resumeId) {
        log.info("更新用户的简历进度: {}, 阶段: {}, 简历ID: {}", userId, currentStage, resumeId);
        
        // 先尝试通过简历ID查找进度记录
        ResumeProgress progress = null;
        if (resumeId != null) {
            progress = resumeProgressDao.selectByResumeId(resumeId);
        }
        
        // 如果通过简历ID没找到，再尝试通过用户ID查找
        if (progress == null) {
            progress = resumeProgressDao.selectByUserId(userId);
        }
        
        if (progress == null) {
            // 如果不存在，则创建新的进度记录
            progress = new ResumeProgress();
            progress.setUserId(userId);
            progress.setResumeId(resumeId);
            progress.setCurrentStage(currentStage);
            progress.setIsDeleted(0);
            resumeProgressDao.insert(progress);
            log.info("创建用户简历进度: {}", progress);
        } else {
            // 如果存在，则更新进度
            progress.setCurrentStage(currentStage);
            // 如果原记录没有简历ID，则更新简历ID
            if (progress.getResumeId() == null && resumeId != null) {
                progress.setResumeId(resumeId);
            }
            resumeProgressDao.update(progress);
            log.info("更新用户简历进度: {}", progress);
        }
        
        return BaseResponse.ok("更新简历进度成功");
    }

    @Override
    @Transactional
    public BaseResponse<String> createHrInterviewTodo(Long userId, String systemUrl) {
        log.info("创建HR面试待办任务: {}, 系统URL: {}", userId, systemUrl);
        
        // 创建HR面试待办任务
        SysTodo todo = new SysTodo();
        todo.setUserId(userId);
        todo.setTitle("HR面试");
        todo.setContent("请您及时完成HR面试环节的面试！");
        todo.setUrl(systemUrl);
        todo.setTodoType(2); // HR面试类型
        todo.setStatus(0); // 未完成
        todo.setCreateTime(new Date());
        todo.setUpdateTime(new Date());
        
        int result = sysTodoDao.insert(todo);
        
        if (result > 0) {
            log.info("创建HR面试待办任务成功: {}", todo);
            return BaseResponse.ok("创建HR面试待办任务成功");
        } else {
            log.error("创建HR面试待办任务失败");
            return BaseResponse.error(500, "创建HR面试待办任务失败");
        }
    }
    
    @Override
    @Transactional
    public BaseResponse<String> createHrInterviewTodo(Long userId, String systemUrl, Long resumeId) {
        log.info("创建HR面试待办任务: {}, 系统URL: {}, 简历ID: {}", userId, systemUrl, resumeId);
        
        // 创建HR面试待办任务
        SysTodo todo = new SysTodo();
        todo.setUserId(userId);
        todo.setResumeId(resumeId);
        todo.setTitle("HR面试");
        todo.setContent("请您及时完成HR面试环节的面试！");
        todo.setUrl(systemUrl);
        todo.setTodoType(2); // HR面试类型
        todo.setStatus(0); // 未完成
        todo.setCreateTime(new Date());
        todo.setUpdateTime(new Date());
        
        int result = sysTodoDao.insert(todo);
        
        if (result > 0) {
            log.info("创建HR面试待办任务成功: {}", todo);
            return BaseResponse.ok("创建HR面试待办任务成功");
        } else {
            log.error("创建HR面试待办任务失败");
            return BaseResponse.error(500, "创建HR面试待办任务失败");
        }
    }

    @Override
    public BaseResponse<ResumeProgress> getProgressByUserIdAndCatId(Integer userId, Long catId) {
        log.info("根据用户ID和职位类别ID获取简历进度: userId={}, catId={}", userId, catId);

        // 根据用户ID和职位类别ID查找关联记录
        ResumeCategoryRelation relation = resumeCategoryRelationDao.selectByUserIdAndCatId(userId.longValue(), catId);

        if (relation == null) {
            // 如果没有关联记录，返回默认进度
            ResumeProgress progress = new ResumeProgress();
            progress.setUserId(userId);
            progress.setCurrentStage("0"); // 默认从创建简历开始
            progress.setCreateTime(new Date());
            progress.setUpdateTime(new Date());
            progress.setIsDeleted(0);
            log.info("用户在该职位类别下没有简历，返回默认进度: {}", progress);
            return BaseResponse.ok(progress);
        }

        // 根据简历ID查找进度记录
        ResumeProgress progress = resumeProgressDao.selectByResumeId(relation.getResumeId());

        if (progress == null) {
            // 如果没有进度记录，返回默认进度
            progress = new ResumeProgress();
            progress.setUserId(userId);
            progress.setResumeId(relation.getResumeId());
            progress.setCurrentStage("0"); // 默认从创建简历开始
            progress.setCreateTime(new Date());
            progress.setUpdateTime(new Date());
            progress.setIsDeleted(0);
            log.info("简历没有进度记录，返回默认进度: {}", progress);
        }

        return BaseResponse.ok(progress);
    }
}