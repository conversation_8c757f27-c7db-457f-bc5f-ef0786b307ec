package com.bimowu.unified.filter;


import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.base.Constant;
import com.bimowu.unified.common.utils.RedisUtils;
import com.bimowu.unified.utils.CookieUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.MimeHeaders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 *  判断用户是否登录，若未登录则返回重定向地址给前端.
 * <AUTHOR>
 *
 */
@Slf4j
@Configuration
public class LoginFilter implements Filter {
    private Logger logger = LoggerFactory.getLogger(LoginFilter.class);
	@Autowired
	private RedisUtils redisUtils;
	@Autowired
	private RestTemplate restTemplate;
	@Autowired
	private ObjectMapper objectMapper;

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response,
						 FilterChain chain) throws IOException, ServletException {
		HttpServletRequest httpRequest = (HttpServletRequest) request;
		HttpServletResponse httpResponse = (HttpServletResponse) response;



		String url = httpRequest.getRequestURI();
		// login judge
		log.info("请求url="+url);
		//需要验证登录的情况
		String token = getToken(httpRequest);
		log.info("token="+token);
		if (token != null) {
			//前端上传了token
			//h获取用户id
			Map map = (Map)redisUtils.get(String.format(Constant.TOKEN_PREFIX, token));
			if(map!=null){
				Object userId = map.get("userId");
				// 将userId设置到请求头中
				Map<String, Long> headerMap = new HashMap<>();
				headerMap.put("userId", Long.parseLong(userId.toString()));
				addHeader(httpRequest, headerMap);
			}else{
				//登录失效
			}
		}
		log.info("filter 处理完成");
		chain.doFilter(httpRequest, httpResponse);
	}
	
	/**
	 * 返回重定向响应给前端
	 */
	private void sendRedirectResponse(HttpServletResponse response, HttpServletRequest request, String redirectUrl) throws IOException {
		response.setContentType(MediaType.APPLICATION_JSON_VALUE);
		response.setCharacterEncoding("UTF-8");
		
		// 添加CORS头，根据请求的Origin头来设置
		String origin = request.getHeader("Origin");
		if (origin != null) {
			response.setHeader("Access-Control-Allow-Origin", origin);
		} else {
			// 如果没有Origin头，则使用默认值
			response.setHeader("Access-Control-Allow-Origin", "http://localhost:3000");
		}
		response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
		response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, token");
		response.setHeader("Access-Control-Allow-Credentials", "true");
		response.setHeader("Access-Control-Max-Age", "3600");
		
		// 创建响应对象
		Map<String, String> data = new HashMap<>();
		data.put("redirectUrl", redirectUrl);
		BaseResponse<Map<String, String>> baseResponse = new BaseResponse<>(401,"用户未登录或登录已过期",data);
		log.info("响应参数：{}",baseResponse.toString());
		
		// 将响应写入输出流
		try (PrintWriter writer = response.getWriter()) {
			String jsonResponse = objectMapper.writeValueAsString(baseResponse);
			log.info("JSON响应：{}", jsonResponse);
			writer.write(jsonResponse);
			writer.flush();
		} catch (Exception e) {
			log.error("生成JSON响应时发生错误", e);
		}
	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub
	}
	private static String getToken(HttpServletRequest request) {
		// 首先尝试从Authorization头中获取Bearer token
		String authHeader = request.getHeader("Authorization");
		if (authHeader != null && authHeader.startsWith("Bearer ")) {
			String bearerToken = authHeader.substring(7); // 去掉"Bearer "前缀
			log.info("从Authorization头中获取到Bearer token: {}", bearerToken);
			return bearerToken;
		}
		
		// 然后尝试从token头中获取
		String tokenHeader = request.getHeader(Constant.TOKEN_KEY);
		if (org.apache.commons.lang3.StringUtils.isNotBlank(tokenHeader)) {
			log.info("从token头中获取到token: {}", tokenHeader);
			return tokenHeader;
		}
		
		// 然后尝试从请求参数中获取
		String tokenParam = request.getParameter(Constant.TOKEN_KEY);
		if (org.apache.commons.lang3.StringUtils.isNotBlank(tokenParam)) {
			log.info("从请求参数中获取到token: {}", tokenParam);
			return tokenParam;
		}
		
		// 最后尝试从Cookie中获取
		String tokenCookie = CookieUtils.getValue(request, Constant.TOKEN_KEY);
		if (org.apache.commons.lang3.StringUtils.isNotBlank(tokenCookie)) {
			log.info("从Cookie中获取到token: {}", tokenCookie);
			return tokenCookie;
		}
		
		log.info("未找到token");
		return null;
	}

	private void addHeader(HttpServletRequest request, Map<String, Long> headerMap) {
		if (headerMap==null||headerMap.isEmpty()){
			log.info("headerMap为空");
			return;
		}

		Class<? extends HttpServletRequest> c=request.getClass();
		//System.out.println(c.getName());
		log.info("request实现类="+c.getName());
		try{
			Field requestField=c.getDeclaredField("request");
			requestField.setAccessible(true);

			Object o=requestField.get(request);
			Field coyoteRequest=o.getClass().getDeclaredField("coyoteRequest");
			coyoteRequest.setAccessible(true);

			Object o2=coyoteRequest.get(o);
			Field headers=o2.getClass().getDeclaredField("headers");
			headers.setAccessible(true);

			MimeHeaders mimeHeaders=(MimeHeaders) headers.get(o2);
			for (Map.Entry<String,Long> entry:headerMap.entrySet()){
				mimeHeaders.removeHeader(entry.getKey());
				mimeHeaders.addValue(entry.getKey()).setLong(entry.getValue());
			}

		}catch (Exception e){
			log.info("headerMap<UNK>:{}",e.getMessage());
		}
	}

}
