//package com.bimowu.unified.config;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.web.servlet.FilterRegistrationBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * Web配置类
// */
//@Configuration
//public class WebConfig {
//
////    @Autowired
////    private TokenFilter tokenFilter;
//
//    /**
//     * 注册Token过滤器
//     */
//    @Bean
//    public FilterRegistrationBean<TokenFilter> tokenFilterRegistration() {
//        FilterRegistrationBean<TokenFilter> registration = new FilterRegistrationBean<>();
////        registration.setFilter(tokenFilter);
//        registration.addUrlPatterns("/*"); // 拦截所有请求
//        registration.setName("tokenFilter");
//        registration.setOrder(1); // 过滤器顺序
//        return registration;
//    }
//}