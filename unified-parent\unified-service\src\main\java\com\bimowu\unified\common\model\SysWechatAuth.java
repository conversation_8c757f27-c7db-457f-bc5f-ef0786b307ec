package com.bimowu.unified.common.model;

import lombok.Data;

import java.util.Date;

/**
 * 微信授权记录实体类
 */
@Data
public class SysWechatAuth {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 场景值字符串
     */
    private String sceneStr;

    /**
     * 二维码URL
     */
    private String qrcodeUrl;

    /**
     * 微信OpenID
     */
    private String openid;

    /**
     * 微信UnionID
     */
    private String unionid;

    /**
     * 微信昵称
     */
    private String nickname;

    /**
     * 是否关注：0-未关注，1-已关注
     */
    private Integer subscribe;

    /**
     * 状态：0-未绑定，1-已绑定
     */
    private Integer status;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 