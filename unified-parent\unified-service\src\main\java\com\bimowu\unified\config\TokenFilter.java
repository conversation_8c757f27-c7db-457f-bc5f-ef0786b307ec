//package com.bimowu.unified.config;
//
//import com.bimowu.unified.base.Constant;
//import com.bimowu.unified.common.utils.RedisUtils;
//import com.bimowu.unified.utils.CookieUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.util.StringUtils;
//import org.springframework.web.filter.OncePerRequestFilter;
//
//import javax.servlet.FilterChain;
//import javax.servlet.ServletException;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//import java.util.Arrays;
//import java.util.List;
//import java.util.Map;
//
///**
// * Token验证过滤器
// */
//@Component
//@Slf4j
//public class TokenFilter extends OncePerRequestFilter {
//
//    @Autowired
//    private RedisUtils redisUtils;
//
//    // 不需要验证token的URL列表
//    private static final List<String> EXCLUDE_URLS = Arrays.asList(
//            "/sso/login",
//            "/sso/mobileLogin",
//            "/sso/verifyCode",
//            "/sso/registerClient",
//            "/system/list",
//            "/news/list",
//            "/user/wechat/callback",
//            "/sso/auth"
//    );
//
//    @Override
//    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
//            throws ServletException, IOException {
//        // 获取请求路径
//        String path = request.getRequestURI();
//        String contextPath = request.getContextPath();
//        if (StringUtils.hasText(contextPath)) {
//            path = path.substring(contextPath.length());
//        }
//
//        // 对于不需要验证token的URL，直接放行
//        for (String excludeUrl : EXCLUDE_URLS) {
//            if (path.startsWith(excludeUrl)) {
//                filterChain.doFilter(request, response);
//                return;
//            }
//        }
//
//        // 获取请求头中的token
//        String token = request.getHeader("token");
//        if (StringUtils.isEmpty(token)) {
//            // token为空，返回401未授权
//            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
//            response.setContentType("application/json;charset=UTF-8");
//            response.getWriter().write("{\"code\":401,\"message\":\"未授权，请先登录\",\"data\":null}");
//            return;
//        }
//
//        // 从Redis获取token信息
//        String cacheKey = Constant.TOKEN_PREFIX + token;
//        Map<Object, Object> tokenInfo = redisUtils.hmget(cacheKey);
//
//        if (tokenInfo == null || tokenInfo.isEmpty()) {
//            // token无效或已过期，返回401未授权
//            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
//            response.setContentType("application/json;charset=UTF-8");
//            response.getWriter().write("{\"code\":401,\"message\":\"token无效或已过期，请重新登录\",\"data\":null}");
//            return;
//        }
//
//        // token有效，延长token有效期
//        redisUtils.expire(cacheKey, 7200); // 2小时
//
//        // 将用户ID存入请求属性，供后续使用
//        Long userId = Long.valueOf(tokenInfo.get("userId").toString());
//        request.setAttribute("userId", userId);
//        request.setAttribute("clientId", tokenInfo.get("clientId"));
//
//        // 继续执行后续过滤器
//        filterChain.doFilter(request, response);
//    }
//}