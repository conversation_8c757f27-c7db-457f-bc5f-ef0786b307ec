import{y as X,r as w,b as F,A as Y,k as V,l as _,C as g,D as d,F as s,G as r,I as p,E as i,K as Z,p as ee,L as se,J as E,S as ae,H as z,V as oe}from"./vendor-477e13dd.js";import{u as re,c as D,d as le,e as te,f as ne,g as de,r as ue,h as ie}from"./index-e9d76465.js";import{_ as me}from"./index-97fb9448.js";const ce={class:"profile-container"},fe={class:"header"},we={class:"profile-card"},pe={class:"form-container"},ve={class:"form-actions"},_e={class:"form-container"},be={class:"form-container"},ge={class:"wechat-bind-container"},ye={class:"bind-status"},Pe={key:0,class:"bound-info"},Ve={class:"nickname"},ke={key:1,class:"qrcode-container"},Ue={class:"qrcode-image"},<PERSON>=["src"],he={key:1,class:"qrcode-placeholder"},Ie={key:0,class:"qrcode-tips"},Se={key:1,class:"qrcode-refresh"},qe={__name:"Profile",setup(Ne){const Q=X(),b=re(),R=w("basic"),n=F({username:"",mobile:"",email:"",realName:""}),k=w(!1),S=w(""),q=w(null),c=F({oldPassword:"",newPassword:"",confirmPassword:""}),N=w(null),t=F({mobile:"",verifyCode:"",newPassword:"",confirmPassword:""}),m=w(!1),y=w(""),P=w(""),U=w(!1),C=w(60);let h=null;const $={oldPassword:[{required:!0,message:"请输入原密码",trigger:"blur"},{min:6,message:"密码长度不能小于6位",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能小于6位",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"},{validator:(o,e,a)=>{e!==c.newPassword?a(new Error("两次输入的密码不一致")):a()},trigger:"blur"}]},L={mobile:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确",trigger:"blur"}],verifyCode:[{required:!0,message:"请输入验证码",trigger:"blur"},{len:6,message:"验证码长度为6位",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"密码长度不能小于6位",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"},{validator:(o,e,a)=>{e!==t.newPassword?a(new Error("两次输入的密码不一致")):a()},trigger:"blur"}]},M=async()=>{try{const e=(await le()).data;if(n.username=e.nickname,n.mobile=e.phone,n.email=e.email,n.realName=e.nickname,e.wechatBindStatus!==void 0){m.value=e.wechatBindStatus===1||e.wechatBindStatus===!0,y.value=e.wechatNickname||"";const a=b.userInfo;a.wechatBindStatus=m.value,a.wechatNickname=y.value,b.setUserInfo(a),console.log("用户信息中的微信绑定状态:",m.value)}}catch(o){console.error("获取用户信息失败:",o),i.error("获取用户信息失败")}},T=()=>{S.value=n.username,k.value=!0},W=()=>{n.username=S.value,k.value=!1},A=async()=>{var o,e;if(!n.username){i.warning("用户名不能为空");return}try{await te({newUsername:n.username}),i.success("用户名修改成功"),k.value=!1;const a=b.userInfo;a.nickname=n.username,b.setUserInfo(a)}catch(a){console.error("修改用户名失败:",a),i.error(((e=(o=a.response)==null?void 0:o.data)==null?void 0:e.message)||"修改用户名失败"),n.username=S.value}},G=async()=>{q.value&&await q.value.validate(async o=>{var e,a;if(o)try{await ne({oldPassword:c.oldPassword,newPassword:c.newPassword}),i.success("密码修改成功"),c.oldPassword="",c.newPassword="",c.confirmPassword=""}catch(u){console.error("修改密码失败:",u),i.error(((a=(e=u.response)==null?void 0:e.data)==null?void 0:a.message)||"修改密码失败")}})},H=async()=>{var o,e;if(!t.mobile){i.warning("请输入手机号");return}if(!/^1[3-9]\d{9}$/.test(t.mobile)){i.warning("手机号格式不正确");return}try{await de(t.mobile),i.success("验证码发送成功"),U.value=!0,C.value=60,h=setInterval(()=>{C.value--,C.value<=0&&(clearInterval(h),U.value=!1)},1e3)}catch(a){console.error("获取验证码失败:",a),i.error(((e=(o=a.response)==null?void 0:o.data)==null?void 0:e.message)||"获取验证码失败")}},J=async()=>{N.value&&await N.value.validate(async o=>{var e,a;if(o)try{await ue({mobile:t.mobile,verifyCode:t.verifyCode,newPassword:t.newPassword}),i.success("密码重置成功"),t.mobile="",t.verifyCode="",t.newPassword="",t.confirmPassword="",h&&(clearInterval(h),U.value=!1)}catch(u){console.error("重置密码失败:",u),i.error(((a=(e=u.response)==null?void 0:e.data)==null?void 0:a.message)||"重置密码失败")}})},B=async()=>{try{const o=await ie();P.value=o.data.qrcodeUrl,i.success("二维码生成成功，请使用微信扫码")}catch(o){console.error("生成二维码失败:",o),i.error("生成二维码失败，请重试")}},K=async()=>{try{const o=await D(),e=o.data.bindStatus===!0||o.data.bindStatus===1;m.value=e,y.value=o.data.wechatNickname||"";const a=b.userInfo;a.wechatBindStatus=m.value,a.wechatNickname=y.value,b.setUserInfo(a),m.value?i.success("微信已成功绑定"):(i.info("尚未绑定微信，请扫描二维码并关注公众号"),P.value||B())}catch(o){console.error("检查绑定状态失败:",o),i.error("检查绑定状态失败")}},j=()=>{Q.push("/home")};return Y(async()=>{await M();try{const o=await D(),e=o.data.bindStatus===!0||o.data.bindStatus===1;if(e!==m.value){console.log("绑定状态不一致，后端:",e,"前端:",m.value),m.value=e,y.value=o.data.wechatNickname||"";const a=b.userInfo;a.wechatBindStatus=m.value,a.wechatNickname=y.value,b.setUserInfo(a)}o.data.qrcodeUrl&&!m.value&&(P.value=o.data.qrcodeUrl)}catch(o){console.error("检查微信绑定状态失败:",o)}}),(o,e)=>{const a=V("el-icon"),u=V("el-input"),v=V("el-button"),f=V("el-form-item"),x=V("el-form"),I=V("el-tab-pane"),O=V("el-tabs");return _(),g("div",ce,[d("div",fe,[d("div",{class:"back",onClick:j},[s(a,null,{default:r(()=>[s(Z(oe))]),_:1}),e[12]||(e[12]=p(" 返回 "))]),e[13]||(e[13]=d("h2",null,"个人中心",-1))]),d("div",we,[s(O,{modelValue:R.value,"onUpdate:modelValue":e[11]||(e[11]=l=>R.value=l)},{default:r(()=>[s(I,{label:"基本信息",name:"basic"},{default:r(()=>[d("div",pe,[s(x,{model:n,"label-width":"100px"},{default:r(()=>[s(f,{label:"用户名"},{default:r(()=>[s(u,{modelValue:n.username,"onUpdate:modelValue":e[0]||(e[0]=l=>n.username=l),placeholder:"请输入用户名",disabled:!k.value},null,8,["modelValue","disabled"]),d("div",ve,[k.value?(_(),g(se,{key:1},[s(v,{type:"success",size:"small",onClick:A},{default:r(()=>e[15]||(e[15]=[p("保存")])),_:1,__:[15]}),s(v,{size:"small",onClick:W},{default:r(()=>e[16]||(e[16]=[p("取消")])),_:1,__:[16]})],64)):(_(),ee(v,{key:0,type:"primary",size:"small",onClick:T},{default:r(()=>e[14]||(e[14]=[p("修改")])),_:1,__:[14]}))])]),_:1}),s(f,{label:"手机号"},{default:r(()=>[s(u,{modelValue:n.mobile,"onUpdate:modelValue":e[1]||(e[1]=l=>n.mobile=l),disabled:""},null,8,["modelValue"])]),_:1}),s(f,{label:"邮箱"},{default:r(()=>[s(u,{modelValue:n.email,"onUpdate:modelValue":e[2]||(e[2]=l=>n.email=l),disabled:""},null,8,["modelValue"])]),_:1}),s(f,{label:"真实姓名"},{default:r(()=>[s(u,{modelValue:n.realName,"onUpdate:modelValue":e[3]||(e[3]=l=>n.realName=l),disabled:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1}),s(I,{label:"修改密码",name:"password"},{default:r(()=>[d("div",_e,[s(x,{model:c,rules:$,ref_key:"passwordFormRef",ref:q,"label-width":"100px"},{default:r(()=>[s(f,{label:"原密码",prop:"oldPassword"},{default:r(()=>[s(u,{modelValue:c.oldPassword,"onUpdate:modelValue":e[4]||(e[4]=l=>c.oldPassword=l),type:"password",placeholder:"请输入原密码","show-password":""},null,8,["modelValue"])]),_:1}),s(f,{label:"新密码",prop:"newPassword"},{default:r(()=>[s(u,{modelValue:c.newPassword,"onUpdate:modelValue":e[5]||(e[5]=l=>c.newPassword=l),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),s(f,{label:"确认密码",prop:"confirmPassword"},{default:r(()=>[s(u,{modelValue:c.confirmPassword,"onUpdate:modelValue":e[6]||(e[6]=l=>c.confirmPassword=l),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),s(f,null,{default:r(()=>[s(v,{type:"primary",onClick:G},{default:r(()=>e[17]||(e[17]=[p("修改密码")])),_:1,__:[17]})]),_:1})]),_:1},8,["model"])])]),_:1}),s(I,{label:"找回密码",name:"reset"},{default:r(()=>[d("div",be,[s(x,{model:t,rules:L,ref_key:"resetFormRef",ref:N,"label-width":"100px"},{default:r(()=>[s(f,{label:"手机号",prop:"mobile"},{default:r(()=>[s(u,{modelValue:t.mobile,"onUpdate:modelValue":e[7]||(e[7]=l=>t.mobile=l),placeholder:"请输入手机号"},{append:r(()=>[s(v,{disabled:U.value,onClick:H},{default:r(()=>[p(E(U.value?`${C.value}s后重新获取`:"获取验证码"),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue"])]),_:1}),s(f,{label:"验证码",prop:"verifyCode"},{default:r(()=>[s(u,{modelValue:t.verifyCode,"onUpdate:modelValue":e[8]||(e[8]=l=>t.verifyCode=l),placeholder:"请输入验证码"},null,8,["modelValue"])]),_:1}),s(f,{label:"新密码",prop:"newPassword"},{default:r(()=>[s(u,{modelValue:t.newPassword,"onUpdate:modelValue":e[9]||(e[9]=l=>t.newPassword=l),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),s(f,{label:"确认密码",prop:"confirmPassword"},{default:r(()=>[s(u,{modelValue:t.confirmPassword,"onUpdate:modelValue":e[10]||(e[10]=l=>t.confirmPassword=l),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),s(f,null,{default:r(()=>[s(v,{type:"primary",onClick:J},{default:r(()=>e[18]||(e[18]=[p("重置密码")])),_:1,__:[18]})]),_:1})]),_:1},8,["model"])])]),_:1}),s(I,{label:"微信绑定",name:"wechat"},{default:r(()=>[d("div",ge,[d("div",ye,[e[19]||(e[19]=d("div",{class:"status-label"},"绑定状态：",-1)),d("div",{class:ae(["status-value",{"status-bound":m.value}])},E(m.value?"已绑定":"未绑定"),3)]),m.value?(_(),g("div",Pe,[d("div",Ve,"微信昵称："+E(y.value||"未获取"),1)])):(_(),g("div",ke,[e[24]||(e[24]=d("div",{class:"qrcode-title"},"扫描下方二维码绑定微信",-1)),d("div",Ue,[P.value?(_(),g("img",{key:0,src:P.value,alt:"微信公众号二维码"},null,8,Ce)):(_(),g("div",he,[s(v,{type:"primary",onClick:B},{default:r(()=>e[20]||(e[20]=[p("生成二维码")])),_:1,__:[20]})]))]),P.value?(_(),g("div",Ie,e[21]||(e[21]=[d("p",null,"1. 请使用微信扫描二维码",-1),d("p",null,"2. 关注公众号后自动完成绑定",-1),d("p",null,"3. 二维码有效期30分钟，过期请刷新",-1),d("p",null,'4. 关注后点击"检查绑定状态"',-1)]))):z("",!0),P.value?(_(),g("div",Se,[s(v,{onClick:K},{default:r(()=>e[22]||(e[22]=[p("检查绑定状态")])),_:1,__:[22]}),s(v,{type:"primary",onClick:B},{default:r(()=>e[23]||(e[23]=[p("刷新二维码")])),_:1,__:[23]})])):z("",!0)]))])]),_:1})]),_:1},8,["modelValue"])])])}}},Ee=me(qe,[["__scopeId","data-v-f9da85d3"]]);export{Ee as default};
