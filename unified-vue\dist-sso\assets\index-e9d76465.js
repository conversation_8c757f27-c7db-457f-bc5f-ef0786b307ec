import{r as s,d as n}from"./index-97fb9448.js";import{W as i,E as r}from"./vendor-477e13dd.js";const u=window.location.hostname==="ceping.bimowo.com",l=window.location.protocol==="file:"||window.location.pathname.endsWith("/index.html");let a="/unified";(l||!u)&&(a="http://127.0.0.1:7092/unified");const t=i.create({baseURL:a,timeout:1e4,withCredentials:!0});t.interceptors.request.use(e=>{const o=localStorage.getItem("token");return o&&(e.headers.token=o),e},e=>(console.error("请求错误：",e),Promise.reject(e)));t.interceptors.response.use(e=>{const o=e.data;return o.code!==0?(o.code!==401&&r({message:o.message||"请求出错",type:"error",duration:3*1e3}),o.code===401&&(localStorage.getItem("logoutFlag")?localStorage.removeItem("logoutFlag"):r.warning("登录已过期，请重新登录"),localStorage.removeItem("token"),localStorage.removeItem("userInfo"),s.currentRoute.value.path!=="/login"&&s.push("/login")),Promise.reject(new Error(o.message||"请求出错"))):o},e=>(console.error("响应错误：",e),r({message:e.message||"请求失败",type:"error",duration:3*1e3}),Promise.reject(e)));function d(e){return t({url:"/sso/login",method:"post",data:e})}function f(e){return t({url:"/sso/mobileLogin",method:"post",data:e})}function h(e){return t({url:"/sso/verifyCode",method:"get",params:{mobile:e}})}function p(e){return t({url:"/sso/verifyCaptcha",method:"get",params:{code:e}})}function I(){return t({url:"/user/info",method:"get"})}function w(e){return t({url:"/user/updatePassword",method:"post",params:e})}function S(e){return t({url:"/user/resetPassword",method:"post",params:e})}function k(e){return t({url:"/user/updateUsername",method:"post",params:e})}function y(){return t({url:"/user/generateWechatQrCode",method:"get"})}function v(){return t({url:"/user/checkWechatBindStatus",method:"get"})}function c(){return t({url:"/sso/logout",method:"post"})}const P=n("user",{state:()=>({token:localStorage.getItem("token")||"",userInfo:JSON.parse(localStorage.getItem("userInfo")||"{}")}),actions:{setToken(e){this.token=e,localStorage.setItem("token",e)},setUserInfo(e){this.userInfo=e,localStorage.setItem("userInfo",JSON.stringify(e))},async logout(){try{await c()}catch(e){console.error("退出登录失败:",e)}finally{this.token="",this.userInfo={},localStorage.removeItem("token"),localStorage.removeItem("userInfo")}}},getters:{isLoggedIn:e=>!!e.token}});export{f as a,c as b,v as c,I as d,k as e,w as f,h as g,y as h,d as l,S as r,t as s,P as u,p as v};
