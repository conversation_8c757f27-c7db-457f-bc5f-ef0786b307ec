package com.bimowu.unified.login.service.impl;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.common.utils.RedisUtils;
import com.bimowu.unified.login.service.VerifyCodeService;
import com.bimowu.unified.utils.CCPRestSmsSDK;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Random;

/**
 * 验证码服务实现类
 */
@Service
@Slf4j
public class VerifyCodeServiceImpl implements VerifyCodeService {

    @Autowired
    private RedisUtils redisUtils;
    
    // Redis中存储验证码的前缀
    private static final String VERIFY_CODE_PREFIX = "sso:verify_code:";
    
    // Redis中存储图形验证码的前缀
    private static final String CAPTCHA_PREFIX = "sso:captcha:";
    
    // 验证码有效期（秒）
    private static final long VERIFY_CODE_EXPIRE = 300; // 5分钟
    
    // 图形验证码有效期（秒）
    private static final long CAPTCHA_EXPIRE = 300; // 5分钟
    
    // 验证码长度
    private static final int CODE_LENGTH = 6;
    
    // 图形验证码长度
    private static final int CAPTCHA_LENGTH = 4;

    @Value("${sms.daily-limit:5}")
    private int smsDailyLimit;

    @Override
    public BaseResponse<String> sendSmsCode(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return BaseResponse.error(1200, "手机号不能为空");
        }
        // 统计今日已发送次数
        String today = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE); // yyyyMMdd
        String countKey = "sso:sms_count:" + mobile + ":" + today;
        Integer count = (Integer) redisUtils.get(countKey);
        if (count == null) count = 0;
        if (count >= smsDailyLimit) {
            return BaseResponse.error(1202, "今日短信发送已达上限，请明天再试");
        }
        // 生成6位随机验证码
        String verifyCode = RandomStringUtils.randomNumeric(6);
        // 存储验证码到Redis
        String cacheKey = VERIFY_CODE_PREFIX + mobile;
        redisUtils.set(cacheKey, verifyCode, VERIFY_CODE_EXPIRE);
        // 发送短信
        log.info("向手机号 {} 发送验证码: {}", mobile, verifyCode);
        Map<String, Object> result = CCPRestSmsSDK.sendTemplateSMS(mobile,verifyCode);
        log.info("<UNK> {} <UNK>: {}", mobile, result);
        String statusCode = (String) result.get("statusCode");
        if (!"000000".equals(statusCode)) {
            log.error("登录验证码发送失败，发送手机号为" + mobile);
            return BaseResponse.error(1201, "验证码发送失败");
        } else {
            log.info("登录验证码发送成功，发送手机号为" + mobile);
            // 发送成功，计数+1，设置过期到当天24点
            long secondsToMidnight = java.time.LocalDateTime.now().until(
                java.time.LocalDate.now().plusDays(1).atStartOfDay(),
                java.time.temporal.ChronoUnit.SECONDS
            );
            redisUtils.set(countKey, count + 1, secondsToMidnight);
        }
        return BaseResponse.ok("验证码发送成功");
    }
    
    @Override
    public boolean verifySmsCode(String mobile, String code) {
        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(code)) {
            return false;
        }
        
        // 从Redis获取验证码
        String cacheKey = VERIFY_CODE_PREFIX + mobile;
        Object cachedCode = redisUtils.get(cacheKey);
        
        // 验证码不存在或不匹配
        if (cachedCode == null || !cachedCode.toString().equals(code)) {
            return false;
        }
        
        // 验证通过后，删除验证码
        redisUtils.del(cacheKey);
        
        return true;
    }
    
    @Override
    public void generateCaptcha(HttpServletResponse response, String sessionId) {
        if (StringUtils.isEmpty(sessionId)) {
            return;
        }
        
        // 生成随机验证码
        String captchaCode = generateVerifyCode(CAPTCHA_LENGTH);
        
        // 存储验证码到Redis
        String cacheKey = CAPTCHA_PREFIX + sessionId;
        redisUtils.set(cacheKey, captchaCode, CAPTCHA_EXPIRE);
        
        log.info("生成图形验证码: {}, sessionId: {}", captchaCode, sessionId);
        
        // 设置响应类型和头信息
        response.setContentType("image/jpeg");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        
        // 创建图像
        int width = 100;
        int height = 40;
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();
        
        // 生成随机类
        Random random = new Random();
        
        try {
            // 设置背景色
            g.setColor(new Color(245, 245, 245));
            g.fillRect(0, 0, width, height);
            
            // 设置字体
            g.setFont(new Font("Arial", Font.BOLD, 28));
            
            // 绘制干扰线
            for (int i = 0; i < 5; i++) {
                g.setColor(getRandColor(130, 200));
                int x = random.nextInt(width);
                int y = random.nextInt(height);
                int xl = random.nextInt(12);
                int yl = random.nextInt(12);
                g.drawLine(x, y, x + xl, y + yl);
            }
            
            // 添加噪点
            for (int i = 0; i < 30; i++) {
                g.setColor(getRandColor(170, 255));
                int x = random.nextInt(width);
                int y = random.nextInt(height);
                g.drawOval(x, y, 1, 1);
            }
            
            // 绘制验证码
            for (int i = 0; i < captchaCode.length(); i++) {
                // 随机旋转
                int degree = random.nextInt(15) - 7;
                g.rotate(degree * Math.PI / 180, 20 * i + 10, 28);
                g.setColor(getRandColor(20, 110));
                g.drawString(String.valueOf(captchaCode.charAt(i)), 20 * i + 10, 28);
                g.rotate(-degree * Math.PI / 180, 20 * i + 10, 28);
            }
            
            // 释放图形上下文
            g.dispose();
            
            // 输出图像到页面
            ImageIO.write(image, "JPEG", response.getOutputStream());
            response.getOutputStream().flush();
        } catch (IOException e) {
            log.error("生成图形验证码失败", e);
        }
    }
    
    @Override
    public boolean verifyCaptcha(String sessionId, String code) {
        if (StringUtils.isEmpty(sessionId) || StringUtils.isEmpty(code)) {
            return false;
        }
        
        // 从Redis获取验证码
        String cacheKey = CAPTCHA_PREFIX + sessionId;
        Object cachedCode = redisUtils.get(cacheKey);
        
        // 验证码不存在或不匹配
        if (cachedCode == null || !cachedCode.toString().equalsIgnoreCase(code)) {
            return false;
        }
        
        // 验证通过后，删除验证码
        redisUtils.del(cacheKey);
        
        return true;
    }
    
    /**
     * 生成指定长度的随机验证码（数字+字母）
     * @param length 验证码长度
     * @return 验证码
     */
    private String generateVerifyCode(int length) {
        Random random = new Random();
        StringBuilder verifyCode = new StringBuilder();
        
        // 验证码字符集，不包含容易混淆的字符（如0和O, 1和I, 9和g等）
        String chars = "23456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefhijkmnpqrstuvwxyz";
        
        for (int i = 0; i < length; i++) {
            int idx = random.nextInt(chars.length());
            verifyCode.append(chars.charAt(idx));
        }
        
        return verifyCode.toString();
    }
    
    /**
     * 生成随机颜色
     */
    private Color getRandColor(int fc, int bc) {
        Random random = new Random();
        if (fc > 255) {
            fc = 255;
        }
        if (bc > 255) {
            bc = 255;
        }
        int r = fc + random.nextInt(bc - fc);
        int g = fc + random.nextInt(bc - fc);
        int b = fc + random.nextInt(bc - fc);
        return new Color(r, g, b);
    }
} 