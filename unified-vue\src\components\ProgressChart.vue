<template>
  <div class="progress-chart-container">
    <div class="progress-header">
      <h3>我的求职进度</h3>
      <p class="progress-desc">跟踪您的求职进展，一步步走向成功</p>
    </div>
    
    <div class="progress-content">
      <div v-if="loading" class="progress-loading">
        <el-skeleton :rows="3" animated />
      </div>
      <div v-else class="chart-wrapper">
        <v-chart 
          class="chart" 
          :option="chartOption" 
          autoresize
        />
        
        <!-- 进度说明 -->
        <div class="progress-legend">
          <div class="legend-item">
            <span class="legend-dot completed"></span>
            <span class="legend-text">已完成</span>
          </div>
          <div class="legend-item">
            <span class="legend-dot current"></span>
            <span class="legend-text">当前进度</span>
          </div>
          <div class="legend-item">
            <span class="legend-dot pending"></span>
            <span class="legend-text">未完成</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

use([
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  GridComponent
])

// Props
const props = defineProps({
  userProgress: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 进度阶段定义
const progressStages = [
  { key: '0', name: '创建简历', index: 0 },
  { key: '1', name: '知识学习', index: 1 },
  { key: '2', name: 'HR模拟面试', index: 2 },
  { key: '3', name: '技术模拟面试', index: 3 },
  { key: '4', name: '正式面试', index: 4 }
]

// 获取当前阶段索引
const getCurrentStageIndex = () => {
  if (!props.userProgress || !props.userProgress.currentStage) {
    return 0
  }
  const stage = progressStages.find(s => s.key === props.userProgress.currentStage)
  return stage ? stage.index : 0
}

// 测试用的进度数据（当没有真实数据时使用）
const getTestProgress = () => {
  // 模拟当前在"知识学习"阶段
  return {
    currentStage: '1',
    userId: 1
  }
}

// 图表配置
const chartOption = computed(() => {
  // 如果没有用户进度数据，使用测试数据进行演示
  const progressData = props.userProgress || getTestProgress()
  const currentStageIndex = progressData ?
    progressStages.find(s => s.key === progressData.currentStage)?.index || 0 : 0
  
  return {
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const stage = progressStages[params.dataIndex]
        let status = '未完成'
        if (params.dataIndex < currentStageIndex) {
          status = '已完成'
        } else if (params.dataIndex === currentStageIndex) {
          status = '当前进度'
        }
        return `${stage.name}<br/>状态: ${status}`
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      top: '10%',
      bottom: '20%'
    },
    xAxis: {
      type: 'category',
      data: progressStages.map(stage => stage.name),
      axisLabel: {
        interval: 0,
        rotate: 0,
        fontSize: 11,
        color: '#666'
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      }
    },
    yAxis: {
      type: 'value',
      show: false,
      min: 0,
      max: 100
    },
    series: [
      {
        name: '进度',
        type: 'line',
        data: progressStages.map((stage, index) => {
          if (index < currentStageIndex) {
            return 100 // 已完成
          } else if (index === currentStageIndex) {
            return 50 // 当前进度
          } else {
            return 0 // 未完成
          }
        }),
        lineStyle: {
          width: 3,
          color: '#409EFF'
        },
        symbol: 'circle',
        symbolSize: 10,
        itemStyle: {
          color: function(params) {
            if (params.dataIndex < currentStageIndex) {
              return '#67C23A' // 绿色 - 已完成
            } else if (params.dataIndex === currentStageIndex) {
              return '#E6A23C' // 黄色 - 当前进度
            } else {
              return '#C0C4CC' // 灰色 - 未完成
            }
          }
        }
      }
    ]
  }
})
</script>

<style scoped>
.progress-chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.progress-header {
  text-align: center;
  margin-bottom: 20px;
}

.progress-header h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.progress-desc {
  margin: 0;
  color: #666;
  font-size: 12px;
}

.progress-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.progress-loading {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chart {
  flex: 1;
  min-height: 200px;
}

.progress-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 15px;
  padding: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.legend-dot.completed {
  background-color: #67C23A;
}

.legend-dot.current {
  background-color: #E6A23C;
}

.legend-dot.pending {
  background-color: #C0C4CC;
}

.legend-text {
  font-size: 12px;
  color: #666;
}
</style>
