package com.bimowu.unified.login.dao;

import com.bimowu.unified.login.model.SsoClient;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SSO客户端应用DAO接口
 */
@Mapper
public interface SsoClientDao {
    
    /**
     * 根据主键查询
     * @param clientId 客户端ID
     * @return 客户端应用信息
     */
    SsoClient selectByPrimaryKey(String clientId);
    
    /**
     * 插入记录
     * @param record 客户端应用信息
     * @return 影响行数
     */
    int insert(SsoClient record);
    
    /**
     * 更新记录
     * @param record 客户端应用信息
     * @return 影响行数
     */
    int updateByPrimaryKey(SsoClient record);
    
    /**
     * 选择性更新记录
     * @param record 客户端应用信息
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(SsoClient record);
    
    /**
     * 根据应用名称查询
     * @param appName 应用名称
     * @return 客户端应用信息
     */
    SsoClient selectByAppName(String appName);
    
    /**
     * 查询所有活跃的客户端
     * @return 客户端应用信息列表
     */
    List<SsoClient> selectAll();
} 