package com.bimowu.unified.login.service;

import com.bimowu.unified.base.BaseResponse;
import com.bimowu.unified.login.model.KsUser;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * SSO单点登录服务接口
 */
public interface SsoService {

    /**
     * 用户登录
     * @param username 用户名/昵称/邮箱/手机号
     * @param password 密码
     * @param clientId 客户端ID
     * @return 登录结果，包含token等信息
     */
    BaseResponse<Map<String, Object>> login(String username, String password, String clientId, String redirectUrl, HttpServletResponse  response);

    /**
     * 手机号登录
     * @param mobile 手机号
     * @param verifyCode 验证码
     * @param clientId 客户端ID
     * @return 登录结果，包含token等信息
     */
    BaseResponse<Map<String, Object>> mobileLogin(String mobile, String verifyCode, String clientId, String redirectUrl, HttpServletResponse response);

    /**
     * 验证token是否有效
     * @return 验证结果，包含用户信息
     */
    BaseResponse<Map<String, Object>> validateToken();
    
    /**
     * 刷新token
     * @param token 旧令牌
     * @param clientId 客户端ID
     * @return 新令牌
     */
    BaseResponse<Map<String, Object>> refreshToken(String token, String clientId);
    
    /**
     * 退出登录
     * @param response HTTP响应对象，用于清除Cookie
     * @param request HTTP请求对象，用于获取当前会话信息
     * @return 退出结果
     */
    BaseResponse<String> logout(HttpServletResponse response, HttpServletRequest request);
    
    /**
     * 注册客户端应用
     * @param appName 应用名称
     * @param appUrl 应用URL
     * @param redirectUrl 回调URL
     * @return 客户端ID和密钥
     */
    BaseResponse<Map<String, String>> registerClient(String appName, String appUrl, String redirectUrl);
} 